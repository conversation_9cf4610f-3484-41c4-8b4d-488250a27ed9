import { lazy, Suspense } from 'react';
import Login from '@src/pages/auth/login';
import DownloadPage from '@src/pages/downloadPage';
import Statistics from '@src/pages/statistics';
import { getToken, roleTypeIsManage, setToken } from '@src/utils/tokenUtils';
import { DotLoading } from 'antd-mobile';
import { parse, stringify } from 'qs';
import {
  createHashRouter,
  type LoaderFunction,
  Navigate,
  redirect,
  RouteObject,
  RouterProvider,
} from 'react-router-dom';
import Layout from '../layout';
import ErrorPage from '../pages/error-page';
const isDudao = roleTypeIsManage();

// token 鉴权
const loaderCheckAuth: LoaderFunction = async ({ request }) => {
  const { search, pathname } = new URL(request.url);
  const { token, ...rest } = parse(search.slice(1));
  // 暂时加一个设备统计的页面不校验token
  if (pathname === '/statistics') return;
  if (token) {
    await setToken(token as string);
    return redirect(`${pathname}${Object.keys(rest).length ? `?${stringify(rest)}` : ''}`);
  } else {
    const localToken = getToken();
    return localToken ? null : redirect('/auth/login');
  }
};

// const DocumentTitle = ({
//   children,
// }: {
//   title: string;
//   children: React.ReactNode;
// }) => {
//   // document.title = title || '塔塔运营通';
//   return children;
// };

const dig = (list: any) => {
  return list.map(({ Component, ...v }: RouteObject & { title: string }) => {
    return {
      index: v.index,
      path: v.path,
      loader: loaderCheckAuth,
      errorElement: <ErrorPage />,
      handle: v.handle,
      children: v.children && v.children.length ? (dig(v.children) as RouteObject[]) : undefined,
      element: Component ? (
        <Suspense
          fallback={
            <div className="pt-4 text-center">
              <DotLoading color="primary" />
            </div>
          }
        >
          {/* <DocumentTitle title={v.title}> */}
          <Component />
          {/* </DocumentTitle> */}
        </Suspense>
      ) : (
        v.element
      ),
    };
  });
};

const routes = [
  {
    path: '/',
    Component: Layout,
    handle: {
      title: '塔塔运营通',
    },
    children: [
      { index: true, element: <Navigate to="/home" replace /> },
      {
        title: '数据看板',
        path: '/boards',
        handle: {
          title: '数据看板',
        },
        Component: lazy(() => import('../pages/boards')),
        children: [
          {
            title: '人力明细列表',
            handle: {
              title: '人力明细列表',
            },
            path: '/boards/hr/list',
            Component: lazy(() => import('../pages/boards/hr/list')),
          },
          {
            title: '人力明细数据',
            handle: {
              title: '人力明细数据',
            },
            path: '/boards/hr/detail',
            Component: lazy(() => import('../pages/boards/hr/detail')),
          },
          {
            title: '稽查明细列表',
            handle: {
              title: '稽查明细列表',
            },
            path: '/boards/audit/list',
            Component: lazy(() => import('../pages/boards/audit/list')),
          },
          {
            title: '稽查明细数据',
            handle: {
              title: '稽查明细数据',
            },
            path: '/boards/audit/detail',
            Component: lazy(() => import('../pages/boards/audit/detail')),
          },
          {
            title: '业绩明细列表',
            handle: {
              title: '业绩明细列表',
            },
            path: '/boards/achievement/list',
            Component: lazy(() => import('../pages/boards/achievement/list')),
          },
          {
            title: 'TC分析',
            handle: {
              title: 'TC分析',
            },
            path: '/boards/achievement/detail/tc',
            Component: lazy(() => import('../pages/boards/achievement/detail/tc')),
          },
          {
            title: '门店实时预计实付收入分析',
            handle: {
              title: '门店实时预计实付收入分析',
            },
            path: '/boards/achievement/detail/estimatedRevenue',
            Component: lazy(() => import('../pages/boards/achievement/detail/estimatedRevenue')),
          },
          {
            title: '市场分析明细',
            handle: {
              title: '市场分析明细',
            },
            path: '/boards/market/detail',
            Component: lazy(() => import('../pages/boards/market/detail')),
          },
          {
            title: '满意度看板列表',
            handle: {
              title: '满意度看板列表',
            },
            path: '/boards/satisfaction/list',
            Component: lazy(() => import('../pages/boards/satisfaction/list')),
          },
          {
            title: '历史报告',
            handle: {
              title: '历史报告',
            },
            path: '/boards/exception/report',
            Component: lazy(() => import('../pages/boards/reportList')),
          },
          {
            title: '快速派发任务',
            handle: {
              title: '快速派发任务',
            },
            path: '/boards/quick_creation',
            Component: lazy(() => import('../pages/boards/quickCreation')),
          },
        ],
      },
      {
        title: '数据看板',
        path: '/boards/select',
        handle: {
          title: '数据看板',
        },
        Component: lazy(() => import('../pages/boards/selectBoard')),
      },
      {
        title: '任务中心',
        handle: {
          title: '任务中心',
        },
        path: '/tasks',
        children: [
          { index: true, element: <Navigate to="/tasks/list" replace /> },
          {
            title: '新建任务',
            handle: {
              title: '新建任务',
            },
            path: '/tasks/create',
            Component: lazy(() => import('../pages/tasks/create')),
          },
          {
            title: '任务管理',
            handle: {
              title: '任务管理',
            },
            path: '/tasks/detail/:taskUserId',
            Component: lazy(() => import('../pages/tasks/detail')),
          },
          {
            title: '任务中心',
            handle: {
              title: '任务中心',
            },
            path: '/tasks/list',
            Component: lazy(() => import('../pages/tasks/layout')),
            children: [
              // { index: true, element: <Navigate to="/tasks/list/normalWork" replace /> },
              // 常规任务都走这个路由
              {
                title: '任务中心',
                handle: {
                  title: '任务中心',
                },
                path: '/tasks/list/normalWork',
                Component: lazy(() => import('../pages/tasks/normalWork')),
              },
              {
                title: '任务中心',
                handle: {
                  title: '任务中心',
                },
                path: '/tasks/list/patrol',
                Component: lazy(() => import('../pages/tasks/patrol')),
              },
              {
                title: '任务中心',
                handle: {
                  title: '任务中心',
                },
                path: '/tasks/list/principal/patrol',
                Component: lazy(() => import('../pages/tasks/yyt/principal_patrol')),
              },
              {
                title: '任务中心',
                handle: {
                  title: '任务中心',
                },
                path: '/tasks/list/principal/structKill',
                Component: lazy(() => import('../pages/tasks/yyt/principal_structKill')),
              },
              {
                title: '任务中心',
                handle: {
                  title: '任务中心',
                },
                path: '/tasks/list/created/emergencyStructKill',
                Component: lazy(() => import('../pages/tasks/yyt/CreateEmergencyStructKill')),
              },
              {
                // 我管辖的巡检任务
                title: '任务中心',
                handle: {
                  title: '任务中心',
                },
                path: '/tasks/list/under/patrol',
                Component: lazy(() => import('../pages/tasks/yyt/under_patrol')),
              },
              {
                // 我管辖的自检任务
                title: '任务中心',
                handle: {
                  title: '任务中心',
                },
                path: '/tasks/list/under/self',
                Component: lazy(() => import('../pages/tasks/yyt/under_self')),
              },
              {
                // 我管辖的巡检任务
                title: '任务中心',
                handle: {
                  title: '任务中心',
                },
                path: '/tasks/list/under/structKill',
                Component: lazy(() => import('../pages/tasks/yyt/under_structKill')),
              },
            ],
          },
          {
            title: '任务执行时段确认明细',
            handle: {
              title: '任务执行时段确认明细',
            },
            path: '/tasks/execute/confirm',
            Component: lazy(() => import('../pages/tasks/taskExecuteConfirm')),
          },
          {
            title: '添加巡检任务计划',
            path: 'patrol/create',
            Component: lazy(() => import('../pages/tasks/patrolTask')),
            handle: {
              title: '添加巡检任务计划',
              RNtitleShow: true,
            },
            children: [
              {
                title: '检查表',
                path: 'checklist',
                handle: {
                  title: '检查表',
                  RNtitleShow: true,
                },
                Component: lazy(() => import('../pages/tasks/patrolTask/Checklist')),
                children: [
                  {
                    title: '检查表',
                    path: 'select',
                    handle: {
                      title: '检查表',
                      RNtitleShow: true,
                    },
                    Component: lazy(() => import('../pages/tasks/patrolTask/Checklist/Select')),
                  },
                ],
              },
              {
                title: '签到/签离',
                handle: {
                  title: '签到/签离',
                  RNtitleShow: true,
                },
                path: 'signInOrOut',
                Component: lazy(() => import('../pages/tasks/patrolTask/SignInOrOut')),
              },
              {
                title: '巡检人',
                path: 'inspector',
                handle: {
                  title: '巡检人',
                  RNtitleShow: true,
                },
                Component: lazy(() => import('../pages/tasks/patrolTask/Inspector')),
              },
              {
                title: '巡检组织',
                handle: {
                  title: '巡检组织',
                  RNtitleShow: true,
                },
                path: 'organization',
                Component: lazy(() => import('../pages/tasks/patrolTask/Organization')),
              },
              {
                title: '必检门店',
                handle: {
                  title: '必检门店',
                  RNtitleShow: true,
                },
                path: 'mandatory',
                Component: lazy(() => import('../pages/tasks/patrolTask/Mandatory')),
              },
            ],
          },
          {
            title: '创建计划外到店巡检任务',
            path: 'patrol/unplanned',
            Component: lazy(() => import('../pages/tasks/unplannedTask')),
            handle: {
              title: (queryParams: any) => {
                if (!!queryParams.subType) {
                  return '创建计划外食安线下稽核任务';
                }
                return '创建计划外到店巡检任务';
              },
              RNtitleShow: true,
            },
            children: [
              {
                title: '添加任务',
                path: 'create',
                Component: lazy(() => import('../pages/tasks/unplannedTask/create')),
                handle: {
                  title: '添加任务',
                },
              },
            ],
          },
          {
            title: '检查表',
            path: 'checklist/select',
            Component: lazy(() => import('../pages/tasks/checklist')),
            handle: {
              title: '检查表',
            },
          },
        ],
      },
      {
        title: '看板数据',
        path: '/dataViewBoard',
        handle: {
          title: '看板数据',
          RNtitleShow: true,
        },
        Component: lazy(() => import('../pages/dataViewBoard/index')),
        children: [],
      },
      {
        title: '数据实时情况',
        path: '/dataViewBoardRealTime',
        handle: {
          title: '数据实时情况',
          RNtitleShow: true,
        },
        Component: lazy(() => import('../pages/dataViewBoard/tactics')),
        children: [],
      },
      // 智慧运管看板
      {
        title: '看板数据',
        path: '/smartOperationBoard',
        handle: {
          title: '看板数据',
        },
        Component: lazy(() => import('../pages/boards/smartOperationBoard')),
      },
      // 门店数据看板
      {
        title: '门店数据',
        path: '/storeDataViewBoard',
        handle: {
          title: '看板数据',
          RNtitleShow: true,
        },
        Component: lazy(() => import('../pages/boards/storeDataViewBoard')),
      },
      {
        title: '到家加盟业主看板',
        path: '/arrive',
        handle: {
          title: '到家加盟业主看板',
        },
        Component: lazy(() => import('../pages/boards/arrive')),
      },
      {
        title: '检查项排行',
        handle: {
          title: '检查项排行',
        },
        path: '/dataViewBoard/checkItemRank',
        Component: lazy(() => import('../pages/dataViewBoard/checkItemRank/index')),
      },
      {
        title: '检查项排行详情',
        handle: {
          title: '检查项排行详情',
        },
        path: '/dataViewBoard/CheckItemDetail',
        Component: lazy(() => import('../pages/dataViewBoard/checkItemDetail/index')),
      },
      {
        title: '子组织情况详情',
        handle: {
          title: '子组织情况详情',
        },
        path: '/dataViewBoard/subGroupCondition',
        Component: lazy(() => import('../pages/dataViewBoard/subConditionDetail/index')),
      },
      {
        title: '巡检不合格情况详情',
        handle: {
          title: '巡检不合格情况详情',
        },
        path: '/dataViewBoard/patrolUnqualifiedDetail',
        Component: lazy(() => import('../pages/dataViewBoard/patrolUnqualified/detail')),
      },
      {
        title: '不合格内容',
        handle: {
          title: '不合格内容',
        },
        path: '/dataViewBoard/patrolUnqualifiedContent',
        Component: lazy(() => import('../pages/dataViewBoard/patrolUnqualified/content')),
      },
      {
        title: '到店巡检',
        handle: {
          title: '到店巡检',
        },
        path: '/patrol/shopcheck',
        Component: lazy(() => import('../pages/patrol/index')),
      },
      {
        title: '自检检查',
        handle: {
          title: '自检检查',
        },
        path: '/self/review',
        Component: lazy(() => import('../pages/selfCheckComment/index')),
      },
      {
        title: '自检报告详情',
        handle: {
          title: '自检报告详情',
          RNtitleShow: true,
        },
        path: '/self/reportdetail',
        Component: lazy(() => import('../pages/reportDetail/self/index')),
      },
      {
        title: '巡检报告详情',
        handle: {
          title: '巡检报告详情',
          RNtitleShow: true,
        },
        path: '/patrol/reportdetail',
        Component: lazy(() => import('../pages/reportDetail/patrol/index')),
      },
      {
        title: '巡检点评',
        handle: {
          title: '巡检点评',
        },
        path: '/patrol/reportdetail/review',
        Component: lazy(() => import('../pages/reportDetail/patrol/review')),
      },
      {
        title: '添加任务',
        handle: {
          title: '添加任务',
        },
        path: '/patrol/plan/add',
        Component: lazy(() => import('../pages/patrol/addWork/index')),
      },
      {
        title: '巡检计划详情',
        handle: {
          title: '巡检计划详情',
        },
        path: '/patrol/planList/planDetail',
        Component: lazy(() => import('../pages/patrol/planDetail/index')),
      },
      {
        title: '附近门店',
        handle: {
          title: '附近门店',
        },
        path: '/patrol/plan/nearshop',
        Component: lazy(() => import('../pages/patrol/nearShop/index')),
      },
      {
        title: '签到/签离',
        handle: {
          title: '签到/签离',
        },
        path: '/patrol/plan/sign',
        Component: lazy(() => import('../pages/patrol/sign/index')),
      },
      {
        title: '整改跟踪',
        handle: {
          title: '整改跟踪',
          RNtitleShow: true,
        },
        path: '/tasks/pollingAbarbeitung', // pollingType:'自检-SelfPolling' | '巡检-NormalPolling'; cross:权限内外
        Component: lazy(() => import('../pages/tasks/pollingAbarbeitung')),
      },
      {
        title: '循环整改项',
        handle: {
          title: '循环整改项',
        },
        path: '/tasks/circularRectification',
        Component: lazy(() => import('../pages/tasks/circularRectification')),
      },
      {
        title: '整改任务',
        handle: {
          title: '整改任务',
          RNtitleShow: true,
        },
        path: '/tasks/pollingAbarbeitung/paTaskList',
        Component: lazy(() => import('../pages/tasks/pollingAbarbeitung/PATaskList')),
        // Component: lazy(() => import('../pages/tasks/pollingAbarbeitung/PATaskListNew')),
      },
      {
        title: '整改任务', // 店长侧 1.1的调整
        handle: {
          title: '整改任务',
          RNtitleShow: true,
        },
        path: '/tasks/pollingAbarbeitung/paTaskList/shop',
        // Component: lazy(() => import('../pages/tasks/pollingAbarbeitung/PATaskList')),
        Component: lazy(() => import('../pages/tasks/pollingAbarbeitung/PATaskListNew')),
      },
      {
        title: '整改详情',
        handle: {
          title: '整改详情',
          RNtitleShow: true,
        },
        path: '/tasks/pollingAbarbeitung/paTaskList/detail',
        Component: lazy(() => import('../pages/tasks/pollingAbarbeitung/detail')),
      },
      {
        title: isDudao ? '驳回' : '整改反馈提交',
        handle: {
          title: isDudao ? '驳回' : '整改反馈提交',
          RNtitleShow: true,
        },
        path: '/tasks/pollingAbarbeitung/edit',
        Component: lazy(() => import('../pages/tasks/pollingAbarbeitung/Edit')),
      },
      {
        title: '公告详情',
        handle: {
          title: '公告详情',
        },
        path: '/notice/detail',
        Component: lazy(() => import('../pages/notice/detail')),
      },
      {
        title: '申诉待处理任务',
        path: '/complaint/waitForDeal',
        Component: lazy(() => import('../pages/tasks/complaintWaitForDeal')),
      },
      {
        title: '首页',
        path: '/home',
        Component: lazy(() => import('../pages/home')),
      },
      {
        title: '首页',
        path: '/alive-home',
        Component: lazy(() => import('../pages/aliveHome')),
      },
      {
        title: '报告',
        path: '/mission',
        handle: {
          title: '报告中心',
          RNtitleShow: true,
        },
        Component: lazy(() => import('../pages/mission')),
      },
      {
        title: '到店巡检（权限外）',
        path: '/missionOutSide',
        handle: {
          title: '报告中心',
        },
        Component: lazy(() => import('../pages/mission/missionOutSide')),
      },
      {
        title: '欢迎',
        path: '/welcome',
        Component: lazy(() => import('../pages/welcome')),
      },
      {
        title: '计划管理',
        path: '/plan',
        handle: {
          title: '塔塔运营通',
        },
        Component: lazy(() => import('../pages/plan')),
      },
      {
        title: '面试任务',
        path: '/recruit/interview',
        handle: {
          title: '面试任务',
        },
        Component: lazy(() => import('../pages/recruit/interview')),
      },
      {
        title: '查看详情',
        path: '/recruit/interview/detail',
        handle: {
          title: '查看详情',
          dynamicTitle: true,
        },
        Component: lazy(() => import('../pages/recruit/interview/detail')),
      },
      {
        title: '招聘管理',
        path: '/recruit',
        handle: {
          title: '招聘管理',
        },
        Component: lazy(() => import('../pages/recruit')),
      },
      {
        title: '门店招聘需求提报',
        path: '/recruit/demand',
        handle: {
          title: '门店招聘需求提报',
          dynamicTitle: true,
        },
        Component: lazy(() => import('../pages/recruit/demand')),
      },

      {
        title: '门店招聘需求提报',
        path: '/recruit/demand/detail',
        handle: {
          title: '门店招聘需求提报',
          dynamicTitle: true,
        },
        Component: lazy(() => import('../pages/recruit/demand/detail')),
      },
      {
        title: '门店招聘需求审核',
        path: '/recruit/demand/result',
        handle: {
          title: '门店招聘需求审核',
          dynamicTitle: true,
        },
        Component: lazy(() => import('../pages/recruit/demand/detail/result')),
      },
      {
        title: '品牌投流计划管理',
        path: '/flow',
        children: [
          { index: true, element: <Navigate to="/flow/index" replace /> },
          {
            handle: {
              title: '品牌投流计划管理',
            },
            title: '品牌投流计划管理',
            path: '/flow/index',
            Component: lazy(() => import('../pages/flow/index')),
          },
          {
            handle: {
              title: '品牌投流计划详情',
            },
            title: '品牌投流计划详情',
            path: '/flow/detail',
            Component: lazy(() => import('../pages/flow/detail')),
          },
          {
            title: '批量调整',
            path: '/flow/batchEdit',
            Component: lazy(() => import('../pages/flow/batchEdit')),
          },
          {
            title: '批量调整',
            path: '/flow/addBatchEdit',
            Component: lazy(() => import('../pages/flow/addBatchEdit')),
          },
          {
            handle: {
              title: '选择门店',
            },
            title: '选择门店',
            path: '/flow/shopSelected',
            Component: lazy(() => import('../pages/flow/shopSelected')),
          },
        ],
      },
    ],
  },
];

const main_routes: RouteObject[] = dig(routes);
console.log('mainRouter', main_routes);

function getRouterMap(routes: any[]) {
  const routeMap: any = {};

  routes.forEach((route) => {
    const path = `${route.path}`;
    routeMap[path] = route.title || route?.handle?.title;

    if (route.children && route.children.length > 0) {
      const childMap = getRouterMap(route.children);
      Object.assign(routeMap, childMap);
    }
  });

  return routeMap;
}

// eslint-disable-next-line react-refresh/only-export-components
export const routerMap = getRouterMap(routes);

const Routers = () => {
  const router = createHashRouter([
    ...main_routes,
    {
      path: '/auth/login',
      element: <Login />,
    },
    {
      path: '/statistics',
      element: <Statistics />,
    },
    {
      path: '/downloadPage',
      element: <DownloadPage />,
    },
  ]);
  return <RouterProvider router={router} />;
};

export default Routers;
