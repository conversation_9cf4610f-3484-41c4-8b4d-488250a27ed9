import type { AxiosRequestConfig } from 'axios';
import api from './axios';
import errorHandler from './errorHandler';
import type { HttpResult } from './result';

type Options = AxiosRequestConfig & {
  /* 是否统一notification展示错误信息 */
  noAlert?: boolean;
  /* 是否直接返回response */
  useOriginResponse?: boolean;
};

const customRequest =
  (method: 'POST' | 'GET' | 'DELETE' | 'PUT') =>
  <T = any>(url: string, { useOriginResponse, ...options }: Options = {}): Promise<T> => {
    return api
      .request<HttpResult<T>>({
        url,
        ...options,
        method,
      })
      .then(async (res) => {
        if (useOriginResponse) return res as unknown as T;

        return res.data.data ?? (res.data as any).result;
      })
      .catch(errorHandler(!options.noAlert));
  };

const request = {
  get: customRequest('GET'),
  post: customRequest('POST'),
  del: customRequest('DELETE'),
  put: customRequest('PUT'),
};

export const { get, post, del, put } = request;
