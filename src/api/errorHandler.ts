import { Toast } from 'antd-mobile';

const errorHandler = (alertErr: boolean) => (error: any) => {
  if (error) {
    if (alertErr) {
      const { response, message } = error;
      const msg = response?.data?.message || message || '';
      const code = response?.data?.code || response?.statusCode || '';
      Toast.show({
        content: `${msg}${code ? `【code：${code}】` : ''}`,
      });
    }
  }
  throw error;
};

export default errorHandler;
