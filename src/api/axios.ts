import { getBrandId, getTag, getToken, getVersion, roleTypeIsManage } from '@src/utils/tokenUtils';
import axios from 'axios';
import { ErrCancel, ServerErrorException, UnauthorizedException } from './errors';
import { logout } from '../utils/tokenUtils';

const api = axios.create({});

api.interceptors.request.use((config) => {
  const token = getToken();
  const tag = getTag();
  /** 判断是否是督导 */
  const isDudao = roleTypeIsManage();
  if (token) {
    config.headers.Authorization = `${token}`;
    config.headers['user-token'] = `${token}`;
    config.headers['token'] = `${token}`; // 招聘助手
    config.headers['Role-type'] = isDudao ? 'CORP' : 'SHOP';
    config.headers['Brand-Id'] = getBrandId();
    config.headers['tag'] = tag;
    config.headers['version'] = getVersion();
    config.headers['platform'] = 'SHM_APP';
  }
  return config;
});

api.interceptors.response.use(
  (response) => {
    const { data } = response;
    // 紧急需求先临时加下，后续会删除
    if (response.config.url === '/weboffice-admin-api/collect') return response;

    // 根据后端请求结构，如果code不为200，则认为是异常
    if (data.code !== 200) {
      // 抛出服务器异常，并且携带response
      return Promise.reject(new ServerErrorException(data.message || data.msg, response));
    }

    return response;
  },
  (error) => {
    if (error.code === 'ERR_NETWORK') {
      return Promise.reject(new ServerErrorException('网络错误，请检查网络！'));
    }

    if (error?.code === 'ERR_CANCELED') {
      return Promise.reject(new ErrCancel());
    }

    if (error.response.status === 401) {
      logout();
      return Promise.reject(new UnauthorizedException());
    }

    if (error.response.status === 500) {
      return Promise.reject(new ServerErrorException(error.response.data.message, error.response));
    }
    if (error.response.data && error.response.data.message) {
      return Promise.reject(new Error(error.response.data.message));
    }
    return Promise.reject(error);
  },
);

export default api;
