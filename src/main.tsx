import * as Sentry from '@sentry/react';
import { getEnv, getVersion } from '@src/utils/tokenUtils';
import ReactDOM from 'react-dom/client';
import VConsole from 'vconsole';
import App from './App.tsx';
import './index.css';

const env = getEnv(); // ENV: 'test' | 'stage' | 'prod';
const version = getVersion();

// 调试vConsole
if (env && env !== 'prod') {
  const vConsole = new VConsole();
}

if (env === 'prod') {
  Sentry.init({
    dsn: 'https://<EMAIL>/31',
    tracesSampleRate: 1.0,
    environment: getEnv() || 'prod',
    autoSessionTracking: false,
    release: `H5-${version}`,
  });
}

ReactDOM.createRoot(document.getElementById('root')!).render(<App />);
