:root {
  // --adm-color-primary: #378BFF;
  --adm-color-primary: #378bff;
  --adm-color-text: #151516;
  --adm-font-size-main: 14px;
  // 主题浅色
  --color-primary-1: rgba(55, 139, 255, 0.1);
  --adm-color-border: rgba(0, 0, 0, 0.03);
  --z-index: 1002;
  --adm-dialog-z-index: 1002;
  --adm-popup-z-index: 1002;
  --adm-mask-z-index: 1002;
}

.adm-error-block .adm-error-block-image svg {
  display: inline;
}

.ka-wrapper {
  height: 100%;
}
.ka-content {
  height: 100%;
}

.ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.ellipsis-2 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2; // 控制多行的行数
  -webkit-box-orient: vertical;
}

.ellipsis-1 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 1; // 控制多行的行数
  -webkit-box-orient: vertical;
}

.adm-toast-icon svg {
  display: inline;
}
.adm-ellipsis {
  a {
    color: var(--adm-color-primary);
  }
}

.adm-dropdown-popup-body {
  border-radius: 0 0 8px 8px;
  overflow: hidden;
}

.tdm {
  &-modal-body {
    &.adm-modal-body:not(.adm-modal-with-image) {
      padding-top: 32px;
    }

    .adm-modal-content {
      padding: 0 24px 16px 24px;
    }
  }
  &-search-bar {
    .adm-search-bar-input-box .adm-search-bar-input-box-icon {
      color: #858585;
    }
  }
  &-form {
    // .adm-form-item-child {
    //   flex: auto;
    // }

    .adm-list-item {
      padding-left: 16px;
    }
    .adm-list-body {
      background-color: transparent;
    }
    .adm-form-item-label {
      font-size: 16px;
      line-height: 24px;
      font-weight: 400;
      color: #141414;
      // display: flex;
      // align-items: center;
    }
    .adm-list-item-content-main {
      padding: 0;
      display: flex;
      align-items: center;
      justify-content: flex-end;
    }

    // .adm-input {
    //   height: 24px;
    //   min-height: 24px;
    // }
    .adm-input-element {
      // height: 24px;
      // min-height: 24px;
      font-size: 16px;
      text-align: right;
    }
    .adm-list-item-content {
      border-top: none;
      padding-right: 16px;
    }

    .adm-form-item {
      border-top: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: 1px solid #f0f0f0;
      }
    }

    .adm-selector {
      .adm-space-horizontal > .adm-space-item {
        margin-right: 8px;
        &:last-child {
          margin-right: 0;
        }
      }
    }

    .adm-selector-item {
      border-radius: 4px;
      font-size: 16px;
      line-height: 24px;
      padding: 2px 8px;
      background-color: #f5f5f5;
      color: #858585;
    }

    .adm-selector-item-active,
    .adm-selector-item-multiple-active {
      background: rgba(55, 139, 255, 0.1);
      color: #378bff;
    }

    &-long-label {
      .adm-form-item-label {
        width: 136px;
      }
    }
  }

  .adm-radio {
    display: flex;
    padding: 12px 16px;
    background-color: #fff;
    border-top: 1px solid #f0f0f0;
    font-size: 16px;
    line-height: 23px;
    font-weight: 400;
    &:last-child {
      border-bottom: 1px solid #f0f0f0;
    }
  }

  &-checkbox-reverse {
    display: flex;
    flex-direction: row-reverse;
    justify-content: space-between;
    align-items: center;

    .adm-checkbox-content {
      font-size: 15px;
      color: #151516;
      font-weight: 400;
      line-height: 24px;
    }

    .adm-checkbox-icon {
      width: 20px;
      height: 20px;
    }
  }

  &-checkbox {
    line-height: 24px;
    .adm-checkbox-icon {
      width: 20px;
      height: 20px;
    }
    .adm-checkbox-content {
      flex: auto;
      font-size: 16px;
      font-weight: 400;
      color: #141414;
    }
  }

  &-list {
    &-item {
      & + & {
        border-top: 1px solid #f0f0f0;
      }
      .adm-list-item-content {
        border-top: none;
      }
    }
  }

  &-dropdown {
    .adm-dropdown-item {
      margin: 8px 0;
      justify-content: start;
      flex: none;
      margin-right: 8px;
      color: #5e5e5e;
    }
    .adm-dropdown-item .adm-dropdown-item-title {
      background-color: #f5f5f5;
      padding: 3px 8px;
      border-radius: 4px;
    }
    .adm-dropdown-item .adm-dropdown-item-title-text {
      line-height: 22px;
      font-size: 14px;
    }
  }

  &-error-block {
    .adm-error-block-description {
      font-size: 14px;
      line-height: 28px;
      margin-top: 9px;
    }

    .adm-error-block-image img {
      margin: auto;
    }
  }

  &-popover {
    .adm-popover-inner {
      border-radius: 4px;
    }
    .adm-popover-inner-content {
      padding: 8px;
    }
  }
}
