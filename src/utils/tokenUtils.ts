import { getUser } from '@src/common/api';

const TOKEN_STORAGE_KEY = 'TST_SPEED_H5_TOKEN_V1';
const TST_TAG = 'TST_SPEED_H5_TAG_V1';
const ROLE_TYPE_STORAGE_KEY = 'TST_SPEED_H5_ROLE_V1';
const TST_SPEED_H5_BRAND_ID_V1 = 'TST_SPEED_H5_BRAND_ID_V1';
const TST_SPEED_H5_SHOP_ID_V1 = 'TST_SPEED_H5_SHOP_ID_V1';
const TST_SPEED_H5_USER_ID_V1 = 'TST_SPEED_H5_USER_ID_V1';
const TST_SPEED_H5_ROLE_NAME_V1 = 'TST_SPEED_H5_ROLE_NAME_V1';
const TST_ENV = 'TST_ENV'; // ENV: 'test' | 'stage' | 'prod';
const RNWebview = 'RNWebview';
const TST_VERSION = 'TST_VERSION';
const getCookie = (name: string) => {
  const cookies = document.cookie.split('; '); // 将所有Cookie分隔成数组

  for (let i = 0; i < cookies.length; i++) {
    const cookiePair = cookies[i].split('='); // 将每个Cookie分隔成名称和值的数组

    if (cookiePair[0] === RNWebview) {
      // 如果当前Cookie的名称与目标名称相同
      const value = decodeURIComponent(cookiePair[1]); // 返回对应的值（需要进行URL编码解码）
      return value ? JSON.parse(value)[name] : value;
    }
  }

  return null; // 没有找到指定名称的Cookie时返回null或undefined
};

const setCookie = (key: string, value: string) => {
  document.cookie = `${RNWebview}=${JSON.stringify({ [key]: value })}`;
  return true;
};

const removeCookie = (name: string) => {
  document.cookie = `${name}=; expires=${new Date().toUTCString()}; path=/`;
};

const getToken = (): string | null => {
  return getCookie(TOKEN_STORAGE_KEY);
};

const getTag = (): string | null => {
  return getCookie(TST_TAG);
};

const setToken = async (token: string) => {
  if (!(window as any).ReactNativeWebView) {
    console.log('浏览器环境');
    // 不是rn环境，硬编码
    const userInfo = await getUser(token);
    document.cookie = `${RNWebview}=${JSON.stringify({
      [TOKEN_STORAGE_KEY]: token,
      [ROLE_TYPE_STORAGE_KEY]: '1',
      [TST_SPEED_H5_SHOP_ID_V1]: 'T129',
      [TST_SPEED_H5_BRAND_ID_V1]: userInfo?.brandBaseInfos?.[0].brandId || '6533',
    })}`;
  }
};

const removeToken = (): void => {
  return removeCookie(RNWebview);
};

/**
 * @description 获取当前用户的角色类型信息
 * @returns 角色类型信息
 */
export function getCurrentRoleTypeInfo() {
  const currentRoleType = getCookie(ROLE_TYPE_STORAGE_KEY) as string;
  // 1:督导 2:门店 5:加盟商
  const isSupervisor = currentRoleType === '1' || currentRoleType === '5';
  const isShop = currentRoleType === '2';

  return {
    /** 当前用户的角色类型 */
    currentRoleType,
    /** 是否为督导 */
    isSupervisor,
    /** 是否为门店 */
    isShop,
  };
}

const roleTypeIsManage = () => getCookie(ROLE_TYPE_STORAGE_KEY) === '1' || getCookie(ROLE_TYPE_STORAGE_KEY) === '5'; // 品牌端督导是true 店长是false

const getBrandId = () => getCookie(TST_SPEED_H5_BRAND_ID_V1);

const getUserId = (): string | null => {
  return getCookie(TST_SPEED_H5_USER_ID_V1);
};
const getCurShopId = (): string | null => {
  return getCookie(TST_SPEED_H5_SHOP_ID_V1);
};
const getRoleName = (): string | null => {
  return decodeURIComponent(getCookie(TST_SPEED_H5_ROLE_NAME_V1) || '');
};

const getEnv = (): string | null => {
  return getCookie(TST_ENV);
};
const getVersion = (): string | null => {
  return getCookie(TST_VERSION);
};
// 退出登录
const logout = () => {
  // APP通知退出登录
  if ((window as any).ReactNativeWebView) {
    if ((window as any)?.['ReactNativeWebView']?.['postMessage']) {
      // 安卓
      (window as any)['ReactNativeWebView']['postMessage'](JSON.stringify({ handleLogout: true }));
    } else {
      // ios
      window.parent.postMessage(JSON.stringify({ handleLogout: true }), '*');
    }
  } else {
    removeToken();
    window.location.href = `${window.location.protocol}//${window.location.host}/#/auth/login`;
  }
};

export {
  getToken,
  removeToken,
  setToken,
  roleTypeIsManage,
  TOKEN_STORAGE_KEY,
  getBrandId,
  getUserId,
  getCookie,
  getRoleName,
  getEnv,
  logout,
  getTag,
  getVersion,
  getCurShopId,
};
