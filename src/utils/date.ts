import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
dayjs.extend(utc);

const SecondStamp: number = 1000;
const MinuteStamp: number = 60 * SecondStamp;
const HourStamp: number = 60 * MinuteStamp;
const DayStamp: number = 24 * HourStamp;

//将时间格式转成UTC时间
export const formatDateToUTC = (date: any) => {
  return dayjs(date).utc().format();
};

//计算时间差
export const calcTimeDiff = (start: any, end: any) => {
  const diffSeconds = dayjs(start).diff(dayjs(end), 'milliseconds');
  let remaining: number = diffSeconds;
  const days: number = Math.floor(Math.abs(diffSeconds / DayStamp));
  remaining %= DayStamp;
  const hours: number = Math.floor(Math.abs(remaining / HourStamp));
  remaining %= HourStamp;
  const minutes: number = Math.floor(Math.abs(remaining / MinuteStamp));
  remaining %= MinuteStamp;
  const seconds: number = Math.floor(Math.abs(remaining / SecondStamp));

  return `${days>0&&days+"天"}${hours>0&&hours+"小时"}${minutes>0&&minutes+"分钟"}${seconds>0&&seconds+"秒"}`;
};
