import { getOssFileUrl, getOssToken } from '@src/common/api';
import type { OssTokenProps } from '@src/common/api.type';
import { Toast } from 'antd-mobile';
import dayjs, { type Dayjs } from 'dayjs';
import { isNumber } from 'lodash';
import OSS from 'ali-oss';
import { nanoid } from 'nanoid';

/**
 * 把对象中的某个字段转换为 JSON 字符串
 * @param pathnames 需要转换的字段路径，支持通过字符串数组的方式选择嵌套路径
 */
export function convertFieldToJSON(obj: any, pathnames: string[]) {
  const newObj = { ...obj };
  pathnames.forEach((pathname) => {
    const keys = pathname.split('.');
    let temp = newObj;
    keys.forEach((key, index) => {
      if (index === keys.length - 1) {
        temp[key] = JSON.stringify(temp[key]);
      } else {
        temp = temp[key];
      }
    });
  });
  return newObj;
}

/**
 * 把枚举转换为Select组件中的options
 * @param enumObj 枚举对象
 * @param enumNames 可选的 枚举值的名称 {xxx: 'xxx'}, 如果不传则使用枚举对象的key作为label
 * @param exclude 可选的 排除的枚举值 ['xxx']，不会出现在options中
 * @returns Select组件中的options [{label: 'xxx', value: 'xxx'}]
 */
export function convertEnumToOptions(
  enumObj: any,
  enumNames?: Record<string, any>,
  exclude: string[] = [],
) {
  return Object.keys(enumObj)
    .filter((key) => !exclude.includes(enumObj[key]))
    .map((key) => ({ label: enumNames ? enumNames[enumObj[key]] : key, value: enumObj[key] }));
}

/**
 * 把JSON字符串反序列化为JSON对象
 * @param str JSON字符串
 * @returns 反序列化后的JSON对象，如果解析失败则返回undefined
 */
export const parserJSON = <T = any>(str: string): T | undefined => {
  try {
    return JSON.parse(str);
  } catch (e) {
    return undefined;
  }
};

/**
 * 把JSON对象序列化为JSON字符串
 * @param obj  JSON对象
 * @returns JSON字符串，如果序列化失败则返回undefined
 */
export const stringifyJSON = (obj: any): string | undefined => {
  try {
    return JSON.stringify(obj);
  } catch (e) {
    return undefined;
  }
};

/**
 * 将列表转换为映射表
 * @param {string} idKeyName 从列表元素中获取id的键名
 * @param {Array} list 原始数据列表
 * @returns {Map} 映射表
 */
export const convertListToMap = <K extends string | number, T extends Record<string, any>>(
  idKeyName: string,
  list: T[],
): Map<K, T> => {
  return new Map(
    list.reduce(
      (acc, cur) => {
        acc.push([cur[idKeyName], cur]);
        return acc;
      },
      [] as [K, T][],
    ),
  );
};

export type TreeNode<T extends { [key: string]: any }> = T & { children?: TreeNode<T>[] };

/**
 * 将列表转换为树结构
 * @param {string} idKeyName 从列表元素中获取id的键名
 * @param {string} parentIdKeyName 从列表元素中获取pid的键名
 * @param {Array} list 原始数据列表
 * @returns {Array} 树结构列表
 */
export function convertListToTree<T extends { [key: string]: any }>(
  idKeyName: keyof T,
  parentIdKeyName: keyof T,
  list: T[],
): TreeNode<T>[] {
  // 创建一个空的树结构列表
  const treeList: T[] = [];

  // 创建一个映射表，用于快速查找元素
  const map: { [key: string]: TreeNode<T> } = {};

  // 遍历原始数据列表，将每个元素添加到映射表中
  list.forEach((item) => {
    // 通过结构赋值clone一份数据
    map[item[idKeyName]] = { ...item };
  });

  // 遍历原始数据列表，将每个元素添加到树结构列表中
  list.forEach((item) => {
    // 获取当前元素的父节点ID
    const parentId = item[parentIdKeyName];

    // 如果当前元素没有父节点，则将其作为根节点添加到树结构列表中
    if (!parentId) {
      treeList.push(map[item[idKeyName]]);
      return;
    }

    // 如果当前元素有父节点，则将其添加到父节点的children属性中
    const parentItem = map[parentId];
    if (parentItem) {
      if (!parentItem.children) {
        parentItem.children = [];
      }
      parentItem.children.push(map[item[idKeyName]]);
    }
  });

  return treeList;
}

const tree2list = (node: TreeNode<any>, arr: any[], pid: any = 0) => {
  const newNode = { ...node };
  if (newNode.children) {
    newNode.children.forEach((item: any) => {
      tree2list(item, arr, newNode.id);
    });
  }
  delete newNode.children;

  arr.push({ ...newNode, pid });
};

/**
 * 把树形结构展开为列表
 * @param nodes 树形节点列表
 * @returns 展开的树节点列表
 */
export const convertTreeToList = <T extends Record<string, any>>(nodes: TreeNode<T>[]): T[] => {
  const result: T[] = [];
  nodes.forEach((node) => {
    tree2list(node, result, 0);
  });

  return result;
};

// 千分位转换
export const thousandthFormat = (
  num: number | string | undefined | null,
  fixed?: number | undefined,
) => {
  if (num === undefined || num === null) {
    return '';
  }
  if (typeof num === 'string' && !num.trim()) {
    return '';
  }
  if (fixed === undefined) {
    return String(num);
  }
  return String((+num).toFixed(fixed)).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

/**
 * 数字转为万/亿
 * @param value 数值
 * @param unit 显示的单位
 * @param decimal 保留的小数位数
 * @returns (10051, 元) => 1.01万元
 */
export const formatAmount = (value: number, unit = '', decimal = 2, isDiyUnit: boolean = false) => {
  if (isNaN(value)) return '';
  if (typeof value !== 'number') return '-';
  const prefix = value < 0 ? '-' : '';
  value = Math.abs(value);
  const k = 10000;
  const sizes = ['', '万'];
  let i = undefined;
  let str = '';
  if (value < k) {
    str = prefix + value + sizes[0] + unit;
  } else {
    // i = Math.floor(Math.log(value) / Math.log(k));
    i = 1;
    str = prefix + (value / Math.pow(k, i)).toFixed(decimal) + (isDiyUnit ? '' : sizes[i]) + unit;
  }
  return str;
};

export const commonSorter = (a: any, b: any, desc = 'desc') => {
  let numA = a;
  let numB = b;
  if (desc === 'desc') {
    numA = b;
    numB = a;
  }
  if (isNumber(numA) && isNumber(numB)) {
    return numA - numB;
  }
  if (!isNumber(numA) || !isNumber(numB)) {
    if (!isNumber(numA)) {
      return desc === 'desc' ? -1 : 1;
    }
    if (!isNumber(numB)) {
      return desc === 'desc' ? 1 : -1;
    }
  }
  return 0;
};

export const formatPer = (v: any, unit: string = '%', fixed?: number) => {
  if (typeof v !== 'number') {
    return '-';
  }
  return `${thousandthFormat(v, fixed)}${unit}`;
};

/**
 * 根据区间日期起始
 * @return day/default/month 日同比/同比/月同比
 */
export const getDatePercentType = (startDate: string, endDate: string) => {
  const startD = dayjs(startDate);
  const endD = dayjs(endDate);
  if (startD.format('YYYYMMDD') === endD.format('YYYYMMDD')) {
    return 'day';
  }
  if (
    startD.format('MM') === endD.format('MM') &&
    startD.format('DD') === '01' &&
    endD.format('DD') === endD.endOf('M').format('DD')
  ) {
    return 'month';
  }
  return 'default';
};

/**
 * 上传文件到 oss
 * @param {any} file:File
 * @returns {any}
 */
export const uploadFile = async (
  file: File,
  requestPreviewUrl?: boolean,
  isYYT?: boolean,
): Promise<{ key: string; url: string; bucket: string; originName: string }> => {
  if (!file) {
    Toast.show('上传文件错误');
    return Promise.reject('上传文件错误');
  }

  const getToken = async () => {
    let token: OssTokenProps;
    const tokenStr = localStorage.getItem(isYYT ? 'ALI_OSS_YYTTOKEN' : 'ALI_OSS_TOKEN');
    const tokenObj = tokenStr ? JSON.parse(tokenStr || '') : null;
    if (tokenObj && tokenObj.expiration >= dayjs().unix()) {
      token = tokenObj;
    } else {
      token = await getOssToken();
      localStorage.setItem(isYYT ? 'ALI_OSS_YYTTOKEN' : 'ALI_OSS_TOKEN', JSON.stringify(token));
    }
    return token;
  };
  const token = await getToken();
  const client = token
    ? new OSS({
        // yourRegion填写Bucket所在地域。以华东1（杭州）为例，yourRegion填写为oss-cn-hangzhou。
        region: 'oss-cn-shanghai',
        accessKeyId: token.accessKeyId,
        accessKeySecret: token.accessKeySecret,
        stsToken: token.securityToken,
        // 填写Bucket名称。
        bucket: token.bucket,
        // 刷新临时访问凭证。
        secure: true,
        refreshSTSToken: async () => {
          const newToken = await getOssToken();
          // const newToken = { accessKeyId: '', accessKeySecret: '', securityToken: '' };
          return {
            accessKeyId: newToken.accessKeyId,
            accessKeySecret: newToken.accessKeySecret,
            stsToken: newToken.securityToken,
          };
        },
      })
    : null;

  const uid = nanoid();
  const name = `${token?.prefix}${uid}`;
  const fileName = encodeURIComponent(file.name || '');

  return new Promise(async (resolve, reject) => {
    try {
      const response = await client?.put(name, file, {
        headers: {
          'Content-Disposition': `attachment;filename=${fileName}`,
        },
      });
      console.log(response, 'oss put then');
      if (response) {
        const params = {
          key: response.name,
          url: response.url,
          bucket: token?.bucket!,
          originName: fileName,
        };
        if (requestPreviewUrl) {
          const url = await getOssFileUrl(response.name);
          params.url = url;
        }
        resolve(params);
      }
    } catch (e: any) {
      console.log(e, e?.code);
      reject(e);
    }
  });
};

// 获取assets静态资源
// TODO:动态路径vite解析不了？？？
export const getAssetsFile = (url: string) => {
  return new URL(`@src/assets/images/${url}`, import.meta.url).href;
};

export const weeks = ['日', '一', '二', '三', '四', '五', '六'];
export const formatDiff = (a: Dayjs, b: Dayjs) => {
  const diff = a.diff(b, 'day');
  switch (diff) {
    case 0:
      return '今天';

    case 1:
      return '昨天';

    default:
      return `${diff} 天前`;
  }
};
export const radians = (d: number) => (d * Math.PI) / 180.0;

export const calcTableWidth = (columns: any[]) =>
  columns.reduce((sum, item) => sum + item.width, 0);
