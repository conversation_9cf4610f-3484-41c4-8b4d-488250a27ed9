import { FileBusinessTypeMap, getOssFileUrl, getOssToken, getYytOssToken } from '@src/common/api';
import { OssTokenProps } from '@src/common/api.type';
import { ShopDataViewBoardEnum, SupervisorDataViewBoardEnum } from '@src/common/authority';
import { GeoLocation } from '@src/pages/tasks/api.type';
import OSS from 'ali-oss';
import { Toast } from 'antd-mobile';
import CryptoJS from 'crypto-js';
import dayjs, { Dayjs } from 'dayjs';
import utc from 'dayjs/plugin/utc';
import { isNumber } from 'lodash';
import { nanoid } from 'nanoid';
import { thousandthFormat } from './helper';
import { getCurrentRoleTypeInfo } from './tokenUtils';

dayjs.extend(utc);

// export const commonSorter = (numA: any, numB: any) => {
//   if (!isNaN(numA) && !isNaN(numB)) {
//     return numA - numB;
//   }
//   if (isNaN(numA) || isNaN(numB)) {
//     if (isNaN(numA)) {
//       return -1;
//     }
//     if (isNaN(numB)) {
//       return 1;
//     }
//   }
//   return 0;
// };

export const commonSorter = (a: any, b: any, desc = 'desc') => {
  let numA = a;
  let numB = b;
  if (desc === 'desc') {
    numA = b;
    numB = a;
  }
  if (isNumber(numA) && isNumber(numB)) {
    return numA - numB;
  }
  if (!isNumber(numA) || !isNumber(numB)) {
    if (!isNumber(numA)) {
      return desc === 'desc' ? -1 : 1;
    }
    if (!isNumber(numB)) {
      return desc === 'desc' ? 1 : -1;
    }
  }
  return 0;
};

export const formatPer = (v: any, unit: string = '%', fixed?: number) => {
  if (typeof v !== 'number') {
    return '-';
  }
  return `${thousandthFormat(v, fixed)}${unit}`;
};

/**
 * 根据区间日期起始
 * @return day/default/month 日同比/同比/月同比
 */
export const getDatePercentType = (startDate: string, endDate: string) => {
  const startD = dayjs(startDate);
  const endD = dayjs(endDate);
  if (startD.format('YYYYMMDD') === endD.format('YYYYMMDD')) {
    return 'day';
  }
  if (
    startD.format('MM') === endD.format('MM') &&
    startD.format('DD') === '01' &&
    endD.format('DD') === endD.endOf('M').format('DD')
  ) {
    return 'month';
  }
  return 'default';
};

/**
 * 上传文件到 oss
 * @param {any} file:File
 * @returns {any}
 */
export const uploadFile = async (
  file: File,
  requestPreviewUrl?: boolean,
  isYYT?: boolean,
  fileBusinessType?: FileBusinessTypeMap,
): Promise<{
  key: string;
  url: string;
  bucket: string;
  originName: string;
}> => {
  if (!file) {
    Toast.show('上传文件错误');
    return Promise.reject('上传文件错误');
  }

  const getToken = async () => {
    let token: OssTokenProps;
    const tokenStr = localStorage.getItem(isYYT ? 'ALI_OSS_YYTTOKEN' : 'ALI_OSS_TOKEN');
    const tokenObj = tokenStr ? JSON.parse(tokenStr || '') : null;
    if (tokenObj && tokenObj.expiration >= dayjs().unix()) {
      token = tokenObj;
    } else {
      token = isYYT ? await getYytOssToken(fileBusinessType) : await getOssToken(fileBusinessType);
      localStorage.setItem(isYYT ? 'ALI_OSS_YYTTOKEN' : 'ALI_OSS_TOKEN', JSON.stringify(token));
    }
    return token;
  };
  const token = await getToken();
  const client = token
    ? new OSS({
        // yourRegion填写Bucket所在地域。以华东1（杭州）为例，yourRegion填写为oss-cn-hangzhou。
        region: 'oss-cn-shanghai',
        accessKeyId: token.accessKeyId,
        accessKeySecret: token.accessKeySecret,
        stsToken: token.securityToken,
        // 填写Bucket名称。
        bucket: token.bucket,
        // 刷新临时访问凭证。
        secure: true,
        refreshSTSToken: async () => {
          const newToken = await getOssToken(fileBusinessType);
          // const newToken = { accessKeyId: '', accessKeySecret: '', securityToken: '' };
          return {
            accessKeyId: newToken.accessKeyId,
            accessKeySecret: newToken.accessKeySecret,
            stsToken: newToken.securityToken,
          };
        },
      })
    : null;

  const uid = nanoid();
  const name = `${token?.prefix}${uid}`;
  const fileName = encodeURIComponent(file.name || '');

  return new Promise(async (resolve, reject) => {
    try {
      const response = await client?.put(name, file, {
        headers: {
          // 'Content-Disposition': `attachment;filename=${fileName}`,
        },
      });
      console.log(response, 'oss put then');
      if (response) {
        const params = {
          key: response.name,
          url: response.url,
          bucket: token?.bucket!,
          originName: fileName,
        };
        if (requestPreviewUrl) {
          const url = await getOssFileUrl(response.name);
          params.url = url;
        }
        resolve(params);
      }
    } catch (e: any) {
      console.log(e, e?.code);
      reject(e);
    }
  });
};

// 获取assets静态资源
// TODO:动态路径vite解析不了？？？
export const getAssetsFile = (url: string) => {
  return new URL(`@src/assets/images/${url}`, import.meta.url).href;
};

export const weeks = ['日', '一', '二', '三', '四', '五', '六'];
export const formatDiff = (a: Dayjs, b: Dayjs) => {
  const diff = a.diff(b, 'day');
  switch (diff) {
    case 0:
      return '今天';

    case 1:
      return '昨天';

    default:
      return `${diff} 天前`;
  }
};
export const radians = (d: number) => (d * Math.PI) / 180.0;

const EARTH_RADIUS = 6378.137;
/**
 * 根据经纬度计算距离
 * @param l1 位置 1
 * @param l2 位置 2
 * @returns distance (米)
 */
export const getDistance = (l1: GeoLocation, l2: GeoLocation) => {
  if (!l1?.latitude || !l1?.longitude || !l2) return Infinity;

  const radLat1 = radians(l1.latitude);
  const radLat2 = radians(l2.latitude);
  const a = radLat1 - radLat2;
  const b = radians(l1.longitude) - radians(l2.longitude);
  const s =
    2 *
    Math.asin(
      Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)),
    );
  return s * EARTH_RADIUS * 1000;
};

export const calcTableWidth = (columns: any[]) => columns.reduce((sum, item) => sum + item.width, 0);

export const formatDateToUTC = (date: any) => {
  return dayjs(date).utc().format();
};
export const SECRETKEY = 'tst-om-native-webview';

// 加密
export const encrypt = (params: Record<string, any>) => {
  return CryptoJS.AES.encrypt(JSON.stringify(params), SECRETKEY).toString();
};

// 解密
export const decrypt = (params: string) => {
  return JSON.parse(CryptoJS.AES.decrypt(params, SECRETKEY).toString(CryptoJS.enc.Utf8));
};

/**
 * @description 获取数据看板权限枚举
 * @param boardKey 数据看板key
 * @returns 权限枚举
 */
export function getDataViewBoardEnum(
  boardKey: keyof typeof SupervisorDataViewBoardEnum | keyof typeof ShopDataViewBoardEnum,
) {
  const { isSupervisor, isShop } = getCurrentRoleTypeInfo();

  if (isSupervisor) {
    return SupervisorDataViewBoardEnum?.[boardKey as keyof typeof SupervisorDataViewBoardEnum];
  } else if (isShop) {
    return ShopDataViewBoardEnum?.[boardKey as keyof typeof ShopDataViewBoardEnum];
  } else {
    return null;
  }
}

/**
 * @description 判断当前是否在微信浏览器中
 * @returns
 */
export function isInWeChatBrowser() {
  try {
    const ua = navigator?.userAgent?.toLowerCase();
    return ua?.includes('micromessenger');
  } catch (error) {
    return false;
  }
}
