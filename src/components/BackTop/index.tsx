import { UpOutline } from 'antd-mobile-icons';
import styles from './index.module.scss';
import { useEffect, useRef, useState } from 'react';

// const rootElement = document.getElementById('root');

const BackTop: React.FC<{ domId: string }> = ({ domId }) => {
  const [show, setShow] = useState<boolean>(false);
  const showRef = useRef(false);
  const elementRef = useRef<HTMLElement | null>();
  const handleClick = () => {
    elementRef.current && (elementRef.current.scrollTop = 0);
  };

  const handleScroll = () => {
    if (elementRef.current && elementRef.current.scrollTop >= 700) {
      !showRef.current && setShow(true);
      showRef.current = true;
    } else {
      showRef.current && setShow(false);
      showRef.current = false;
    }
  };

  useEffect(() => {
    if (domId) {
      elementRef.current = document.getElementById(domId);
      elementRef.current?.addEventListener('scroll', handleScroll);
      return () => {
        elementRef.current?.removeEventListener('scroll', handleScroll);
      };
    }
  }, [domId]);

  return (
    <div className={styles.backTop} onClick={handleClick}>
      {show && (
        <div className={`${styles.icon} bg-gray-200 text-2xl flex justify-center items-center`}>
          <UpOutline />
        </div>
      )}
    </div>
  );
};

export default BackTop;
