import { Popup, PopupProps } from 'antd-mobile';
import { CloseOutline } from 'antd-mobile-icons';

interface PdfViewerProps extends PopupProps {
  url: string;
}

const PdfViewer: React.FC<PdfViewerProps> = ({ url, onClose, ...props }) => {
  return (
    <Popup onClose={onClose} bodyStyle={{ height: '100vh' }} {...props}>
      <div className="absolute top-10 right-2 rounded-full p-2 bg-gray-200" onClick={onClose}>
        <CloseOutline className="text-xl" />
      </div>
      <iframe
        width="100%"
        height="100%"
        src={`/pdfjs-dist/web/viewer.html?file=${encodeURIComponent(url)}`}
      />
    </Popup>
  );
};

export default PdfViewer;
