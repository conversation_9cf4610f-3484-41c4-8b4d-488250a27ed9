import { HTMLAttributes } from 'react';

interface StatusTagProps extends HTMLAttributes<HTMLSpanElement> {
  text: string;
  color?: string;
  bg?: string;
  borderColor?: string;
  className?: string;

}

const ItemTag: React.FC<StatusTagProps> = ({ text, style, bg, borderColor, className, color, ...props }) => {
  return (
    <div
      {...props}
      className={`pt-1 pb-1 text-center min-w-[48px] border rounded-sm  text-xs leading-[12px] ` + className}
      style={{
        backgroundColor: bg,
        borderColor: borderColor,
        color: color,
        textAlign: 'center',
        ...style,
      }}
    >
      {text}
    </div>
  );
};

export default ItemTag;
