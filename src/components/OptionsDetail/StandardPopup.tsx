import styles from './index.module.scss';
import { IPopup, IPopupProps } from '@src/components/IPopup';
interface StandardPopupProps extends IPopupProps {
  standard?: string | undefined;
}
const StandardPopup: React.FC<StandardPopupProps> = ({
  standard,
  onClose,
  title,
  visible,
  ...props
}) => {
  return (
    <IPopup
      className={styles.PopupHeight}
      {...props}
      visible={visible}
      title={title}
      onClose={onClose}
    >
      <div className="p-4">{standard}</div>
    </IPopup>
  );
};

export default StandardPopup;
