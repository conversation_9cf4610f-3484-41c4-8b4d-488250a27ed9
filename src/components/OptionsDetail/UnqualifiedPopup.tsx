import { IPopup, IPopupProps } from '@src/components/IPopup';
import { useMemo } from 'react';
import styles from './index.module.scss';
interface UnqualifiedPopupProps extends IPopupProps {
  selectReasons: string[];
}
const UnqualifiedPopup: React.FC<UnqualifiedPopupProps> = ({
  selectReasons = [],
  onClose,
  title,
  visible,
  ...props
}) => {
  const selectReasonsList = useMemo(() => {
    let textList = selectReasons?.map((v, index) => {
      return <div key={index}>{`${v}`}</div>;
    });
    return textList;
  }, [selectReasons]);

  return (
    <IPopup
      className={styles.PopupHeight}
      {...props}
      visible={visible}
      title={title}
      onClose={onClose}
    >
      <div className="p-4">{selectReasonsList}</div>
    </IPopup>
  );
};

export default UnqualifiedPopup;
