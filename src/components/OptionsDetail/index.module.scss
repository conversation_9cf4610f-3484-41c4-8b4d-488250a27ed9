.activeColor {
  color: #000000;
  font-weight: 500;
}
.operationArea {
  display: flex;
  justify-content: space-between;
  margin-top: 12px;

  > span {
    height: 32px;
    width: 42%;
    font-size: 14px;
    line-height: 32px;
    border-radius: 4px;
    font-weight: 400px;
    border: 1px solid;
    text-align: center;
    cursor: pointer;
  }

  .pass {
    color: #378bff;
    border-color: #378bff;

    &.active {
      color: #fff;
      background-color: #378bff;
    }
  }

  .fail {
    color: #f53f3f;
    border-color: #f53f3f;

    &.active {
      color: #fff;
      background-color: #f53f3f;
    }
  }
}

.WfullPopup {
  :global {
    .adm-popup-body {
      width: 100%;
    }
  }
}
.imgOrVideo {
  video {
    height: revert-layer;
  }
}
.INoticeBar {
  border: 1px solid #86e8dd;
  border-radius: 4px;
  font-size: 12px;
  line-height: 12px;
  background: rgba(0, 187, 180, 0.05);
}
.Tabs {
  :global {
    .adm-tabs-tab-wrapper {
      // padding: 0 0 0 16px;
    }
    .adm-tabs-header {
      border-bottom: none;
    }
  }
}
.Collapse {
  :global {
    .adm-list-body-inner {
      .adm-list-item {
        padding-left: 16px;
      }
    }
    .adm-collapse-panel-content-inner {
      .adm-list-item {
        background-color: #f5f5f5;
        padding-left: 8px;
        .adm-list-item-content {
          padding-right: 8px;
          border-color: none;
          .adm-list-item-content-main {
            padding: 10px 0 0 0;
          }
        }
      }
    }
    .adm-collapse-arrow {
      font-size: 12px;
      color: #b8b8b8;
    }
    .adm-list-default .adm-list-body {
      border-top: none;
    }
  }
}
.PopupHeight {
  :global {
    .adm-popup-body {
      height: 80vh;
    }
  }
}
.Selector {
  :global {
    .adm-selector-item {
      text-align: left;
    }
  }
}
