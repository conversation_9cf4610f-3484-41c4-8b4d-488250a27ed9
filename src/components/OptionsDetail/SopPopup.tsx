import { IPopup, IPopupProps } from '@src/components/IPopup';
import { Image, ErrorBlock } from 'antd-mobile';
import { useEffect, useState } from 'react';
import styles from './index.module.scss';
import { querySopDetail } from '@src/pages/patrol/api';

interface SopPopupProps extends IPopupProps {
  sopId?: number | undefined;
}
const SopPopup: React.FC<SopPopupProps> = ({ sopId, onClose, title, visible, ...props }) => {
  const [sopDetail, setSopDetail] = useState<any>();
  const [hasSopPermission, setHasSopPermission] = useState<boolean>(false);

  useEffect(() => {
    const handleReadSop = async () => {
      if (!sopId) return;
      await querySopDetail(sopId).then((v: any) => {
        setHasSopPermission(v.hasUsePermission);
        if (v.content) {
          setSopDetail(v.content);
        }
      });
    };
    try {
      handleReadSop();
    } catch (error: any) {}
    return setSopDetail(undefined);
  }, [sopId]);
  return (
    <IPopup
      className={styles.PopupHeight}
      {...props}
      visible={visible}
      title={title}
      onClose={onClose}
    >
      {visible && (
        <div className="p-4">
          <div>
            {hasSopPermission ? (
              sopDetail?.fileType === 'VIDEO' ? (
                <div>
                  <video
                    key="Player"
                    id="myPlayer"
                    preload="auto"
                    controls
                    playsInline
                    src={sopDetail?.fileUrl}
                  />
                </div>
              ) : (
                <div>
                  {sopDetail?.imageUrls?.map((item, index) => {
                    return <Image key={index} src={item} />;
                  })}
                </div>
              )
            ) : (
              <ErrorBlock
                status="empty"
                title="您没有权限"
                description={<span>请与客服人员联系！</span>}
              />
            )}
          </div>
        </div>
      )}
    </IPopup>
  );
};

export default SopPopup;
