import React, { memo, useEffect, useMemo, useState } from 'react';
import { IconFont } from '@src/components';
import { IPopup } from '@src/components/IPopup';
import VideoViewer from '@src/components/VideoViewer';
import { INoticeBar } from '@src/pages/boards/components';
import { CheckItem, DetailItem, WeekSheet } from '@src/pages/patrol/api.type';
import { Checkbox, Collapse, Image, ImageViewer, Selector, Tabs } from 'antd-mobile';
import { PlayOutline } from 'antd-mobile-icons';
import cn from 'classnames';
import styles from './index.module.scss';
import ItemTag from './itemTag';
import SopPopup from './SopPopup';
import StandardPopup from './StandardPopup';
import UnqualifiedPopup from './UnqualifiedPopup';
type GroupType = Pick<
  WeekSheet,
  'patrolWorksheetCategoryId' | 'patrolWorksheetCategoryName' | 'filledCount' | 'itemCount'
>;
type props = {
  detailItem: DetailItem;
  hiddenItemTitle?: boolean;
  isCheckRequire?: boolean;
};
export enum buttonTypeEnum {
  判断型 = 'JUDGE',
  打分型 = 'SCORE',
}
export enum scoreTypeEnum {
  得分项 = 'ADD_SCORE',
  扣分项 = 'REDUCE_SCORE',
}
const necessaryFilter: (data: WeekSheet[]) => WeekSheet[] = (data) =>
  data
    .map((item) => {
      if (item.checkListTypeList?.length) {
        const checkListTypeList = necessaryFilter(item.checkListTypeList);
        return (checkListTypeList.length && {
          ...item,
          checkListTypeList,
        }) as any;
      }
      const children = (item.children || []).filter((item) => !!item.necessaryFlag);
      return (children.length as any as typeof item) && { ...item, children };
    })
    .filter((v) => !!v);
const PatrolDetailScoreBtn = (checkItem: CheckItem) => {
  const { type, scoreType, qualified, fullScore, score, reportDisplayScore } = checkItem;
  return (
    <div className={styles.operationArea}>
      <span
        role="button"
        className={`${styles.active} ${
          !!qualified ||
          (type === buttonTypeEnum.打分型 && score === (scoreType === scoreTypeEnum.扣分项 ? 0 : fullScore))
            ? styles.pass
            : styles.fail
        }`}
      >
        {type === buttonTypeEnum.判断型
          ? (!!qualified
              ? `${checkItem?.qualifiedButtonCustomName || '合格'}`
              : `${checkItem?.notQualifiedButtonCustomName || '不合格'}`
            ).concat(
              scoreType === scoreTypeEnum.扣分项
                ? !!qualified
                  ? '(不扣分)'
                  : `(扣${score}分)`
                : fullScore === 0 || !reportDisplayScore
                  ? ''
                  : `(${score}分)`,
            )
          : (scoreType === scoreTypeEnum.扣分项 ? score === 0 : score === fullScore)
            ? `合格(${scoreType === scoreTypeEnum.扣分项 ? '不扣' : score}分)`
            : `${scoreType === scoreTypeEnum.扣分项 ? '扣' : ''}${score}分${
                scoreType === scoreTypeEnum.扣分项 ? '' : `(满分${fullScore}分)`
              }`}
      </span>
    </div>
  );
};
const reasonListText = (reasonLis: string[]) => {
  const text = reasonLis?.map((v, index) => {
    return <span key={index}>#{v}&nbsp;</span>;
  });
  return text || '';
};
export type TagType = {
  accentedTermTags: string[];
};

export const QuestionTags: React.FC<TagType> = ({ accentedTermTags }) => {
  const propsData = accentedTermTags || [];
  console.log(propsData, '=propsData');

  return (
    <>
      {!!propsData?.includes('RED_LINE') && (
        <ItemTag color="#F53F3F" bg="rgba(245, 63, 63, 0.05)" borderColor="#FBACA3" text="S项" />
      )}
      {!!propsData?.includes('PENALTY') && (
        <ItemTag color="#4E5969" bg="rgba(78, 89, 105, 0.05)" borderColor="#C9CDD4" text="罚款项" />
      )}
      {!!propsData?.includes('NECESSARY') && (
        <ItemTag color="#4E5969" bg="rgba(78, 89, 105, 0.05)" borderColor="#C9CDD4" text="必检项" />
      )}
      {!!propsData?.includes('KEY') && (
        <ItemTag color="#4E5969" bg="rgba(78, 89, 105, 0.05)" borderColor="#C9CDD4" text="关键项" />
      )}
      {!!propsData?.includes('YELLOW') && (
        <ItemTag color="#4E5969" bg="rgba(78, 89, 105, 0.05)" borderColor="#C9CDD4" text="M项" />
      )}
      {!!propsData?.includes('POSITIVE') && (
        <ItemTag color="#4E5969" bg="rgba(78, 89, 105, 0.05)" borderColor="#C9CDD4" text="阳性指标项" />
      )}
    </>
  );
};

export const SheetsRender = memo<{
  groups: GroupType[];
  dataSource: WeekSheet[];
  status?: number;
  fillItemFlag?: number;
  isCheckRequire?: boolean;
}>(({ groups, dataSource }) => {
  const [activeGroup, setActiveGroup] = useState<number>(-1);
  const [tabPopupVisible, setTabPopupVisible] = useState(false);
  const [sopPopupVisible, setSopPopupVisible] = useState(false);
  const [standardPopupVisible, setStandardPopupVisible] = useState(false);
  const [videoPreviewUrl, setVideoPreviewUrl] = useState('');
  const [videoPreviewVisible, setVideoPreviewVisible] = useState(false);
  const [unqualifiedVisible, setUnqualifiedVisible] = useState(false);
  const [selectReasons, setSelectReasons] = useState<string[]>([]);
  const [collapseKeys, setCollapseKeys] = useState([]);

  const [sopMsg, setSopMsg] = useState<{
    standard: string;
    sopId: number;
  }>();
  useEffect(() => {
    setActiveGroup(activeGroup);
    return () => setActiveGroup(-1);
  }, [groups]);

  const { viewList } = useMemo(() => {
    let showData = dataSource;
    if (activeGroup !== -1) {
      if (activeGroup !== 0) {
        // 分类过滤
        // eslint-disable-next-line
        showData = showData.filter((v) => v.patrolWorksheetCategoryId == activeGroup);
      } else {
        // 必检项
        showData = necessaryFilter(showData);
      }
    }

    return {
      showSecondary: activeGroup > 0 && !!showData[0]?.checkListTypeList?.length,
      viewList: showData.map((item) => (item?.checkListTypeList?.length ? item?.checkListTypeList : item)).flat(1),
    };
  }, [dataSource, activeGroup]);
  const imagesOrVideoRender = (
    images: {
      url: string;
      type: 'IMG' | 'VIDEO';
      id: string;
      snapshotUrl: string;
    }[],
  ) => {
    return (
      !!images?.length && (
        <div className={`flex  flex-wrap gap-1 items-center ${styles.imgOrVideo}`}>
          {images.map((item) => {
            return (
              <div key={item.url}>
                {item.type === 'VIDEO' ? (
                  <div
                    className="relative"
                    onClick={() => {
                      setVideoPreviewVisible(true);
                      setVideoPreviewUrl(item.url);
                    }}
                  >
                    <video
                      width={48}
                      style={{ objectFit: 'cover' }}
                      preload="auto"
                      poster={item?.snapshotUrl}
                      height={48}
                      src={item.url}
                    />
                    <div className="absolute left-[50%] top-[50%] -translate-x-[50%] -translate-y-[50%]">
                      <PlayOutline color="#000" fontSize={30} />
                    </div>
                  </div>
                ) : (
                  <Image
                    onClick={() => {
                      ImageViewer.show({ image: item.url });
                    }}
                    src={item?.snapshotUrl || item.url}
                    width={48}
                    height={48}
                  />
                )}
              </div>
            );
          })}
        </div>
      )
    );
  };

  useEffect(() => {
    const init = viewList?.map((v) => {
      return `${v?.patrolWorksheetCategoryId}`;
    });
    setCollapseKeys(init as any);
  }, [viewList]);
  return (
    <React.Fragment>
      <div className="bg-white flex items-center pl-1">
        <Tabs
          className={styles.Tabs}
          activeKey={activeGroup as any}
          onChange={(value) => setActiveGroup(+value as number)}
        >
          {groups?.map((item) => (
            <Tabs.Tab
              title={
                <div
                  className={cn('flex text-sm items-center leading-[14px] text-[#858585]', {
                    // eslint-disable-next-line eqeqeq
                    [styles.activeColor]: item.patrolWorksheetCategoryId == activeGroup,
                  })}
                >
                  <div className="max-w-[100px]  text-ellipsis overflow-hidden whitespace-nowrap">
                    {item.patrolWorksheetCategoryName}
                  </div>
                  <div>
                    ({item.filledCount}/{item.itemCount})
                  </div>
                </div>
              }
              key={item.patrolWorksheetCategoryId}
            />
          ))}
        </Tabs>
        {groups?.length > 3 && (
          <>
            <div className="bg-[rgba(0,0,0,0.03)]" />
            <div className="flex h-full justify-center items-center px-3" onClick={() => setTabPopupVisible(true)}>
              <IconFont type="icon-menu" className="text-[#858585] text-sm" />
            </div>
          </>
        )}
      </div>
      <div className="pb-1">
        {
          <Collapse
            className={styles.Collapse}
            activeKey={collapseKeys}
            onChange={(e) => {
              setCollapseKeys(e as any);
            }}
          >
            {viewList?.map((info) => {
              return (
                <Collapse.Panel
                  key={`${info.patrolWorksheetCategoryId}`}
                  title={
                    <div className="text-[#141414] text-sm leading-[14px]  font-medium  max-w-[300px]  text-ellipsis overflow-hidden whitespace-nowrap">
                      {info?.patrolWorksheetCategoryName} {info.filledCount}/{info.itemCount}
                    </div>
                  }
                >
                  {info?.children?.map((item) => {
                    console.log(item, '=111item');
                    const { otherReason, selectReasons } = item || {};
                    const allReasons = otherReason ? [...(selectReasons || []), otherReason] : selectReasons;
                    return (
                      <div key={item.itemId} className="p-3 pb-4 mb-[10px] bg-[#FFF] rounded-[4px]">
                        <div className="flex justify-center items-center mt-1 mb-2">
                          <div className="flex-1 flex flex-wrap gap-1">
                            <QuestionTags accentedTermTags={item?.accentedTermTags || []} />
                          </div>
                          {!!item?.sopId && (
                            <div
                              className="text-primary mr-2"
                              onClick={() => {
                                setSopMsg({
                                  standard: item?.standard,
                                  sopId: item?.sopId,
                                });
                                setSopPopupVisible(true);
                              }}
                            >
                              参考标准
                            </div>
                          )}
                          {!!item?.standard && (
                            <div
                              className="text-primary"
                              onClick={() => {
                                setSopMsg({
                                  standard: item?.standard,
                                  sopId: item?.sopId,
                                });
                                setStandardPopupVisible(true);
                              }}
                            >
                              评分标准
                            </div>
                          )}
                        </div>
                        <div className="text-sm leading-[22px] break-all">{item.itemSOP}</div>
                        <div>{PatrolDetailScoreBtn(item as any)}</div>
                        <div>
                          {(!!item?.itemRemark || allReasons || item?.imageList) && (
                            <div className="bg-[#FAFAFA] my-2 p-2">
                              <div className="break-all mb-2">{item?.itemRemark}</div>
                              {!item?.qualified && (
                                <div
                                  className="break-all  ellipsis-2 text-primary"
                                  onClick={() => {
                                    setSelectReasons(allReasons);
                                    setUnqualifiedVisible(true);
                                  }}
                                >
                                  {reasonListText(allReasons)}
                                </div>
                              )}
                              {item.imageList && imagesOrVideoRender(item.imageList as any)}
                            </div>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </Collapse.Panel>
              );
            })}
          </Collapse>
        }
      </div>
      <IPopup title="分类" visible={tabPopupVisible} onClose={() => setTabPopupVisible(false)}>
        <div className="pt-5 px-4 pb-10">
          <Selector
            style={{
              '--checked-color': 'var(--color-primary-1)',
              '--border-radius': '4px',
              fontSize: '13px',
            }}
            className={styles.Selector}
            value={[activeGroup]}
            onChange={(value) => setActiveGroup(value[0])}
            columns={1}
            showCheckMark={false}
            options={groups?.map((i) => ({
              label: `${i.patrolWorksheetCategoryName}`,
              value: i.patrolWorksheetCategoryId,
            }))}
          />
        </div>
      </IPopup>
      <StandardPopup
        {...sopMsg}
        title="评分标准"
        visible={standardPopupVisible}
        onClose={() => setStandardPopupVisible(false)}
      />
      <SopPopup {...sopMsg} title="参考标准" visible={sopPopupVisible} onClose={() => setSopPopupVisible(false)} />
      <UnqualifiedPopup
        title="不合格原因"
        visible={unqualifiedVisible}
        selectReasons={selectReasons}
        onClose={() => setUnqualifiedVisible(false)}
      />
      <VideoViewer visible={videoPreviewVisible} url={videoPreviewUrl} onClose={() => setVideoPreviewVisible(false)} />
    </React.Fragment>
  );
});

const OptionsDetail: React.FC<props> = ({ detailItem }) => {
  const [isShowImg] = useState(false);
  const [isShowUnCheck] = useState(false);
  const [unqualified, setUnqualified] = useState(false);
  const [tempDataSource, setTempDataSource] = useState<DetailItem>(detailItem);

  useEffect(() => {
    setTempDataSource(detailItem);
  }, [detailItem]);

  const { groups, dataSource } = useMemo(() => {
    let dataSource = tempDataSource?.children || [];

    if (unqualified) {
      const dig: (data: typeof dataSource) => typeof dataSource = (children) => {
        return children
          .map((item) => {
            if (item.checkListTypeList?.length) {
              const checkListTypeList = dig(item.checkListTypeList);
              return (checkListTypeList.length && {
                ...item,
                checkListTypeList,
              }) as any;
            }
            const children = (item.children || []).filter(({ hasApply, qualified }) => hasApply && !qualified);
            return (children.length as any as typeof item) && { ...item, children };
          })
          .filter((v) => !!v);
      };

      dataSource = dig(dataSource);
    }
    if (isShowImg) {
      const dig: (data: typeof dataSource) => typeof dataSource = (data) =>
        data
          .map((item) => {
            if (item.checkListTypeList?.length) {
              const checkListTypeList = dig(item.checkListTypeList);
              return (checkListTypeList.length && {
                ...item,
                checkListTypeList,
              }) as any;
            }
            const children = (item.children || []).filter(({ imageURLS }) => imageURLS && !!imageURLS?.length);
            return (children.length as any as typeof item) && { ...item, children };
          })
          .filter((v) => !!v);
      dataSource = dig(dataSource);
    }
    if (isShowUnCheck) {
      const dig: (data: typeof dataSource) => typeof dataSource = (data) =>
        data
          .map((item) => {
            if (item.checkListTypeList?.length) {
              const checkListTypeList = dig(item.checkListTypeList);
              return (checkListTypeList.length && {
                ...item,
                checkListTypeList,
              }) as any;
            }
            const children = (item.children || []).filter(
              ({ qualified, score }) => qualified === null || score === null,
            );
            return (children.length as any as typeof item) && { ...item, children };
          })
          .filter((v) => !!v);
      dataSource = dig(dataSource);
    }
    const groups: GroupType[] = [
      {
        patrolWorksheetCategoryId: -1,
        patrolWorksheetCategoryName: '全部',
        filledCount: tempDataSource.filledCount!,
        itemCount: tempDataSource.itemCount!,
      },
      ...dataSource,
    ];

    return {
      groups,
      dataSource: dataSource.filter((v: { patrolWorksheetCategoryId: number }) => v.patrolWorksheetCategoryId !== 0),
    };
  }, [tempDataSource, unqualified, isShowImg, isShowUnCheck]);

  return (
    <div className="leading-[14px]">
      <div className="bg-white flex p-4 pb-3 justify-between items-center">
        <div className="text-base leading-[16px]" style={{ fontWeight: '500' }}>
          检查明细
        </div>
        <div className="flex justify-between items-center">
          <Checkbox
            style={{
              '--icon-size': '18px',
              '--font-size': '24px',
              '--gap': '6px',
            }}
            checked={unqualified}
            onChange={(checked) => {
              setUnqualified(checked);
            }}
          >
            <div className="text-sm text-[#5E5E5E]">仅显示不合格项</div>
          </Checkbox>
        </div>
      </div>
      {!!detailItem.notApplyCount && (
        <div className="bg-white pl-4 pr-4">
          <INoticeBar
            status="success"
            className={styles.INoticeBar}
            isShowIcon={false}
            content={`
          有${detailItem.notApplyCount}个检查项被设为"不适用"，已从报告中隐藏`}
          />
        </div>
      )}
      <SheetsRender groups={groups} dataSource={dataSource} />
    </div>
  );
};

export default OptionsDetail;
