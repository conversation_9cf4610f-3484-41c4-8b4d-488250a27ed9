import { useEffect, useState } from 'react';
import { DataViewTypeEnum } from '@src/pages/dataViewBoard';
import { ESheetType } from '@src/pages/mission/api';
import { AllUserSelect } from '@src/pages/mission/components/AllUserSelect';
import { TaskSelect } from '@src/pages/mission/components/TaskSelect';
import { WorkSheet } from '@src/pages/mission/components/WorkSheet';
import { FilterDrawer } from '@src/pages/tasks/components/FilterDrawer';
import { yytShopTypeEnum } from '@src/pages/tasks/enum';
import { DrawerProps, Select } from 'antd';
import { RoleListSelect } from './RoleListSelect';

interface DataBoardFilterProps extends DrawerProps {
  value: Record<string, any>;
  onChange: (value: Record<string, any>) => void;
  onClose: () => void;
  type: string;
}

// eslint-disable-next-line react-refresh/only-export-components
export const init = {
  shopType: undefined,
  workSheetId: undefined,
  taskIds: undefined,
  planId: undefined,
  patrolUserIds: undefined,
  alarmLevel: undefined,
  roleIdList: undefined,
};

export const DataBoardFilter = ({ value, onChange, onClose, type, ...props }: DataBoardFilterProps) => {
  const [activity, setActivity] = useState<any>(value);

  const isSelfChecking = [DataViewTypeEnum.自检看板, DataViewTypeEnum['自检看板 (新)']].includes(
    type as DataViewTypeEnum,
  );
  const onOk = () => {
    onChange(activity);
  };
  const onClear = () => {
    setActivity(init);
  };
  useEffect(() => {
    setActivity(value);
  }, [type, value]);

  return (
    <FilterDrawer
      {...props}
      onClose={() => {
        // 重置一下activity
        setActivity(value);
        onClose();
      }}
      onOk={onOk}
      onClear={onClear}
    >
      <div className="flex flex-col gap-3">
        <h3 className="text-[#141414] text-sm leading-[14px]">门店类型</h3>
        <div className="flex flex-wrap gap-2">
          {[
            { label: '加盟T', value: yytShopTypeEnum.加盟T },
            { label: '加盟M', value: yytShopTypeEnum.加盟M },
          ].map((item) => {
            return (
              <button
                key={item.value}
                className={`w-20 h-[30px] rounded ${
                  activity?.shopType === yytShopTypeEnum[item.label as keyof typeof yytShopTypeEnum]
                    ? 'text-primary bg-primary/10'
                    : 'text-58 bg-black/[0.03]'
                }  text-sm left-[14px]`}
                onClick={() => {
                  setActivity((pre: any) => {
                    if (activity?.shopType === item.value) {
                      return {
                        ...pre,
                        shopType: undefined,
                      };
                    } else {
                      return {
                        ...pre,
                        shopType: item.value,
                      };
                    }
                  });
                }}
              >
                {item.label}
              </button>
            );
          })}
        </div>
        {!isSelfChecking && (
          <RoleListSelect
            value={activity?.roleIdList}
            onChange={(val: number) => {
              setActivity((pre: any) => ({
                ...pre,
                roleIdList: val,
              }));
            }}
          />
        )}
        {type !== DataViewTypeEnum.诊断巡检看板 && (
          <WorkSheet
            type={type}
            sheetType={isSelfChecking ? ESheetType.自检 : ESheetType.巡检}
            taskType={1}
            value={activity.workSheetId}
            onChange={(val: number) => {
              setActivity((pre: any) => ({
                ...pre,
                workSheetId: val,
              }));
            }}
          />
        )}
        {![DataViewTypeEnum.诊断巡检看板, DataViewTypeEnum['自检看板 (新)']].includes(type as DataViewTypeEnum) && (
          <TaskSelect
            isSelfCheck={isSelfChecking}
            value={isSelfChecking ? activity.taskIds : activity.planId}
            onChange={(val: number) => {
              if (isSelfChecking) {
                setActivity((pre: any) => ({
                  ...pre,
                  planId: val,
                }));
              } else {
                setActivity((pre: any) => ({
                  ...pre,
                  planId: val,
                }));
              }
            }}
          />
        )}
        {!isSelfChecking && (
          <AllUserSelect
            value={activity?.patrolUserIds}
            onChange={(val: number) => {
              setActivity((pre: any) => ({
                ...pre,
                patrolUserIds: val,
              }));
            }}
          />
        )}
        {/* TODO: */}
        {type === DataViewTypeEnum.诊断巡检看板 && (
          <div className="flex justify-between items-center">
            <h3 className="text-[#141414] text-sm leading-[14px]">诊断类型</h3>
            <Select
              style={{ width: 200 }}
              placeholder="请选择诊断类型"
              options={[
                {
                  label: '倔强青铜',
                  value: 'ORANGE',
                },
                {
                  label: '秩序白银',
                  value: 'RED',
                },
                {
                  label: '尊贵铂金',
                  value: 'YELLOW',
                },
              ]}
              value={activity?.alarmLevel}
              onChange={(val: number) => {
                setActivity((pre: any) => ({
                  ...pre,
                  alarmLevel: val,
                }));
              }}
            />
          </div>
        )}
      </div>
    </FilterDrawer>
  );
};
