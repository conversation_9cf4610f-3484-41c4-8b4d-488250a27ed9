import { getRoleList } from '@src/pages/tasks/api';
import { useRequest } from 'ahooks';
import { Select } from 'antd';
interface RoleProps {
  value?: number[];
  onChange?: any;
}

export const RoleListSelect = ({ value, onChange }: RoleProps) => {
  const { data } = useRequest(() => getRoleList({ roleCategory: 1 }));

  return (
    <div className="flex items-center justify-between">
      <h3 className="text-[#141414] text-sm leading-[14px]">角色</h3>
      <Select
        showSearch
        style={{ width: 200 }}
        options={data}
        fieldNames={{
          value: 'id',
          label: 'name',
        }}
        value={value}
        onChange={onChange}
        allowClear
        filterOption={(val, opt) => {
          return opt?.name?.toLowerCase()?.includes(val?.toLowerCase()) || false;
        }}
        mode="multiple"
        placeholder="请输入角色"
      />
    </div>
  );
};
