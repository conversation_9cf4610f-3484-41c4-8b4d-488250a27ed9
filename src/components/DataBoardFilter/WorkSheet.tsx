import { getWorkSheet } from '@src/pages/tasks/api';
import { useRequest } from 'ahooks';
import { Select } from 'antd';

interface WorkSheetProps {
  taskType: 0 | 1;
  value?: number;
  onChange?: any;
}

export const WorkSheet = ({ taskType, value, onChange }: WorkSheetProps) => {
  const { data } = useRequest(() => getWorkSheet(taskType));
  return (
    <div className="flex items-center justify-between">
      <h3 className="text-[#141414] text-sm leading-[14px]">检查表</h3>
      <Select
        // showSearch
        style={{ width: 200 }}
        options={data}
        placeholder="请选择检查表"
        fieldNames={{
          value: 'id',
          label: 'sheetName',
        }}
        value={value}
        onChange={onChange}
        allowClear
      />
    </div>
  );
};
