import { querySimpleTaskList } from '@src/pages/tasks/api';
import { useRequest } from 'ahooks';
import { Select } from 'antd';
import { debounce, uniqBy, concat } from 'lodash';

interface WorkNameProps {
  value?: number;
  onChange?: any;
}

export const WorkName = ({ value, onChange }: WorkNameProps) => {
  const { data, run } = useRequest((val) => querySimpleTaskList({ taskTitle: val }), {
    manual: true,
  });
  const { data: data2 } = useRequest(() => querySimpleTaskList({ taskIds: [value] }), {
    ready: value,
  });
  const handleSearch = debounce((val) => {
    run(val);
  }, 500);
  return (
    <div className="flex items-center justify-between">
      <h3 className="text-[#141414] text-sm leading-[14px]">任务名称</h3>
      <Select
        showSearch
        style={{ width: 200 }}
        options={uniqBy(concat(data || [], data2 || []), 'taskId')}
        placeholder="请输入选择任务名称"
        fieldNames={{
          value: 'taskId',
          label: 'taskTitle',
        }}
        filterOption={false}
        value={value}
        onChange={onChange}
        allowClear
        onSearch={(val) => {
          !!val && val.length >= 1 && handleSearch(val);
        }}
      />
    </div>
  );
};
