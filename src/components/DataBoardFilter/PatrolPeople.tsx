import { detailPageByIds, getUserList } from '@src/pages/tasks/api';
import { useRequest } from 'ahooks';
import { Select } from 'antd';

import { debounce, uniqBy, concat } from 'lodash';

interface PatrolPeopleProps {
  value?: any;
  onChange?: any;
}

export const PatrolPeople = ({ value, onChange }: PatrolPeopleProps) => {
  const { data, run } = useRequest((val) => getUserList(val), {
    manual: true,
  });
  const { data: data2 } = useRequest(() => detailPageByIds(value), {
    ready: (value || []).length !== 0,
  });

  const handleSearch = debounce((val) => {
    run(val);
  }, 500);

  return (
    <div className="flex items-center justify-between">
      <h3 className="text-[#141414] text-sm leading-[14px]">巡检人</h3>
      <Select
        showSearch
        style={{ width: 200 }}
        mode="multiple"
        options={uniqBy(concat(data || [], data2?.records || []), 'userId')}
        placeholder="请输入选择巡检人"
        fieldNames={{
          value: 'userId',
          label: 'name',
        }}
        filterOption={false}
        value={value}
        onChange={onChange}
        allowClear
        onSearch={(val) => {
          !!val && val.length >= 1 && handleSearch(val);
        }}
      />
    </div>
  );
};
