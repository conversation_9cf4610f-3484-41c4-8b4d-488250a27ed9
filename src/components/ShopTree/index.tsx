import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useScrollFetch } from '@src/hooks';
import { typeTagMap } from '@src/pages/tasks/unplannedTask';
import {
  getMineOrganizationTree,
  getOtherOrganizationTree,
  IMineChildren,
  IMineTreeData,
  IOtherTreeData,
  TypeTagEnum,
} from '@src/pages/tasks/unplannedTask/api';
import OrganizationDropdown, {
  TDropdownRef,
} from '@src/pages/tasks/unplannedTask/components/OrganizationDropdown';
import { useRequest } from 'ahooks';
import { Button, InfiniteScroll, SearchBar } from 'antd-mobile';
import { debounce } from 'lodash';

type TProps = {
  /** 是否是权限内 */
  isInPermission?: boolean;
  // 样式太长是否...展示
  hasEllipsis?: boolean;
  onSelectChange: (params: { groupId: number; shopId: string[] }) => void;
  /** 权限外组织数据是否需要连带门店信息 默认是带 */
  needShop?: boolean;
  shopId?: string;
};

export function ShopTree({
  isInPermission = false,
  hasEllipsis = false,
  onSelectChange,
  needShop = true,
  shopId,
}: TProps) {
  const [filterData, setFilterData] = useState<{
    viewId?: string;
    selectId?: string;
  }>(
    !!shopId
      ? {
          selectId: /[a-zA-Z]/.test(shopId)
            ? `SHOP-${shopId}`
            : `ORGANIZATION-${shopId}`,
        }
      : {},
  );
  const [searchValue, setSearchValue] = useState('');

  const orgDropDownRef = useRef<TDropdownRef>(null);

  // 我的门店组织数据
  const { data: mineOrgs } = useRequest(
    async () => {
      const res = await getMineOrganizationTree();
      return {
        id: null,
        name: '全部',
        type: 1,
        typeTag: TypeTagEnum.组织,
        children:
          res?.map((v) => ({ ...v, typeTag: typeTagMap[v.type] })) || [],
      };
      // if (res.length === 1) {
      //   // return { ...res[0], typeTag: TypeTagEnum.组织 };
      //   return {
      //     id: 0,
      //     name: '全部',
      //     type: 1,
      //     typeTag: TypeTagEnum.组织,
      //     children: res?.map((v) => ({ ...v, typeTag: typeTagMap[v.type] })) || [],
      //   };
      // }
      // // 返回的是数组类型 在外面包一层 根组织
      // return {
      //   id: 0,
      //   name: '全部',
      //   type: 1,
      //   typeTag: TypeTagEnum.组织,
      //   children: res?.map((v) => ({ ...v, typeTag: typeTagMap[v.type] })) || [],
      // };
    },
    { refreshDeps: [] },
  );

  const getOrgOrShopId = (target: string) => {
    return target.split('-')[1];
  };

  // 添加标记
  function addMark(arr: any[], typeTag: TypeTagEnum) {
    return arr.map((item) => ({ ...item, typeTag }));
  }
  // 递归处理组织和门店
  function dig(
    data: Array<IMineTreeData | IOtherTreeData>,
    isInPermission?: boolean,
  ): any[] {
    if (isInPermission) {
      return (data as IMineChildren[])?.map((item) => ({
        ...item,
        children: item?.children?.length
          ? dig(
              item.children.map((v: IMineChildren) => {
                return { ...v, typeTag: typeTagMap[v.type] };
              }) || [],
              true,
            )
          : [],
      }));
    }
    return (data as IOtherTreeData[])?.map((item) => {
      // children 代表组织
      const children = item?.children?.length
        ? dig(addMark(item.children, TypeTagEnum.组织))
        : [];
      // shopInfos 代表门店
      const shopInfos = item?.shopInfos?.length
        ? dig(addMark(item.shopInfos, TypeTagEnum.门店))
        : [];
      return {
        ...item,
        children: children.concat(shopInfos),
      };
    });
  }

  const { data: otherOrgs } = useRequest(
    async () => {
      const res = await getOtherOrganizationTree({ needShop });
      // 最外层 标识为组织
      res.typeTag = TypeTagEnum.组织;
      return res;
    },
    { refreshDeps: [] },
  );

  const selectOrg = (selectId: string) => {
    console.log('select*Id', selectId);
    // SHOP --> 门店
    if (selectId.includes(TypeTagEnum.门店)) {
      const groupId = +getOrgOrShopId(highestOrgId);
      const shopIds = [getOrgOrShopId(selectId)];
      onSelectChange({
        groupId,
        shopId: shopIds,
      });
      // ORGANIZATION --> 组织
    } else if (selectId.includes(TypeTagEnum.组织)) {
      const groupId = +getOrgOrShopId(selectId);
      onSelectChange({
        groupId,
        shopId: [],
      });
    }
  };

  const { orgMap, highestOrgId } = useMemo(() => {
    const map: { [key: string]: any } = {};
    let highestOrgId: string = '';

    const loopOrgs = (nodes: any[], parentId?: string) => {
      let allChild: any[] = [];
      console.log(nodes, '=nodes');

      const children = nodes?.map(
        ({
          id,
          typeTag,
          children: treeSet,
          name,
          shopId,
          shopName,
          headNickname,
        }) => {
          const value =
            typeTag === TypeTagEnum.门店
              ? `${typeTag}-${shopId}`
              : `${typeTag}-${id}`;
          const label =
            typeTag === TypeTagEnum.门店
              ? `${shopId} ${shopName || name}`
              : `${name}${headNickname ? `  (${headNickname})` : ''}`;

          map[value] = { value, label, parentId };
          let children: any[] = [];

          let allKeys: any = [];
          if (treeSet?.length > 0) {
            const { children: sub, allChild: subAll } = loopOrgs(
              treeSet,
              value,
            );
            children = children.concat(sub);
            allKeys = subAll;
            allChild = Array.from(new Set(allChild.concat(subAll.concat(sub))));
          }

          map[value].allChild = Array.from(new Set(allKeys.concat(children)));

          map[value].children = children;
          return value;
        },
      );
      return { children, allChild };
    };

    if (isInPermission) {
      if (mineOrgs) {
        loopOrgs(dig([mineOrgs!], true));
        highestOrgId = `${mineOrgs!.typeTag}-${mineOrgs!.id}`;
      }
    } else {
      if (otherOrgs) {
        loopOrgs(dig([otherOrgs!], false));
        highestOrgId = `${otherOrgs!.typeTag}-${otherOrgs!.id}`;
      }
    }

    // if (mineOrgs) {
    //   // 权限内
    //   loopOrgs(dig([mineOrgs], isInPermission));
    //   highestOrgId = `${mineOrgs.typeTag}-${mineOrgs.id}`;
    // } else if (otherOrgs) {
    //   loopOrgs(dig([otherOrgs], isInPermission));
    //   highestOrgId = `${otherOrgs.typeTag}-${otherOrgs.id}`;
    // }

    return { orgMap: map, highestOrgId };
  }, [mineOrgs, otherOrgs, isInPermission]);

  console.log({ orgMap });

  useEffect(() => {
    if (Object.keys(orgMap).length > 0) {
      console.log('orgMapwwwww', Object.keys(orgMap));
      if (shopId) {
        selectOrg(
          /[a-zA-Z]/.test(shopId) ? `SHOP-${shopId}` : `ORGANIZATION-${shopId}`,
        );
        // selectOrg(`SHOP-${shopId}`);
      } else {
        selectOrg(Object.keys(orgMap)[0]);
      }
    }
  }, [orgMap]);

  const searchShopList = useMemo(() => {
    const list: any[] = [];
    const dig = (child: any[]) => {
      child.forEach((item) => {
        item?.children && dig(item?.children);
        const searchName = item?.name
          ?.toLocaleLowerCase()
          .includes(searchValue?.toLocaleLowerCase());
        const searchNo = item?.shopId
          ?.toLocaleLowerCase()
          .includes(searchValue?.toLocaleLowerCase());
        const search = searchName || searchNo;
        if (item?.type === 2 && search) {
          list.push(item);
        }
      });
    };
    dig([mineOrgs]);
    return list;
  }, [searchValue, mineOrgs]);

  const handleSearch = useCallback(
    debounce((value: string) => {
      setSearchValue(value);
    }, 500),
    [],
  );
  const { list, over, fetchNext } = useScrollFetch(
    ({ pageNo, pageSize }) =>
      Promise.resolve(
        searchShopList?.slice((pageNo - 1) * pageSize, pageNo * pageSize),
      ),
    [searchShopList],
    {
      pageSize: 20,
    },
  );
  const searchdata = useMemo(() => {
    return list?.flatMap((item) => item) || [];
  }, [list]);
  return (
    <OrganizationDropdown
      hasEllipsis={hasEllipsis}
      title={
        orgMap[filterData?.selectId || filterData?.viewId || highestOrgId]
          ?.label || '全部门店'
      }
      className="my-2"
      ref={orgDropDownRef}
    >
      <SearchBar
        className="px-4 py-6  bg-white"
        placeholder="请输入门店名称或门店编号"
        onChange={(val) => {
          handleSearch(val);
        }}
        onCancel={() => {
          setSearchValue('');
        }}
      />
      {!!searchValue ? ( // 搜索出来的门店数据
        <ul>
          {searchdata?.map((item) => {
            return (
              <li
                className="flex justify-between w-full px-4 py-[10px] items-center"
                key={item?.id}
              >
                <div>
                  {item?.shopId} {item?.name}
                </div>
                <div>
                  <Button
                    size="mini"
                    color="primary"
                    className="ml-2"
                    onClick={() => {
                      const selectId = `SHOP-${item?.shopId}`;
                      setFilterData({
                        ...filterData,
                        selectId,
                      });
                      orgDropDownRef?.current?.close();
                      selectOrg(selectId);
                    }}
                  >
                    选择
                  </Button>
                </div>
              </li>
            );
          })}
          <InfiniteScroll loadMore={fetchNext} hasMore={!over} />
        </ul>
      ) : (
        <ul>
          {orgMap[filterData?.viewId || highestOrgId] && (
            <li className="flex justify-between w-full px-4 py-[10px] items-center">
              <div>
                {filterData?.viewId === highestOrgId ? '全部门店' : '全部'}
              </div>
              <div>
                {orgMap[filterData?.viewId || highestOrgId]?.parentId && (
                  <Button
                    size="mini"
                    onClick={() => {
                      setFilterData({
                        ...filterData,
                        viewId:
                          orgMap[filterData?.viewId || highestOrgId]?.parentId,
                      });
                    }}
                  >
                    返回上级
                  </Button>
                )}
                <Button
                  size="mini"
                  color="primary"
                  className="ml-2"
                  onClick={() => {
                    const target = orgMap[filterData?.viewId || highestOrgId];
                    const selectId = target?.value;
                    console.log(selectId, '=selectId');

                    setFilterData({
                      ...filterData,
                      selectId,
                    });
                    orgDropDownRef?.current?.close();
                    selectOrg(selectId);
                  }}
                >
                  选择
                </Button>
              </div>
            </li>
          )}
          {orgMap[filterData.viewId || highestOrgId]?.children?.map(
            (key: string) => {
              return (
                <li
                  key={key}
                  className="flex justify-between w-full px-4 py-[10px] items-center"
                >
                  <div>{orgMap[key].label}</div>
                  <div className="shrink-0">
                    {orgMap[key]?.children?.length > 0 && (
                      <Button
                        size="mini"
                        onClick={() => {
                          setFilterData({
                            ...filterData,
                            viewId: orgMap[key].value,
                          });
                        }}
                      >
                        查看下级
                      </Button>
                    )}
                    <Button
                      size="mini"
                      color="primary"
                      className="ml-2"
                      onClick={() => {
                        const selectId = orgMap[key].value;
                        console.log(selectId, '=selectId');

                        setFilterData({
                          ...filterData,
                          selectId,
                        });
                        orgDropDownRef?.current?.close();
                        selectOrg(selectId);
                      }}
                    >
                      选择
                    </Button>
                  </div>
                </li>
              );
            },
          )}
        </ul>
      )}
    </OrganizationDropdown>
  );
}
