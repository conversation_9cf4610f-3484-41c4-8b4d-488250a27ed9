import React, { memo } from 'react';
// import './index.less';
import { Image } from 'antd-mobile';
import { IconFont } from '..';
type UserAvatarProps = {
  avatarUrl?: string;
  username: string;
  className?: string;
};

const UserAvatar: React.FC<UserAvatarProps> = memo(
  ({ avatarUrl, username }) => {
    if (avatarUrl) {
      return (
        <div className="relative">
          <Image width={24} height={24} style={{ borderRadius: '100%' }} src={avatarUrl} />
          <IconFont
            type="icon-a-check-circle11"
            className="text-base leading-none  text-[#00BBB4] absolute left-3 top-3"
          />
        </div>
      );
    }

    let text = '';

    if (username) {
      text = /^[^\u4e00-\u9fa5]/.test(username) ? username[0] : username?.slice(0, 1);
    }

    return (
      <div className={`head-img bg-primary rounded-full text-white text-center w-6 h-6 leading-6 relative`}>
        {text}
        <IconFont
          type="icon-a-check-circle11"
          className="text-base leading-none  text-[#00BBB4] absolute left-3 top-3"
        />
      </div>
    );
  },
  (prev, next) => prev.username === next.username,
);

export default UserAvatar;
