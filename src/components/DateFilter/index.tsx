import { useMemo, useRef, useState } from 'react';
import { CssProps } from '@src/common/api.type';
import { Button, Calendar, type CalendarProps, DatePickerView, Dialog, Popup, Tabs } from 'antd-mobile';
import dayjs from 'dayjs';
import { IconFont } from '../IconFont';

type IProps = CssProps & {
  type?: 'month' | 'date';
  /**
   * 仅展示月份
   */
  onlyMonth?: boolean;
  /**
   * 仅展示日期
   */
  onlyDate?: boolean;
  value?: [Date, Date];
  noBg?: boolean;
  onChange?: (value: string[] | undefined, key?: string) => void; // 格式化后的数据['YYYY-MM-DD','YYYY-MM-DD']
  isSameYear?: boolean;
  noDefault?: boolean;
  selectionMode?: 'single' | 'range';
  noMaxFlag?: boolean;
  /**
   * 自定义日期范围（天数）
   */
  customizeDateRange?: number;
} & Omit<CalendarProps, 'selectionMode' | 'defaultValue' | 'onChange'>;

const DateFilter: React.FC<IProps> = ({
  noDefault,
  value = noDefault ? undefined : [dayjs().toDate(), dayjs().toDate()],
  type,
  onlyMonth,
  onlyDate,
  noBg,
  onChange,
  className,
  max = dayjs().toDate(),
  isSameYear = false,
  shouldDisableDate,
  selectionMode = 'range',
  noMaxFlag,
  customizeDateRange = 0,
  ...rest
}) => {
  const [activeKey, setActiveKey] = useState<string>(type || 'date');
  const [visible, setVisible] = useState(false);
  const valueRef = useRef<[Date, Date] | undefined>(value);
  const monthRef = useRef<Date | undefined>(value?.[0]);
  const currentActiveRef = useRef<string>(type || 'date');

  const handleConfirm = () => {
    const FORMAT = 'YYYY-MM-DD';

    if (activeKey === 'date' && !valueRef.current && !noDefault) {
      Dialog.alert({
        content: '请选择时间范围',
      });
      return;
    }

    if (activeKey === 'date' && customizeDateRange > 0) {
      const [start, end] = valueRef?.current || [];

      if (dayjs(end).diff(dayjs(start), 'day') >= customizeDateRange) {
        Dialog.alert({ content: `选择的时间范围超过了${customizeDateRange}天，请缩小范围后重试` });
        return;
      }
    }

    if (activeKey === 'date' && dayjs(valueRef.current?.[1]).diff(dayjs(valueRef.current?.[0]), 'M', true) >= 3) {
      Dialog.alert({
        content: '选择的时间范围超过了3个月，请缩小范围后重试',
      });
      return;
    }

    if (isSameYear && !dayjs(valueRef.current?.[1]).isSame(dayjs(valueRef.current?.[0]), 'year')) {
      Dialog.alert({
        content: '选择的时间范围不能跨年',
      });
      return;
    }

    const formatData =
      activeKey === 'date'
        ? valueRef.current?.map((item: Date) => dayjs(item).format(FORMAT))
        : [dayjs(monthRef.current).startOf('M').format(FORMAT), dayjs(monthRef.current).endOf('M').format(FORMAT)];

    onChange && onChange(formatData, activeKey);
    setVisible(false);
  };

  const DateComponent = () => {
    return (
      <Calendar
        {...rest}
        max={noMaxFlag ? undefined : max}
        shouldDisableDate={shouldDisableDate ? shouldDisableDate : undefined}
        selectionMode={selectionMode as any}
        defaultValue={selectionMode === 'single' ? value?.[0] : value}
        onChange={(val: any) => {
          if (!!val) {
            selectionMode === 'single' ? (valueRef.current = [val!, val!]) : (valueRef.current = val!);
          } else {
            valueRef.current = undefined;
          }
        }}
      />
    );
  };
  const MonthComponent = () => {
    const labelRenderer = (type: string, data: number) => {
      switch (type) {
        case 'year':
          return `${data}年`;
        case 'month':
          return `${data}月`;
        case 'day':
          return `${data}日`;
        case 'hour':
          return `${data}时`;
        case 'minute':
          return `${data}分`;
        case 'second':
          return `${data}秒`;
        default:
          return data;
      }
    };
    return (
      <DatePickerView
        {...rest}
        defaultValue={value?.[0] ?? dayjs().toDate()}
        precision="month"
        max={max}
        renderLabel={labelRenderer}
        onChange={(val) => {
          monthRef.current = val;
        }}
      />
    );
  };

  const showLabel = useMemo(() => {
    if (activeKey === 'date') {
      const arr = value?.map((v) => dayjs(v).format('MM/DD'));
      const today = dayjs().format('MM/DD');
      return !!!arr ? '开始日期' : arr.every((v) => v === today) ? '今天' : arr[0] === arr[1] ? arr[0] : arr.join('~');
    } else {
      const selectMonth = dayjs(value?.[0]).format('YYYY年M月');
      const currentMonth = dayjs().format('YYYY年M月');
      return currentMonth === selectMonth ? '当月' : selectMonth;
    }
  }, [activeKey, value]);

  const Component = activeKey === 'date' ? DateComponent : MonthComponent;

  return (
    <>
      <div
        className={`flex items-center ${value ? 'text-primary' : 'text-grey'} leading-[22px] ${
          noBg ? '' : `${value ? 'bg-primary-1' : 'bg-[#F7F7F7]'} px-2 py-[3px] min-w-14 rounded`
        } ${className}`}
        onClick={() => {
          setVisible(true);
          monthRef.current = value?.[0];
          valueRef.current = value;
          currentActiveRef.current = activeKey;
        }}
      >
        {showLabel}
        <IconFont type="icon-chevron-down" className="text-xs ml-1" />
      </div>
      <Popup
        position="top"
        visible={visible}
        bodyClassName="p-4 box-border"
        onMaskClick={() => {
          setVisible(false);
          setActiveKey(currentActiveRef.current);
        }}
      >
        <div>
          <Tabs activeKey={activeKey} onChange={setActiveKey} stretch={false}>
            {!onlyMonth && <Tabs.Tab title="自定义时间" disabled={onlyMonth} key={'date'} />}
            {!onlyDate && <Tabs.Tab title="月份" key={'month'} />}
          </Tabs>
          <Component />
          <div className="flex gap-2 pt-2">
            <Button
              className="flex-1"
              onClick={() => {
                setVisible(false);
                setActiveKey(currentActiveRef.current);
              }}
              color="primary"
              fill="outline"
            >
              取消
            </Button>
            <Button className="flex-1" color="primary" onClick={handleConfirm}>
              确定
            </Button>
          </div>
        </div>
      </Popup>
    </>
  );
};

export default DateFilter;
