import KeepAlive from 'react-activation';
import { useLocation } from 'react-router-dom';

const WithKeepAlive = <T extends {}>(Component: React.ComponentType<T>) => {
  return function AddKeepAliveComponent(props: T) {
    const location = useLocation();

    return (
      <KeepAlive
        key={location.pathname}
        id={location.pathname}
        name={location.pathname}
        cacheKey={location.pathname}
        saveScrollPosition
      >
        <Component {...props} />
      </KeepAlive>
    );
  };
};

export default WithKeepAlive;
