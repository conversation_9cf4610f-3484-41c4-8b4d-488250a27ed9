import { useContext, useEffect, useRef, useState } from 'react';
import { useUpdateEffect } from 'ahooks';
import { Popup, PopupProps } from 'antd-mobile';
import clsx from 'clsx';
import { IPopupPageContext } from './context';
import styles from './index.module.scss';

interface IPopupPageProps extends Omit<PopupProps, 'showCloseButton' | 'closeOnMaskClick'> {
  DocumentTitle?: string;
  footer?: React.ReactNode;
}

const isIos = /(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent);

export const IPopupPage = ({
  visible,
  bodyStyle,
  onClose,
  children,
  DocumentTitle,
  footer,
  ...props
}: IPopupPageProps) => {
  const contextValue = useContext(IPopupPageContext);
  const originTitle = useRef(document.title);
  const handlePopStateTagRef = useRef(false);
  const [hidden, setHidden] = useState(false);
  const isSlidingReturnPage = useRef(false);
  // 使用引用类型当作唯一值，进行比对
  const [uniqId] = useState(() => ({}));

  useEffect(() => {
    if (contextValue) {
      if (visible) {
        contextValue.current.push(uniqId);
      } else {
        contextValue.current = contextValue.current.filter((i) => i !== uniqId);
      }
    }
  }, [visible]);

  useEffect(() => {
    return () => {
      if (contextValue) {
        contextValue.current = contextValue.current.filter((i) => i !== uniqId);
      }
    };
  }, []);

  useUpdateEffect(() => {
    const handlePopState = () => {
      // 使用者没包 Provider 的情况、或当前这个 PopupPage 是最后一个打开的，才关闭
      if (!contextValue || contextValue.current[contextValue.current.length - 1] === uniqId) {
        handlePopStateTagRef.current = true;
        onClose?.();
        if (isIos && isSlidingReturnPage.current) {
          setHidden(true);
        }
      }
    };

    if (visible) {
      history.pushState({}, '', new URL(location.href));
      window.addEventListener('popstate', handlePopState);
      document.title = DocumentTitle ? DocumentTitle : originTitle.current;
    } else {
      document.title = originTitle.current;
      if (handlePopStateTagRef.current) {
        handlePopStateTagRef.current = false;
      } else {
        history.back();
      }
    }

    return () => {
      window.removeEventListener('popstate', handlePopState);
    };
  }, [visible]);

  // 只有 ios 设备才需要
  useEffect(() => {
    if (!isIos) {
      return;
    }
    let isSliding = false;
    const onTouchStart = (e: TouchEvent) => {
      // 是否在屏幕左侧开始滑动
      if (e.touches.length === 1 && e.touches[0].clientX < 24) {
        isSliding = true;
      }
    };
    const onTouchEnd = () => {
      if (isSliding) {
        isSlidingReturnPage.current = true;
        setTimeout(() => {
          // 如果这里还没执行到，却触发了 popState，就认为是侧滑关闭
          isSlidingReturnPage.current = false;
        }, 100);
      }
      isSliding = false;
    };
    if (visible) {
      setHidden(false);
      document.addEventListener('touchstart', onTouchStart);
      document.addEventListener('touchend', onTouchEnd);
    }

    return () => {
      document.removeEventListener('touchstart', onTouchStart);
      document.removeEventListener('touchend', onTouchEnd);
    };
  }, [visible]);

  return (
    <Popup
      {...props}
      visible={visible}
      bodyStyle={{
        ...bodyStyle,
        width: '100vw',
      }}
      className={clsx({ [styles.IPopupPageHidden]: hidden }, props.className)}
      onClose={onClose}
      position="right"
    >
      <div className="flex flex-col h-full divide-y divide-[#F6F6F6]">
        <div className="flex-grow overflow-y-scroll">{children}</div>
        {footer && <footer className="flex-shrink-0">{footer}</footer>}
      </div>
    </Popup>
  );
};
