import React, { useRef } from 'react';

export const IPopupPageContext = React.createContext<React.MutableRefObject<any[]> | null>(null);

export const IPopupPagePopstateProvider = ({ children }: { children?: React.ReactNode }) => {
  // 空对象数组 [{}, {}, {}]，利用引用类型特性进行相等对比
  const openUniqIds = useRef<any[]>([]);

  return <IPopupPageContext.Provider value={openUniqIds}>{children}</IPopupPageContext.Provider>;
};
