import { Popup, PopupProps, SafeArea } from 'antd-mobile';
import styles from './index.module.scss';

export interface IPopupProps extends PopupProps {
  title: string;
  subTitle?: string;
  footer?: React.ReactNode;
  titlePadding?: string; // 顶部标题边距的问题
}

export const IPopup = ({
  bodyStyle,
  children,
  title,
  subTitle,
  footer,
  className,
  titlePadding,
  ...props
}: IPopupProps) => {
  return (
    <Popup
      showCloseButton
      closeOnMaskClick
      {...props}
      bodyStyle={{
        borderTopLeftRadius: '8px',
        borderTopRightRadius: '8px',
        ...bodyStyle,
      }}
      className={`${styles.IPopup} ${className}`}
    >
      <div className="flex flex-col h-full divide-y divide-[#F6F6F6] max-h-[inherit]">
        <div className="pb-4">
          <h3
            className={`${
              titlePadding ? titlePadding : 'px-6'
            } flex-shrink-0 pt-[1.2rem]  text-[1.125rem] leading-[1.125rem] font-medium`}
          >
            {title}
          </h3>
          {subTitle && <h4 className="px-4 mt-2 text-[#858585]">{subTitle}</h4>}
        </div>
        <div className="flex-grow overflow-y-scroll">
          {children}
          <SafeArea position="bottom" />
        </div>
        {footer && (
          <footer className="flex-shrink-0">
            {footer}
            <SafeArea position="bottom" />
          </footer>
        )}
      </div>
    </Popup>
  );
};
