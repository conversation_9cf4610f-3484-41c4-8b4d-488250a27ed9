import { RightOutline } from 'antd-mobile-icons';
import { useEffect, useRef, useState } from 'react';

interface CustomerFormFieldWrapperProps {
  value?: React.ReactNode;
  placeholder?: string;
  onShow: () => void;
  children: React.ReactNode;
  overflowedText?: React.ReactNode;
}

export const CustomerFormFieldWrapper = ({
  value,
  placeholder,
  onShow,
  children,
  overflowedText,
}: CustomerFormFieldWrapperProps) => {
  const [isTextOverflowed, setIsTextOverflowed] = useState(false);

  const textContainerRef = useRef<HTMLDivElement | null>(null);
  const textRef = useRef<HTMLSpanElement | null>(null);

  useEffect(() => {
    const textContainerWidth = textContainerRef.current?.offsetWidth;
    const textWidth = textRef.current?.offsetWidth;
    if (textContainerWidth && textWidth) {
      if (textContainerWidth <= textWidth) {
        setIsTextOverflowed(true);
      } else {
        setIsTextOverflowed(false);
      }
    }
  }, [value]);

  return (
    <>
      <div onClick={onShow} className="flex items-center justify-end gap-[3px]">
        <div
          ref={textContainerRef}
          id="textContainer"
          className="text-15 truncate w-full text-right relative"
        >
          {isTextOverflowed && overflowedText && (
            <span className="float-right">{overflowedText}</span>
          )}
          <span ref={textRef} id="text" className="">
            {value ? value : <span className="text-[#CCCCCC]">{placeholder}</span>}
          </span>
        </div>
        <RightOutline className="text-[#cccccc] text-xs shrink-0" />
      </div>
      {children}
    </>
  );
};
