import { FC, ReactNode } from 'react';

interface PageContainerProps {
  children?: ReactNode;
  footer?: ReactNode;
}

const PageContainer: FC<PageContainerProps> = ({ children, footer }) => {
  return (
    <div className="flex flex-col fixed inset-x-0 inset-y-0">
      <div className="grow overflow-y-scroll">{children}</div>
      {footer && <div className="shrink-0">{footer}</div>}
    </div>
  );
};

export default PageContainer;
