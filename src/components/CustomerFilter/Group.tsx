import useGroupTree, { ArchitectureDataNode } from '@src/hooks/useGroupTree';
import styles from './index.module.scss';
import { TreeSelect } from 'antd';
import { Button, Popup } from 'antd-mobile';
import { useEffect, useState } from 'react';
import { IconFont } from '../IconFont';

type IProps = {
  needAll: boolean;
  pid: string; //组织树第一个id
  value: string;
  treeData: ArchitectureDataNode[];
  noBg: boolean;
  maxWidth?: string;
  onChange: (e: string) => void;
};
const GroupFilter: React.FC<IProps> = ({
  needAll,
  treeData,
  onChange,
  pid,
  value: initValue = '',
  noBg,
  maxWidth = '7.125rem',
}) => {
  const { data, tagIdObj } = useGroupTree();

  const [visible, setVisible] = useState(false);
  const [value, setValue] = useState('');

  useEffect(() => {
    setValue(initValue);
  }, [initValue]);

  const handleConfirm = () => {
    onChange && onChange(value);
    setVisible(false);
  };

  if (data && data.length === 0) {
    return (
      <div className="mr-1 text-left ellipsis leading-[22px]" style={{ maxWidth: maxWidth }}>
        暂无组织
      </div>
    );
  }

  return (
    <>
      {/* <Button key="group" color="default" size="small" onClick={() => setVisible(true)} style={{ '--border-radius': '8px' }}> */}
      <div
        key="group"
        className={`flex items-center ${noBg ? '' : 'px-2 py-[3px]'} rounded ${value
          ? `text-primary ${noBg ? '' : 'bg-primary-1'}`
          : `text-grey ${noBg ? '' : 'bg-gray-50'}`
          }`}
        onClick={() => setVisible(true)}
      >
        <div className="mr-1 text-left ellipsis leading-[22px]" style={{ maxWidth: maxWidth }}>
          {value ? tagIdObj[value]?.name : '全部组织'}
        </div>
        {/* <Ellipsis
          className="mr-1 flex-1 text-left"
          direction="end"
          content={value ? tagIdObj[value]?.name : '全部组织'}
        /> */}
        <IconFont type="icon-chevron-down" className="text-xs" />
      </div>
      {/* </Button> */}
      <Popup
        position="top"
        visible={visible}
        bodyClassName="p-4 box-border"
        // bodyStyle={{ height: '450px' }}
        onMaskClick={() => {
          setValue(initValue);
          setVisible(false);
        }}
      >
        <div>
          <div style={{ height: '356px' }} className={styles.groupFilter}>
            <TreeSelect
              className={styles.iSelect}
              style={{ width: '100%' }}
              open
              showSearch
              // treeCheckable
              // treeDefaultExpandAll
              treeDefaultExpandedKeys={[pid]}
              autoClearSearchValue={false}
              getPopupContainer={(node) => node.parentElement}
              treeNodeFilterProp="title"
              placement="bottomLeft"
              treeData={treeData}
              value={value}
              onChange={(value) => setValue(value)}
              // maxTagCount={2}
              placeholder="请输入"
              listHeight={320}
              dropdownStyle={{ height: 320 }}
            />
          </div>
          <div className="flex pt-2 gap-3">
            <Button
              block
              className="h-[42px] text-sm"
              onClick={() => {
                console.log('needAll', needAll);
                setValue(pid);
                onChange && onChange(pid);
                //巡检下使用，以避免影响其他地方 暂时使用needAll 是否选择全部组织
                needAll ? setValue('') : setValue(pid);
                needAll ? onChange && onChange('') : onChange && onChange(pid);
                setVisible(false);
              }}
              color="primary"
              fill="outline"
            >
              清空筛选
            </Button>
            <Button block className="h-[42px] text-sm" color="primary" onClick={handleConfirm}>
              确定筛选
            </Button>
          </div>
        </div>
      </Popup>
    </>
  );
};

export default GroupFilter;
