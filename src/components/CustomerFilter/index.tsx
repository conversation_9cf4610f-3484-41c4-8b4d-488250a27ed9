import { Space } from 'antd-mobile';
import { useMemo } from 'react';
import GroupFilter from './Group';
import ShopFilter from './Shop';
import { useRequest, useControllableValue } from 'ahooks';
import { CssProps } from '@src/common/api.type';
import { ConfigProvider } from 'antd';
import { usePageContext } from '@src/common/page-context';
import clsx from 'clsx';
import useGroupTree from '@src/hooks/useGroupTree';
import { getZTGroupShop } from '@src/common/api';

type IProps = CssProps & {
  needAll?: boolean;
  value?: { groupId?: string; shopCodes?: string[] } | { groupId?: string; shopIds?: string[] };
  onChange?:
  | (({ groupId, shopCodes }: { groupId?: string; shopCodes?: string[] }) => void)
  | (({ groupId, shopIds }: { groupId?: string; shopIds?: string[] }) => void);
  type?: 'group' | 'shop' | 'default';
  noBg?: boolean;
  groupMaxWidth?: string;
  shopKey?: 'shopCodes' | 'shopIds';
};
const CustomerFilter: React.FC<IProps> = ({
  needAll = false,
  className,
  type = 'default',
  noBg,
  groupMaxWidth,
  shopKey = 'shopCodes',
  ...props
}) => {
  const { treeData } = useGroupTree();
  const { pid } = usePageContext() || {};

  const [params, setParams] = useControllableValue(props, {
    defaultValue: { groupId: '', [shopKey]: [] as string[] },
  });

  const { data } = useRequest(
    () => {
      if (type === 'group') {
        console.error('type === group');
        throw null;
      }
      // if (!params.groupId && !pid) {
      //   console.error('缺少一级组织pid');
      //   throw null;
      // }
      return getZTGroupShop({ fightGroupId: +params.groupId || +pid });
    },
    {
      refreshDeps: [params.groupId, pid],
    },
  );

  const shopData = useMemo(
    () => (data || []).map((v: any) => ({ ...v, title: v.shopName, value: `${v.shopId}` })),
    [data],
  );

  return (
    <ConfigProvider
      theme={{
        token: {
          colorPrimary: '#378BFF', // 塔斯汀红
        },
      }}
    >
      <div className={clsx(className)}>
        <Space wrap style={{ '--gap-horizontal': '1.5rem' }}>
          {type !== 'shop' && (
            <GroupFilter
              needAll={needAll}
              pid={pid}
              treeData={treeData}
              value={params.groupId}
              noBg={!!noBg}
              maxWidth={groupMaxWidth}
              onChange={(groupId) => setParams({ [shopKey]: [], groupId })}
            />
          )}
          {type !== 'group' && (
            <ShopFilter
              value={params[shopKey] as string[]}
              treeData={shopData}
              noBg={!!noBg}
              onChange={(ids) => setParams((p) => ({ ...p, [shopKey]: ids }))}
            />
          )}
        </Space>
      </div>
    </ConfigProvider>
  );
};

export default CustomerFilter;
