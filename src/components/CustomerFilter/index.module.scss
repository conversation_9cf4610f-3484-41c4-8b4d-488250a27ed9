.iSelect {
  :global {
    .ant-select-selector {
    }

    .ant-select-arrow {
      top: 16px;
    }

    .ant-select-dropdown {
      position: static !important;
      background: transparent !important;
      box-shadow: none !important;
      padding: 8px 0 !important;
      width: 100% !important;
    }
    .ant-select-tree-node-content-wrapper {
      outline: none !important;
    }
  }
}

.groupFilter {
  :global {
    .ant-select-arrow {
      top: 16px;
    }
    :where(.css-dev-only-do-not-override-rl9pi5).ant-tree-select-dropdown
      .ant-select-tree
      .ant-select-tree-node-content-wrapper.ant-select-tree-node-selected {
      background-color: rgba(55, 139, 255, 0.1);
      border-radius: 0.75rem;
    }
  }
}
