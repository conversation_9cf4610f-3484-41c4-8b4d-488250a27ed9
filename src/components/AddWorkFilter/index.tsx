import { DrawerProps } from 'antd';
import { useState } from 'react';
import { FilterDrawer } from '@src/pages/tasks/components/FilterDrawer';
import { sortOptionSEnum, yytShopTagEnum, yytShopTypeEnum } from '@src/pages/tasks/enum';

interface AddWorkFilterFilterProps extends DrawerProps {
  value: Record<string, any>;
  onChange: (value: Record<string, any>) => void;
  onClose: () => void;
}

export const init = {
  type: undefined,
  tag: undefined,
};
export const AddWorkFilter = ({ value, onChange, onClose, ...props }: AddWorkFilterFilterProps) => {
  const [activity, setActivity] = useState<any>(value);
  const onOk = () => {
    onChange(activity);
  };
  const onClear = () => {
    setActivity(init);
  };
  return (
    <FilterDrawer
      {...props}
      onClose={() => {
        // 重置一下activity
        setActivity(value);
        onClose();
      }}
      onOk={onOk}
      onClear={onClear}
    >
      <div className="flex flex-col gap-6">
        <div className="flex flex-col gap-3">
          <h3 className="text-[#141414] text-sm leading-[14px]">门店类型</h3>

          <div className="flex flex-wrap gap-2">
            {[
              { label: '加盟T', value: yytShopTypeEnum.加盟T },
              { label: '加盟M', value: yytShopTypeEnum.加盟M },
            ].map((item) => {
              return (
                <button
                  key={item.value}
                  className={`w-20 h-[30px] rounded ${
                    activity?.type === yytShopTypeEnum[item.label as keyof typeof yytShopTypeEnum]
                      ? 'text-primary bg-primary/10'
                      : 'text-58 bg-black/[0.03]'
                  }  text-sm left-[14px]`}
                  onClick={() => {
                    setActivity((pre: any) => {
                      if (activity?.type === item.value) {
                        return {
                          ...pre,
                          type: undefined,
                        };
                      } else {
                        return {
                          ...pre,
                          type: item.value,
                        };
                      }
                    });
                  }}
                >
                  {item.label}
                </button>
              );
            })}
          </div>
          <h3 className="text-[#141414] text-sm leading-[14px]">门店过滤</h3>
          <div className="flex flex-wrap gap-2">
            {[
              { label: '必检门店', value: yytShopTagEnum.必检门店 },
              { label: '本次未巡检过的门店', value: yytShopTagEnum.本次未巡检过的门店 },
            ].map((item) => {
              return (
                <button
                  key={item.value}
                  className={`h-[30px]  pl-2 pr-2 rounded ${
                    activity?.tag === yytShopTagEnum[item.label as keyof typeof yytShopTagEnum]
                      ? 'text-primary bg-primary/10'
                      : 'text-58 bg-black/[0.03]'
                  }  text-sm left-[14px]`}
                  onClick={() => {
                    setActivity((pre: any) => {
                      if (activity?.tag === item.value) {
                        return {
                          ...pre,
                          tag: undefined,
                        };
                      } else {
                        return {
                          ...pre,
                          tag: item.value,
                        };
                      }
                    });
                  }}
                >
                  {item.label}
                </button>
              );
            })}
          </div>
          <h3 className="text-[#141414] text-sm leading-[14px]">默认排序</h3>
          <div className="flex flex-wrap gap-2">
            {[
              { label: '上次巡检日期由远到近', value: sortOptionSEnum.上次巡检日期由远到近 },
              { label: '上次巡检分数由低到高', value: sortOptionSEnum.上次巡检分数由低到高 },
            ].map((item) => {
              return (
                <button
                  key={item.value}
                  className={`h-[30px]  pl-2 pr-2 rounded ${
                    activity?.sorter === sortOptionSEnum[item.label as keyof typeof sortOptionSEnum]
                      ? 'text-primary bg-primary/10'
                      : 'text-58 bg-black/[0.03]'
                  }  text-sm left-[14px]`}
                  onClick={() => {
                    setActivity((pre: any) => {
                      if (activity?.sorter === item.value) {
                        return {
                          ...pre,
                          sorter: undefined,
                        };
                      } else {
                        return {
                          ...pre,
                          sorter: item.value,
                        };
                      }
                    });
                  }}
                >
                  {item.label}
                </button>
              );
            })}
          </div>
        </div>
      </div>
    </FilterDrawer>
  );
};
