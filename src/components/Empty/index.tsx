import React, { ReactNode } from 'react';
import classNames from 'classnames';
import emptyImage from '@src/assets/images/task-empty.png';
type EmptyProps = {
  className?: string;
  text?: ReactNode;
};

const Empty: React.FC<EmptyProps> = ({ className, text }) => {
  return (
    <div className={classNames('flex flex-col items-center', className)}>
      <img src={emptyImage} className="w-[240px]" />
      <span className="text-base leading-[16px] text-[#5E5E5E]">{text || '暂无数据'}</span>
    </div>
  );
};

export default Empty;
