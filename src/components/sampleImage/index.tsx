import React from 'react';
import { Image, ImageViewer } from 'antd-mobile';

type SampleImageProps = {
  SampleimageList: { snapshotUrl: string; url: string }[];
};

const SampleImage: React.FC<SampleImageProps> = ({ SampleimageList = [] }) => {
  return (
    <div className={`flex flex-wrap gap-1 items-center py-1`}>
      {SampleimageList?.map((item, index) => {
        return (
          <div key={`${item.url}-${index}`} className="relative">
            <div className="absolute bottom-0 left-0  bg-[#378BFF] w-full flex justify-center">
              <span className="text-xs  text-white">示例图片</span>
            </div>
            <Image
              onClick={() => {
                ImageViewer.show({ image: item.url });
              }}
              src={item?.snapshotUrl || item.url}
              width={52}
              height={52}
            />
          </div>
        );
      })}
    </div>
  );
};

export default SampleImage;
