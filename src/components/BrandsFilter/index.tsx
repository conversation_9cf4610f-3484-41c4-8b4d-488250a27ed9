import styles from './index.module.scss';
import { ConfigProvider } from 'antd';
import { Button, IndexBar, Popup, SearchBar, Space } from 'antd-mobile';
import { useMemo, useState } from 'react';
import { CssProps } from '@src/common/api.type';
import { useBrands } from '@src/hooks';
import { pinyin } from 'pinyin-pro';
import { IconFont } from '..';

type IProps = CssProps & {
  noBg?: boolean;
  value?: string[];
  onChange?: (val: string[]) => void;
};

const BrandsFilter: React.FC<IProps> = ({ onChange, noBg, value: initValue = [] }) => {
  const { data: dataSource } = useBrands();
  const [keyWords, setKeyWords] = useState<string>('');

  //取首字母 给IndexBar序列组件 使用
  const { indexData } = useMemo(() => {
    let data = dataSource;
    if (keyWords) {
      data = data.filter((v: string) => v.includes(keyWords));
    }
    const indexMap = new Map();
    (data || []).forEach((v: string) => {
      const arr = pinyin(v, { pattern: 'first', toneType: 'none', type: 'array' });
      if (arr && arr.length) {
        const key = arr[0].toUpperCase();
        indexMap.has(key) ? indexMap.set(key, [...indexMap.get(key), v]) : indexMap.set(key, [v]);
      }
    });
    const indexData = [...indexMap.keys()]
      .sort()
      .map((key) => ({ key, children: indexMap.get(key) }));
    return { indexMap, indexData };
  }, [dataSource, keyWords]);

  // const treeData = useMemo(() => (data || []).map((v: string, index: number) => ({ title: v, value: v, key: index })), [data]);

  const [visible, setVisible] = useState(false);
  const [value, setValue] = useState<string[]>(initValue);

  const handleConfirm = () => {
    onChange && onChange(value);
    setVisible(false);
  };

  return (
    <ConfigProvider
      theme={{
        token: {
          colorPrimary: '#378BFF', // 塔斯汀红
        },
      }}
    >
      <div
        key="brand"
        className={`flex items-center ${noBg ? '' : 'px-2 py-[3px]'} rounded ${
          value && value.length
            ? `text-primary ${noBg ? '' : 'bg-primary-1'}`
            : `text-grey ${noBg ? '' : 'bg-gray-50'}`
        }`}
        style={{ maxWidth: '6.5rem' }}
        onClick={() => setVisible(true)}
      >
        <div className="mr-1 flex-1 text-left ellipsis leading-[22px]">
          {value && value.length ? `品牌已选${value.length}` : '筛选品牌'}
        </div>
        <IconFont type="icon-chevron-down" className="text-xs" />
      </div>
      <Popup
        position="top"
        visible={visible}
        bodyClassName="p-4 box-border"
        onMaskClick={() => {
          setValue(initValue);
          setVisible(false);
        }}
      >
        <div className={styles.iIndexBar}>
          <SearchBar
            className="mb-2"
            placeholder="输入要筛选的品牌"
            onSearch={setKeyWords}
            onClear={() => setKeyWords('')}
            style={{ '--border-radius': '8px', '--height': '40px' }}
          />
          <div style={{ height: '356px' }} className="overflow-y-auto">
            {/* <TreeSelect
              className={styles.iSelect}
              style={{ width: '100%' }}
              open
              showSearch
              treeCheckable
              treeDefaultExpandAll
              autoClearSearchValue={false}
              getPopupContainer={(node) => node.parentElement}
              treeNodeFilterProp="title"
              placement="bottomLeft"
              treeData={treeData}
              value={value}
              onChange={(value) => setValue(value)}
              // maxTagCount={2}
              placeholder="请输入"
              listHeight={320}
              dropdownStyle={{ height: 320 }}
            /> */}
            <IndexBar>
              {indexData.map((v) => {
                const { key, children } = v;
                return (
                  <IndexBar.Panel index={key} key={key}>
                    <Space wrap>
                      {children.map((item: string) => (
                        <div
                          key={item}
                          onClick={() => {
                            value.includes(item)
                              ? setValue((p) => p.filter((ele) => ele !== item))
                              : setValue((p) => [...p, item]);
                          }}
                          className={`${
                            value.includes(item) ? 'bg-primary-1 text-primary' : 'bg-line text-grey'
                          } px-2 py-1 rounded`}
                        >
                          {item}
                        </div>
                      ))}
                    </Space>
                  </IndexBar.Panel>
                );
              })}
            </IndexBar>
          </div>
          <div className="flex gap-2 pt-2">
            <Button
              className="flex-1"
              onClick={() => {
                setValue([]);
                onChange && onChange([]);
                setVisible(false);
              }}
              color="primary"
              fill="outline"
            >
              清空筛选
            </Button>
            <Button className="flex-1" color="primary" onClick={handleConfirm}>
              确定筛选
            </Button>
          </div>
        </div>
      </Popup>
    </ConfigProvider>
  );
};

export default BrandsFilter;
