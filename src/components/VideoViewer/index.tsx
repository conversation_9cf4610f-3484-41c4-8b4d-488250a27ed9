import { Popup, PopupProps } from 'antd-mobile';
import { CloseOutline } from 'antd-mobile-icons';

interface VideoViewerProps extends PopupProps {
  url: string;
}

const VideoViewer: React.FC<VideoViewerProps> = ({ url, onClose, ...props }) => {
  return (
    <Popup onClose={onClose} bodyStyle={{ height: '100vh' }} {...props}>
      <div
        className="absolute z-50 top-5 right-5 rounded-full p-2 bg-gray-200"
        onClick={(e) => {
          e.stopPropagation();
          onClose && onClose();
        }}
      >
        <CloseOutline className="text-xl" />
      </div>
      {props.visible && (
        <video controls={true} width="100%" src={url} style={{ height: '100%' }}></video>
      )}
    </Popup>
  );
};

export default VideoViewer;
