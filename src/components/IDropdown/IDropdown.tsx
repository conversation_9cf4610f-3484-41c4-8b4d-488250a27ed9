import { Button, Dropdown, DropdownRef } from 'antd-mobile';
import React, { useEffect, useRef } from 'react';
import styles from './index.module.scss';
import { IconFont } from '../IconFont';

interface IDropdownProps {
  title?: React.ReactNode;
  children?: React.ReactNode;
  noBg?: boolean;
  onOk?: () => void;
  onClose?: () => void;
  onClear?: () => void;
  isFilter?: boolean;
}

export const IDropdown = ({
  children,
  onOk,
  onClose,
  onClear,
  isFilter,
  noBg,
  title = [
    <div
      key="default_dropdown_title"
      className={`${noBg ? 'h-[22px]' : 'h-[29px]'} my-2 flex justify-center items-center ${
        isFilter ? 'text-primary' : ''
      }`}
    >
      筛选
    </div>,
  ],
}: IDropdownProps) => {
  const ref = useRef<DropdownRef>(null);

  const isOkCallback = useRef(false);

  useEffect(() => {
    const handlePopState = () => {
      ref.current?.close();
    };

    window.addEventListener('popstate', handlePopState);

    return () => {
      window.removeEventListener('popstate', handlePopState);
    };
  }, []);

  return (
    <Dropdown
      className={styles.IDropdown}
      ref={ref}
      closeOnClickAway
      onChange={(val) => {
        if (val === null) {
          if (isOkCallback.current) {
            isOkCallback.current = false;
          } else {
            onClose?.();
          }
        }
      }}
    >
      <Dropdown.Item
        destroyOnClose
        key="sorter"
        title={title}
        arrow={
          <IconFont
            type="icon-chevron-down"
            className={`text-xs ${isFilter ? 'text-primary' : ''}`}
          />
        }
      >
        <div className="px-4 py-5">{children}</div>
        <footer>
          <div className="p-4 flex gap-2 border-t border-[rgba(0, 0, 0, 0.03)]">
            <Button block color="primary" fill="outline" onClick={onClear}>
              清空筛选
            </Button>
            <Button
              onClick={() => {
                onOk?.();
                isOkCallback.current = true;
                ref.current?.close();
              }}
              block
              color="primary"
            >
              确认筛选
            </Button>
          </div>
        </footer>
      </Dropdown.Item>
    </Dropdown>
  );
};
