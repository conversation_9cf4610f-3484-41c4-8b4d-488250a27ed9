import ReactECharts, { EChartsOption, EChartsReactProps } from 'echarts-for-react';
import { useMemo } from 'react';
import { color, colorGradient } from '../Bar/BarOptionColor';
import { cloneDeep } from 'lodash';
import { formatAmount, formatPer } from '@src/utils/helper';

interface BarProps extends Partial<EChartsReactProps> {
  id: string;
  dataset: any;
  yType?: '￥' | '%' | '个';
}

export const BarOfDataSet = ({ id, dataset, yType = '￥', ...restProps }: BarProps) => {
  const option: EChartsOption = {
    color: colorGradient,
    tooltip: {
      formatter: (value: any) => {
        let html = `<div style="display:flex;align-items:center">
        <div>${value?.name}</div>
      </div>`;
        cloneDeep(value?.dimensionNames || [])
          .splice(1)
          .forEach((o: any, idx: number) => {
            html += `<div style="display:flex;align-items:center;gap: 8px;margin-top:8px">
                <div
                  style="background: ${color[idx]};border-radius: 9999px;width: 5px;height: 5px;"
                ></div>
                <div style="font-size: 12px;line-height: 12px">${o}：${value?.value?.[o]}</div>
              </div>`;
          });

        return html;
      },
    },
    dataset,
    xAxis: {
      type: 'category',
      axisLabel: {
        interval: 0,
        color: '#5E5E5E',
        margin: 12,
      },
      axisLine: {
        // 设置 x 轴线的样式
        show: true, // 显示 x 轴线
        lineStyle: {
          color: 'rgba(65, 97, 128, 0.1519)', // 设置线的颜色
          width: 1, // 设置线的宽度
          type: 'solid', // 设置线的类型（实线、虚线等）
        },
      },
      axisTick: {
        show: false,
      },
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: (value: any) => {
          if (yType === '￥') {
            return '￥' + formatAmount(value, '', 1);
          }
          if (yType === '%') {
            return formatPer(value);
          }
          if (yType === '个') {
            return value + '个';
          }
        }, // 刻度标签的格式，这里添加了百分号
        color: 'rgba(44, 53, 66, 0.45)',
        fontSize: 10,
      },
      splitNumber: 2,
      splitLine: {
        //分割线配置
        lineStyle: {
          color: 'rgba(65, 97, 128, 0.1519)',
        },
      },
    },
    series: cloneDeep(dataset.dimensions)
      .splice(1)
      .map(() => ({
        type: 'bar',
        barGap: 0,
        itemStyle: {
          normal: {
            barBorderRadius: [2, 2, 0, 0], // 设置圆角大小
          },
        },
        barWidth: 17,
      })),
    grid: {
      top: '6px',
      bottom: '0px',
      right: '0px',
      left: '0px',
      containLabel: true,
    },
  };

  const BarCharts = useMemo(() => {
    if (!dataset || !(dataset?.source || []).length) return <div></div>;
    return <ReactECharts key={id} option={option} {...restProps} />;
  }, [dataset]);

  return <div className="w-full h-full">{BarCharts}</div>;
};
