import ReactECharts, { EChartsOption, EChartsReactProps } from 'echarts-for-react';
import { horizontalColorGradient } from './BarOptionColor';
import { useMemo, useRef } from 'react';
import { useClickAway } from 'ahooks';
import { formatAmount, formatPer } from '@src/utils/helper';

interface HorizontalBarProps extends Partial<EChartsReactProps> {
  id: string;
  data: any;
  barWidth?: number;
  horizontalColorIndex: number;
}

export const HorizontalBar = ({
  id,
  data,
  barWidth = 17,
  horizontalColorIndex,
  ...restProps
}: HorizontalBarProps) => {
  const ref = useRef<ReactECharts>(null);
  const refContainer = useRef<HTMLDivElement>(null);

  const option: EChartsOption = {
    color: horizontalColorGradient.slice(horizontalColorIndex),
    xAxis: {
      type: 'value',
      axisLabel: {
        formatter: (value: any) => {
          return '￥' + formatAmount(value, '', 1);
        }, // 刻度标签的格式，这里添加了百分号
        color: 'rgba(44, 53, 66, 0.45)',
        fontSize: 10,
      },
      splitNumber: 3,
      axisLine: {
        // 设置 x 轴线的样式
        show: true, // 显示 x 轴线
        lineStyle: {
          color: 'rgba(156, 156, 156, 0.48)', // 设置线的颜色
          width: 1, // 设置线的宽度
          type: 'solid', // 设置线的类型（实线、虚线等）
        },
      },
    },
    yAxis: {
      show: false,
      type: 'category',
      inverse: true, // 更改排序。默认从下往上->从上往下
    },
    series: [
      {
        data,
        type: 'bar',
        itemStyle: {
          normal: {
            color: function (params: any) {
              // 根据数据的索引来匹配相应的颜色
              return option.color[params.dataIndex % option.color.length];
            },
            barBorderRadius: [0, 2, 2, 0], // 设置圆角大小
          },
        },
        barWidth,
      },
    ],
    grid: {
      top: '6px',
      bottom: '0px',
      right: '24px',
      left: '0px',
      containLabel: true,
    },
    tooltip: {
      show: true,
      trigger: 'item',
      backgroundColor: '#333',
      borderColor: '#333',
      textStyle: {
        color: '#fff',
      },
      confine: true,
      formatter: ({ marker, name, data }: { marker: string; name: string; data: any }) => {
        return `<div>
          <div>${marker}${name}</div>
          <div style="padding-left: 10px">${data?.label}</div>
          <div style="margin-top:10px;">
            <div style="color:#D6D6D6;">同比:<span style="margin-left:4px">${formatPer(
              data?.yoy,
            )}</span></div>
            <div style="margin-top:4px;color:#D6D6D6;">环比:<span style="margin-left:4px">${formatPer(
              data?.qoq,
            )}</span></div>
          </div>
        </div>`;
      },
    },
  };

  const BarCharts = useMemo(() => {
    if (!data || !data.length) return <div></div>;
    return (
      <div ref={refContainer}>
        <ReactECharts key={id} option={option} {...restProps} ref={ref} />
      </div>
    );
  }, [data]);

  useClickAway(() => {
    ref.current?.getEchartsInstance().dispatchAction({ type: 'hideTip' });
  }, refContainer);

  return <div className="w-full h-full">{BarCharts}</div>;
};
