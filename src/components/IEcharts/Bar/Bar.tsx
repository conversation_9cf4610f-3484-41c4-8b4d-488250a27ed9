import ReactECharts, { EChartsOption, EChartsReactProps } from 'echarts-for-react';
import { useMemo } from 'react';
import { colorGradient } from './BarOptionColor';
import { formatAmount, formatPer } from '@src/utils/helper';

interface BarProps extends Partial<EChartsReactProps> {
  id: string;
  data: any;
  barWidth?: number;
  yType?: '￥' | '%';
}

export const Bar = ({ id, data, barWidth = 17, yType = '￥', ...restProps }: BarProps) => {
  const option: EChartsOption = {
    color: colorGradient,
    xAxis: {
      show: false,
      type: 'category',
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: (value: any) => {
          if (yType === '￥') {
            return '￥' + formatAmount(value, '', 1);
          }
          if (yType === '%') {
            return formatPer(value);
          }
        }, // 刻度标签的格式，这里添加了百分号
        color: 'rgba(44, 53, 66, 0.45)',
        fontSize: 10,
      },
      splitNumber: 2,
    },
    series: [
      {
        data,
        type: 'bar',
        itemStyle: {
          normal: {
            color: function (params: any) {
              // 根据数据的索引来匹配相应的颜色
              return option.color[params.dataIndex % option.color.length];
            },
            barBorderRadius: [2, 2, 0, 0], // 设置圆角大小
          },
        },
        barWidth,
      },
    ],
    grid: {
      top: '6px',
      bottom: '0px',
      right: '0px',
      left: '0px',
      containLabel: true,
    },
  };

  const BarCharts = useMemo(() => {
    if (!data || !data.length) return <div></div>;
    return <ReactECharts key={id} option={option} {...restProps} />;
  }, [data]);

  return <div className="w-full h-full">{BarCharts}</div>;
};
