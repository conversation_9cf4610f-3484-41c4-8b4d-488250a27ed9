// label的提示标签点颜色
export const color = [
  '#41C9C4',
  '#4592F6',
  '#7870FF',
  '#3600A1',
  '#8D32D2',
  '#6C148F',
  '#F33DC2',
  '#F45E5E',
  '#FF984F',
  '#FFDC4F',
  '#ADE153',
  '#31B862',
  '#06704B',
];
// 柱体的渐变色
export const colorGradient = [
  {
    type: 'linear',
    x: 0,
    y: 0,
    x2: 0,
    y2: 1,
    colorStops: [
      {
        offset: 0,
        color: 'rgba(65, 201, 196, 1)',
      },
      {
        offset: 1,
        color: 'rgba(0, 187, 180, 0.15)',
      },
    ],
    global: false,
  },
  {
    type: 'linear',
    x: 0,
    y: 0,
    x2: 0,
    y2: 1,
    colorStops: [
      {
        offset: 0,
        color: 'rgba(69, 146, 246, 1)',
      },
      {
        offset: 1,
        color: 'rgba(69, 146, 246, 0.15)',
      },
    ],
    global: false,
  },
  {
    type: 'linear',
    x: 0,
    y: 0,
    x2: 0,
    y2: 1,
    colorStops: [
      {
        offset: 0,
        color: 'rgba(120, 112, 255, 1)',
      },
      {
        offset: 1,
        color: 'rgba(120, 112, 255, 0.15)',
      },
    ],
    global: false,
  },
  {
    type: 'linear',
    x: 0,
    y: 0,
    x2: 0,
    y2: 1,
    colorStops: [
      {
        offset: 0,
        color: 'rgba(54, 0, 161, 1)',
      },
      {
        offset: 1,
        color: 'rgba(54, 0, 161, 0.15)',
      },
    ],
    global: false,
  },
  {
    type: 'linear',
    x: 0,
    y: 0,
    x2: 0,
    y2: 1,
    colorStops: [
      {
        offset: 0,
        color: 'rgba(141, 50, 210, 1)',
      },
      {
        offset: 1,
        color: 'rgba(141, 50, 210, 0.15)',
      },
    ],
    global: false,
  },
  {
    type: 'linear',
    x: 0,
    y: 0,
    x2: 0,
    y2: 1,
    colorStops: [
      {
        offset: 0,
        color: 'rgba(108, 20, 143, 1)',
      },
      {
        offset: 1,
        color: 'rgba(108, 20, 143, 0.15)',
      },
    ],
    global: false,
  },
  {
    type: 'linear',
    x: 0,
    y: 0,
    x2: 0,
    y2: 1,
    colorStops: [
      {
        offset: 0,
        color: 'rgba(243, 61, 194, 1)',
      },
      {
        offset: 1,
        color: 'rgba(243, 61, 194, 0.15)',
      },
    ],
    global: false,
  },
  {
    type: 'linear',
    x: 0,
    y: 0,
    x2: 0,
    y2: 1,
    colorStops: [
      {
        offset: 0,
        color: 'rgba(244, 94, 94, 1)',
      },
      {
        offset: 1,
        color: 'rgba(244, 94, 94, 0.15)',
      },
    ],
    global: false,
  },
  {
    type: 'linear',
    x: 0,
    y: 0,
    x2: 0,
    y2: 1,
    colorStops: [
      {
        offset: 0,
        color: 'rgba(255, 152, 79, 1)',
      },
      {
        offset: 1,
        color: 'rgba(255, 152, 79, 0.15)',
      },
    ],
    global: false,
  },
  {
    type: 'linear',
    x: 0,
    y: 0,
    x2: 0,
    y2: 1,
    colorStops: [
      {
        offset: 0,
        color: 'rgba(255, 220, 79, 1)',
      },
      {
        offset: 1,
        color: 'rgba(255, 220, 79, 0.15)',
      },
    ],
    global: false,
  },
  {
    type: 'linear',
    x: 0,
    y: 0,
    x2: 0,
    y2: 1,
    colorStops: [
      {
        offset: 0,
        color: 'rgba(173, 225, 83, 1)',
      },
      {
        offset: 1,
        color: 'rgba(173, 225, 83, 0.15)',
      },
    ],
    global: false,
  },
  {
    type: 'linear',
    x: 0,
    y: 0,
    x2: 0,
    y2: 1,
    colorStops: [
      {
        offset: 0,
        color: 'rgba(49, 184, 98, 1)',
      },
      {
        offset: 1,
        color: 'rgba(49, 184, 98, 0.15)',
      },
    ],
    global: false,
  },
  {
    type: 'linear',
    x: 0,
    y: 0,
    x2: 0,
    y2: 1,
    colorStops: [
      {
        offset: 0,
        color: 'rgba(6, 112, 75, 1)',
      },
      {
        offset: 1,
        color: 'rgba(6, 112, 75, 0.15)',
      },
    ],
    global: false,
  },
];

export const horizontalColor = ['#FF8B38', '#F45E5E', '#FFD52C', '#ADE153'];
export const horizontalColorGradient = [
  {
    type: 'linear',
    x: 1,
    y: 0,
    x2: 0,
    y2: 0,
    colorStops: [
      {
        offset: 0,
        color: 'rgba(255, 139, 56, 1)',
      },
      {
        offset: 1,
        color: 'rgba(255, 139, 56, 0.15)',
      },
    ],
    global: false,
  },
  {
    type: 'linear',
    x: 1,
    y: 0,
    x2: 0,
    y2: 0,
    colorStops: [
      {
        offset: 0,
        color: 'rgba(244, 94, 94, 1)',
      },
      {
        offset: 1,
        color: 'rgba(244, 94, 94, 0.15)',
      },
    ],
    global: false,
  },
  {
    type: 'linear',
    x: 1,
    y: 0,
    x2: 0,
    y2: 0,
    colorStops: [
      {
        offset: 0,
        color: 'rgba(255, 213, 44, 1)',
      },
      {
        offset: 1,
        color: 'rgba(255, 213, 44, 0.15)',
      },
    ],
    global: false,
  },
  {
    type: 'linear',
    x: 1,
    y: 0,
    x2: 0,
    y2: 0,
    colorStops: [
      {
        offset: 0,
        color: 'rgba(173, 225, 83, 1)',
      },
      {
        offset: 1,
        color: 'rgba(173, 225, 83, 0.15)',
      },
    ],
    global: false,
  },
];
