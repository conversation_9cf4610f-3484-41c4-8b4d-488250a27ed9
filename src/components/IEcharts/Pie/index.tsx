import React, { useMemo, useRef } from 'react';
import ReactECharts, { EChartsOption } from 'echarts-for-react';
import { useClickAway } from 'ahooks';
import { color } from '../Bar/BarOptionColor';

const Pie: React.FC<any> = ({ data, id, showTip = false, ...restProps }: any) => {
  const ref = useRef<ReactECharts>(null);
  const refContainer = useRef<HTMLDivElement>(null);

  const options: EChartsOption = {
    color: color,
    tooltip: {
      show: showTip,
      position: [0, -20],
      trigger: 'item',
      formatter: ({
        marker,
        name,
        percent,
      }: {
        marker: string;
        name: string;
        value: number;
        percent: number;
      }) =>
        `<div style="display:flex;align-items:center">
          <div>${marker}${name}</div>
          <div style="padding-left: 20px; font-weight: 600">
          ${percent}%</div>
        </div>`,
    },
    legend: {
      show: false,
    },
    series: [
      {
        // name: 'Access From',
        type: 'pie',
        radius: ['60%', '100%'],
        data: data,
        label: {
          show: false,
        },
        selectedMode: false,
        select: { disabled: false },
        emphasis: {
          scale: false,
        },
      },
    ],
  };

  const PieCharts = useMemo(() => {
    if (!data || !data.length) return <div></div>;
    return (
      <div ref={refContainer}>
        <ReactECharts key={id} option={options} {...restProps} ref={ref} />
      </div>
    );
  }, [data]);

  useClickAway(() => {
    ref.current?.getEchartsInstance().dispatchAction({ type: 'hideTip' });
  }, refContainer);

  return <div>{PieCharts}</div>;
};

export default Pie;
