export enum RecruitDemandStatus {
  DRAFT = 'DRAFT',
  AUDITING = 'AUDITING',
  REJECT = 'REJECT',
  AUDIT_FALSE = 'AUDIT_FALSE',
  RECRUITING = 'RECRUITING',
  RECRUIT_CONFIRM = 'RECRUIT_CONFIRM',
  COMPLETED = 'COMPLETED',
  CANCELED = 'CANCELED',
  SUSPENDED = 'SUSPENDED',
  REVOKE = 'REVOKE',
}

export const RecruitDemandStatusCN: Record<RecruitDemandStatus, string> = {
  [RecruitDemandStatus.DRAFT]: '草稿',
  [RecruitDemandStatus.AUDITING]: '审核中',
  [RecruitDemandStatus.REJECT]: '驳回',
  [RecruitDemandStatus.AUDIT_FALSE]: '审核不通过',
  [RecruitDemandStatus.RECRUITING]: '招聘进行中',
  [RecruitDemandStatus.RECRUIT_CONFIRM]: '待招聘确认',
  [RecruitDemandStatus.COMPLETED]: '已完成',
  [RecruitDemandStatus.CANCELED]: '已撤销',
  [RecruitDemandStatus.SUSPENDED]: '已中止',
  [RecruitDemandStatus.REVOKE]: '撤回待提交',
};

export enum RecruitSalaryType {
  HOUR = 'HOUR',
  MONTH = 'MONTH',
}

export const RecruitSalaryTypeCN: Record<RecruitSalaryType, string> = {
  [RecruitSalaryType.HOUR]: '时薪',
  [RecruitSalaryType.MONTH]: '月薪',
};

export enum SubsidyType {
  '餐补' = '餐补',
  '全勤' = '全勤',
  '岗位工资' = '岗位工资',
}

export enum RecruitDemandAction {
  UNKOWN = 'UNKOWN',
  SUBMIT = 'SUBMIT',
  RESUBMIT = 'RESUBMIT',
  SUPERVISION = 'SUPERVISION',
  OPERATION_MANAGER = 'OPERATION_MANAGER',
  RECRUIT_CONFIRM = 'RECRUIT_CONFIRM',
  RECRUITING = 'RECRUITING',
  CANCEL = 'CANCEL',
  SUSPENDED = 'SUSPENDED',
  COMPLETED = 'COMPLETED',
  REVOKE = 'REVOKE',
}

export const RecruitDemandActionCN: Record<RecruitDemandAction, string> = {
  [RecruitDemandAction.UNKOWN]: '未知',
  [RecruitDemandAction.SUBMIT]: '提交',
  [RecruitDemandAction.RESUBMIT]: '重新提交',
  [RecruitDemandAction.SUPERVISION]: '督导',
  [RecruitDemandAction.OPERATION_MANAGER]: '运营经理',
  [RecruitDemandAction.RECRUIT_CONFIRM]: '招聘确认',
  [RecruitDemandAction.RECRUITING]: '招聘中',
  [RecruitDemandAction.CANCEL]: '撤销',
  [RecruitDemandAction.SUSPENDED]: '中止招聘',
  [RecruitDemandAction.COMPLETED]: '完成招聘',
  [RecruitDemandAction.REVOKE]: '撤回',
};

export enum RecruitDemandActionStatus {
  PASS = 'PASS',
  NO_PASS = 'NO_PASS',
  REJECT = 'REJECT',
}

export const RecruitDemandActionStatusCN: Record<RecruitDemandActionStatus, string> = {
  [RecruitDemandActionStatus.PASS]: '通过',
  [RecruitDemandActionStatus.NO_PASS]: '不通过',
  [RecruitDemandActionStatus.REJECT]: '驳回',
};
