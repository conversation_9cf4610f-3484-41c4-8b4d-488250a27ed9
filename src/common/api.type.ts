import type { SelfTaskStatusEnum } from './enum';

export interface Permission {
  id: number;
  // menuId: number;
  name: string;
  code: string;
}

export type ShopInfo = {
  shopId: string;
  shopName: string;
  province: string;
  org: string;
  id: number;
  name: string;
};

export type GroupTreeItem = {
  id: number;
  brandId: number;
  name: string;
  userCount: number;
  parentId?: number;
  shopInfos?: ShopInfo[];
};

export type OrganizationNode = TreeWith<GroupTreeItem>;

export type CssProps = Partial<{
  className: string;
  style: React.CSSProperties;
}>;

export type OssTokenProps = {
  accessKeyId: string;
  accessKeySecret: string;
  bucket: string; // 桶名
  expiration: string; // 过期
  prefix: string; // 上传文件路径
  securityToken: string;
};

export enum SortEnum {
  ASC = 'asc',
  DESC = 'desc',
}

export type SelfTaskInfoItem = {
  planId: number;
  planName: string;
  shopName: string;
  planStatus: SelfTaskStatusEnum;
  taskId?: number; // 任务组最新子任务的id
  startDate: string;
  endDate: string;
  repeatType: 'CYCLE_DAY' | 'CYCLE_WEEK' | 'CYCLE_MONTH';
  repeatTypeDesc: string;
  timeType: number;
  cornDay?: {
    startTime: string;
    day: number;
    hour: number;
  };
  cornWeek?: {
    startTime: string;
    day: number;
    hour: number;
    weeks: number[];
  };
  cornMonth?: {
    startTime: string;
    day: number;
    hour: number;
    days: number[];
  };
  taskStatus: SelfTaskStatusEnum;
  // taskStatus: TaskStatusEnum;

  buttonStatus?: number;
  finishRatio?: number;
  reviewRatio?: number;
  passRatio?: number;
  selfPlanType: string;
};
export type CheckItem = {
  patrolWorksheetId: number;
  itemId: number;
  itemSOP: string;
  style?: number;
  // hasApply?: boolean;
  score?: number;
  itemRemark?: string;
  imageUrlList?: string[];
  imageURLS: {
    url: string;
    type: 'IMG' | 'VIDEO';
    id?: string;
  }[];
  reformLimit?: number;
  necessaryFlag: 0 | 1; // 必检项
  fineFlag: 0 | 1; // 罚款项
  redLine: 0 | 1; // S项
  deductType: 'CATEGORY' | 'WORKSHEET' | 'REPORT'; // S项扣分类型，CATEGORY：检查表分类，WORKSHEET：检查表，REPORT：最终报告
  deductRatio: 80; // S项扣分比例
  standard?: string;
  fullScore: number;
  sopId?: number;
  hasApply: boolean;
  qualified: boolean | 1 | 0;
  hasFillItem: boolean;
  notQualifiedMustUpload?: number;
  qualifiedMustUpload?: number;
  scoreType: 1 | 2; // 1：得分项，2：扣分项
  copyUsers: {
    id: number;
    name: string;
  }[];
  shopRemark?: string;
  watchId: number | null;
  watchName: string | null;
  ALLOW_ALBUM: 0 | 1 | 2; // 0：跟随系统设置，1：允许，2：不允许 v2.3新增字段
  selectReasons: string[] | null;
  reasons?: string[];
  nonconformityReasons: string[];
  mustChooseNonconformityReasons: boolean;
};
export type ChecklistCategory = {
  patrolWorksheetCategoryId: number;
  patrolWorksheetCategoryName: string;
  itemCount: number;
  filledCount: number;
  children: CheckItem[];
};
export type ChecklistItem = {
  patrolWorksheetId: number;
  patrolWorksheetName: string;
  itemCount?: number;
  filledCount?: number;
  actualTotalScore: number;
  importance?: number;
  children?: ChecklistCategory[];
};
export type ChecklistData = {
  data: ChecklistItem[];
  reportId?: number;
  signType?: string;
  timeType?: number;
  repeatType: any;
  cornDay?: {
    startTime: string;
    day: number;
    hour: number;
  };
  cornWeek?: {
    startTime: string;
    day: number;
    hour: number;
    weeks: number[];
  };
  cornMonth?: {
    startTime: string;
    day: number;
    hour: number;
    days: number[];
  };
  cronType?: 'CYCLE_DAY' | 'CYCLE_WEEK' | 'CYCLE_MONTH';
  period?: string;
  shopName: string;
  taskName?: string;
  expiredTime: string;
  beginTime: string;
  summaryText?: string;
  shopSummary?: string;
  subType?: 1 | 0;
};
type CheckListsItem = {
  patrolWorksheetId: number;
  patrolWorksheetName: string;
  total: number;
  valid: number;
  importance: number;
  score: number;
  redLineCount: number;
  vetoCount: number;
  fineCount: number;
};
export type CategoryItem = {
  patrolWorksheetCategoryId: number;
  patrolWorksheetCategoryName: string;
  total: number;
  validCount: number;
  filledCount: number;
  itemCount: number;
  children: CheckItem[];
  actualScore: number;
  actualTotalScore: number;
};
export type ButtonEnums =
  | 'MODIFY_SCORE'
  | 'SUBMIT_REPORT'
  | 'RECALL_REPORT'
  | 'REJECT_REPORT'
  | 'CONFIRM_REPORT'
  | 'PROBLEM_DETAIL'
  | 'SHOP_MANAGER_WAIT_REVIEW'
  | 'REVOKE_REPORT_COMMENT'
  | 'SUBMIT_REPORT_COMMENT'
  | 'PREVIEW_SELF_REPORT'
  | 'REVOKE_SINGLESHEET_COMMENT'
  | 'NOSAME_COMMENT'
  | 'SUBMIT_PATROL_REVIEW'
  | 'NO_AUTHORITY_SUBMIT' // 无权限点评,
  | 'NO_NEED_REVIEW'
  // --- 点评时效相关 ---
  | 'REVOKE_REPORT_COMMENT_DISABLE'
  | 'SUBMIT_REPORT_COMMENT_DISABLE';
// --- 点评时效相关 ---

export type DetailListItem = {
  patrolWorksheetId: number;
  patrolWorksheetName: string;
  total: number;
  validCount: number;
  redLineCount: number;
  vetoCount: number;
  additionalCount: number; // 有2个附加项不合格 v2.1新增字段
  additionalDeductRatio: number; // 对最终报告得分扣80% v2.1新增字段
  fineCount: number;
  keyItemCount: number;
  yellowItemCount: number;
  score: number;
  actualScore: number;
  actualTotalScore: number;
  filledCount: number;
  itemCount: number;
  children: CategoryItem[];
  notApplyCount?: number;
  importance: any;
  validCategoryCount?: number;
  allCategoryCount?: number;
};
export type operateList = {
  operateType: string;
  operateUserId: number;
  operateUserName: string;
  operateUserAvatar: string;
  operateTime: string;
};
export type ReportDetailItem = {
  alreadySubmitted: 0 | 1; // 0:展示转发tips, 1:不展示转发tips
  buttonEnums: ButtonEnums[];
  shopName: string;
  shopNo: string;
  taskId: number;
  shopId: number;
  shopManagerPhone: string;
  shopManagerName: string;
  checkUserAvatar: string;
  checkUserName: string;
  checkTime: string;
  reportDate: string;
  status: 1 | 2 | 3;
  statusDesc: 'string';
  summaryText: string;
  // allCheckItemFilled: 0 | 1; // 0:有未填写项目, 1:无未填写项目
  patrolWorksheetName: string;
  confirmUserAvatar: string;
  confirmUserName: string;
  confirmTime: string;
  filledCount: number;
  itemCount: number;
  confirmUserId: number;
  count: {
    sum: {
      total: number;
      valid: number;
      score: number;
      actualScore: number;
      actualTotalScore: number;
      additionalCount: number; // S项-对最终报告扣分
      additionalDeductRatio: number; // 对最终报告扣分比例
      validCategoryCount: number;
      allCategoryCount: number;
    };
    itemList: CheckListsItem[];
  };
  details: DetailListItem[];
  operateList: operateList[];
  reformStatus: number;
  submitUserId: number;
  submitUserName: string;
  submitUserAvatar: string;
  submitTime: string;
  noReformCount: number; // 未整改项
  issuesCount: number; // 问题项
};
export type QueryIssueListParams = {
  normalTaskPlanId?: number;
  workSheetId?: number;
  reportId?: number;
  onlyMe: boolean;
  startTime?: string;
  endTime?: string;
  status?: number;
  groupId?: number;
  shopIds?: number[];
  pageNo?: number;
  pageSize?: number;
  keyword?: string;
  issueReportId?: string;
  onlyMeSubmitted?: boolean; // 看我提交的
};
export type Issue = {
  worksheetNames?: string | string[];
  worksheetName?: string;
  taskPlanName?: string;
  existProcess: boolean;
  statusEnum: 'CREATED' | 'REFORM_SUBMITTED' | 'SUPERVISOR_REJECTED' | 'COMPLETED' | 'PAST_DUE';
  id: number;
  issueId: number;
  reportId: number;
  shopId: number;
  shopName: string;
  shopNo: string;
  status: number;
  statusDesc: string;
  deadline: string;
  elapsedTime: string;
  patrolWorksheetId: number;
  patrolWorksheetName: string;
  checkListTypeName: string;
  itemId: number;
  itemName: string;
  itemRemark: string;
  selectReasons?: string[];
  itemImages: {
    url: string;
    type: 'IMG' | 'VIDEO';
    id: string;
  }[];
  submitedUserId: number;
  submitedUserName: string;
  submitedUserAvatar: string;
  sumbimitedTime: string;
  redLine: 0 | 1;
  deductType: 'CATEGORY' | 'WORKSHEET' | 'REPORT';
  deductRatio: number;
  necessaryFlag: 0 | 1;
  fineFlag: 0 | 1;
  watchId: number;
  watchName: string;
  belongsMe: boolean;
  children?: Comment[];
  buttonEnums: Array<
    | 'AUDIT_PASS'
    | 'FOLLOW_PROBLEM'
    | 'REJECT_PROBLEM'
    | 'SELF_AUDIT_PASS'
    | 'SELF_FOLLOW_PROBLEM'
    | 'SELF_REJECT_PROBLEM'
  >;
  copyUsers?: Nullable<
    {
      id: number;
      name: string;
    }[]
  >;
  reformMustUpload: number;
  ALLOW_ALBUM: number;
};
export type SelfReportIssuePageResult = {
  copyRecipientNames: string;
  createAt: string;
  expirationAt: string;
  createUserName: string;
  createUserAvatar: string;
  id: number;
  issue_images: string[];
  rectificationPeriod: string;
  shopName: string;
  status: 'CREATED' | 'REFORM_SUBMITTED' | 'SUPERVISOR_REJECTED' | 'COMPLETED' | 'PAST_DUE';
  suggestion: string;
  workSheetItemName: string;
  workSheetItemLabels: ('RED_LINE' | 'NECESSARY' | 'YELLOW' | 'KEY' | 'PENALTY')[];
  workSheetCategoryName: string;
  worksheetName: string;
};

export interface SelfReportIssuePage {
  pages: number;
  result: SelfReportIssuePageResult[];
  total: number;
}
export interface SelfReportIssueList {
  selfReportIssueMiniProgramDTOS: SelfReportIssuePage['result'];
}
type roleListType = {
  roleCode: 'PATROL_SUPER' | 'PATROL_SUPER_SUPERVISION' | 'PATROL_SUPERVISION' | 'SHOP_FRANCHISEE';
  roleId: number;
  roleName: string;
};
export interface yytUserInfo {
  userId?: number;
  userName?: string;
  phone?: string;
  avatar?: string;
  shopManager?: boolean;
  privilegeVoList?: any[];
  shopList?: {
    shopId: number;
    shopName: string;
    shopNo: string;
  }[];
  currentShop?: Nullable<{
    shopId: number;
    shopName: string;
    shopNo: string;
  }>;
  roleList?: roleListType[];
  currentRoleType?: string;
  corpRoleList?: roleListType[]; // 公司角色list
  shopRoleList?: roleListType[]; // 门店角色list
  token?: string;
}
export type TUploadParams = {
  bucket: string;
  key: string;
  originName: string;
  remark?: string;
  userId?: number;
  watermark?: string;
};
export type TUploadGalleryParams = TUploadParams & {
  taskId?: number;
  waterMarkContent?: string;
};
export enum TWatermark {
  none = 'none',
  default = 'default',
  company = 'company',
  extra = 'extra',
}
export type TUpload = {
  contentType: string;
  fileType: string;
  id: string;
  name: string;
  url: string;
};
