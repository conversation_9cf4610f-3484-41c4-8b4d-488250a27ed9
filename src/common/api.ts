import { get, post } from '@src/api';
import type { OssTokenProps, TUpload, TUploadParams, TWatermark } from './api.type';
import type { ShopTypeEnum } from './enum';
// 用户权限
export const getPermissions = async (): Promise<string[]> => {
  const data = await post(`/cc-api/user/getUrlsByBizType`, {
    data: { bizType: 14, clientType: 3 },
  });
  return data;
};
export const getRoleIdList = async () => {
  const data = await post(`/cc-api/user/app/info`, {});
  return data;
};
// 查询中台组织
export const getZTGroupTree = () =>
  post('/cc-api/gzt/shop/getGroupShopTree', {
    data: {
      groupType: 2,
      privilegeCode: 1,
      needShop: false,
    },
  });

// 根据组织查询有权限的门店
export const getFsGroupShop = (data: { groupId: string }) =>
  get<{ shopName: string; shopId: number; shopCode: string; type: ShopTypeEnum }[]>(
    '/api/rest/common/listShopByRootIdWithPermission',
    { params: data },
  );
export const getZTGroupShop = (data: { fightGroupId?: number }) =>
  post<{ shopName: string; shopId: number; shopCode: string; type: ShopTypeEnum }[]>('/cc-api/gzt/shop/getShopList', {
    data: { ...data, groupType: 2, privilegeCode: 1 },
  });

export const getUser = (token?: string) =>
  get<{
    mobile?: string;
    shUserId?: number;
    brandBaseInfos: { brandId: number; brandName: string }[];
  }>('/cc-api/user/getUserInfo', {
    headers: {
      'user-token': token,
    },
  });

/**
 * 获取OSS上传临时凭证
 */

export type FileBusinessTypeMap =
  | 'SELF'
  | 'PATROL'
  | 'ROUTINE_TASK'
  | 'USER_PHOTO'
  | 'USER_PHOTO_CHECK'
  | 'SOP'
  | 'DISINFECTION'
  | 'ANNOUNCEMENT'
  | 'TMP';
export const getOssToken = (fileBusinessType?: FileBusinessTypeMap) =>
  get<OssTokenProps>(`/om-api/common/sts${fileBusinessType ? `?fileBusinessType=${fileBusinessType}` : ''}`);

// 获取 OSS 文件预览地址
export const getOssFileUrl = (key: string) => post('/om-api/common/oss/getFileUrl', { data: { key } });

export const getBrands = () => get('/om-api/common/market/dashboard/brand-name/list', {});
/**
 * 获取营运通OSS上传临时凭证
 */
// export const getYytOssToken = () => get<OssTokenProps>('/yy-api/sts', {});
export const getYytOssToken = (fileBusinessType?: FileBusinessTypeMap) =>
  get<OssTokenProps>(`/om-api/common/sts${fileBusinessType ? `?fileBusinessType=${fileBusinessType}` : ''}`, {
    headers: {
      'Role-Type': 'CORP',
    },
  });

export const getYyGroupTreeAll = () =>
  get('/yy-api/resource/tree', {
    headers: {
      'Role-Type': 'CORP',
    },
  });
export const uploadImage = async (data: TUploadParams, watermark: TWatermark) => {
  const url = {
    none: '/om-api/common/file/upload/image',
    default: '/om-api/common/file/upload/image/watermark',
    company: '/om-api/common/file/upload/image/watermark/company',
    extra: `/om-api/common/file/upload/image/watermarkOfShop`,
  }[watermark];

  return post<TUpload>(url, { data });
};

export const uploadVideo = async (data: TUploadParams) =>
  post<TUpload>('/om-api/common/file/upload/video/watermark', { data });
