export enum ShopTypeEnum {
  '大加盟' = 1,
  '小加盟',
}
export enum SelfTaskStatusEnum {
  /**
   * @description 索引0
   */
  nothing,
  /**
   * @description 待开始1
   */
  pending,

  /**
   * @description 进行中2
   */
  processing,

  /**
   * @description 已完成3
   */
  completed,

  /**
   * @description 已过期4
   */
  expired,

  /**
   * @description 已取消5
   */
  canceled,
}

export enum dayTypeEnum {
  '一' = 1,
  '二',
  '三',
  '四',
  '五',
  '六',
  '日',
}
