enum AuthorityEnum {
  作战小组数据 = 'dataBoard:boards',
  AuditPage = 'dataBoard:boards:audit',
  AuditShopBtn = 'dataBoard:boards:audit:shopBtn',
  AuditGroupBtn = 'dataBoard:boards:audit:groupBtn',
  红线门店占比 = 'dataBoard:boards:audit:data:redLine',
  redLineHq = 'dataBoard:boards:audit:data:redLine:hq',
  redLineOnLine = 'dataBoard:boards:audit:data:redLine:onLine',
  redLineOffLine = 'dataBoard:boards:audit:data:redLine:offLine',
  redLineOther = 'dataBoard:boards:audit:data:redLine:other',
  门店通过率 = 'dataBoard:boards:audit:data:unPassPatrol',
  unPassPatrolHq = 'dataBoard:boards:audit:data:unPassPatrol:hq',
  unPassPatrolOnLine = 'dataBoard:boards:audit:data:unPassPatrol:onLine',
  unPassPatrolOffLine = 'dataBoard:boards:audit:data:unPassPatrol:offLine',
  unPassPatrolOther = 'dataBoard:boards:audit:data:unPassPatrol:other',
  门店整改完成率 = 'dataBoard:boards:audit:data:unCompleteRectify',
  unCompleteRectifyHq = 'dataBoard:boards:audit:data:unCompleteRectify:hq',
  unCompleteRectifyOnLine = 'dataBoard:boards:audit:data:unCompleteRectify:onLine',
  unCompleteRectifyOffLine = 'dataBoard:boards:audit:data:unCompleteRectify:offLine',
  unCompleteRectifyOther = 'dataBoard:boards:audit:data:unCompleteRectify:other',
  自检合格率 = 'dataBoard:boards:audit:data:unPassSelf',
  unPassSelfDaily = 'dataBoard:boards:audit:data:unPassSelf:daily',
  unPassSelfTank = 'dataBoard:boards:audit:data:unPassSelf:tank',
  unPassSelfCoke = 'dataBoard:boards:audit:data:unPassSelf:coke',
  unPassSelfIce = 'dataBoard:boards:audit:data:unPassSelf:ice',
  unPassSelfOil = 'dataBoard:boards:audit:data:unPassSelf:oil',
  unPassSelfClean = 'dataBoard:boards:audit:data:unPassSelf:clean',
  自检完成率 = 'dataBoard:boards:audit:data:unCompleteSelf',
  unCompleteSelfDaily = 'dataBoard:boards:audit:data:unCompleteSelf:daily',
  unCompleteSelfTank = 'dataBoard:boards:audit:data:unCompleteSelf:tank',
  unCompleteSelfCoke = 'dataBoard:boards:audit:data:unCompleteSelf:coke',
  unCompleteSelfIce = 'dataBoard:boards:audit:data:unCompleteSelf:ice',
  unCompleteSelfOil = 'dataBoard:boards:audit:data:unCompleteSelf:oil',
  unCompleteSelfClean = 'dataBoard:boards:audit:data:unCompleteSelf:clean',
  督导巡检完成率 = 'dataBoard:boards:audit:data:supervision',
  foodSafe = 'dataBoard:boards:audit:data:supervision:foodSafe',
  bySupervision = 'dataBoard:boards:audit:data:supervision:bySupervision',
  HrPage = 'dataBoard:boards:hr',
  HrShopBtn = 'dataBoard:boards:hr:shopBtn',
  HrGroupBtn = 'dataBoard:boards:hr:groupBtn',
  排班准确率 = 'dataBoard:boards:hr:data:scheduling',
  编制达成率 = 'dataBoard:boards:hr:data:organization',
  招聘达成率 = 'dataBoard:boards:hr:data:recruit',
  离职率 = 'dataBoard:boards:hr:data:depart',
  AchievementPage = 'dataBoard:boards:achievement',
  AchievementShopBtn = 'dataBoard:boards:achievement:shopBtn',
  AchievementGroupBtn = 'dataBoard:boards:achievement:groupBtn',
  预计收入 = 'dataBoard:boards:achievement:data:estimatedRevenue',
  优惠金额 = 'dataBoard:boards:achievement:data:estimatedRevenue:discountAmount',
  退款金额 = 'dataBoard:boards:achievement:data:estimatedRevenue:refundAmount',
  日均收入 = 'dataBoard:boards:achievement:data:estimatedRevenue:averageDailyIncome',
  订单场景 = 'dataBoard:boards:achievement:data:estimatedRevenue:orderScene',
  各场景日均收入 = 'dataBoard:boards:achievement:data:estimatedRevenue:orderSceneAverageDailyIncome',
  订单来源 = 'dataBoard:boards:achievement:data:estimatedRevenue:orderSource',
  各支付方式预计收入 = 'dataBoard:boards:achievement:data:estimatedRevenue:orderPaymentMode',
  实付收入 = 'dataBoard:boards:achievement:data:estimatedRevenue:actualPaymentIncome',
  日均实付 = 'dataBoard:boards:achievement:data:estimatedRevenue:dailyActualPayment',
  // 其他支出 = 'dataBoard:boards:achievement:data:estimatedRevenue:otherExpenses',
  TC = 'dataBoard:boards:achievement:data:tc',
  优惠订单 = 'dataBoard:boards:achievement:data:tc:discount',
  退款订单 = 'dataBoard:boards:achievement:data:tc:refund',
  日均单数 = 'dataBoard:boards:achievement:data:tc:averageDaily',
  TC各订单占比 = 'dataBoard:boards:achievement:data:tc:orderScene',
  各场景日均订单数 = 'dataBoard:boards:achievement:data:tc:orderSceneAverageDailyIncome',
  TC订单来源 = 'dataBoard:boards:achievement:data:tc:orderSource',
  各支付方式订单量 = 'dataBoard:boards:achievement:data:tc:orderPaymentMode',
  tc实付收入 = 'dataBoard:boards:achievement:data:tc:actualPaymentIncome',
  tc日均实付 = 'dataBoard:boards:achievement:data:tc:dailyActualPayment',

  AC = 'dataBoard:boards:achievement:data:ac',
  // 日均AC = 'dataBoard:boards:achievement:data:ac:dailyAc',
  各场景AC = 'dataBoard:boards:achievement:data:ac:orderScene',
  各订单来源AC = 'dataBoard:boards:achievement:data:ac:orderSource',
  各支付方式AC = 'dataBoard:boards:achievement:data:ac:orderPaymentMode',
  ac实付收入 = 'dataBoard:boards:achievement:data:ac:actualPaymentIncome',
  营业额预估准确率 = 'dataBoard:boards:achievement:data:turnover',
  物料采购差异率 = 'dataBoard:boards:achievement:data:materialPurchase',
  SatisfactionPage = 'dataBoard:boards:satisfaction',
  SatisfactionShopBtn = 'dataBoard:boards:satisfaction:shopBtn',
  SatisfactionGroupBtn = 'dataBoard:boards:satisfaction:groupBtn',
  体验星级得分 = 'dataBoard:boards:satisfaction:data:star',
  全渠道万次投诉率 = 'dataBoard:boards:satisfaction:data:inout',
  差评回复率 = 'dataBoard:boards:satisfaction:data:reply',
  平均出餐时长 = 'dataBoard:boards:satisfaction:data:avgMeal',
  出餐时长超时率 = 'dataBoard:boards:satisfaction:data:timeoutMeal',
  marketPage = 'dataBoard:boards:market',
  任务管理 = 'dataBoard:tasks',
  督导创建任务 = 'dataBoard:tasks:create:crop',
  店长创建任务 = 'dataBoard:tasks:create:shop',
  督导创建常规任务 = 'dataBoard:tasks:create:normal:crop',
  店长创建常规任务 = 'dataBoard:tasks:create:normal:shop',
  创建巡检计划 = 'dataBoard:tasks:create:patrol:plan',
  督导计划外到店巡检任务 = 'dataBoard:tasks:create:patrol:unplanned:crop',
  督导稽核任务 = 'dataBoard:tasks:create:patrol:food:crop',
  我处理的 = 'dataBoard:tasks:categorize:principal',
  PrincipalNormal = 'dataBoard:tasks:categorize:principal:normal',
  PrincipalPatrol = 'dataBoard:tasks:categorize:principal:patrol',
  PrincipalStructKill = 'dataBoard:tasks:categorize:principal:structKill',
  PrincipalSelfPatrol = 'dataBoard:tasks:categorize:principal:selfPatrol',
  我派发的 = 'dataBoard:tasks:categorize:created',
  CreatedNormal = 'dataBoard:tasks:categorize:created:normal',
  CreatedPatrol = 'dataBoard:tasks:categorize:created:patrol',
  CreatedSelfPatrol = 'dataBoard:tasks:categorize:created:selfPatrol',
  抄送我的 = 'dataBoard:tasks:categorize:recipients',
  RecipientsNormal = 'dataBoard:tasks:categorize:recipients:normal',
  RecipientsPatrol = 'dataBoard:tasks:categorize:recipients:patrol',
  RecipientsSelfPatrol = 'dataBoard:tasks:categorize:recipients:selfPatrol',
  我管辖的 = 'dataBoard:tasks:categorize:under',
  UnderNormal = 'dataBoard:tasks:categorize:under:normal',
  UnderPatrol = 'dataBoard:tasks:categorize:under:patrol',
  UnderStructKill = 'dataBoard:tasks:categorize:under:structKill',
  UnderSelfPatrol = 'dataBoard:tasks:categorize:under:selfPatrol',
  门店实时订单量 = 'dataBoard:boards:achievement:data:realTime',
  门店实时预计收入 = 'dataBoard:boards:achievement:data:estimatedRevenue:realTime',

  YYTPatrolReview = 'report:routineInspection:report:comment', // 营运通巡检点评权限

  营运通数据看板 = 'dataBoard:yytBoards',
  到家加盟业主看板 = 'dataBoard:arriveBoards',
  自检报告 = 'app$report$selftask',
  巡检报告 = 'app$report$patrol',
  消杀报告 = 'app$report$disinfection',
  报告中心 = 'selftask$reportcenter$view',
  申诉 = 'selftask$reportcenter$view$complaints',
  申诉_督导端 = 'selftask$reportcenter$view$complaints$supervisor',
  食安稽核到店辅导点评 = 'report:food:safety',

  //
  到店巡检 = '$app$planManagement$create$normal',
  视频云巡检 = '$app$planManagement$create$video',
  食安线下稽核 = '$app$planManagement$create$FOOD_SAFETY_NORMAL',

  需求提报 = '$app$recruit:demand',
  面试管理 = '$app$recruit:interview',

  紧急消杀 = 'dataBoard:tasks:emergencyStructKill',

  紧急消杀_驳回 = '$app$disinfection$reject',
}

/** 督导数据看板权限 */
export enum SupervisorDataViewBoardEnum {
  自检实时情况 = '$app$supervisor$data$view$board$self$check$real$time$situation',
  '自检实时情况（新）' = '$app$supervisor$data$view$board$self$check$real$time$situation$tactics',
  自检实时情况_自检看板 = '$app$supervisor$data$view$board$self$check$real$time$situation$self$check$view$board',
  自检实时情况_到店巡检看板 = '$app$supervisor$data$view$board$self$check$real$time$situation$reach$shop$patrol$view$board',
  自检实时情况_视频云巡检看板 = '$app$supervisor$data$view$board$self$check$real$time$situation$video$cloud$patrol$view$board',
  自检实时情况_食安线下稽核看板 = '$app$supervisor$data$view$board$self$check$real$time$situation$food$safety$offline$auditing$board',
  自检实时情况_食安线上稽核看板 = '$app$supervisor$data$view$board$self$check$real$time$situation$food$safety$online$auditing$board',
  自检实时情况_诊断巡检看板 = '$app$supervisor$data$view$board$self$check$real$time$situation$diagnosis$patrol$view$board',
  // -----------------
  智慧运营看板 = '$app$supervisor$data$view$board$wisdom$operator$view$board',
  门店数据看板 = '$app$supervisor$data$view$board$store$data$view$board',
  // -----------------
  作战小组数据看板 = 'dataBoard:boards',
  作战小组数据看板_业绩看板 = 'dataBoard:boards:achievement',
  作战小组数据看板_满意度数据看板 = 'dataBoard:boards:satisfaction',
  作战小组数据看板_食安数据看板 = 'dataBoard:boards:audit',
  作战小组数据看板_人力数据看板 = 'dataBoard:boards:hr',
  作战小组数据看板_市场分析看板 = 'dataBoard:boards:market',

  数据实时情况 = '$app$supervisor$data$view$board$data$real$time$situation',
  数据实时情况_巡检不合格情况 = '$app$supervisor$data$view$board$data$real$time$situation$patrol$unqualified',
}

/** 门店数据看板权限 */
export enum ShopDataViewBoardEnum {
  自检实时情况 = '$app$shop$data$view$board$self$check$real$time$situation',
  '自检实时情况（新）' = '$app$shop$data$view$board$self$check$real$time$situation$tactics',
  自检实时情况_自检看板 = '$app$shop$data$view$board$self$check$real$time$situation$self$check$view$board',
  自检实时情况_到店巡检看板 = '$app$shop$data$view$board$self$check$real$time$situation$reach$shop$patrol$view$board',
  自检实时情况_视频云巡检看板 = '$app$shop$data$view$board$self$check$real$time$situation$video$cloud$patrol$view$board',
  自检实时情况_食安线下稽核看板 = '$app$shop$data$view$board$self$check$real$time$situation$food$safety$offline$auditing$board',
  自检实时情况_食安线上稽核看板 = '$app$shop$data$view$board$self$check$real$time$situation$food$safety$online$auditing$board',
  自检实时情况_诊断巡检看板 = '$app$shop$data$view$board$self$check$real$time$situation$$diagnosis$patrol$view$board',
  // -----------------
  智慧运营看板 = '$app$shop$data$view$board$wisdom$operator$view$board',
  // -----------------
  作战小组数据看板 = '$app$shop$data$view$board$fight$team$group$data$view$board',
  作战小组数据看板_业绩看板 = '$app$shop$data$view$board$fight$team$group$data$view$board$performance$view$board',
  作战小组数据看板_满意度数据看板 = '$app$shop$data$view$board$fight$team$group$data$view$board$degree$of$satisfaction$board',
  作战小组数据看板_食安数据看板 = '$app$shop$data$view$board$fight$team$group$data$view$board$food$safety$data$view$board',
  作战小组数据看板_人力数据看板 = '$app$shop$data$view$board$fight$team$group$data$view$board$manpower$data$view$board',
  作战小组数据看板_市场分析看板 = '$app$shop$data$view$board$fight$team$group$data$view$board$market$analysis$view$board',
  到家加盟业主看板 = 'dataBoard:arriveBoards',

  数据实时情况 = '$app$shop$data$view$board$data$real$time$situation',
  数据实时情况_巡检不合格情况 = '$app$shop$data$view$board$data$real$time$situation$patrol$unqualified',
}

export default AuthorityEnum;
