// 求职者面试状态
export enum RecruitCandidateStatus {
  WAIT_APPOINTMENT = 'WAIT_APPOINTMENT',
  WART_INTERVIEW = 'WART_INTERVIEW',
  WAIT_ENTRY = 'WAIT_ENTRY',
  ENTRYED = 'ENTRYED',
  INTERVIEW_FAILED = 'INTERVIEW_FAILED',
  APPOINTMENT_CANCELED = 'APPOINTMENT_CANCELED',
  ENTRY_CANCELED = 'ENTRY_CANCELED',
}

export const RecruitCandidateStatusCN: Record<RecruitCandidateStatus, string> = {
  [RecruitCandidateStatus.WAIT_APPOINTMENT]: '待约面',
  [RecruitCandidateStatus.WART_INTERVIEW]: '待面试',
  [RecruitCandidateStatus.WAIT_ENTRY]: '待入职',
  [RecruitCandidateStatus.ENTRYED]: '已入职',
  [RecruitCandidateStatus.INTERVIEW_FAILED]: '面试不通过',
  [RecruitCandidateStatus.APPOINTMENT_CANCELED]: '约面取消',
  [RecruitCandidateStatus.ENTRY_CANCELED]: ' 入职取消',
};

// 岗位类型
export enum JobType {
  DIAN_YUAN = 'DIAN_YUAN',
  DIAN_ZHU = 'DIAN_ZHU',
  XIAO_SHI_MEI = 'XIAO_SHI_MEI',
  YOU_SHI = 'YOU_SHI',
}

export const JobTypeCN: Record<JobType, string> = {
  [JobType.DIAN_YUAN]: '店员',
  [JobType.DIAN_ZHU]: '店助',
  [JobType.XIAO_SHI_MEI]: '小狮妹',
  [JobType.YOU_SHI]: '幼狮',
};

export enum GenderType {
  Unlimited = -1,
  Female = 0,
  Male = 1,
}

export const GenderTypeCN: Record<GenderType, string> = {
  [GenderType.Unlimited]: '不限',
  [GenderType.Female]: '女',
  [GenderType.Male]: '男',
};
