import axios from 'axios';
import { customAlphabet } from 'nanoid/non-secure';

const nanoid = customAlphabet('abcdefghijklmnopqrstuvwxyz0123456789', 10);

import { getEnv, getRoleName, getUserId } from '@src/utils/tokenUtils';

const env = getEnv();

function getReportUrlByEnv() {
  switch (env) {
    case 'test':
      return 'https://mdlog-test.tastientech.com';
    case 'stage':
      return 'https://mdlog-stage.tastientech.com';
    default:
      return 'https://mdlog.tastientech.com';
  }
}

const reportInstance = axios.create({
  baseURL: getReportUrlByEnv(),
  timeout: 10000,
});

const REQUEST_ID = nanoid();

export type ReportBody = {
  /** 系统 */
  system: string;
  /** 上报类型 page/ability */
  type: 'page' | 'ability';
  /** 用户id */
  userId: number;
  /** 角色名称 */
  roleName: string;
  /** 页面名称 */
  page: string;
  /** 跳转过来的页面 */
  prePage: string;
  /** 功能按钮 */
  abilityButton: string;
  /** 请求时间 */
  requestTime: string;
  /** 请求id  每次打开app生成一个唯一的就行 */
  requestIdentifying: string;
  /** 附带消息 */
  ext: string;
  /** 内部使用 */
  keywordId: number;
};
console.log('REQUEST_ID', REQUEST_ID);

export const report = (payload: Partial<ReportBody>) => {
  const userId = getUserId();
  const roleName = getRoleName();

  const data = {
    roleName: roleName,
    system: '塔塔运营通',
    userId: userId || 1234,
    requestTime: new Date(),
    requestIdentifying: REQUEST_ID,
    ...payload,
  };

  return reportInstance.request({
    url: '/report',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: {
      ...data,
      prePage: data.prePage,
      page: data.page,
    },
  });
};
