import type { FC, ReactNode } from 'react';
import { createContext, useContext, useMemo } from 'react';
import Statistics from '@src/pages/statistics';
import { ConfigKey, SystemConfigItem, useSystemConfig } from './api';

type TShopConfig = {
  originConfig: SystemConfigItem[];
  config: { [key in string]: string };
  refetchConfig?: () => void;
};

const ShopConfigContext = createContext<TShopConfig>({
  originConfig: [],
  config: {},
});

type TProps = {
  children: ReactNode;
};

export const ShopConfigProvider: FC<TProps> = ({ children }) => {
  // 紧急需求，暂时加一个设备统计的页面不校验token，后续会删除
  if (window.location.href.indexOf('/statistics') > -1) return <Statistics />;
  const { data, refetch } = useSystemConfig(['SystemConfig']);

  const config = useMemo(() => {
    let obj: { [key in ConfigKey]?: string } = {};
    data?.forEach((item) => {
      obj[item.key as ConfigKey] = item.value;
    });
    return obj;
  }, [data]);

  return (
    <ShopConfigContext.Provider
      value={{
        originConfig: data || [],
        config,
        refetchConfig: refetch,
      }}
    >
      {children}
    </ShopConfigContext.Provider>
  );
};

export function useShopConfig() {
  return useContext(ShopConfigContext);
}
