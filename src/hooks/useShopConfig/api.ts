import { get } from '@src/api';
import { ClientError } from '@src/api/react-query';
import { getToken } from '@src/utils/tokenUtils';
import { QueryKey, useQuery } from '@tanstack/react-query';

export type ConfigKey =
  | 'ALLOW_ALBUM'
  | 'CHECK_IN_DISTANCE'
  | 'REPORT_AUTO_CONFIRM_DAYS'
  | 'MUST_CHECK_IN'
  | 'ISSUE_FIX_DEFAULT_DAYS'
  | 'REPORT_SHOW_PASS_RATE'
  | 'REPORT_VIDEO_TIME_LIMIT'
  | 'REPORT_VIDEO_TIME_LIMITCheck'
  | 'IN_STORE_INSPECTION_NOT_QUALIFIED_ISSUE_ITEM_DEFAULT_DEADLINE'
  | 'IN_STORE_INSPECTION_RECTIFY_REJECT_AFTER_DEADLINE'
  | 'REPORT_VIDEO_NOT_QUALIFIED_ISSUE_ITEM_DEFAULT_RECTIFY_DEADLINE'
  | 'REPORT_VIDEO_RECTIFY_REJECT_RECTIFY_AFTER_DEADLINE'
  | 'SELF_INSPECTION_RECTIFICATION_PERIOD'
  | 'ISSUE_FIX_DEFAULT_DAYS'
  | 'SELF_INSPECTION_RECTIFY_REJECT_AFTER_DEADLINE';

export interface SystemConfigItem {
  key: string;
  value: any;
}
/**
 * 全局系统配置
 */
export function useSystemConfig(queryKey: QueryKey) {
  return useQuery<SystemConfigItem[], ClientError>({
    queryKey,
    queryFn: () => get<SystemConfigItem[]>('/om-api/common/system/config/all', {}),
    refetchOnWindowFocus: false,
    enabled: !!getToken(),
  });
}
