import { DependencyList, useMemo, useRef, useState, useLayoutEffect } from 'react';
import { isEmpty } from 'ramda';
import { useUpdate } from 'ahooks';

type PagingParams = { pageNo: number; pageSize: number };

type Fn<T> = (pagingParams: PagingParams) => Promise<T>;

type Options<T, R> = {
  /**
   * @default 10
   */
  pageSize?: number;
  computeOver?: (result: T, pagingParams: PagingParams) => boolean;
  postResult?: (result: T) => R;
  resolver?: (list: R[], result: T) => R[];
};

const useScrollFetch = <T, R = T>(
  fn: Fn<T>,
  deps?: DependencyList | null,
  options?: Options<T, R>,
) => {
  const forceUpdate = useUpdate();
  const [list, setList] = useState<R[]>([]);
  const infoRef = useRef({
    fn,
    options,
    runCount: 0,
    loading: false,
    pageNo: 1,
    over: false,
  });
  infoRef.current.fn = fn;
  infoRef.current.options = options;

  const methods = useMemo(() => {
    const _fn = async () => {
      const {
        pageSize,
        computeOver,
        postResult,
        resolver: _resolver,
      } = {
        pageSize: 10,
        computeOver: (result, { pageSize }) => (result as unknown as any[]).length < pageSize,
        ...infoRef.current.options,
      } as Options<T, R>;

      const resolver =
        _resolver ||
        ((list, result) => {
          const addItem = postResult ? postResult(result) : (result as unknown as R);
          return isEmpty(addItem) ? list : [...list, addItem];
        });

      const count = ++infoRef.current.runCount;
      infoRef.current.loading = true;
      forceUpdate();

      try {
        const pagingParams = {
          pageNo: infoRef.current.pageNo++,
          pageSize: pageSize!,
        };
        const result = await infoRef.current.fn(pagingParams);

        if (count !== infoRef.current.runCount) return;

        infoRef.current.over = computeOver!(result, pagingParams);
        setList((list) => resolver!(list, result));
      } catch (e) {
        if (count !== infoRef.current.runCount) return;
      }

      infoRef.current.loading = false;
      forceUpdate();
    };

    return {
      fetchNext: async () => {
        if (infoRef.current.over || infoRef.current.loading) return;

        return _fn();
      },
      reload: () => {
        infoRef.current.pageNo = 1;
        setList([]);

        return _fn();
      },
      cancel: () => ++infoRef.current.runCount,
    };
  }, []);

  useLayoutEffect(() => {
    if (deps === null) return;
    methods.reload();
  }, [...(deps || [])]);

  return {
    list,
    setList,
    loading: infoRef.current.loading,
    over: infoRef.current.over,
    ...methods,
  };
};

export default useScrollFetch;
