import { getBrands } from '@src/common/api';
import { useRequest } from 'ahooks';

let cache: any;

const useBrands = () => {
  const { data, loading } = useRequest(async () => {
    if (!cache) {
      cache = getBrands().catch((e) => {
        setTimeout(() => {
          cache = null;
        });
        throw e;
      });
    }
    return cache;
  });

  return { loading, data };
};

export default useBrands;
