import { getYyGroupTreeAll } from '@src/common/api';
import { OrganizationNode } from '@src/common/api.type';
import { useRequest } from 'ahooks';
import memoizeOne from 'memoize-one';
import { useMemo } from 'react';

let cache: any;

export type ArchitectureNode = TreeWith<
  Omit<OrganizationNode, 'children' | 'shops'> & {
    key: string;
    title: string;
  }
>;

export type ArchitectureDataNode = {
  type: 'group' | 'shop';
  value: string;
  key: string;
  title: string;
  children?: ArchitectureDataNode[];
};

const dig: (list: ArchitectureNode[]) => ArchitectureDataNode[] = memoizeOne((list) => {
  return list
    .filter((v: any) => v.type === 'ORGANIZATION')
    .map(({ type, treeSet, id, name }: any) => {
      return {
        title: name,
        type,
        key: `${id}`,
        value: `${id}`,
        ...(treeSet.length && { children: dig(treeSet) }),
      };
    }) as ArchitectureDataNode[];
});

// 组织数filter children大于1的树
// const singleDig = (obj: any) => {
//   if (obj.children && obj.children.length > 1) {
//     return [obj];
//   } else if (obj.children && obj.children.length === 1) {
//     return dig(obj.children[0]);
//   } else {
//     return [obj];
//   }
// };

const useGroupTreeYyt = () => {
  const { data, loading } = useRequest(async () => {
    if (!cache) {
      cache = getYyGroupTreeAll().catch((e) => {
        setTimeout(() => {
          cache = null;
        });
        throw e;
      });
    }
    return cache;
  });

  const { tagIdObj, shopIdObj } = useMemo(() => {
    if (!data) return { tagIdObj: {}, shopIdObj: {} };
    const tagIdObj = {} as { [key: string]: ArchitectureNode & { treeSet: any[]; type: string } };
    const shopIdObj = {} as { [key: string]: any };
    const dig = (tree?: Array<ArchitectureNode & { treeSet: any[]; type: string }>) =>
      tree &&
      tree.forEach((item) => {
        dig(item?.treeSet || []);
        if (item.type === 'ORGANIZATION') {
          tagIdObj[`${item.id}`] = item;
        } else {
          shopIdObj[`${item.id}`] = item;
        }
      });

    dig([data]!);

    return { tagIdObj, shopIdObj };
  }, [data]);

  //根据组织id获取门店列表
  const getShopList = (groupId: number | string) => {
    return new Promise<any[]>((resolve) => {
      let list: ArchitectureNode[] = [];
      const dig = (arr: any[]) => {
        const shopList = arr.filter((v) => v.type === 'SHOP');
        const groupList = arr.filter((v) => v.type === 'ORGANIZATION');
        list = [...list, ...shopList];
        groupList.length && groupList.forEach((ele) => dig(ele.treeSet));
      };
      const obj = tagIdObj[groupId];
      if (obj) {
        dig(obj.treeSet);
      }
      resolve(list);
    });
  };

  const { treeData, pid } = useMemo(() => {
    const treeData = data ? dig([data]) : [];
    const pid = treeData.length ? treeData[0].value : '';
    return { treeData, pid };
  }, [data]);

  const response = {
    pid,
    treeData,
    tagIdObj,
    shopIdObj,
    getShopList,
  };

  return { loading, data, ...response };
};

export default useGroupTreeYyt;
