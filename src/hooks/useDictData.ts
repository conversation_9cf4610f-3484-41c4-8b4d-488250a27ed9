import { post } from '@src/api';
import { useRequest } from 'ahooks';
import { useMemo } from 'react';

let cache: any;

/**
 * 数据字典，减少请求次数
 * @param postData
 * @returns
 */
const useDictData = (postData?: (v: any) => void) => {
  const webEntry = localStorage.getItem('webEntry') || '';
  const { data, loading } = useRequest(async () => {
    //飞书获取的token无法直接请求中台，兼容一下
    if (webEntry === 'feishu') {
      return [
        {
          dictCode: 'shopType',
          dictName: '门店类型',
          remark: null,
          enable: 1,
          relList: [
            {
              id: 37,
              dictId: 39,
              enumCode: '1',
              enumValue: '加盟T3',
              remark: '由总部统一管理',
              enable: 1,
              createUserName: 'im测试666',
              createUserId: 1347,
              createTime: 1687920853000,
              updateTime: 1703126721000,
              updateUserId: null,
            },
            {
              id: 38,
              dictId: 39,
              enumCode: '2',
              enumValue: '加盟M1',
              remark: '由商户各自管理',
              enable: 1,
              createUserName: 'im测试666',
              createUserId: 1347,
              createTime: 1687919675000,
              updateTime: 1703126715000,
              updateUserId: null,
            },
          ],
        },
      ];
    }
    if (!cache) {
      const response = await post('/cc-api/principalSystemDict/dict/list', {
        useOriginResponse: true,
        data: {},
      }).catch((e) => {
        setTimeout(() => {
          cache = null;
        });
        throw e;
      });
      cache = response.data?.result || null;
    }
    return cache;
  });

  const ProductData = useMemo(() => (postData ? postData(data!) : data!), [data]);

  return { loading, data: ProductData };
};

export default useDictData;
