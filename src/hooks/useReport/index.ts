import { report } from '@src/common/report';
import { routerMap } from '@src/router';
import { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
// import { routerMap } from './core';

/**
 * 路由更新的 hook 暂时业务不需要，后续需要使用再放开
 */
export function useRouteChange() {
  const location = useLocation();
  const [prePage, setPrePage] = useState<string>('');
  const [page, setPage] = useState(location.pathname);

  // console.log('routerMap', routerMap);
  useEffect(() => {
    setPrePage(page);
    setPage(location.pathname);

    console.log('prePage', page);
    console.log('nowPage', location.pathname);
    console.log('routerMap', routerMap);

    console.log('✨ 切换页面上报', {
      type: 'page',
      prePage: routerMap[page] || '-',
      page: routerMap[location.pathname] || '-',
    });

    report({
      type: 'page',
      prePage: routerMap[page] || '-',
      page: routerMap[location.pathname] || '-',
    });
  }, [location]);

  return { prePage, page };
}

export function useCurrentPage() {
  const location = useLocation();
  const pageName = routerMap[location.pathname] || '-';
  return {
    pageName,
  };
}
