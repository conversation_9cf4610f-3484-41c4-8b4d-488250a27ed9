import { useState, useMemo } from 'react';
import { debounce } from 'lodash';

const useDebounceState = <T>(initialState: T | (() => T), wait: number = 500) => {
  const [state, setState] = useState(initialState);

  const setDebounceState = useMemo(() => {
    const f = debounce(setState, wait);

    return (value: React.SetStateAction<T>, immediate?: boolean) =>
      immediate ? setState(value) : f(value);
  }, []);

  return [state, setDebounceState] as const;
};

export default useDebounceState;
