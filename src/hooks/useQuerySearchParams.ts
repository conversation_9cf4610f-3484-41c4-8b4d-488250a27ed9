import { useMemo } from 'react';
import { parse, stringify } from 'qs';
import { NavigateOptions, useSearchParams } from 'react-router-dom';

const useQuerySearchParams = <T = any>(): [T, (params: T, navigateOpts?: NavigateOptions) => void] => {
  const [searchParams, setSearchParams] = useSearchParams();

  const params = useMemo(() => parse(searchParams.toString()), [searchParams]) as T;

  const setParams = (params: T, navigateOpts?: NavigateOptions) => {
    const options = Object.assign({ replace: true }, navigateOpts);

    if (typeof params === 'function') {
      setSearchParams(stringify(params()), options);
    } else {
      setSearchParams(stringify(params), options);
    }
  };

  return [params, setParams];
};

export default useQuerySearchParams;
