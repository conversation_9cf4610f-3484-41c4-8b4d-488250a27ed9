import { useEffect, useMemo, useState } from 'react';
import type { sheetsItem } from '@src/pages/selfCheckComment/api.type';
import { Toast } from 'antd-mobile';
import dayjs from 'dayjs';

export const useCanReviewFlag = (curCheckSheetInfo: Nullable<sheetsItem>, hasSecondReview?: boolean) => {
  // 点评有效期
  const [reviewEffective, setReviewEffective] = useState(true);

  useEffect(() => {
    let _interval: any = null;
    // 第一点评人才需要计算点评时效
    if (!hasSecondReview) {
      if (!!curCheckSheetInfo?.reviewPrescription && dayjs() < dayjs(curCheckSheetInfo?.reviewPrescription)) {
        _interval = setInterval(() => {
          if (dayjs() > dayjs(curCheckSheetInfo?.reviewPrescription)) {
            setReviewEffective(false);
            clearInterval(_interval);
            Toast.show({
              content: '未按时完成，点评已关闭',
            });
          }
        }, 1000);
      }
    }

    return () => {
      clearInterval(_interval);
      setReviewEffective(true);
    };
  }, [curCheckSheetInfo?.reviewPrescription, curCheckSheetInfo?.worksheetId, hasSecondReview]);

  const canReviewFlag = useMemo(
    () =>
      !curCheckSheetInfo?.reviewPrescription ||
      (!!curCheckSheetInfo?.reviewPrescription &&
        dayjs() < dayjs(curCheckSheetInfo?.reviewPrescription) &&
        reviewEffective),
    [reviewEffective, curCheckSheetInfo?.reviewPrescription],
  );

  return !!hasSecondReview || canReviewFlag;
};
