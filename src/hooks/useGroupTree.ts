import { getZTGroupTree } from '@src/common/api';
import { OrganizationNode } from '@src/common/api.type';
import { useRequest } from 'ahooks';
import memoizeOne from 'memoize-one';
import { useMemo } from 'react';

let cache: any;

export type ArchitectureNode = TreeWith<
  Omit<OrganizationNode, 'children' | 'shops'> & {
    key: string;
    title: string;
  }
>;

export type ArchitectureDataNode = {
  type: 'group' | 'shop';
  value: string;
  key: string;
  title: string;
  children?: ArchitectureDataNode[];
};

const dig: (list: ArchitectureNode[]) => ArchitectureDataNode[] = memoizeOne(
  (list) =>
    list.map(({ shopInfos, children, id, name, headNickname }: any) => {
      const newChildren = dig(children || []).concat(
        (shopInfos || []).map(({ shopId, shopName }: { shopId: string; shopName: string }) => ({
          title: shopName,
          type: 'shop',
          key: `shop-${shopId}`,
          value: `shop-${shopId}`,
        })),
      );
      return {
        title: `${name}${headNickname ? `  (${headNickname})` : ''}`,
        type: 'group',
        key: `${id}`,
        value: `${id}`,
        ...(newChildren!.length && { children: newChildren }),
      };
    }) as ArchitectureDataNode[],
);

const useGroupTree = () => {
  const { data, loading } = useRequest(async () => {
    if (!cache) {
      cache = await getZTGroupTree().catch((e) => {
        setTimeout(() => {
          cache = null;
        });
        throw e;
      });
    }

    return Array.isArray(cache) ? cache : typeof cache === 'object' ? [{ ...cache }] : undefined;
  });

  const { tagIdObj, shopIdObj } = useMemo(() => {
    if (!data) return { tagIdObj: {}, shopIdObj: {} };
    const tagIdObj = {} as { [key: string]: ArchitectureNode };
    const shopIdObj = {} as { [key: string]: any };
    const dig = (tree?: ArchitectureNode[]) =>
      tree &&
      tree.forEach((item) => {
        dig(item.children);
        tagIdObj[`${item.id}`] = item;
        (item.shopInfos || []).forEach((ele) => (shopIdObj[ele.shopId] = ele));
      });

    dig(data!);

    return { tagIdObj, shopIdObj };
  }, [data]);

  //当前节点的tree
  const getGroupTree = (groupId: number, stopId?: number) => {
    const list: ArchitectureNode[] = [];
    const dig = (id: number) => {
      const obj = tagIdObj[id];
      if (obj) {
        list.push(obj);
        if (obj.parentId !== stopId && obj.parentId) {
          dig(obj.parentId);
        }
      }
    };
    dig(groupId);
    list.reverse();
    const groupNames = list.map((v) => v.name);
    return {
      list,
      names: groupNames,
    };
  };

  const { treeData, pid } = useMemo(() => {
    const treeData = data ? dig(data) : [];
    const pid = treeData.length ? treeData[0].value : '';
    return { treeData, pid };
  }, [data]);

  const response = {
    pid,
    treeData,
    tagIdObj,
    shopIdObj,
    getGroupTree,
  };

  return { loading, data, ...response };
};

export default useGroupTree;
