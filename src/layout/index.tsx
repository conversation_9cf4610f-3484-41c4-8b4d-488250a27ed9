import { useEffect, useState } from 'react';
import * as Sentry from '@sentry/react';
import useQuerySearchParams from '@src/hooks/useQuerySearchParams';
import { userStore } from '@src/store';
import { header } from '@tastien/rn-bridge/lib/h5';
import { SafeArea } from 'antd-mobile';
import { observer } from 'mobx-react';
import { AliveScope } from 'react-activation';
import { useLocation, useMatches, useNavigate, useOutlet } from 'react-router-dom';
function Layout() {
  const [searchParams] = useQuerySearchParams();
  const [activeKey, setActiveKey] = useState<string>('/dataViewBoard');
  const navigate = useNavigate();
  const location = useLocation();
  const { userInfo } = userStore;
  const outlet = useOutlet();
  const matches: any = useMatches();

  // useRouteChange();

  useEffect(() => {
    const lastMatch = matches?.[matches?.length - 1];
    lastMatch?.handle?.RNtitleShow &&
      header.showNavbar({
        title:
          (typeof lastMatch?.handle?.title === 'string'
            ? lastMatch?.handle?.title
            : lastMatch?.handle?.title(searchParams)) || '塔塔运营通',
        onError: (error) => {
          console.log('error :>> ', error);
        },
      });
    if (!lastMatch?.handle?.dynamicTitle) {
      if ((window as any).ReactNativeWebView && (window as any).ReactNativeWebView.postMessage) {
        (window as any).ReactNativeWebView.postMessage(
          JSON.stringify({
            title:
              (typeof lastMatch?.handle?.title === 'string'
                ? lastMatch?.handle?.title
                : lastMatch?.handle?.title(searchParams)) || '塔塔运营通',
            hideShow: lastMatch?.handle?.hideShow,
          }),
        );
      } else {
        document.title =
          (typeof lastMatch?.handle?.title === 'string'
            ? lastMatch?.handle?.title
            : lastMatch?.handle?.title(searchParams)) || '塔塔运营通';
      }
    }
  }, [matches]);

  useEffect(() => {
    if (userInfo?.userId) {
      Sentry.setUser({ id: userInfo?.userId, username: userInfo?.nickName, mobile: userInfo?.phone });
    }
  }, [userInfo?.userId]);

  return (
    <AliveScope>
      <div className="h-full w-full flex flex-col">
        {/* 督导角色显示以下tab && 访问的是看板类页面 */}
        {/* {roleTypeIsManage() &&
          (location.pathname.startsWith('/boards') || location.pathname.startsWith('/dataViewBoard')) && (
            <Tabs
              className={styles.iAdmTabs}
              style={{ '--title-font-size': '1rem', background: 'white' }}
              onChange={(key) => {
                setActiveKey(key);
                navigate(key, {
                  replace: true,
                });
              }}
              activeKey={activeKey}
            >
              <Tabs.Tab title="营运通数据" key="/dataViewBoard" />
              <Tabs.Tab title="作战小队数据" key="/boards" />
            </Tabs>
          )} */}
        <div className="grow overflow-y-scroll flex flex-col">
          {/* 确保进入页面的时候，用户权限都获取好了 */}
          {userStore.getUserAuthAlready && outlet}
        </div>
        <SafeArea position="bottom" />
      </div>
    </AliveScope>
  );
}
export default observer(Layout);
