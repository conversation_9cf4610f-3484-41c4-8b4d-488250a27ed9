import { useEffect, useLayoutEffect } from 'react';
import { ConfigProvider as AntdConfigProvider } from 'antd';
import antdZhCN from 'antd/locale/zh_CN';
import { Card, ConfigProvider, ImageViewer } from 'antd-mobile';
import zhCN from 'antd-mobile/es/locales/zh-CN';
import { config } from './api/react-query';
import { IPopupPagePopstateProvider } from './components/IPopup';
import { ShopConfigProvider } from './hooks/useShopConfig';
import Routers from './router';
import './App.scss';
import userStore from './store/user';
import { getToken } from './utils/tokenUtils';

const Container = () => {
  return (
    <ShopConfigProvider>
      <IPopupPagePopstateProvider>
        <Routers />
      </IPopupPagePopstateProvider>
    </ShopConfigProvider>
  );
};

export default function App() {
  const { queryClient, QueryClientProvider } = config();
  const init = () => {
    // 紧急需求，暂时加一个设备统计的页面不校验token，后续会删除
    if (window.location.href.indexOf('/statistics') > -1) return;
    // 获取用户信息，用户权限
    if (getToken()) {
      userStore.getUserAuth();
      userStore.getUser();
      userStore.getRoleIdList();
      userStore.getIsSuperManger();
    }
  };

  useLayoutEffect(() => {
    init();
  }, []);
  useEffect(() => {
    // url返回清一下ImageViewer弹窗
    const handlePopState = () => {
      ImageViewer.clear();
    };
    window.addEventListener('popstate', handlePopState);

    return () => {
      window.removeEventListener('popstate', handlePopState);
    };
  }, []);

  return (
    <ConfigProvider locale={zhCN}>
      <AntdConfigProvider locale={antdZhCN}>
        <QueryClientProvider client={queryClient}>
          <Container />
        </QueryClientProvider>
        <Card bodyClassName="hidden" />
      </AntdConfigProvider>
    </ConfigProvider>
  );
}
