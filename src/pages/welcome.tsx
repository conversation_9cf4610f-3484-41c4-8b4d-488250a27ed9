import { useAliveController } from 'react-activation';
import { Link, useNavigate } from 'react-router-dom';

export default function PagesWelcome() {
  const { refresh } = useAliveController();
  const navigate = useNavigate();
  return (
    <div className="h-full overflow-y-scroll">
      <Link to="/home">PagesWelcome</Link>
      {Array(20)
        .fill(0)
        .map((_, idx) => (
          <div
            key={idx}
            className="px-20 py-10 flex flex-col"
            onClick={() => {
              refresh('/home');
              navigate('/home');
            }}
          >
            {'清空缓存并退出'}
          </div>
        ))}
    </div>
  );
}
