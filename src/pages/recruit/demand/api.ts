import { post } from '@src/api';

export const getRecruitDemandList = (data: any) =>
  post('/rm-api/shop/shop-recruit/list-by-app', {
    data,
  });

export const getRecruitDemandCount = () => post('/rm-api/shop/shop-recruit/count-by-app');

export const cancelRecruitDemand = (id: number) => post('/rm-api/shop/shop-recruit/cancel', { data: { id } });
export const revokeRecruitDemand = (id: number) => post('/rm-api/shop/shop-recruit/revoke', { data: { id } });

export const deleteRecruitDemand = (data: any) => post('/rm-api/shop/shop-recruit/delete', { data });

export const getCropRecruitDemandList = (data: any) =>
  post('/rm-api/corp/shop-recruit/list-by-app', {
    data,
  });

export const finishRecruitDemand = (data: any) =>
  post('/rm-api/common/shop-recruit/finish-batch', {
    data,
  });
