import { get, post } from '@src/api';

export const getShopList = () =>
  post('/cc-api/user/app/getShopList', {
    data: { roleCategory: 2 },
  });

export const saveRecruitDemand = (data: any) =>
  post('/rm-api/shop/shop-recruit/save', {
    data,
  });

export const getRecruitDemandDetail = (id: any) =>
  get('/rm-api/common/shop-recruit/detail', {
    params: { id },
  });

export const getStaticRoleMaps = () => get('/rm-api/common/getStaticRoleMaps');

export const getUsersByRole = (data: { shopId: string; roleIds: number[] }) =>
  post('/rm-api/common/findUsersByRole', { data });

export const sumbitRecruitDemand = (data: any) => post('/rm-api/shop/shop-recruit/submit', { data });

export const getRecruitDemandProcess = (id: any) =>
  get('/rm-api/common/shop-recruit/process-log', {
    params: { id },
  });
