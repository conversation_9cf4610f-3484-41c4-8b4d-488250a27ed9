import { FC } from 'react';
import TimeRangePickerFormItem from '@src/pages/recruit/components/time-range-picker-form-item';
import WorkDurationFormItem from '@src/pages/recruit/components/work-duration-form-item';
import { Card, Form, Stepper } from 'antd-mobile';
import { OperateType } from '../..';

export interface EntryCancelCardProps {
  type: OperateType;
  data?: any;
  form?: any;
}
const WorkingTime: FC<EntryCancelCardProps> = ({ type, data, form }) => {
  return (
    <Card title="工作时间" className="mb-3">
      {type === OperateType.Edit ? (
        <>
          <WorkDurationFormItem
            name="workTime"
            label="上班时长(几小时)"
            form={form}
            rules={[{ required: true, message: '请选择上班时长' }]}
          />
          <TimeRangePickerFormItem
            name="workTimeMorning"
            allowSpan={true}
            label="早班"
            form={form}
            rules={[{ required: true, message: '请选择早班时段' }]}
          />
          <TimeRangePickerFormItem name="workTimeAfternoon" label="中班（若有）" allowSpan={true} form={form} />
          <TimeRangePickerFormItem
            name="workTimeEvening"
            label="晚班"
            allowSpan={true}
            form={form}
            rules={[{ required: true, message: '请选择晚班时段' }]}
          />
          <Form.Item
            name="monthRest"
            label="月休（天）"
            rules={[{ required: true, message: '请输入月休天数' }]}
            childElementPosition="right"
          >
            <Stepper
              step={1}
              min={0}
              inputReadOnly={true}
              defaultValue={0}
              className="rm-stepper"
              max={99}
              style={{
                '--input-width': '30px',
                '--border-radius': '4px',
              }}
            />
          </Form.Item>
        </>
      ) : (
        <>
          <Form.Item label="上班时长">
            {data?.workTimeStart && data?.workTimeEnd && `${data?.workTimeStart}~${data?.workTimeEnd}`}
          </Form.Item>
          <Form.Item label="早班">
            {data?.workTimeMorningStart &&
              data?.workTimeMorningEnd &&
              `${data?.workTimeMorningStart}~${data?.workTimeMorningEnd}`}
          </Form.Item>
          <Form.Item label="中班（若有）">
            {data?.workTimeAfternoonStart &&
              data?.workTimeAfternoonEnd &&
              `${data?.workTimeAfternoonStart}~${data?.workTimeAfternoonEnd}`}
          </Form.Item>
          <Form.Item label="晚班">
            {data?.workTimeEveningStart &&
              data?.workTimeEveningEnd &&
              `${data?.workTimeEveningStart}~${data?.workTimeEveningEnd}`}
          </Form.Item>
          <Form.Item label="月休（天）">{data?.monthRest}</Form.Item>
        </>
      )}
    </Card>
  );
};

export default WorkingTime;
