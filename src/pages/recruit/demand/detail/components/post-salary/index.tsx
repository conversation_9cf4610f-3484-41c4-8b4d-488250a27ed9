import { FC, RefObject, useState } from 'react';
import { RecruitSalaryType, RecruitSalaryTypeCN, SubsidyType } from '@src/common/recruit';
import { emojiValidator } from '@src/common/regexp';
import SalaryRangeFormItem from '@src/pages/recruit/components/salary-range-form-item';
import { Button, Card, CheckList, Form, Input, Picker, PickerRef, Popup, TextArea } from 'antd-mobile';
import { OperateType } from '../..';

export interface EntryCancelCardProps {
  type: OperateType;
  data?: any;
  form?: any;
}

const salaryTypeColumns = Object.keys(RecruitSalaryTypeCN).map((value: unknown) => ({
  value: value as string,
  label: RecruitSalaryTypeCN?.[value as RecruitSalaryType],
}));
const PostSalary: FC<EntryCancelCardProps> = ({ type, data, form }) => {
  const salaryType = Form.useWatch('salaryType', form);
  const [popupProps, setPopupProps] = useState<{
    visible: boolean;
    value?: string[];
  }>({
    visible: false,
  });

  const allowance = Form.useWatch('allowance', form);

  return (
    <>
      <Card title="岗位薪资" className="mb-3">
        {type === OperateType.Edit ? (
          <>
            <Form.Item
              name="salaryType"
              label="时薪或月薪"
              trigger="onConfirm"
              rules={[{ required: true, message: '请选择时薪或月薪' }]}
              onClick={(_, pickerRef: RefObject<PickerRef>) => {
                pickerRef.current?.open();
              }}
            >
              <Picker columns={[salaryTypeColumns]}>
                {(items) => {
                  return items?.[0]?.label;
                }}
              </Picker>
            </Form.Item>

            <SalaryRangeFormItem
              name="hourSalary"
              label="时薪"
              startName="hourSalaryStart"
              endName="hourSalaryEnd"
              form={form}
              rules={
                salaryType?.[0] === RecruitSalaryType.HOUR
                  ? [
                      {
                        required: true,
                        validator() {
                          const hourSalaryStart = form?.getFieldValue('hourSalaryStart');
                          const hourSalaryEnd = form?.getFieldValue('hourSalaryEnd');
                          if (!hourSalaryStart) {
                            return Promise.reject('请输入时薪下限');
                          }
                          if (!hourSalaryEnd) {
                            return Promise.reject('请输入时薪上限');
                          }
                          if (+hourSalaryStart >= hourSalaryEnd) {
                            return Promise.reject('时薪上限必须大于等于时薪下限');
                          }
                          return Promise.resolve();
                        },
                      },
                    ]
                  : undefined
              }
            />
            <SalaryRangeFormItem
              name="monthSalary"
              label="月薪"
              startName="monthSalaryMin"
              endName="monthSalaryMax"
              form={form}
              rules={[
                {
                  required: true,
                  validator() {
                    const monthSalaryMin = form?.getFieldValue('monthSalaryMin');
                    const monthSalaryMax = form?.getFieldValue('monthSalaryMax');
                    console.log(monthSalaryMin, monthSalaryMax);
                    if (!monthSalaryMin) {
                      return Promise.reject('请输入月薪下限');
                    }
                    if (!monthSalaryMax) {
                      return Promise.reject('请输入月薪上限');
                    }
                    if (+monthSalaryMin >= monthSalaryMax) {
                      return Promise.reject('月薪上限必须大于等于月薪下限');
                    }
                    return Promise.resolve();
                  },
                },
              ]}
            />

            <Form.Item
              name="allowance"
              label="补贴详情"
              trigger="onConfirm"
              // rules={[{ required: true, message: '请选择补贴详情' }]}
              onClick={() => {
                setPopupProps({
                  visible: true,
                  value: form?.getFieldValue('allowance'),
                });
              }}
            >
              <Picker columns={[]}>
                {() => {
                  return allowance?.join('+');
                }}
              </Picker>
            </Form.Item>
            <Form.Item
              name="mealAllowance"
              label="餐补（元/天）"
              rules={
                allowance?.includes(SubsidyType.餐补) ? [{ required: true, message: '请输入餐补金额' }] : undefined
              }
              childElementPosition="right"
            >
              <Input placeholder="请输入" type="number" min={0} max={999999} pattern="[0-9]*" inputMode={'numeric'} />
            </Form.Item>
            <Form.Item
              name="fullAttendenceReward"
              label="全勤奖（元/月）"
              rules={
                allowance?.includes(SubsidyType.全勤) ? [{ required: true, message: '请输入全勤奖金额' }] : undefined
              }
              childElementPosition="right"
            >
              <Input placeholder="请输入" type="number" min={0} max={999999} pattern="[0-9]*" inputMode={'numeric'} />
            </Form.Item>
            <Form.Item
              name="salary"
              label="岗位工资（元/月）"
              rules={
                allowance?.includes(SubsidyType.岗位工资)
                  ? [{ required: true, message: '请输入岗位工资金额' }]
                  : undefined
              }
              childElementPosition="right"
            >
              <Input placeholder="请输入" type="number" min={0} max={999999} pattern="[0-9]*" inputMode={'numeric'} />
            </Form.Item>
            <Form.Item
              name="salaryDesc"
              label="薪资补充说明"
              layout="vertical"
              rules={[
                {
                  validator: emojiValidator,
                },
              ]}
            >
              <TextArea placeholder="请输入内容" maxLength={255} rows={3} showCount />
            </Form.Item>
          </>
        ) : (
          <>
            <Form.Item label="时薪或月薪">{RecruitSalaryTypeCN[data?.salaryType as RecruitSalaryType]}</Form.Item>
            <Form.Item label="时薪">
              {data?.hourSalaryStart} {data?.hourSalaryStart && data?.hourSalaryEnd ? '~' : ''} {data?.hourSalaryEnd}
            </Form.Item>
            <Form.Item label="月薪">
              {data?.monthSalaryMin} {data?.monthSalaryMin && data?.monthSalaryMax ? '~' : ''} {data?.monthSalaryMax}
            </Form.Item>
            <Form.Item label="补贴详情">{data?.allowance}</Form.Item>
            <Form.Item label="餐补（元/天）">{data?.mealAllowance}</Form.Item>
            <Form.Item label="全勤奖（元/月）">{data?.fullAttendenceReward}</Form.Item>
            <Form.Item label="岗位工资（元/月）">{data?.salary}</Form.Item>
            <Form.Item label="薪资补充说明" layout="vertical" className="interview-text-right">
              {data?.salaryDesc}
            </Form.Item>
          </>
        )}
      </Card>
      <Popup
        visible={popupProps?.visible}
        onMaskClick={() => {
          setPopupProps({
            visible: false,
          });
        }}
        onClose={() => {
          setPopupProps({
            visible: false,
          });
        }}
        position="bottom"
        bodyStyle={{ height: '208px' }}
      >
        <CheckList
          multiple={true}
          value={popupProps?.value}
          onChange={(val) => {
            setPopupProps({
              ...popupProps,
              value: val as string[],
            });
          }}
        >
          {Object.keys(SubsidyType).map((key) => {
            return (
              <CheckList.Item value={key} key={key}>
                {key}
              </CheckList.Item>
            );
          })}
        </CheckList>
        <div className="w-full h-[56px] px-3 py-2 bg-white left-0 bottom-0 flex">
          <Button
            className="h-[40px] flex-1"
            style={{
              '--background-color': '#00BBB4',
              '--text-color': '#FFFFFF',
            }}
            onClick={() => {
              form?.setFieldValue('allowance', popupProps?.value);
              setPopupProps({
                visible: false,
              });
            }}
          >
            确定
          </Button>
        </div>
      </Popup>
    </>
  );
};

export default PostSalary;
