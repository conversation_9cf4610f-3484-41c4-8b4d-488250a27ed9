import { FC } from 'react';
import {
  RecruitDemandAction,
  RecruitDemandActionCN,
  RecruitDemandActionStatus,
  RecruitDemandActionStatusCN,
} from '@src/common/recruit';
import { Timeline } from 'antd';
import dayjs from 'dayjs';

export interface ProcessTimelineProps {
  data: any;
}

const PersonMap: Record<RecruitDemandAction, string> = {
  [RecruitDemandAction.UNKOWN]: '操作人',
  [RecruitDemandAction.SUBMIT]: '提交人',
  [RecruitDemandAction.RESUBMIT]: '提交人',
  [RecruitDemandAction.SUPERVISION]: '审核人',
  [RecruitDemandAction.OPERATION_MANAGER]: '审核人',
  [RecruitDemandAction.RECRUIT_CONFIRM]: '操作人',
  [RecruitDemandAction.RECRUITING]: '操作人',
  [RecruitDemandAction.CANCEL]: '操作人',
  [RecruitDemandAction.SUSPENDED]: '操作人',
  [RecruitDemandAction.COMPLETED]: '操作人',
  [RecruitDemandAction.REVOKE]: '操作人',
};
const ProcessTimeline: FC<ProcessTimelineProps> = ({ data }) => {
  return (
    <Timeline
      className="text-sm"
      items={data?.map(({ createTime, auditComment, action, name, status, remark }: any) => {
        return {
          children: (
            <>
              <div>{createTime && dayjs(createTime).format('YYYY-MM-DD HH:mm:ss')}</div>
              <div className="flex justify-between">
                <span>
                  <span>{PersonMap[action as RecruitDemandAction]}：</span>
                  <span>{name}</span>
                </span>
                <span>
                  {[RecruitDemandAction.SUPERVISION, RecruitDemandAction.OPERATION_MANAGER].includes(action)
                    ? RecruitDemandActionStatusCN[status as RecruitDemandActionStatus]
                    : RecruitDemandActionCN[action as RecruitDemandAction]}
                </span>
              </div>
              <div />
              {auditComment && (
                <div>
                  <span>审核意见：</span>
                  <span>{auditComment}</span>
                </div>
              )}
              {remark && (
                <div>
                  <span>备注：</span>
                  <span>{remark}</span>
                </div>
              )}
            </>
          ),
        };
      })}
    />
  );
};

export default ProcessTimeline;
