import { FC, RefObject } from 'react';
import { GenderType, GenderTypeCN, JobType, JobTypeCN } from '@src/common/interview';
import AgeRangeFormItem from '@src/pages/recruit/components/age-range-form-item';
import { Card, DatePickerRef, Form, Picker, PickerRef, Stepper } from 'antd-mobile';
import { PickerColumn } from 'antd-mobile/es/components/picker-view';
import { OperateType } from '../..';

export interface EntryCancelCardProps {
  type: OperateType;
  data?: any;
  form?: any;
  shopColumns?: PickerColumn;
}

const jobTypeColumns = Object.keys(JobTypeCN).map((value: unknown) => ({
  value: value as string,
  label: JobTypeCN?.[value as JobType],
}));
const genderTypeColumns = Object.keys(GenderTypeCN).map((value: unknown) => ({
  value: value as string,
  label: GenderTypeCN?.[value as GenderType],
}));

const BasicInfo: FC<EntryCancelCardProps> = ({ type, data, shopColumns = [], form }) => {
  return (
    <Card title="基础信息" className="mb-3">
      {type === OperateType.Edit ? (
        <>
          <Form.Item
            name="shopId"
            label="门店"
            disabled={data?.hasReject}
            trigger="onConfirm"
            rules={[{ required: true, message: '请选择门店' }]}
            onClick={(_, pickerRef: RefObject<PickerRef>) => {
              pickerRef.current?.open();
            }}
          >
            <Picker columns={[shopColumns]}>
              {(items) => {
                return items?.[0]?.label;
              }}
            </Picker>
          </Form.Item>
          <Form.Item
            name="jobType"
            label="岗位类型"
            trigger="onConfirm"
            rules={[{ required: true, message: '请选择岗位类型' }]}
            onClick={(_, pickerRef: RefObject<PickerRef>) => {
              pickerRef.current?.open();
            }}
          >
            <Picker columns={[jobTypeColumns]}>
              {(items) => {
                return items?.[0]?.label;
              }}
            </Picker>
          </Form.Item>
          <Form.Item
            name="fullTime"
            label="是否全职"
            trigger="onConfirm"
            rules={[{ required: true, message: '请选择是否全职' }]}
            onClick={(_, pickerRef: RefObject<PickerRef>) => {
              pickerRef.current?.open();
            }}
          >
            <Picker
              columns={[
                [
                  { label: '全职', value: 1 },
                  { label: '兼职', value: 0 },
                ],
              ]}
            >
              {(items) => {
                return items?.[0]?.label;
              }}
            </Picker>
          </Form.Item>
          <Form.Item
            name="requireNum"
            label="需求数量"
            rules={[{ required: true, message: '请输入需求数量' }]}
            childElementPosition="right"
          >
            <Stepper
              step={1}
              min={1}
              inputReadOnly={true}
              defaultValue={1}
              className="rm-stepper"
              allowEmpty={false}
              max={99}
              style={{
                '--input-width': '30px',
                '--border-radius': '4px',
              }}
            />
          </Form.Item>
          <AgeRangeFormItem
            name="ageRange"
            label="年龄要求"
            rules={[{ required: true, message: '请选择年龄范围' }]}
            form={form}
          />
          <Form.Item
            name="gender"
            label="性别"
            trigger="onConfirm"
            rules={[{ required: true, message: '请选择性别' }]}
            onClick={(_, datePickerRef: RefObject<DatePickerRef>) => {
              datePickerRef.current?.open();
            }}
          >
            <Picker columns={[genderTypeColumns]}>
              {(items) => {
                return items?.[0]?.label;
              }}
            </Picker>
          </Form.Item>
        </>
      ) : (
        <>
          <Form.Item label="门店">
            {data?.shopId} {data?.shopName}
          </Form.Item>
          <Form.Item label="岗位类型">{data?.jobType && JobTypeCN[data?.jobType as JobType]}</Form.Item>
          <Form.Item label="是否全职">
            {typeof data?.fullTime === 'boolean' ? (data?.fullTime ? '全职' : '兼职') : ''}
          </Form.Item>
          <Form.Item label="需求数量">{data?.requireNum}</Form.Item>
          <Form.Item label="年龄要求">{data?.ageRange}</Form.Item>
          <Form.Item label="性别">
            {data?.gender?.toString() && GenderTypeCN[data?.gender?.toString() as GenderType]}
          </Form.Item>
        </>
      )}
    </Card>
  );
};

export default BasicInfo;
