import { FC, RefObject } from 'react';
import { emojiValidator } from '@src/common/regexp';
import { Card, DatePickerRef, Form, Picker, TextArea } from 'antd-mobile';
import { OperateType } from '../..';

export interface AccommodationInfoProps {
  type: OperateType;
  data?: any;
  form: any;
}

const AccommodationInfo: FC<AccommodationInfoProps> = ({ type, data, form }) => {
  const houseInclude = Form.useWatch('houseInclude', form);

  return (
    <Card title="住宿信息" className="mb-3">
      {type === OperateType.Edit ? (
        <>
          <Form.Item
            name="houseInclude"
            label="是否包住"
            trigger="onConfirm"
            rules={[{ required: true, message: '请选择是否包住' }]}
            onClick={(_, datePickerRef: RefObject<DatePickerRef>) => {
              datePickerRef.current?.open();
            }}
          >
            <Picker
              columns={[
                [
                  { label: '包住', value: 'true' },
                  { label: '不包住', value: 'false' },
                ],
              ]}
            >
              {(items) => {
                return items?.[0]?.label;
              }}
            </Picker>
          </Form.Item>
          <Form.Item
            name="accommodationsDesc"
            label="住宿补充说明"
            layout="vertical"
            rules={
              houseInclude?.[0] === 'true'
                ? [
                    { required: true, message: '请输入住宿补充说明' },
                    {
                      validator: emojiValidator,
                    },
                  ]
                : [
                    {
                      validator: emojiValidator,
                    },
                  ]
            }
          >
            <TextArea placeholder="请输入内容" maxLength={255} rows={3} showCount />
          </Form.Item>
        </>
      ) : (
        <>
          <Form.Item label="是否包住">
            {typeof data?.houseInclude === 'boolean' ? (data?.houseInclude ? '包住' : '不包住') : ''}
          </Form.Item>
          <Form.Item label="住宿补充说明" layout="vertical" className="interview-text-right">
            {data?.accommodationsDesc}
          </Form.Item>
        </>
      )}
    </Card>
  );
};

export default AccommodationInfo;
