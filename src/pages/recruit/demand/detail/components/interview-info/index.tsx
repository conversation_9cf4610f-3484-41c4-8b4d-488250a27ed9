import { FC } from 'react';
import TimeRangePickerFormItem from '@src/pages/recruit/components/time-range-picker-form-item';
import { userStore } from '@src/store';
import { Card, Form, Picker } from 'antd-mobile';
import { OperateType } from '../..';

export interface EntryCancelCardProps {
  type: OperateType;
  data?: any;
  form?: any;
}
const InterviewInfo: FC<EntryCancelCardProps> = ({ form, type, data }) => {
  const { userInfo } = userStore;

  return (
    <Card title="面试信息" className="mb-3">
      {type === OperateType.Edit ? (
        <>
          <Form.Item
            name="recruitUserId"
            label="面试人"
            trigger="onConfirm"
            rules={[{ required: true, message: '请选择面试人' }]}
            // onClick={(_, datePickerRef: RefObject<DatePickerRef>) => {
            //   datePickerRef.current?.open();
            // }}
          >
            <Picker columns={[userInfo?.userId ? [{ label: userInfo?.nickName, value: userInfo?.userId }] : []]}>
              {(items) => {
                return items?.[0]?.label;
              }}
            </Picker>
          </Form.Item>
          <TimeRangePickerFormItem
            name="recruitTime"
            label="常规面试时间段"
            form={form}
            rules={[{ required: true, message: '请选择常规面试时间段' }]}
          />
        </>
      ) : (
        <>
          <Form.Item label="面试人">{data?.recruitUserName ?? userInfo?.nickName}</Form.Item>
          <Form.Item label="常规面试时间段">
            {data?.recruitTimeStart} {data?.recruitTimeStart && data?.recruitTimeEnd ? '~' : ''} {data?.recruitTimeEnd}
          </Form.Item>
        </>
      )}
    </Card>
  );
};

export default InterviewInfo;
