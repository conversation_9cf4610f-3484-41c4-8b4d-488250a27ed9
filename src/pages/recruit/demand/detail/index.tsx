import './index.scss';

import { FC, RefObject, useEffect, useState } from 'react';
import { RecruitDemandActionStatus, RecruitDemandStatus } from '@src/common/recruit';
import useQuerySearchParams from '@src/hooks/useQuerySearchParams';
import { userStore } from '@src/store';
import { roleTypeIsManage } from '@src/utils/tokenUtils';
import { useRequest } from 'ahooks';
import { Button, Card, Form, Picker, PickerRef, Tabs, Toast } from 'antd-mobile';
import qs from 'qs';
import { useNavigate } from 'react-router-dom';
import {
  getRecruitDemandDetail,
  getRecruitDemandProcess,
  getShopList,
  getStaticRoleMaps,
  getUsersByRole,
  saveRecruitDemand,
  sumbitRecruitDemand,
} from './api';
import AccommodationInfo from './components/accommodation-info';
import BasicInfo from './components/basic-info';
import InterviewInfo from './components/interview-info';
import PostSalary from './components/post-salary';
import ProcessTimeline from './components/process-timeline';
import WorkingTime from './components/working-time';
import PageContainer from '../../components/page-container';

export enum OperateType {
  Edit = 'Edit',
  View = 'View',
  Audit = 'Audit',
}

const timeReg = /\d{2}|[:]|\d{2}/gi;
const ageReg = /\d+|[~]|\d+/gi;

enum TabKey {
  Form = 'Form',
  Process = 'Process',
}

export interface RecruitDetailProps {
  params: {
    demandId?: number;
    type: OperateType;
  };
}

const RecruitDetail: FC<RecruitDetailProps> = () => {
  const [form] = Form.useForm();
  const isDudao = roleTypeIsManage();
  const { userInfo } = userStore;
  const [params]: any = useQuerySearchParams();
  const navigator = useNavigate();
  const { data: shopList } = useRequest(getShopList);
  const shopId = Form.useWatch('shopId', form);
  const [activeKey, setActiveKey] = useState<string>(TabKey.Form);

  const { data: detail, run: requestDetail } = useRequest((id) => getRecruitDemandDetail(id), {
    manual: true,
    onSuccess(data) {
      form?.setFieldsValue({
        shopId: data?.shopId ? [data?.shopId] : undefined,
        jobType: data?.jobType ? [data?.jobType] : undefined,
        fullTime: typeof data?.fullTime === 'boolean' ? [Number(data?.fullTime)] : undefined,
        requireNum: data?.requireNum >= 1 ? data?.requireNum : 1,
        ageRange: data?.ageRange?.match(ageReg),
        gender: data?.gender?.toString() ? [data?.gender?.toString()] : undefined,
        workTime:
          data?.workTimeStart?.toString() && data?.workTimeEnd?.toString()
            ? [data?.workTimeStart?.toString(), '~', data?.workTimeEnd?.toString()]
            : undefined,
        workTimeMorning:
          data?.workTimeMorningStart && data?.workTimeMorningEnd
            ? [...data?.workTimeMorningStart?.match(timeReg), '~', ...data?.workTimeMorningEnd?.match(timeReg)]
            : undefined,
        workTimeAfternoon:
          data?.workTimeAfternoonStart && data?.workTimeAfternoonEnd
            ? [...data?.workTimeAfternoonStart?.match(timeReg), '~', ...data?.workTimeAfternoonEnd?.match(timeReg)]
            : undefined,
        workTimeEvening:
          data?.workTimeEveningStart && data?.workTimeEveningEnd
            ? [...data?.workTimeEveningStart?.match(timeReg), '~', ...data?.workTimeEveningEnd?.match(timeReg)]
            : undefined,
        monthRest: data?.monthRest >= 0 ? data?.monthRest : 0,
        salaryType: data?.salaryType ? [data?.salaryType] : undefined,
        hourSalaryStart: data?.hourSalaryStart,
        hourSalaryEnd: data?.hourSalaryEnd,
        monthSalaryMin: data?.monthSalaryMin,
        monthSalaryMax: data?.monthSalaryMax,
        allowance: data?.allowance?.split('+'),
        mealAllowance: data?.mealAllowance,
        fullAttendenceReward: data?.fullAttendenceReward,
        salary: data?.salary,
        salaryDesc: data?.salaryDesc,
        houseInclude: data?.houseInclude?.toString() ? [data?.houseInclude?.toString()] : undefined,
        accommodationsDesc: data?.accommodationsDesc,
        recruitTime:
          data?.recruitTimeStart && data?.recruitTimeEnd
            ? [...data?.recruitTimeStart?.match(timeReg), '~', ...data?.recruitTimeEnd?.match(timeReg)]
            : undefined,
        auditUserId: data?.auditUserId ? [data?.auditUserId] : undefined,
        recruitUserId: [userInfo?.userId],
      });
    },
  });

  const { data: process, run: requestProcess } = useRequest((id) => getRecruitDemandProcess(id), {
    manual: true,
  });

  const { run: requestSave, loading: saveLoading } = useRequest((data) => saveRecruitDemand(data), {
    manual: true,
    onSuccess: () => {
      Toast.show({
        content: '暂存成功',
      });

      history.back();
    },
  });

  const { run: requestSubmit, loading: submitLoading } = useRequest(
    (data) =>
      saveRecruitDemand(data).then((id) => {
        return sumbitRecruitDemand({ id });
      }),
    {
      manual: true,
      onSuccess: () => {
        Toast.show({
          content: '提交成功',
        });

        history.back();
      },
    },
  );

  useEffect(() => {
    let title: string;
    if (params?.type === OperateType.View) {
      title = '招聘需求详情';
    } else if (params?.type === OperateType.Edit) {
      if (params?.demandId) {
        title = '编辑招聘需求';
      } else {
        title = '新增招聘需求';
      }
    } else {
      title = '审核招聘需求';
    }
    if ((window as any).ReactNativeWebView && (window as any).ReactNativeWebView.postMessage) {
      (window as any).ReactNativeWebView.postMessage(
        JSON.stringify({
          title,
          hideShow: false,
        }),
      );
    } else {
      document.title = title;
    }
  }, [params?.type, params?.demandId]);

  useEffect(() => {
    const id = shopId?.[0] || detail?.shopId;
    if (id) {
      requestUsers(id);
    }
  }, [shopId, detail?.shopId]);

  const { data: users, run: requestUsers } = useRequest(
    (shopId) =>
      getStaticRoleMaps()?.then((res: any) => {
        return getUsersByRole({
          shopId,
          roleIds: isDudao ? res?.operationManagerRoleIdList : res?.supervisionRoleIdList,
        });
      }),
    { manual: true },
  );

  useEffect(() => {
    if (params?.demandId) {
      requestDetail(params?.demandId);
      requestProcess(params?.demandId);
    } else {
      form?.resetFields();
      form?.setFieldValue('recruitUserId', [userInfo?.userId]);
      form?.setFieldValue('requireNum', 1);
      form?.setFieldValue('monthRest', 0);
    }
  }, [params?.demandId]);

  const formatValues = (values: any) => {
    const {
      shopId,
      jobType,
      fullTime,
      ageRange,
      requireNum,
      gender,
      workTime,
      workTimeMorning,
      workTimeAfternoon,
      workTimeEvening,
      monthRest,
      salaryType,
      hourSalaryStart,
      hourSalaryEnd,
      monthSalaryMin,
      monthSalaryMax,
      allowance,
      mealAllowance,
      fullAttendenceReward,
      salary,
      salaryDesc,
      houseInclude,
      accommodationsDesc,
      recruitUserId,
      recruitTime,
      auditUserId,
    } = values;
    return {
      id: params?.demandId,
      shopId: shopId?.[0],
      jobType: jobType?.[0],
      fullTime: fullTime?.[0],
      ageRange: ageRange?.join(''),
      requireNum,
      gender: gender?.[0],
      workTimeStart: workTime?.[0],
      workTimeEnd: workTime?.[2],
      workTimeMorningStart: workTimeMorning?.slice(0, 3)?.join(''),
      workTimeMorningEnd: workTimeMorning?.slice(4, 7)?.join(''),
      workTimeAfternoonStart: workTimeAfternoon?.slice(0, 3)?.join(''),
      workTimeAfternoonEnd: workTimeAfternoon?.slice(4, 7)?.join(''),
      workTimeEveningStart: workTimeEvening?.slice(0, 3)?.join(''),
      workTimeEveningEnd: workTimeEvening?.slice(4, 7)?.join(''),
      monthRest,
      salaryType: salaryType?.[0],
      hourSalaryStart,
      hourSalaryEnd,
      monthSalaryMin,
      monthSalaryMax,
      allowance: allowance?.join('+'),
      mealAllowance,
      fullAttendenceReward,
      salary,
      salaryDesc,
      houseInclude: houseInclude?.[0],
      accommodationsDesc,
      recruitUserId: recruitUserId?.[0],
      recruitTimeStart: recruitTime?.slice(0, 3)?.join(''),
      recruitTimeEnd: recruitTime?.slice(4, 7)?.join(''),
      auditUserId: auditUserId?.[0],
    };
  };

  const goToAudit = (status: RecruitDemandActionStatus) => {
    navigator(
      `/recruit/demand/result${qs.stringify(
        {
          status,
          shopId: detail?.shopId,
          idList: [detail?.id],
          hasNext: detail?.hasNext,
          hasReject: detail?.hasReject,
          nextFlowableAuditUserId: detail?.nextFlowableAuditUserId,
        },
        { addQueryPrefix: true },
      )}`,
    );
  };

  return (
    <>
      <PageContainer
        header={
          params?.demandId && detail?.status !== RecruitDemandStatus.DRAFT && process?.length ? (
            <Tabs
              className="w-full text-[#858585] font-medium text-sm bg-white"
              defaultActiveKey={TabKey.Form}
              activeKey={activeKey}
              style={{
                '--active-line-height': '4px',
                '--fixed-active-line-width': '24px',
                '--active-title-color': '#141414',
              }}
              onChange={(val) => {
                setActiveKey(val);
              }}
            >
              <Tabs.Tab title="表单" key={TabKey.Form} />
              <Tabs.Tab title="流程记录" key={TabKey.Process} />
            </Tabs>
          ) : undefined
        }
        footer={
          params?.type === OperateType.Audit ? (
            <div className="w-full h-[56px] px-3 py-2 bg-white left-0 bottom-0 flex">
              <Button
                className="h-[40px] flex-1 mr-2"
                onClick={() => {
                  goToAudit(RecruitDemandActionStatus.REJECT);
                }}
              >
                驳回
              </Button>
              <Button
                className="h-[40px] flex-1 mr-2"
                onClick={() => {
                  goToAudit(RecruitDemandActionStatus.NO_PASS);
                }}
              >
                不通过
              </Button>
              <Button
                className="h-[40px] flex-1"
                onClick={() => {
                  goToAudit(RecruitDemandActionStatus.PASS);
                }}
              >
                通过
              </Button>
            </div>
          ) : params?.type === OperateType.Edit ? (
            <div className="w-full h-[56px] px-3 py-2 bg-white left-0 bottom-0 flex">
              {![RecruitDemandStatus.REJECT].includes(detail?.status) && (
                <Button
                  className="h-[40px] flex-1 mr-4"
                  loading={saveLoading}
                  onClick={() => {
                    requestSave(formatValues(form?.getFieldsValue()));
                  }}
                >
                  暂存
                </Button>
              )}
              <Button
                className="h-[40px] flex-1"
                loading={submitLoading}
                style={{
                  '--background-color': '#00BBB4',
                  '--text-color': '#FFFFFF',
                }}
                onClick={() => {
                  console.log(form?.getFieldsValue());
                  form?.validateFields().then(() => {
                    requestSubmit(formatValues(form?.getFieldsValue()));
                  });
                }}
              >
                {[RecruitDemandStatus.REJECT].includes(detail?.status) ? '重新提交' : '提交'}
              </Button>
            </div>
          ) : undefined
        }
      >
        {activeKey === TabKey.Process ? (
          <div className="p-2 recruit-detail bg-[#f5f5f5] min-h-full">
            <Card className="pt-5">
              <ProcessTimeline data={process} />
            </Card>
          </div>
        ) : (
          <div className="p-2 recruit-detail bg-[#f5f5f5] min-h-full">
            <Form layout="horizontal" form={form}>
              <BasicInfo
                type={params?.type}
                data={detail}
                form={form}
                shopColumns={shopList?.map(({ shopId, shopName }: any) => ({
                  label: `${shopId} ${shopName}`,
                  value: shopId,
                }))}
              />
              <WorkingTime type={params?.type} data={detail} form={form} />
              <PostSalary type={params?.type} data={detail} form={form} />
              <AccommodationInfo type={params?.type} data={detail} form={form} />
              <InterviewInfo type={params?.type} data={detail} form={form} />
              {(!isDudao || detail?.hasNext) && params?.type === OperateType.Edit && (
                <Card className=" mb-3">
                  <Form.Item
                    name="auditUserId"
                    label="流程下一节点"
                    // disabled={detail?.status === RecruitDemandStatus.REJECT}
                    trigger="onConfirm"
                    rules={[{ required: true, message: '请选择流程下一审核人' }]}
                    onClick={(_, pickerRef: RefObject<PickerRef>) => {
                      pickerRef.current?.open();
                    }}
                  >
                    <Picker
                      columns={[
                        users?.map(({ userId, nickname }: any) => ({
                          label: nickname,
                          value: userId,
                        })) || [],
                      ]}
                    >
                      {(items) => {
                        return items?.[0]?.label;
                      }}
                    </Picker>
                  </Form.Item>
                </Card>
              )}
            </Form>
          </div>
        )}
      </PageContainer>
    </>
  );
};

export default RecruitDetail;
