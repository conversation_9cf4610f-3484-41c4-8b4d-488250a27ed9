import './index.scss';
import { FC, RefObject, useEffect } from 'react';
import { RecruitDemandActionStatus } from '@src/common/recruit';
import { emojiValidator } from '@src/common/regexp';
import useQuerySearchParams from '@src/hooks/useQuerySearchParams';
import PageContainer from '@src/pages/recruit/components/page-container';
import { roleTypeIsManage } from '@src/utils/tokenUtils';
import { useRequest } from 'ahooks';
import { Button, Card, Form, Picker, PickerRef, TextArea, Toast } from 'antd-mobile';
import { useNavigate } from 'react-router-dom';
import { batchAuditRecruitDemand } from './api';
import { getStaticRoleMaps, getUsersByRole } from '../api';

const DocumentTitleMap: Record<RecruitDemandActionStatus, string> = {
  [RecruitDemandActionStatus.NO_PASS]: '确认不通过',
  [RecruitDemandActionStatus.PASS]: '确认通过',
  [RecruitDemandActionStatus.REJECT]: '驳回',
};

const RecruitDemandResult: FC = () => {
  const [params]: any = useQuerySearchParams();
  const [form] = Form.useForm();
  const isDudao = roleTypeIsManage();
  const navigator = useNavigate();

  useEffect(() => {
    if (params?.status) {
      const title: string = DocumentTitleMap?.[params?.status as RecruitDemandActionStatus];
      if ((window as any).ReactNativeWebView && (window as any).ReactNativeWebView.postMessage) {
        (window as any).ReactNativeWebView.postMessage(
          JSON.stringify({
            title,
            hideShow: false,
          }),
        );
      } else {
        document.title = title;
      }
    }
  }, [params?.status]);

  useEffect(() => {
    if (params?.shopId && params?.status === RecruitDemandActionStatus.PASS) {
      requestUsers(params?.shopId);
    }

    form?.setFieldsValue({
      ...params,
      nextAuditUserId:
        params?.hasReject === 'true' && params?.nextFlowableAuditUserId
          ? [+params?.nextFlowableAuditUserId]
          : undefined,
    });
  }, [params]);

  const { loading, run: requestBatchAudit } = useRequest((data) => batchAuditRecruitDemand(data), {
    manual: true,
    onSuccess: () => {
      navigator(-2);
      Toast.show({
        content: '提交成功',
      });
    },
  });

  const formatValues = (values: any) => {
    return { ...values, nextAuditUserId: values?.nextAuditUserId?.[0] };
  };

  const { data: users, run: requestUsers } = useRequest(
    (shopId) => {
      return getStaticRoleMaps()?.then((res) => {
        return getUsersByRole({
          shopId,
          roleIds: isDudao ? res?.operationManagerRoleIdList : res?.supervisionRoleIdList,
        });
      });
    },
    { manual: true },
  );
  return (
    <PageContainer
      footer={
        <div className="w-full h-[56px] px-3 py-2 bg-white fixed left-0 bottom-0">
          <Button
            className="h-[40px] w-full"
            style={{
              '--background-color': '#00BBB4',
              '--text-color': '#FFFFFF',
            }}
            loading={loading}
            onClick={() => {
              form?.validateFields().then(() => {
                requestBatchAudit(formatValues(form?.getFieldsValue()));
              });
            }}
          >
            提交
          </Button>
        </div>
      }
    >
      <div className="rm-form p-2">
        <Form form={form} layout="horizontal">
          <Card className="mb-3">
            <Form.Item name="idList" hidden />
            <Form.Item name="status" hidden />
            <Form.Item name="shopId" hidden />
            <Form.Item
              name="auditComment"
              label="审核意见"
              layout="vertical"
              rules={
                params?.status !== RecruitDemandActionStatus.PASS
                  ? [
                      { required: true, message: '请输入审核意见' },
                      {
                        validator: emojiValidator,
                      },
                    ]
                  : [
                      {
                        validator: emojiValidator,
                      },
                    ]
              }
            >
              <TextArea placeholder="请输入审核意见" maxLength={255} rows={3} showCount />
            </Form.Item>
          </Card>
          {params?.status === RecruitDemandActionStatus.PASS && params?.hasNext === 'true' && (
            <Card>
              <Form.Item
                name="nextAuditUserId"
                label="流程下一节点"
                trigger="onConfirm"
                disabled={params?.hasReject === 'true' && params?.nextFlowableAuditUserId}
                rules={[{ required: true, message: '请选择流程下一审核人' }]}
                onClick={(_, pickerRef: RefObject<PickerRef>) => {
                  pickerRef.current?.open();
                }}
              >
                <Picker
                  columns={[
                    users?.map(({ userId, nickname }: any) => ({
                      label: nickname,
                      value: userId,
                    })) || [],
                  ]}
                >
                  {(items) => {
                    return items?.[0]?.label;
                  }}
                </Picker>
              </Form.Item>
            </Card>
          )}
        </Form>
      </div>
    </PageContainer>
  );
};

export default RecruitDemandResult;
