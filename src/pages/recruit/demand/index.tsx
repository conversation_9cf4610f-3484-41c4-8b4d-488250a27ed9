import './index.scss';

import { FC, RefObject, useEffect, useMemo, useRef, useState } from 'react';
import { RecruitDemandStatus } from '@src/common/recruit';
import { emojiValidator } from '@src/common/regexp';
import useQuerySearchParams from '@src/hooks/useQuerySearchParams';
import { userStore } from '@src/store';
import { roleTypeIsManage } from '@src/utils/tokenUtils';
import { useRequest } from 'ahooks';
import {
  Button,
  DatePickerRef,
  Empty,
  Form,
  InfiniteScroll,
  Input,
  List,
  Loading,
  Modal,
  Picker,
  SpinLoading,
  Tabs,
  TextArea,
} from 'antd-mobile';
import { stringify } from 'qs';
import { useNavigate } from 'react-router-dom';
import {
  cancelRecruitDemand,
  deleteRecruitDemand,
  finishRecruitDemand,
  getCropRecruitDemandList,
  getRecruitDemandCount,
  getRecruitDemandList,
  revokeRecruitDemand,
} from './api';
import { OperateType } from './detail';
import PageContainer from '../components/page-container';
import RecruitDemandCard from '../components/recruit-demand-card';

export enum TabKey {
  All = 'All',
  Reject = 'Reject',
  Wait = 'Wait',
  Recruit = 'Recruit',
  Pending = 'Pending',
  Processed = 'Processed',
}

export enum Role {
  ShopManager, // 店长
  Supervisor, // 督导
}

const DocumentTitleMap: Record<Role, string> = {
  [Role.ShopManager]: '门店招聘需求提报',
  [Role.Supervisor]: '招聘需求审核',
};

const TabOptionMap = {
  [Role.ShopManager]: [
    {
      title: '全部',
      key: TabKey.All,
    },
    {
      title: '待提交',
      // key: [RecruitDemandStatus.REJECT],
      key: TabKey.Reject,
      countKey: 'countReject',
    },
    {
      title: '待审核和确认',
      // key: [RecruitDemandStatus.AUDITING, RecruitDemandStatus.RECRUIT_CONFIRM],
      key: TabKey.Wait,
      countKey: 'countAuditingAndConfirm',
    },
    {
      title: '招聘进行中',
      // key: [RecruitDemandStatus.RECRUITING],
      key: TabKey.Recruit,
      countKey: 'countRecruiting',
    },
  ],
  [Role.Supervisor]: [
    {
      title: '待处理',
      key: TabKey.Pending,
    },
    {
      title: '已处理',
      key: TabKey.Processed,
    },
  ],
};

const RequestListMap: any = {
  [Role.ShopManager]: getRecruitDemandList,
  [Role.Supervisor]: getCropRecruitDemandList,
};

const RequestStatusMap: any = {
  [TabKey.All]: { statusList: undefined },
  [TabKey.Reject]: {
    statusList: [RecruitDemandStatus.REJECT, RecruitDemandStatus.DRAFT, RecruitDemandStatus.REVOKE],
  },
  [TabKey.Wait]: { statusList: [RecruitDemandStatus.AUDITING, RecruitDemandStatus.RECRUIT_CONFIRM] },
  [TabKey.Recruit]: { statusList: [RecruitDemandStatus.RECRUITING] },
  [TabKey.Pending]: { status: 'AUDITING' },
  [TabKey.Processed]: { status: 'AUDITED' },
};

const Recruit: FC = () => {
  const [params, setParams]: any = useQuerySearchParams();
  // const [popupProps, setPopupProps] = useState<{
  //   visible: boolean;
  //   demandId?: number;
  //   type: OperateType;
  // }>({
  //   visible: false,
  //   type: OperateType.View,
  // });
  // const [visible, setVisible] = useState<boolean>(false);
  const { userInfo } = userStore;
  const navigator = useNavigate();
  const containerRef: any = useRef();
  /** 判断是否是督导 */
  const isDudao = roleTypeIsManage();
  const role = Number(isDudao);

  const [suspendForm] = Form.useForm();
  const [deleteForm] = Form.useForm();

  const reason = Form.useWatch('reason', deleteForm);
  const [pagination, setPagination] = useState<{ pageNo: number; pageSize: number; total: number }>({
    pageNo: 1,
    pageSize: 5,
    total: 0,
  });
  const [list, setList] = useState([]);

  const hasMore = useMemo(() => {
    return list?.length < pagination?.total;
  }, [pagination.total, list]);

  const { loading, run: requestRecruitDemandList } = useRequest((data: any, isReset?: boolean) => {
    const tabKey = data?.tabKey || params?.tabKey || (isDudao ? TabKey.Pending : TabKey.All);
    const pageNo = data?.pageNo || pagination.pageNo;
    const pageSize = data?.pageSize || pagination?.pageSize;
    return RequestListMap?.[role]?.({
      ...RequestStatusMap?.[tabKey],
      ...data,
      pageNo,
      pageSize,
    }).then((res: any) => {
      setPagination({
        pageNo,
        pageSize,
        total: res?.total || 0,
      });
      const data = res?.data || [];

      setList(isReset || pageNo === 1 ? data : list.concat(data));
    });
  });

  const toDetail = ({ demandId, type }: any) => {
    return navigator(
      `/recruit/demand/detail?${stringify({
        type,
        demandId,
      })}`,
    );
  };

  useEffect(() => {
    const title = DocumentTitleMap?.[role as Role];
    if (title) {
      if ((window as any).ReactNativeWebView && (window as any).ReactNativeWebView.postMessage) {
        (window as any).ReactNativeWebView.postMessage(
          JSON.stringify({
            title,
            hideShow: false,
          }),
        );
      } else {
        document.title = title;
      }
    }
  }, []);

  const { loading: cancelLoading, run: requestCancel } = useRequest((id: number) => cancelRecruitDemand(id), {
    manual: true,
    onSuccess: () => {
      refreshList?.();
    },
  });
  const { loading: revokeLoading, run: requestRevoke } = useRequest((id: number) => revokeRecruitDemand(id), {
    manual: true,
    onSuccess: () => {
      refreshList?.();
    },
  });

  const { loading: deleteLoading, run: requestDelete } = useRequest((data: any) => deleteRecruitDemand(data), {
    manual: true,
    onSuccess: () => {
      refreshList?.();
    },
  });

  const { loading: finishLoading, run: requestFinish } = useRequest((data: any) => finishRecruitDemand(data), {
    manual: true,
    onSuccess: () => {
      refreshList?.();
    },
  });

  const { data: countMap, run: requestCount } = useRequest(getRecruitDemandCount, { manual: true });

  useEffect(() => {
    if (!isDudao) {
      requestCount();
    }
  }, [isDudao]);

  const refreshList = () => {
    setPagination({
      pageNo: 1,
      pageSize: 10,
      total: 0,
    });
    requestRecruitDemandList(
      {
        pageNo: 1,
        pageSize: 10,
      },
      true,
    );
    requestCount();
  };

  return (
    <>
      <PageContainer
        ref={containerRef}
        header={
          <Tabs
            className="w-full text-[#858585] font-medium text-sm bg-white"
            defaultActiveKey={isDudao ? TabKey.Pending : TabKey.All}
            activeKey={params?.tabKey}
            style={{
              '--active-line-height': '4px',
              '--fixed-active-line-width': '24px',
              '--active-title-color': '#141414',
            }}
            onChange={(val) => {
              setList([]);
              setPagination({ pageNo: 1, pageSize: 10, total: 0 });
              containerRef?.current?.scrollToTop();
              requestRecruitDemandList({
                tabKey: val,
                pageNo: 1,
                pageSize: 10,
              });
              setParams(
                {
                  ...params,
                  tabKey: val,
                },
                {
                  replace: true,
                },
              );
            }}
          >
            {TabOptionMap?.[role as Role]?.map(({ key, title, countKey }: any) => (
              <Tabs.Tab
                title={
                  <span className="leading-4 text-sm font-normal">{`${title}${countKey ? ` (${countMap?.[countKey] || 0})` : ''}`}</span>
                }
                key={key}
              />
            ))}
          </Tabs>
        }
        footer={
          !isDudao ? (
            <>
              <div className="h-[56px]" />
              <div className="w-full h-[56px] px-3 py-2 bg-white fixed left-0 bottom-0">
                <Button
                  className="h-[40px] w-full"
                  style={{
                    '--background-color': '#00BBB4',
                    '--text-color': '#FFFFFF',
                  }}
                  onClick={() => {
                    toDetail({ type: OperateType.Edit });
                  }}
                >
                  新增
                </Button>
              </div>
            </>
          ) : undefined
        }
      >
        <div>
          <List className="demand">
            {list?.map((item: any) => {
              return (
                <List.Item
                  key={item?.id}
                  style={{
                    backgroundColor: 'transparent',
                  }}
                >
                  <RecruitDemandCard
                    data={item}
                    readonly={params?.tabKey === TabKey.Processed}
                    onEdit={() => {
                      toDetail({ type: OperateType.Edit, demandId: item?.id });
                    }}
                    userId={userInfo?.userId}
                    cancelLoading={cancelLoading}
                    deleteLoading={deleteLoading}
                    revokeLoading={revokeLoading}
                    onCancel={() => {
                      Modal.confirm({
                        content: '是否确认撤销',
                        onConfirm: () => {
                          return requestCancel(item?.id);
                        },
                      });
                    }}
                    onRevoke={() => {
                      Modal.confirm({
                        content: '是否确认撤回重新编辑',
                        onConfirm: () => {
                          return requestRevoke(item?.id);
                        },
                      });
                    }}
                    onDelete={() => {
                      deleteForm?.resetFields();
                      Modal.confirm({
                        title: '删除草稿',
                        content: (
                          <>
                            <div className="pb-3">注意: 若有需求，请直接在此草稿上编辑提交，不用删除此草稿。</div>
                            <Form form={deleteForm}>
                              <Form.Item
                                name="reason"
                                label="原因"
                                trigger="onConfirm"
                                rules={[{ required: true, message: '请选择删除原因' }]}
                                onClick={(_, datePickerRef: RefObject<DatePickerRef>) => {
                                  datePickerRef.current?.open();
                                }}
                              >
                                <Picker
                                  columns={[
                                    [
                                      { label: '暂无招聘需求', value: '暂无招聘需求' },
                                      { label: '门店自招', value: '门店自招' },
                                      { label: '需求已提报过, 无需更新', value: '需求已提报过, 无需更新' },
                                      { label: '其它', value: '其它' },
                                    ],
                                  ]}
                                >
                                  {(items) => {
                                    return items?.[0]?.label;
                                  }}
                                </Picker>
                              </Form.Item>
                              <Form.Item
                                name="currentStaffCount"
                                label="当前店员数"
                                dependencies={['reason']}
                                rules={[
                                  { required: true, message: '请输入当前店员数' },
                                  {
                                    validator: emojiValidator,
                                  },
                                ]}
                              >
                                <Input placeholder="全职+兼职总数" min={1} max={999} type="number" step={1} />
                              </Form.Item>
                              <Form.Item noStyle dependencies={['reason']}>
                                {() => {
                                  const reason = deleteForm?.getFieldValue('reason');
                                  return (
                                    reason?.[0] !== '暂无招聘需求' && (
                                      <Form.Item
                                        name="staffLackCount"
                                        label="店员缺人数"
                                        dependencies={['reason']}
                                        rules={[
                                          { required: true, message: '请输入店员缺人数' },
                                          {
                                            validator: emojiValidator,
                                          },
                                        ]}
                                      >
                                        <Input placeholder="全职+兼职总数" min={0} max={999} type="number" step={1} />
                                      </Form.Item>
                                    )
                                  );
                                }}
                              </Form.Item>
                              <Form.Item noStyle dependencies={['reason']}>
                                {() => {
                                  const reason = deleteForm?.getFieldValue('reason');
                                  return (
                                    <Form.Item
                                      name="remark"
                                      label="备注"
                                      dependencies={['reason']}
                                      rules={
                                        reason?.[0] === '其它'
                                          ? [
                                              { required: true, message: '请输入备注' },
                                              {
                                                validator: emojiValidator,
                                              },
                                            ]
                                          : [
                                              {
                                                validator: emojiValidator,
                                              },
                                            ]
                                      }
                                    >
                                      <TextArea placeholder="请输入备注" maxLength={255} rows={3} showCount />
                                    </Form.Item>
                                  );
                                }}
                              </Form.Item>
                            </Form>
                          </>
                        ),
                        onConfirm: async () => {
                          return deleteForm?.validateFields().then(() => {
                            const { reason, remark, currentStaffCount, staffLackCount } = deleteForm?.getFieldsValue();
                            return requestDelete({
                              id: item?.id,
                              reason: reason?.[0],
                              currentStaffCount: currentStaffCount ? +currentStaffCount : undefined,
                              staffLackCount: staffLackCount ? +staffLackCount : undefined,
                              remark,
                            });
                          });
                        },
                      });
                    }}
                    onClick={() => {
                      toDetail({ type: OperateType.View, demandId: item?.id });
                    }}
                    isDudao={isDudao}
                    onComplete={() => {
                      Modal.confirm({
                        content: '是否将这些招聘需求设置为已完成？',
                        onConfirm: async () => {
                          return requestFinish({
                            idList: [item?.id],
                            status: RecruitDemandStatus.COMPLETED,
                          });
                        },
                      });
                    }}
                    onSuspend={() => {
                      suspendForm?.resetFields();
                      Modal.confirm({
                        title: '中止招聘',
                        content: (
                          <>
                            <Form form={suspendForm}>
                              <Form.Item
                                name="suspendRemark"
                                label="说明"
                                rules={[{ required: true, message: '请输入中止说明' }]}
                              >
                                <TextArea placeholder="请输入中止说明" maxLength={255} rows={3} showCount />
                              </Form.Item>
                            </Form>
                          </>
                        ),
                        onConfirm: async () => {
                          return suspendForm?.validateFields().then(() => {
                            const { suspendRemark } = suspendForm?.getFieldsValue();
                            return requestFinish({
                              idList: [item?.id],
                              suspendRemark,
                              status: RecruitDemandStatus.SUSPENDED,
                            });
                          });
                        },
                      });
                    }}
                    onAudit={() => {
                      toDetail({ type: OperateType.Audit, demandId: item?.id });
                    }}
                  />
                </List.Item>
              );
            })}
          </List>
          <InfiniteScroll
            loadMore={async () => {
              if (hasMore && !loading) {
                const pageNo = pagination?.pageNo + 1;
                setPagination({
                  ...pagination,
                  pageNo,
                });
                await requestRecruitDemandList({
                  // status: params?.status || RecruitCandidateStatus.WAIT_APPOINTMENT,
                  pageNo,
                  pageSize: pagination?.pageSize,
                });
              }
              return Promise.resolve();
            }}
            hasMore={hasMore}
          >
            {loading ? (
              list?.length ? (
                <Loading />
              ) : (
                <div className="h-[60vh] flex flex-col items-center justify-center">
                  <SpinLoading style={{ '--size': '36px' }} />
                  <div className=" mt-8">加载中...</div>
                </div>
              )
            ) : !list?.length ? (
              <Empty description="暂无数据" />
            ) : (
              <span>--- 没有更多了 ---</span>
            )}
          </InfiniteScroll>
        </div>
      </PageContainer>
      {/* <IPopupPage
        DocumentTitle="招聘需求"
        destroyOnClose={true}
        visible={popupProps?.visible}
        onClose={() => {
          setParams({
            ...params,
            demandId: undefined,
            type: undefined,
          });
          setPopupProps({
            visible: false,
            type: OperateType.View,
          });
        }}
      >
        <RecruitDetail refreshList={refreshList} params={{ demandId: popupProps?.demandId, type: popupProps?.type }} />
      </IPopupPage> */}
    </>
  );
};

export default Recruit;
