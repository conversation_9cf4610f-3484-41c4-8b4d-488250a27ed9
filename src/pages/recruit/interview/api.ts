import { get, post } from '@src/api';

export const getRecruitTaskCount = async () => await get(`/rm-api/shop-recruit-task/count`);

export type GetRecruitTaskListParams = {
  status: 'WAIT_APPOINTMENT' | 'WART_INTERVIEW' | 'WAIT_ENTRY' | 'FINISH';
  pageNo: number;
  pageSize: number;
};
export const getRecruitTaskList = async (data: GetRecruitTaskListParams) =>
  await post(`/rm-api/shop/recruit-task-list`, { data });

export const getRecruitTaskDetail = async (id: number) =>
  await get(`/rm-api/shop-recruit-task/detail`, { params: { id } });

// 确认约面
export type ConfirmInterviewParams = {
  id: number;
  interviewDate: string;
  interviewTime: string;
};
export const confirmInterview = async (data: ConfirmInterviewParams) =>
  await post(`/rm-api/shop-recruit-task/confirm`, { data });

// 取消面试
export type CancelInterviewParams = {
  id: number;
  reason: string;
  remark?: string;
};
export const cancelInterview = async (data: CancelInterviewParams) =>
  await post(`/rm-api/shop-recruit-task/canceled-appointment`, { data });

// 填写面试结果
export type ResultInterviewParams = {
  id: number;
  realName: string;
  passed: boolean;
  remark?: string;
};
export const resultInterview = async (data: ResultInterviewParams) =>
  await post(`/rm-api/shop-recruit-task/result`, { data });

// 取消入职
export type CancelEntryParams = {
  id: number;
  reason: string;
  remark?: string;
};
export const cancelEntry = async (data: CancelEntryParams) =>
  await post(`/rm-api/shop-recruit-task/canceled-entry`, { data });

// 确认入职
export type ConfirmEntryParams = {
  id: number;
  reason: string;
  remark?: string;
};
export const confirmEntry = async (data: ConfirmEntryParams) => await post(`/rm-api/shop-recruit-task/entry`, { data });

export const makeCall = async (id: number) => post(`/rm-api/shop-recruit-task/call`, { data: { id } });
