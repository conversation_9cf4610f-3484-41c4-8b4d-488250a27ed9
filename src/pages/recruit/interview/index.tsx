import './index.scss';

import { FC, useMemo, useRef, useState } from 'react';
import { RecruitCandidateStatus, RecruitCandidateStatusCN } from '@src/common/interview';
import useQuerySearchParams from '@src/hooks/useQuerySearchParams';
import { useRequest } from 'ahooks';
import { InfiniteScroll, List, Tabs } from 'antd-mobile';
import qs from 'qs';
import { useNavigate } from 'react-router-dom';
import { getRecruitTaskCount, getRecruitTaskList, makeCall } from './api';
import InterviewInfoCard from './components/interview-info-card';
import PageContainer from '../components/page-container';

const TabOptions = [
  {
    title: RecruitCandidateStatusCN[RecruitCandidateStatus.WAIT_APPOINTMENT],
    key: RecruitCandidateStatus.WAIT_APPOINTMENT,
    countKey: 'waitAppointmentCount',
  },
  {
    title: RecruitCandidateStatusCN[RecruitCandidateStatus.WART_INTERVIEW],
    key: RecruitCandidateStatus.WART_INTERVIEW,
    countKey: 'waitInterviewCount',
  },
  {
    title: RecruitCandidateStatusCN[RecruitCandidateStatus.WAIT_ENTRY],
    key: RecruitCandidateStatus.WAIT_ENTRY,
    countKey: 'waitEntryCount',
  },
  // {
  //   title: '已结束',
  //   key: 'FINISH',
  //   countKey: 'finishCount',
  // },
];

const Interview: FC = () => {
  const [params, setParams]: any = useQuerySearchParams();
  const [pagination, setPagination] = useState<{ pageNo: number; pageSize: number; total: number }>({
    pageNo: 1,
    pageSize: 5,
    total: 0,
  });
  const [list, setList] = useState([]);
  const navigator = useNavigate();
  const containerRef: any = useRef();
  // const [visible, setVisible] = useState<boolean>(false);
  // const [popupProps, setPopupProps] = useState<{
  //   visible: boolean;
  //   id?: number;
  //   type?: DetailOperateType;
  // }>({
  //   visible: false,
  // });

  const { data: statusCount, run: requestCount } = useRequest(getRecruitTaskCount);

  const { run: requestRecruitTaskList, loading } = useRequest((data: any) => {
    const pageNo = data?.pageNo || pagination.pageNo;
    const pageSize = data?.pageSize || pagination?.pageSize;
    return getRecruitTaskList({
      status: data?.status || params?.status || RecruitCandidateStatus.WAIT_APPOINTMENT,
      pageNo,
      pageSize,
    }).then((res) => {
      setPagination({
        pageNo,
        pageSize,
        total: res?.total || 0,
      });
      const data = res?.data || [];

      setList(pageNo === 1 ? data : list.concat(data));
    });
  });

  // const refreshList = () => {
  //   setPagination({
  //     pageNo: 1,
  //     pageSize: 10,
  //     total: 0,
  //   });
  //   requestCount();
  //   requestRecruitTaskList({
  //     pageNo: 1,
  //     pageSize: 10,
  //   });
  // };

  // useEffect(() => {
  //   if (params?.id) {
  //     setPopupProps({
  //       visible: true,
  //       id: params?.id,
  //       type: params?.type,
  //     });
  //   } else {
  //     setPopupProps({
  //       visible: false,
  //     });
  //   }
  // }, [params]);

  const toDetail = ({ id, type }: any) => {
    navigator(
      `/recruit/interview/detail${qs.stringify(
        {
          id,
          type,
        },
        { addQueryPrefix: true },
      )}`,
    );
  };

  const hasMore = useMemo(() => {
    return list?.length < pagination?.total;
  }, [pagination.total, list]);

  return (
    <>
      <PageContainer
        ref={containerRef}
        header={
          <Tabs
            className="w-full text-[#858585] font-medium text-sm bg-white"
            defaultActiveKey={RecruitCandidateStatus.WAIT_APPOINTMENT}
            activeKey={params?.status}
            style={{
              '--active-line-height': '4px',
              '--fixed-active-line-width': '24px',
              '--active-title-color': '#141414',
            }}
            onChange={(val) => {
              setParams(
                {
                  status: val,
                },
                { replace: true },
              );
              setPagination(() => {
                const pageNo = 1;
                const { pageSize } = pagination;
                requestRecruitTaskList({ status: val, pageNo, pageSize });
                containerRef?.current?.scrollToTop();
                return {
                  ...pagination,
                  pageNo,
                  pageSize,
                };
              });
            }}
          >
            {TabOptions?.map(({ key, title, countKey }) => (
              <Tabs.Tab
                title={
                  <span className="leading-4 text-sm font-normal">{`${title} (${statusCount?.[countKey] || 0})`}</span>
                }
                key={key}
              />
            ))}
          </Tabs>
        }
      >
        <div>
          <List className="interview">
            {list?.map((item: any) => {
              return (
                <List.Item
                  key={item?.id}
                  style={{
                    backgroundColor: 'transparent',
                  }}
                >
                  <InterviewInfoCard
                    data={item}
                    onCall={() => {
                      makeCall?.(item?.id);
                    }}
                    onDetail={(type) => {
                      return toDetail({
                        id: item?.id,
                        type,
                      });
                    }}
                  />
                </List.Item>
              );
            })}
          </List>
          <InfiniteScroll
            loadMore={async () => {
              if (hasMore && !loading) {
                const pageNo = pagination?.pageNo + 1;
                setPagination({
                  ...pagination,
                  pageNo,
                });
                requestRecruitTaskList({
                  status: params?.status || RecruitCandidateStatus.WAIT_APPOINTMENT,
                  pageNo,
                  pageSize: pagination?.pageSize,
                });
              }
              return Promise.resolve();
            }}
            hasMore={hasMore}
          />
        </div>
      </PageContainer>
      {/* <IPopupPage
        DocumentTitle={popupProps?.type ? DocumentTitleMap?.[popupProps?.type] : '查看详情'}
        visible={popupProps?.visible}
        onClose={() => {
          setPopupProps({
            visible: false,
          });
        }}
      >
        <Detail refreshList={refreshList} params={{ id: popupProps?.id!, type: popupProps?.type }} />
      </IPopupPage> */}
    </>
  );
};

export default Interview;
