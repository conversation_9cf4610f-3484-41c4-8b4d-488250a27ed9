import { FC } from 'react';
import { JobType, JobTypeCN, RecruitCandidateStatus, RecruitCandidateStatusCN } from '@src/common/interview';
import { Button } from 'antd-mobile';
import dayjs from 'dayjs';
import { DetailOperateType } from '../../detail';

export interface InterviewInfoCardProps {
  data: {
    id: number;
    status: RecruitCandidateStatus;
    jobType: JobType;
    fullTime: boolean;
    shopId: string;
    shopName: string;
    candidateName: string;
    candidatePhone?: string;
    createAt?: string;
    interviewTime?: string;
    entryTime?: string;
    interviewCancelReason?: string;
    entryCancelReason?: string;
    interviewDate?: string;
  };
  onDetail?: (type?: DetailOperateType) => void;
  onCall?: () => void;
}

const InterviewInfoCard: FC<InterviewInfoCardProps> = ({ data, onDetail, onCall }) => {
  const {
    status,
    jobType,
    fullTime,
    shopId,
    shopName,
    candidateName,
    candidatePhone,
    createAt,
    interviewTime,
    entryTime,
    interviewCancelReason,
    entryCancelReason,
    interviewDate,
    id,
  } = data;

  return (
    <div
      className="bg-white rounded-[8px] px-3 pt-2 pb-4"
      onClick={() => {
        onDetail?.();
        // toDetail();
      }}
    >
      <div className="flex justify-between text-base">
        <span className="font-bold">
          {JobTypeCN[jobType]} {fullTime ? '全职' : '兼职'}
        </span>
        <span className="text-[#00BBB4]">{RecruitCandidateStatusCN[status]}</span>
      </div>
      <div className="my-2 py-2 px-3 rounded-[8px] bg-[#FAFAFA] font-normal text-sm leading-[22px]">
        <p className="mb-1">
          {shopId} {shopName}
        </p>
        <div className="mb-1">
          <span className="mr-2">候选人:</span>
          <span>{candidateName}</span>
          {candidatePhone && (
            <a
              href={`tel:${candidatePhone}`}
              onClick={(e) => {
                e?.stopPropagation();
                onCall?.();
              }}
              className="ml-4 text-[#1677ff]"
            >
              {candidatePhone}
            </a>
          )}
        </div>
        {interviewDate && interviewTime && (
          <div className="mb-1">
            <span className="mr-2">面试时间:</span>
            <span>
              {dayjs(interviewDate).format('YYYY-MM-DD')} {interviewTime}
            </span>
          </div>
        )}
        {interviewCancelReason && (
          <div className="mb-1">
            <span className="mr-2">面试取消原因:</span>
            <span>{interviewCancelReason}</span>
          </div>
        )}
        {entryTime && (
          <div className="mb-1">
            <span className="mr-2">入职日期:</span>
            <span>{dayjs(entryTime).format('YYYY-MM-DD')}</span>
          </div>
        )}
        {entryCancelReason && (
          <div className="mb-1">
            <span className="mr-2">入职取消原因:</span>
            <span>{entryCancelReason}</span>
          </div>
        )}
        {createAt && (
          <div className="mb-1">
            <span className="mr-2">创建时间:</span>
            <span>{dayjs(createAt).format('YYYY-MM-DD HH:mm:ss')}</span>
          </div>
        )}
      </div>
      <div className="flex">
        {[RecruitCandidateStatus.WAIT_APPOINTMENT, RecruitCandidateStatus.WART_INTERVIEW].includes(status) && (
          <Button
            className="mr-3 shrink-0 grow rounded border-[#DCDCDC] h-8 text-sm leading-[14px]"
            onClick={(e) => {
              e?.preventDefault();
              e?.stopPropagation();
              onDetail?.(DetailOperateType.CancelInterview);
            }}
          >
            取消面试
          </Button>
        )}
        {[RecruitCandidateStatus.WAIT_ENTRY].includes(status) && (
          <>
            <Button
              className="mr-3 shrink-0 grow rounded border-[#DCDCDC] h-8 text-sm leading-[14px]"
              onClick={(e) => {
                e?.preventDefault();
                e?.stopPropagation();
                console.log('取消');
                onDetail?.(DetailOperateType.CancelEntry);
              }}
            >
              取消入职
            </Button>
            <Button
              className="shrink-0 grow rounded border-[#DCDCDC] h-8 text-sm leading-[14px]"
              onClick={(e) => {
                e?.preventDefault();
                e?.stopPropagation();
                onDetail?.(DetailOperateType.ConfirmEntry);
              }}
            >
              确认入职
            </Button>
          </>
        )}
        {[RecruitCandidateStatus.WAIT_APPOINTMENT].includes(status) && (
          <Button
            className="shrink-0 grow rounded border-[#DCDCDC] h-8 text-sm leading-[14px]"
            onClick={(e) => {
              e?.preventDefault();
              e?.stopPropagation();
              onDetail?.(DetailOperateType.ScheduleInterview);
            }}
          >
            确认约面
          </Button>
        )}
        {[RecruitCandidateStatus.WART_INTERVIEW].includes(status) && (
          <Button
            className="shrink-0 grow rounded border-[#DCDCDC] h-8 text-sm leading-[14px]"
            onClick={(e) => {
              e?.preventDefault();
              e?.stopPropagation();
              onDetail?.(DetailOperateType.InterviewResult);
            }}
          >
            填写面试结果
          </Button>
        )}
      </div>
    </div>
  );
};
export default InterviewInfoCard;
