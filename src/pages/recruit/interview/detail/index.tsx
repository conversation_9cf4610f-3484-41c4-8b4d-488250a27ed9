import './index.scss';

import { FC, useEffect } from 'react';
import useQuerySearchParams from '@src/hooks/useQuerySearchParams';
import { formatDateToUTC } from '@src/utils/utils';
import { useRequest } from 'ahooks';
import { Button, Form, Toast } from 'antd-mobile';
import BasicInfo from './components/basic-info';
import EntryCancelCard from './components/entry-cancel-card';
import EntryInfoCard from './components/entry-info-card';
import InterviewCancelCard from './components/interview-cancel-card';
import InterviewInfoCard from './components/interview-info-card';
import InterviewResultCard from './components/interview-result-card';
import {
  cancelEntry,
  cancelInterview,
  confirmEntry,
  confirmInterview,
  getRecruitTaskDetail,
  makeCall,
  resultInterview,
} from '../api';

export enum DetailOperateType {
  CancelInterview = 'CancelInterview',
  ScheduleInterview = 'ScheduleInterview',
  InterviewResult = 'InterviewResult',
  ConfirmEntry = 'ConfirmEntry',
  CancelEntry = 'CancelEntry',
}

export const RequestMap: any = {
  [DetailOperateType.CancelInterview]: cancelInterview,
  [DetailOperateType.ScheduleInterview]: confirmInterview,
  [DetailOperateType.InterviewResult]: resultInterview,
  [DetailOperateType.ConfirmEntry]: confirmEntry,
  [DetailOperateType.CancelEntry]: cancelEntry,
};

export const RequestToastContentMap: any = {
  [DetailOperateType.CancelInterview]: '取消面试成功',
  [DetailOperateType.ScheduleInterview]: '确认约面成功',
  [DetailOperateType.InterviewResult]: '面试结果填写完成',
  [DetailOperateType.ConfirmEntry]: '确认入职成功',
  [DetailOperateType.CancelEntry]: '取消入职成功',
};

export const DocumentTitleMap: any = {
  [DetailOperateType.CancelInterview]: '取消面试',
  [DetailOperateType.ScheduleInterview]: '确认约面',
  [DetailOperateType.InterviewResult]: '填写面试结果',
  [DetailOperateType.ConfirmEntry]: '确认入职',
  [DetailOperateType.CancelEntry]: '取消入职',
};

export interface InterviewDetailProps {
  // refreshList?: () => void;
  // params: { id: number; type?: DetailOperateType };
}
const Detail: FC<InterviewDetailProps> = () => {
  const [params]: any = useQuerySearchParams();
  const [form] = Form.useForm();

  useEffect(() => {
    const title: string = params?.type ? DocumentTitleMap?.[params?.type] : '查看详情';

    if ((window as any).ReactNativeWebView && (window as any).ReactNativeWebView.postMessage) {
      (window as any).ReactNativeWebView.postMessage(
        JSON.stringify({
          title,
          hideShow: false,
        }),
      );
    } else {
      document.title = title;
    }
  }, [params?.type]);

  const { data } = useRequest(
    () => {
      form?.resetFields();
      if (params?.id) {
        return getRecruitTaskDetail(params?.id);
      }
      return Promise.resolve();
    },
    { refreshDeps: [params?.id] },
  );

  return (
    <div className="flex flex-col items-stretch bg-[#f5f5f5] min-h-full">
      <div className="grow overflow-auto grow shrink pb-[56px]">
        <div className="interview-detail">
          {data && (
            <BasicInfo
              data={data}
              onCall={() => {
                params?.id && makeCall?.(params?.id);
              }}
            />
          )}
          {(params?.type === DetailOperateType.ScheduleInterview || (data?.interviewDate && data?.interviewTime)) && (
            <InterviewInfoCard data={data} type={params?.type} form={form} />
          )}
          {(params?.type === DetailOperateType.CancelInterview || data?.interviewCancelReason) && (
            <InterviewCancelCard data={data} type={params?.type} form={form} />
          )}
          {(params?.type === DetailOperateType.InterviewResult ||
            (data?.realName && typeof data?.passed === 'boolean')) && (
            <InterviewResultCard data={data} type={params?.type} form={form} />
          )}
          {(params?.type === DetailOperateType.ConfirmEntry || data?.entryTime) && (
            <EntryInfoCard data={data} type={params?.type} form={form} />
          )}
          {(params?.type === DetailOperateType.CancelEntry || data?.entryCancelReason) && (
            <EntryCancelCard data={data} type={params?.type} form={form} />
          )}
        </div>
      </div>
      {params?.type && (
        <div className="w-full h-[56px] px-3 py-2 bg-white fixed left-0 bottom-0">
          <Button
            className="h-[40px] w-full"
            style={{
              '--background-color': '#00BBB4',
              '--text-color': '#FFFFFF',
            }}
            onClick={() => {
              const values = form?.getFieldsValue();
              const { type } = params;
              const data = {
                id: params?.id,
                ...values,
              };
              if ([DetailOperateType.CancelInterview, DetailOperateType.CancelEntry].includes(type!)) {
                data.reason = values?.reason?.[0];
              }
              if ([DetailOperateType.ConfirmEntry].includes(type!)) {
                data.entryTime = formatDateToUTC(values.entryTime);
                data.fullTime = values?.fullTime?.[0];
              }
              if ([DetailOperateType.InterviewResult].includes(type!)) {
                data.passed = !!values?.passed?.[0];
              }
              if ([DetailOperateType.ScheduleInterview].includes(type!)) {
                data.interviewDate = formatDateToUTC(values.interviewDate);
                data.interviewTime = values?.interviewTime?.join('');
              }

              form?.validateFields().then(() => {
                RequestMap?.[type!]?.(data).then(() => {
                  // refreshList?.();
                  Toast.show({
                    content: RequestToastContentMap?.[type!],
                  });
                  history.back();
                });
              });
            }}
          >
            提交
          </Button>
        </div>
      )}
    </div>
  );
};

export default Detail;
