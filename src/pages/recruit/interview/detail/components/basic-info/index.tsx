import { FC } from 'react';

export interface BasicInfoProps {
  data: {
    candidateName: string;
    geekAvatar?: string;
    ageDesc?: string;
    salary?: string;
    candidatePhone?: string;
    geekGender?: number;
    degreeName?: string;
    school?: string;
    major?: string;
  };
  onCall?: () => void;
}
const BasicInfo: FC<BasicInfoProps> = ({ data, onCall }) => {
  const { candidateName, geekAvatar, ageDesc, salary, candidatePhone, geekGender, degreeName, school, major } = data;
  return (
    <div className="flex mx-3 my-3 bg-white rounded-lg p-2 text-sm font-normal">
      <div className="w-[30%] mr-3">{geekAvatar && <img src={geekAvatar} alt={candidateName} />}</div>
      <div className="grow">
        <div className="flex justify-between mb-2">
          <span>{candidateName}</span>
          <span>{ageDesc}</span>
          <span>{geekGender ? '男' : '女'}</span>
        </div>
        <div className="mb-2">
          <span className="mr-2">手机号:</span>
          <a
            href={`tel:${candidatePhone}`}
            onClick={(e) => {
              e?.stopPropagation();
              onCall?.();
            }}
            className="text-[#1677ff]"
          >
            {candidatePhone}
          </a>
        </div>
        <div className="mb-2">
          <span className="mr-2">期望薪资:</span>
          <span>{salary}</span>
        </div>
        <div className="mb-2">
          <span className="mr-2">学历:</span>
          <span>{degreeName}</span>
        </div>
        <div className="mb-2">
          <span className="mr-2">学校:</span>
          <span>{school}</span>
        </div>
        <div className="mb-2">
          <span className="mr-2">专业:</span>
          <span>{major}</span>
        </div>
      </div>
    </div>
  );
};

export default BasicInfo;
