import { FC, RefObject } from 'react';
import { emojiValidator } from '@src/common/regexp';
import { Card, DatePickerRef, Form, Picker, TextArea } from 'antd-mobile';
import { DetailOperateType } from '../..';

export interface EntryCancelCardProps {
  type?: DetailOperateType;
  data: {
    entryCancelReason?: string;
    entryCancelRemark?: string;
  };
  form: any;
}
const EntryCancelCard: FC<EntryCancelCardProps> = ({ form, type, data }) => {
  return (
    <Card title="入职取消信息" className="mx-3 mb-3">
      <Form layout="horizontal" form={form}>
        {type === DetailOperateType.CancelEntry ? (
          <>
            <Form.Item
              name="reason"
              label="入职取消原因"
              trigger="onConfirm"
              rules={[{ required: true, message: '请选择入职取消原因' }]}
              onClick={(_, datePickerRef: RefObject<DatePickerRef>) => {
                datePickerRef.current?.open();
              }}
            >
              <Picker
                columns={[
                  [
                    { label: '候选人联系不上', value: '候选人联系不上' },
                    { label: '候选人有其他选择', value: '候选人有其他选择' },
                    { label: '健康证办理不下来', value: '健康证办理不下来' },
                  ],
                ]}
              >
                {(items) => {
                  return items?.[0]?.label;
                }}
              </Picker>
            </Form.Item>
            <Form.Item
              name="remark"
              label="备注"
              layout="vertical"
              rules={[
                { required: true, message: '请输入入职取消备注' },
                {
                  validator: emojiValidator,
                },
              ]}
            >
              <TextArea placeholder="请输入内容" maxLength={255} rows={3} showCount />
            </Form.Item>
          </>
        ) : (
          <>
            <Form.Item label="入职取消原因">{data?.entryCancelReason}</Form.Item>
            <Form.Item name="remark" label="备注" layout="vertical" className="interview-text-right">
              {data?.entryCancelRemark}
            </Form.Item>
          </>
        )}
      </Form>
    </Card>
  );
};

export default EntryCancelCard;
