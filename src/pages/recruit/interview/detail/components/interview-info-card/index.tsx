import { FC, RefObject } from 'react';
import TimeRangePickerFormItem from '@src/pages/recruit/components/time-range-picker-form-item';
import { Card, DatePicker, DatePickerRef, Form } from 'antd-mobile';
import dayjs from 'dayjs';
import { DetailOperateType } from '../..';
export interface InterviewInfoCardProps {
  type?: DetailOperateType;
  data: {
    interviewDate?: string;
    interviewTime?: string;
  };
  form?: any;
}

const InterviewInfoCard: FC<InterviewInfoCardProps> = ({ type, data, form }) => {
  // const { interviewDate, interviewTime } = data;

  return (
    <Card title="面试信息" className="mx-3 mb-3">
      <Form layout="horizontal" form={form}>
        {type === DetailOperateType.ScheduleInterview ? (
          <>
            <Form.Item
              name="interviewDate"
              label="面试日期"
              rules={[{ required: true, message: '请选择面试日期' }]}
              trigger="onConfirm"
              onClick={(_, datePickerRef: RefObject<DatePickerRef>) => {
                datePickerRef.current?.open();
              }}
            >
              <DatePicker>{(value) => (value ? dayjs(value).format('YYYY-MM-DD') : '请选择面试日期')}</DatePicker>
            </Form.Item>
            <TimeRangePickerFormItem
              name="interviewTime"
              label="面试时间"
              form={form}
              rules={[{ required: true, message: '请选择面试时间' }]}
            />
            <div className="my-2">请先和候选人电话沟通后填写预计面试时间</div>
          </>
        ) : (
          <>
            <Form.Item label="面试日期">
              {data?.interviewDate && dayjs(data?.interviewDate).format('YYYY-MM-DD')}
            </Form.Item>
            <Form.Item label="面试时间">{data?.interviewTime}</Form.Item>
          </>
        )}
      </Form>
    </Card>
  );
};

export default InterviewInfoCard;
