import { FC, RefObject } from 'react';
import { emojiValidator } from '@src/common/regexp';
import { Card, DatePickerRef, Form, Picker, TextArea } from 'antd-mobile';
import { DetailOperateType } from '../..';

export interface InterviewCancelCardProps {
  type?: DetailOperateType;
  data: {
    interviewCancelReason?: string;
    remark?: string;
  };
  form: any;
}
const InterviewCancelCard: FC<InterviewCancelCardProps> = ({ data, type, form }) => {
  return (
    <Card title="面试取消信息" className="mx-3 mb-3">
      <Form layout="horizontal" form={form}>
        {type === DetailOperateType.CancelInterview ? (
          <>
            <Form.Item
              name="reason"
              label="面试取消原因"
              trigger="onConfirm"
              rules={[{ required: true, message: '请选择面试取消原因' }]}
              onClick={(_, datePickerRef: RefObject<DatePickerRef>) => {
                datePickerRef.current?.open();
              }}
              dependencies={['reason']}
            >
              <Picker
                columns={[
                  [
                    { label: '店员已满', value: '店员已满' },
                    { label: '候选人爽约', value: '候选人爽约' },
                    { label: '候选人不合适', value: '候选人不合适' },
                  ],
                ]}
              >
                {(items) => {
                  return items?.[0]?.value;
                }}
              </Picker>
            </Form.Item>
            <Form.Item
              name="remark"
              label="备注"
              layout="vertical"
              rules={[
                { required: true, message: '请输入面试取消备注' },
                {
                  validator: emojiValidator,
                },
              ]}
            >
              <TextArea placeholder="请输入内容" maxLength={255} rows={3} showCount />
            </Form.Item>
          </>
        ) : (
          <>
            <Form.Item label="面试取消原因">{data?.interviewCancelReason}</Form.Item>
            <Form.Item label="备注" layout="vertical" className="interview-text-right">
              {data?.remark}
            </Form.Item>
          </>
        )}
      </Form>
    </Card>
  );
};

export default InterviewCancelCard;
