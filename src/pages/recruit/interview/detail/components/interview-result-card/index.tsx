import { FC, RefObject } from 'react';
import { emojiValidator } from '@src/common/regexp';
import { Card, DatePickerRef, Form, Input, Picker, TextArea } from 'antd-mobile';
import { DetailOperateType } from '../..';

export interface InterviewResultCardProps {
  type?: DetailOperateType;
  data: {
    realName?: string;
    remark?: string;
    passed?: boolean;
  };
  form: any;
}
const InterviewResultCard: FC<InterviewResultCardProps> = ({ type, form, data }) => {
  const passed = Form.useWatch('passed', form);

  return (
    <Card title="面试结果" className="mx-3 mb-3">
      <Form layout="horizontal" form={form}>
        {type === DetailOperateType.InterviewResult ? (
          <>
            <Form.Item name="realName" label="真实姓名" rules={[{ required: true, message: '请输入真实姓名' }]}>
              <Input placeholder="请输入内容" />
            </Form.Item>
            <Form.Item
              name="passed"
              label="面试结果"
              trigger="onConfirm"
              rules={[{ required: true, message: '请选择面试结果' }]}
              onClick={(_, datePickerRef: RefObject<DatePickerRef>) => {
                datePickerRef.current?.open();
              }}
            >
              <Picker
                columns={[
                  [
                    { label: '通过', value: 1 },
                    { label: '不通过', value: 0 },
                  ],
                ]}
              >
                {(items) => {
                  return items?.[0]?.label;
                }}
              </Picker>
            </Form.Item>
            <Form.Item
              name="remark"
              label="备注"
              layout="vertical"
              rules={[
                { required: passed?.[0] === 0 ? true : false, message: '请输入不通过备注' },
                {
                  validator: emojiValidator,
                },
              ]}
            >
              <TextArea placeholder="请输入内容" maxLength={255} rows={3} showCount />
            </Form.Item>
          </>
        ) : (
          <>
            <Form.Item label="真实姓名">{data?.realName}</Form.Item>
            <Form.Item label="面试结果">{data?.passed ? '通过' : '不通过'}</Form.Item>
            <Form.Item name="remark" label="备注" layout="vertical" className="interview-text-right">
              {data?.remark}
            </Form.Item>
          </>
        )}
      </Form>
    </Card>
  );
};

export default InterviewResultCard;
