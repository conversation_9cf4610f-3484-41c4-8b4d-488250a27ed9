import { FC, RefObject } from 'react';
import { emojiValidator } from '@src/common/regexp';
import { Card, DatePicker, DatePickerRef, Form, Picker, PickerRef, TextArea } from 'antd-mobile';
import dayjs from 'dayjs';
import { DetailOperateType } from '../..';

export interface EntryInfoCardProps {
  type?: DetailOperateType;
  data: {
    entryTime?: string;
    entryRemark?: string;
  };
  form: any;
}
const EntryInfoCard: FC<EntryInfoCardProps> = ({ type, data, form }) => {
  return (
    <Card title="入职信息" className="mx-3 mb-3">
      <Form layout="horizontal" form={form}>
        {type === DetailOperateType.ConfirmEntry ? (
          <>
            <Form.Item
              name="entryTime"
              label="入职日期"
              trigger="onConfirm"
              rules={[{ required: true, message: '请选择入职日期' }]}
              onClick={(_, datePickerRef: RefObject<DatePickerRef>) => {
                datePickerRef.current?.open();
              }}
            >
              <DatePicker>{(value) => (value ? dayjs(value).format('YYYY-MM-DD') : '请选择日期')}</DatePicker>
            </Form.Item>
            <Form.Item
              name="fullTime"
              label="是否全职"
              trigger="onConfirm"
              rules={[{ required: true, message: '请选择是否全职' }]}
              onClick={(_, pickerRef: RefObject<PickerRef>) => {
                pickerRef.current?.open();
              }}
            >
              <Picker
                columns={[
                  [
                    { label: '全职', value: 1 },
                    { label: '兼职', value: 0 },
                  ],
                ]}
              >
                {(items) => {
                  return items?.[0]?.label;
                }}
              </Picker>
            </Form.Item>
            <Form.Item
              name="entryRemark"
              label="备注"
              layout="vertical"
              rules={[
                {
                  validator: emojiValidator,
                },
              ]}
            >
              <TextArea placeholder="请输入内容" maxLength={255} rows={3} showCount />
            </Form.Item>
          </>
        ) : (
          <>
            <Form.Item label="入职日期">{data?.entryTime && dayjs(data?.entryTime).format('YYYY-MM-DD')}</Form.Item>
            <Form.Item label="备注" layout="vertical" className="interview-text-right">
              {data?.entryRemark}
            </Form.Item>
          </>
        )}
      </Form>
    </Card>
  );
};

export default EntryInfoCard;
