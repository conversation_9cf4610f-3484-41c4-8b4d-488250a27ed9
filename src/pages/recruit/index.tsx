import { FC } from 'react';
import DemandPng from '@src/assets/images/demand.png';
import InterviewPng from '@src/assets/images/interview.png';
import AuthorityEnum from '@src/common/authority';
import Loading from '@src/components/Loading';
import { userStore } from '@src/store';
import { getCurShopId } from '@src/utils/tokenUtils';
import { useRequest } from 'ahooks';
import { Badge } from 'antd';
import classNames from 'classnames';
import { useNavigate } from 'react-router-dom';
import { getRecruitDemandCount } from './api';
import PageContainer from './components/page-container';

const MenuOptions = [
  {
    name: '需求提报',
    key: 'countRecruitSubmit',
    url: '/recruit/demand',
    permission: AuthorityEnum.需求提报,
    src: DemandPng,
  },
  {
    name: '面试管理',
    key: 'countWaitAppoint',
    url: '/recruit/interview',
    permission: AuthorityEnum.面试管理,
    src: InterviewPng,
  },
];
const Recruit: FC = () => {
  const curShopId = getCurShopId();

  const { data: statistics, loading } = useRequest(
    async () => {
      if (!curShopId) return null;
      return await getRecruitDemandCount(curShopId);
    },
    { refreshDeps: [curShopId] },
  );
  const navigator = useNavigate();
  const { permissionsMap } = userStore;

  return (
    <PageContainer>
      <Loading spinning={loading}>
        <div className="p-4 w-full">
          <div className="flex flex-wrap">
            {MenuOptions.map(({ name, key, url, permission, src }, index) => {
              return permissionsMap.has(permission) ? (
                <div
                  key={key}
                  className={classNames('flex w-[48%] flex-shrink-0 bg-white mb-4 h-20 rounded-xl p-2', {
                    'mr-[2%]': index % 2 === 0,
                    'ml-[2%]': index % 2 === 1,
                  })}
                  onClick={() => {
                    url && navigator?.(url);
                  }}
                >
                  <div>
                    <img src={src} alt={name} className="w-16 h-16" />
                  </div>
                  <div className="ml-2 pl-2 flex flex-col justify-center flex-1 bg-[url('@src/assets/images/recruit-item-bg.png')] bg-[length:120px_100px]">
                    <div className="flex items-start gap-2">
                      {!!statistics?.[key] && <h4 className="mb-1 text-xl">{statistics?.[key] || 0}</h4>}
                      {!!statistics?.[key] && <Badge status="error" />}
                    </div>
                    <div className="text-[13px] font-normal">{name}</div>
                  </div>
                </div>
              ) : undefined;
            })}
          </div>
        </div>
      </Loading>
    </PageContainer>
  );
};

export default Recruit;
