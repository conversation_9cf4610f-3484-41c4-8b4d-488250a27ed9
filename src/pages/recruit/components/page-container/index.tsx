import { FC, forwardRef, ReactNode, useImperativeHandle, useRef } from 'react';

export interface PageContainerProps {
  header?: ReactNode;
  footer?: ReactNode;
  children: ReactNode;
}
const PageContainer: FC<PageContainerProps> = forwardRef(({ header, footer, children }, ref) => {
  const scrollElmRef: any = useRef();

  useImperativeHandle(ref, () => ({
    scrollToTop: () => {
      scrollElmRef?.current?.scrollTo({ top: 0 });
    },
  }));

  return (
    <div className="flex flex-col h-full">
      {header && <header className="flex-none">{header}</header>}
      <div className="flex-1 overflow-auto" ref={scrollElmRef}>
        {children}
      </div>
      {footer && <footer className="flex-none">{footer}</footer>}
    </div>
  );
});

export default PageContainer;
