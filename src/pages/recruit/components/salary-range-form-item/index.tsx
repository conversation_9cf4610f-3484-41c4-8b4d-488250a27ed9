import './index.scss';
import { FC } from 'react';
import { Form, FormItemProps, Input } from 'antd-mobile';

export interface SalaryRangeFormItemProps {
  name?: FormItemProps['name'];
  label: FormItemProps['label'];
  rules?: FormItemProps['rules'];
  startName: FormItemProps['name'];
  endName: FormItemProps['name'];
  form?: any;
}
const SalaryRangeFormItem: FC<SalaryRangeFormItemProps> = ({ name, label, startName, endName, rules, form }) => {
  return (
    <Form.Item className="flex salary-range" name={name} label={label} rules={rules} childElementPosition="right">
      <Form.Item noStyle name={startName}>
        <Input
          placeholder="请输入"
          className="max-w-20"
          type="number"
          pattern="[0-9]*"
          inputMode={'numeric'}
          min={0}
          max={999999}
          onBlur={() => {
            const value = form?.getFieldValue(startName);
            value && form?.setFieldValue(startName, Math.round(value));
          }}
        />
      </Form.Item>
      <span className="mx-2">~</span>
      <Form.Item noStyle name={endName}>
        <Input
          placeholder="请输入"
          className="max-w-20"
          type="number"
          pattern="[0-9]*"
          inputMode={'numeric'}
          min={0}
          max={999999}
          onBlur={() => {
            const value = form?.getFieldValue(endName);
            value && form?.setFieldValue(endName, Math.round(value));
          }}
        />
      </Form.Item>
    </Form.Item>
  );
};

export default SalaryRangeFormItem;
