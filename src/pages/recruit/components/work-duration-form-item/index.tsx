import { FC, RefObject, useState } from 'react';
import { Form, FormItemProps, Picker, PickerProps, PickerRef } from 'antd-mobile';

export interface AgeRangeFormItemProps extends Omit<PickerProps, 'columns'> {
  name?: FormItemProps['name'];
  label?: FormItemProps['label'];
  rules?: FormItemProps['rules'];
  form?: any;
}

const hourNums = Array.from({ length: 23 }).map((_, index) => {
  const hour = index + 1;
  return {
    label: hour?.toString(),
    value: hour?.toString(),
  };
});
const WorkDurationFormItem: FC<AgeRangeFormItemProps> = ({ name, label, rules, form }) => {
  const [pickerValue, setPickerValue] = useState<string[]>([]);

  return (
    <Form.Item
      name={name}
      label={label}
      trigger="onConfirm"
      rules={rules}
      onClick={(_, pickerRef: RefObject<PickerRef>) => {
        pickerRef.current?.open();
      }}
    >
      <Picker
        // onCancel={() => {
        //   setPickerValue(form?.getFieldValue(name));
        // }}
        columns={[
          hourNums.slice(0, 22),
          [{ label: '~', value: '~' }],
          pickerValue?.[0] ? hourNums.filter(({ value }) => +value > +pickerValue?.[0]) : hourNums,
        ]}
        onSelect={(value: any[]) => {
          setPickerValue(value);
        }}
      >
        {() => {
          return form?.getFieldValue(name)?.join('');
          // return items?.[0] && items?.[2] && `${items?.[0]?.label} ~ ${items?.[2]?.label}`;
        }}
      </Picker>
    </Form.Item>
  );
};

export default WorkDurationFormItem;
