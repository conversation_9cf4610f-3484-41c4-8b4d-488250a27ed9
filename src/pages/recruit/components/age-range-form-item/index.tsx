import { FC, RefObject, useState } from 'react';
import { Form, FormItemProps, Picker, PickerProps, PickerRef } from 'antd-mobile';

export interface AgeRangeFormItemProps extends Omit<PickerProps, 'columns'> {
  name?: FormItemProps['name'];
  label?: FormItemProps['label'];
  rules?: FormItemProps['rules'];
  form?: any;
}

const ageNums = Array.from({ length: 99 }).map((_, index) => {
  const age = index + 1;
  return {
    label: age?.toString(),
    value: age?.toString(),
  };
});
const AgeRangeFormItem: FC<AgeRangeFormItemProps> = ({ name, label, rules, form }) => {
  const [pickerValue, setPickerValue] = useState<string[]>([]);

  return (
    <Form.Item
      name={name}
      label={label}
      trigger="onConfirm"
      rules={rules}
      onClick={(_, pickerRef: RefObject<PickerRef>) => {
        pickerRef.current?.open();
      }}
    >
      <Picker
        columns={[
          ageNums?.slice(0, 98),
          [{ label: '~', value: '~' }],
          pickerValue?.[0] ? ageNums.filter(({ value }) => +value > +pickerValue?.[0]) : ageNums,
        ]}
        onSelect={(value: any[]) => {
          setPickerValue(value);
        }}
      >
        {(items) => {
          return form?.getFieldValue(name)?.join('');
          // return items?.[0] && items?.[2] && `${items?.[0]?.label} ~ ${items?.[2]?.label}`;
        }}
      </Picker>
    </Form.Item>
  );
};

export default AgeRangeFormItem;
