import { FC } from 'react';
import { GenderType, GenderTypeCN, JobType, JobTypeCN } from '@src/common/interview';
import {
  RecruitDemandStatus,
  RecruitDemandStatusCN,
  RecruitSalaryType,
  RecruitSalaryTypeCN,
} from '@src/common/recruit';
import { ShopStatus, ShopStatusCN } from '@src/pages/tasks/enum';
import { Button } from 'antd-mobile';
import classNames from 'classnames';
import dayjs from 'dayjs';

export interface RecruitDemandCardProps {
  data: {
    id: number;
    status: RecruitDemandStatus;
    jobType: JobType;
    fullTime: boolean;
    requireNum: number;
    shopId: string;
    shopName: string;
    gender?: GenderType;
    salaryType?: RecruitSalaryType;
    hourSalaryStart?: string;
    hourSalaryEnd?: string;
    monthSalaryMin?: string;
    monthSalaryMax?: string;
    ageRange?: string;
    createAt?: string;
    shopStatus: string;
    auditUserId?: number;
    auditUserName?: string;
  };
  cancelLoading?: boolean;
  deleteLoading?: boolean;
  revokeLoading?: boolean;
  onEdit?: (data: any) => void;
  onCancel?: (data: any) => void;
  onClick?: () => void;
  onAudit?: (data: any) => void;
  onDelete?: (data: any) => void;
  onComplete?: (data: any) => void;
  onSuspend?: (data: any) => void;
  onRevoke?: (data: any) => void;
  isDudao: boolean;
  userId?: number;
  readonly?: boolean;
}

const RecruitDemandCard: FC<RecruitDemandCardProps> = ({
  data,
  onEdit,
  onCancel,
  onClick,
  onRevoke,
  cancelLoading,
  deleteLoading,
  revokeLoading,
  isDudao,
  onAudit,
  onDelete,
  onComplete,
  onSuspend,
  userId,
  readonly,
}) => {
  const {
    jobType,
    fullTime,
    status,
    requireNum,
    shopId,
    shopName,
    createAt,
    gender,
    salaryType,
    hourSalaryStart,
    hourSalaryEnd,
    monthSalaryMin,
    monthSalaryMax,
    ageRange,
    shopStatus,
    auditUserId,
    auditUserName,
  } = data;

  return (
    <div className="bg-white rounded-[8px] px-3 pt-2 pb-4" onClick={onClick}>
      <div className="flex justify-between text-base">
        <span className="font-bold">
          {JobTypeCN[jobType]}
          <span className="mr-3 ml-1">({requireNum}人)</span>
          {typeof fullTime === 'boolean' ? (fullTime ? '全职' : '兼职') : ''}
        </span>
        <span className="text-[#00BBB4]">{RecruitDemandStatusCN[status]}</span>
      </div>
      <div className="my-2 py-2 px-3 rounded-[8px] bg-[#FAFAFA] font-normal text-sm leading-[22px]">
        <p className="mb-1">
          {shopId} {shopName}
        </p>
        {shopStatus && <div className="mb-1">{ShopStatusCN?.[shopStatus as ShopStatus]}</div>}
        {(salaryType || ageRange || (gender !== null && gender !== undefined)) && (
          <div className="mb-1">
            {(salaryType === RecruitSalaryType.HOUR || salaryType === RecruitSalaryType.MONTH) && (
              <span className="mr-3">
                <span className="mr-1">{RecruitSalaryTypeCN[salaryType]}:</span>
                {salaryType === RecruitSalaryType.HOUR
                  ? hourSalaryStart && hourSalaryEnd
                    ? `${hourSalaryStart}~${hourSalaryEnd}`
                    : ''
                  : monthSalaryMin && monthSalaryMax
                    ? `${monthSalaryMin}~${monthSalaryMax}`
                    : ''}
              </span>
            )}

            {ageRange && (
              <span className="mr-3">
                <span className="mr-1">年龄:</span>
                {ageRange}
              </span>
            )}
            {gender !== null && gender !== undefined && (
              <span>
                <span className="mr-1">性别:</span>
                {GenderTypeCN[gender]}
              </span>
            )}
          </div>
        )}
        {status === RecruitDemandStatus.AUDITING && auditUserName && (
          <div className="mb-1">
            <span className="mr-2">当前审核人:</span>
            <span>{auditUserName}</span>
          </div>
        )}
        {createAt && (
          <div className="mb-1">
            <span className="mr-2">创建时间:</span>
            <span>{dayjs(createAt).format('YYYY-MM-DD HH:mm:ss')}</span>
            {/* {!!adviceRequireNum && (
              <Tag className="ml-2" fill="outline">
                自动创建
              </Tag>
            )} */}
          </div>
        )}
      </div>
      {!readonly && (
        <div className="flex w-full gap-x-3">
          {[RecruitDemandStatus.DRAFT].includes(status) && (
            <Button
              className="flex-1 rounded border-[#DCDCDC] h-8 text-sm leading-[14px]"
              loading={deleteLoading}
              onClick={(e) => {
                e?.preventDefault();
                e?.stopPropagation();
                onDelete?.(data);
              }}
            >
              删除
            </Button>
          )}
          {!isDudao && [RecruitDemandStatus.REJECT, RecruitDemandStatus.REVOKE].includes(status) && (
            <Button
              className={classNames('flex-1 rounded border-[#DCDCDC] h-8 text-sm leading-[14px]')}
              loading={cancelLoading}
              onClick={(e) => {
                e?.preventDefault();
                e?.stopPropagation();
                onCancel?.(data);
              }}
            >
              撤销
            </Button>
          )}
          {isDudao &&
            userId === auditUserId &&
            [RecruitDemandStatus.AUDITING, RecruitDemandStatus.REJECT].includes(status) && (
              <Button
                className={classNames('flex-1 rounded border-[#DCDCDC] h-8 text-sm leading-[14px]')}
                loading={cancelLoading}
                onClick={(e) => {
                  e?.preventDefault();
                  e?.stopPropagation();
                  onAudit?.(data);
                }}
              >
                审核
              </Button>
            )}
          {[RecruitDemandStatus.DRAFT, RecruitDemandStatus.REJECT, RecruitDemandStatus.REVOKE].includes(status) && (
            <Button
              className="flex-1 rounded border-[#DCDCDC] h-8 text-sm leading-[14px]"
              onClick={(e) => {
                e?.preventDefault();
                e?.stopPropagation();
                onEdit?.(data);
              }}
            >
              编辑
            </Button>
          )}

          {!isDudao && [RecruitDemandStatus.RECRUITING].includes(status) && (
            <Button
              className="flex-1 rounded border-[#DCDCDC] h-8 text-sm leading-[14px]"
              onClick={(e) => {
                e?.preventDefault();
                e?.stopPropagation();
                onSuspend?.(data);
              }}
            >
              中止
            </Button>
          )}
          {!isDudao && [RecruitDemandStatus.RECRUITING].includes(status) && (
            <Button
              className="flex-1 rounded border-[#DCDCDC] h-8 text-sm leading-[14px]"
              onClick={(e) => {
                e?.preventDefault();
                e?.stopPropagation();
                onComplete?.(data);
              }}
            >
              已完成
            </Button>
          )}
          {!isDudao && [RecruitDemandStatus.AUDITING, RecruitDemandStatus.RECRUIT_CONFIRM].includes(status) && (
            <Button
              className={classNames('flex-1 rounded border-[#DCDCDC] h-8 text-sm leading-[14px]')}
              loading={revokeLoading}
              onClick={(e) => {
                e?.preventDefault();
                e?.stopPropagation();
                onRevoke?.(data);
              }}
            >
              撤回
            </Button>
          )}
        </div>
      )}
    </div>
  );
};

export default RecruitDemandCard;
