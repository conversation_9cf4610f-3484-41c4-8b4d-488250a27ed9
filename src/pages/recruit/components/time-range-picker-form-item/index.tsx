import { FC, RefObject, useState } from 'react';
import { Form, FormItemProps, Picker, PickerProps, PickerRef } from 'antd-mobile';
import numeral from 'numeral';

const hours = Array.from({ length: 24 }).map((_, index) => ({
  label: numeral(index).format('00'),
  value: numeral(index).format('00'),
}));
const minutes = Array.from({ length: 60 }).map((_, index) => ({
  label: numeral(index).format('00'),
  value: numeral(index).format('00'),
}));

export interface TimeRangePickerProps extends Omit<PickerProps, 'columns'> {
  name?: FormItemProps['name'];
  label?: FormItemProps['label'];
  rules?: FormItemProps['rules'];
  allowSpan?: boolean;
  form?: any;
}

const TimeRangePickerFormItem: FC<TimeRangePickerProps> = ({ name, label, rules, allowSpan, form, ...props }) => {
  const [pickerValue, setPickerValue] = useState<string[]>([]);

  return (
    <Form.Item
      name={name}
      label={label}
      trigger="onConfirm"
      rules={rules}
      onClick={(_, pickerRef: RefObject<PickerRef>) => {
        pickerRef.current?.open();
      }}
    >
      <Picker
        {...props}
        // onCancel={() => {
        //   setPickerValue(form?.getFieldValue(name));
        // }}
        columns={[
          hours,
          [{ label: ':', value: ':' }],
          !allowSpan && pickerValue?.[0] === '23' ? minutes.slice(0, 59) : minutes,
          [{ label: '~', value: '~' }],
          !allowSpan && pickerValue?.[0] ? hours.filter(({ value }) => value >= pickerValue?.[0]) : hours,
          [{ label: ':', value: ':' }],
          !allowSpan && pickerValue?.[0] === pickerValue?.[4] && pickerValue?.[2]
            ? minutes.filter(({ value }) => value > pickerValue?.[2])
            : minutes,
        ]}
        onSelect={(value: any[]) => {
          setPickerValue(value);
        }}
      >
        {(items) => {
          return form?.getFieldValue(name)?.join('');
          // return items?.[0] && items?.[2] && items?.[4] && items?.[6]
          //   ? `${items?.[0]?.label}:${items?.[2]?.label}~${items?.[4]?.label}:${items?.[6]?.label}`
          //   : undefined;
        }}
      </Picker>
    </Form.Item>
  );
};

export default TimeRangePickerFormItem;
