import AuthorityEnum from '@src/common/authority';
import { report } from '@src/common/report';
import { userStore } from '@src/store';
import { roleTypeIsManage } from '@src/utils/tokenUtils';
import { RightOutline } from 'antd-mobile-icons';
import { observer } from 'mobx-react';
import { useNavigate } from 'react-router-dom';
const Plan = observer(() => {
  const navigate = useNavigate();

  return (
    <div>
      {((roleTypeIsManage() &&
        userStore.permissionsMap.has(AuthorityEnum.督导创建常规任务)) ||
        (!roleTypeIsManage() &&
          userStore.permissionsMap.has(AuthorityEnum.店长创建常规任务))) && (
        <div
          className="rounded-md bg-white flex justify-between items-center overflow-hidden mx-4 mt-4 p-4"
          onClick={() => {
            report({
              type: 'ability',
              page: '创建任务',
              abilityButton: '创建任务-常规任务',
            });
            navigate('/tasks/create');
          }}
        >
          <div className="font-semibold text-17 mb-1">常规任务</div>
          <RightOutline />
        </div>
      )}
      {roleTypeIsManage() &&
        userStore.permissionsMap.has(AuthorityEnum.督导计划外到店巡检任务) && (
          <div
            className="rounded-md bg-white flex justify-between items-center overflow-hidden mx-4 mt-3 p-4"
            onClick={() => {
              report({
                type: 'ability',
                page: '创建任务',
                abilityButton: '创建任务-到店巡检计划外任务',
              });
              navigate('/tasks/patrol/unplanned');
            }}
          >
            <div className="font-semibold text-17 mb-1">到店巡检计划外任务</div>
            <RightOutline />
          </div>
        )}
      {roleTypeIsManage() &&
        userStore.permissionsMap.has(AuthorityEnum.督导稽核任务) && (
          <div
            className="rounded-md bg-white flex justify-between items-center overflow-hidden mx-4 mt-3 p-4"
            onClick={() => {
              report({
                type: 'ability',
                page: '创建任务',
                abilityButton: '创建任务-稽核任务',
              });
              navigate('/tasks/patrol/unplanned?subType=FOOD_SAFETY_NORMAL');
            }}
          >
            <div className="font-semibold text-17 mb-1">稽核任务</div>
            <RightOutline />
          </div>
        )}
    </div>
  );
});

export default Plan;
