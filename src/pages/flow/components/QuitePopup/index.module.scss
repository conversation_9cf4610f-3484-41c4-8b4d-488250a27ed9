@import "../../common.scss";

.confirm__popup {
  :global {
    .adm-center-popup-wrap {
      max-width: pxToVw(320);
    }
    .adm-text-area-element {
      margin: 0 auto pxToVw(8);
      width: pxToVw(272);
      height: pxToVw(120);
      background: #f7f8fa;
      border: pxToVw(1) solid #e5e6eb;
      padding: pxToVw(4) pxToVw(8) 0;
    }
    textarea::-webkit-input-placeholder {
      font-size: pxToVw(13);
      color: #86909c;
    }
  }
  .confirm__popup__title {
    width: pxToVw(320);
    font-size: pxToVw(16);
    font-weight: 500;
    line-height: pxToVw(24);
    text-align: center;
    color: #1d2129;
    margin-bottom: pxToVw(8);
  }

  .confirm__popup__subTitle {
    font-size: pxToVw(16);
    font-weight: normal;
    line-height: pxToVw(24);
    color: #4e5969;
    margin-bottom: pxToVw(8);
  }

  .confirm__popup__content {
    font-size: pxToVw(16);
    font-weight: normal;
    line-height: pxToVw(24);
    text-align: center;
    display: flex;
    align-items: center;
    color: #4e5969;
  }

  .bottom__bar {
    margin-top: pxToVw(25);
    border-top: pxToVw(0.5) solid #e5e6eb;
    width: 100%;
    height: pxToVw(56);
    display: flex;
    justify-content: center;

    &__cancel {
      flex: 1;
      font-size: pxToVw(16);
      display: flex;
      justify-content: center;
      align-items: center;
      color: #1d2129;
    }
    &__line {
      height: pxToVw(56);
      width: pxToVw(0.5);
      background: #e5e6eb;
    }
    &__confirm {
      @extend .bottom__bar__cancel;
      color: #3491fa;
    }
  }
}
