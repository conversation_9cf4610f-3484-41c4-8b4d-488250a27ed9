import { ReactNode } from 'react';
import { CenterPopup, TextArea, Toast } from 'antd-mobile';
import styles from './index.module.scss';
import { PlanState } from '../../types';

export default function ConfirmPopup(props: {
  visible: boolean;
  setVisible: (visible: boolean) => void;
  onConfirmClick?: ({ type }: { type: PlanState | undefined }) => void;
  onCancelClick?: () => void;
  title?: ReactNode | string;
  subTitle?: ReactNode | string;
  description?: ReactNode | string;
  cancelText?: string;
  confirmText?: string;
  value?: string;
  setValue: (value: string) => void;
  type?: PlanState;
  [props: string]: any;
}) {
  const {
    visible,
    setVisible,
    onConfirmClick,
    onCancelClick,
    title,
    subTitle,
    description,
    cancelText = '取消',
    confirmText = '确认',
    value,
    setValue,
    type,
    ...restProp
  } = props;

  return (
    <CenterPopup
      onClose={() => setVisible(false)}
      getContainer={document.querySelector('body')}
      closeOnMaskClick={false}
      visible={visible}
      className={styles.confirm__popup}
      {...restProp}
    >
      <div className="flex flex-col items-center px-6 pt-6 w-80">
        {title && (typeof title === 'string' ? <div className={styles.confirm__popup__title}>{title}</div> : title)}
        {subTitle &&
          (typeof subTitle === 'string' ? <div className={styles.confirm__popup__subTitle}>{subTitle}</div> : subTitle)}
        <TextArea
          placeholder="输入您退出的原因～"
          value={value}
          onChange={(val) => {
            setValue(val);
          }}
        />
        {description &&
          (typeof description === 'string' ? (
            <div className={styles.confirm__popup__content}>{description}</div>
          ) : (
            description
          ))}
      </div>
      <div className={styles.bottom__bar}>
        <div
          onClick={() => {
            onCancelClick?.();
            setVisible(false);
          }}
          className={styles.bottom__bar__cancel}
        >
          {cancelText}
        </div>
        <div className={styles.bottom__bar__line} />
        <div
          onClick={() => {
            if (!value?.trim()) {
              Toast.show({
                content: '请输入退出原因',
              });
              return;
            }
            onConfirmClick?.({ type });
            setVisible(false);
          }}
          className={styles.bottom__bar__confirm}
        >
          {confirmText}
        </div>
      </div>
    </CenterPopup>
  );
}
