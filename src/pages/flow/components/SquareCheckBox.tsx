import { memo } from 'react';
import CheckRectangleFilled from '@src/assets/images/flow/check-rectangle-filled.png';
import { Checkbox, CheckboxProps, Image } from 'antd-mobile';

function SquareCheckBox(props: Omit<CheckboxProps, 'icon'>) {
  return (
    <Checkbox
      {...props}
      icon={(checked) => {
        if (checked) {
          return <Image width={24} src={CheckRectangleFilled} />;
        }
        return <div className="size-[1.125rem] border border-[#C9CDD4] rounded-sm mx-1" />;
      }}
    />
  );
}

const MemoSquareCheckBox = memo(SquareCheckBox);
export default MemoSquareCheckBox;
