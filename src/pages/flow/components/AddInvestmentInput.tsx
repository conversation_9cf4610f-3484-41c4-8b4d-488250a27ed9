import { memo, useMemo, useState } from 'react';
import { Input } from 'antd-mobile';
import { amountPattern, formatAmount } from '../utils';

const AddInvestmentInput = ({
  amount,
  onChange,
  initAmount,
  disabled,
  rate,
  ...props
}: {
  amount: string;
  onChange: (value: string) => void;
  initAmount: number;
  disabled: boolean;
  rate: number;
  [props: string]: any;
}) => {
  const [value, setValue] = useState(amount);
  const amountColor = useMemo(() => {
    if (disabled) return '#4E5969';
    return +value > initAmount * rate ? '#F53F3F' : '#3491fa';
  }, [value, initAmount, rate, disabled]);

  return (
    <Input
      style={{
        '--text-align': 'right',
        '--color': amountColor,
        '--font-size': '20px',
        ...props.style,
      }}
      className={`text-xl font-medium flex-1`}
      placeholder="请输入"
      value={value}
      onChange={(e) => {
        const value = formatAmount(e);
        setValue(String(value));
      }}
      onBlur={() => {
        if (String(value) !== String(amount)) {
          onChange(String(value));
        }
      }}
      type="number"
      min={0}
      pattern={amountPattern}
      inputMode={'decimal'}
      disabled={disabled}
      {...props}
    />
  );
};
const MemoInput = memo(AddInvestmentInput);
export default MemoInput;
