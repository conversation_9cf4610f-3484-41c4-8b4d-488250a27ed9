import { useMemo, useState } from 'react';
import { IconFont, Loading } from '@src/components';
import { Popup, Toast } from 'antd-mobile';
import { useAvailableDate } from '../../hooks';
import type { AddIntentItem } from '../../types';
import { defaultRate } from '../../utils';
import AddInvestmentInput from '../AddInvestmentInput';
import AddInvestmentValue from '../AddInvestmentValue';
import ConfirmPopup from '../ConfirmPopup/index';
import FlowSwitch from '../Switch';
export default function BatchEditAddPromotionPopup({
  visible,
  setVisible,
  data,
  onConfirmClick,
  loading,
}: {
  visible: boolean;
  setVisible: (visible: boolean) => void;
  data: AddIntentItem[];
  onConfirmClick?: (data: AddIntentItem) => void;
  loading: boolean;
}) {
  const [confirmVisible, setConfirmVisible] = useState(false);
  const { availableDate } = useAvailableDate();

  const [shop, setShop] = useState<AddIntentItem>(data[0]);
  const shops = useMemo(() => {
    return data.map((item) => item.shopName + item.shopId).join('、');
  }, [data]);
  return (
    <>
      <Popup
        onClose={() => setVisible(false)}
        closeOnSwipe
        closeOnMaskClick={false}
        showCloseButton
        visible={visible}
        bodyStyle={{
          borderTopLeftRadius: '12px',
          borderTopRightRadius: '12px',
          minHeight: '40vh',
          padding: '0 12px',
        }}
        getContainer={document.querySelector('body')}
      >
        <Loading spinning={loading}>
          <div className="relative flex items-center mt-4">
            <div className="text-xs leading-5 text-[#4e5969]">单位：元</div>
            <div className="absolute left-1/2 translate-x-[-50%]	text-base leading-6 text-[#141414] font-medium">
              批量修改
            </div>
          </div>
          <div className={`text-[13px] leading-[22px] h-[22px] text-[#4E5969] flex items-center mb-1  mt-4`}>
            <IconFont type="icon-home" className="mr-1 text-base" />
            选中门店
          </div>
          <div className={`flex justify-between items-center`}>
            <div className="text-sm font-medium leading-[22px] flex items-center text-[#1D2129]">{shops}</div>
          </div>
          <div className="h-[1px] bg-[#F2F3F5] w-full my-4" />
          <div className="flex items-center justify-between">
            <div className={`text-[13px] leading-[22px] h-[22px] text-[#4E5969] flex items-center`}>
              <IconFont type="icon-notification" className="mr-1 text-base" />
              确认启用加投
            </div>
            <FlowSwitch
              checked={shop.joinFlag}
              onChange={(check) => setShop((prevState) => ({ ...prevState, joinFlag: check }))}
            />
          </div>
          <div className="h-[1px] bg-[#F2F3F5] w-full my-4" />
          <AddInvestmentValue className="mb-2" />
          <div className="overflow-y-scroll max-h-44">
            {shop.shopAddIntentDtos.map((item, index) => {
              const { id, amount, date, initAmount } = item;
              const disabled = new Date(availableDate).getTime() > new Date(date).getTime();

              return (
                <div
                  key={id}
                  className={`${disabled || !shop.joinFlag ? 'h-9' : 'h-12 bg-[#F7F8FA]'}  w-full rounded-lg flex items-center justify-between px-4 mb-2`}
                >
                  <div className={`${disabled ? 'line-through' : ''}  text-base leading-6 text-[#4E5969]`}>{date}</div>
                  {disabled || !shop.joinFlag ? (
                    <div className="text-base font-medium text-[#1D2129]">{item.amount}</div>
                  ) : (
                    <AddInvestmentInput
                      amount={String(amount)}
                      onChange={(amount) => {
                        const { shopAddIntentDtos } = shop;
                        shopAddIntentDtos[index].amount = +amount;
                        setShop((prevState) => ({
                          ...prevState,
                          shopAddIntentDtos,
                        }));
                      }}
                      disabled={disabled}
                      initAmount={initAmount}
                      rate={shop.floatRange ? shop.floatRange + 1 : defaultRate}
                    />
                  )}
                </div>
              );
            })}
          </div>
          <div className="h-[1px] bg-[#F2F3F5] w-full mb-4" />

          <div className="text-xs leading-5 text-[#1d2129] ml-3 mb-9">
            <div className="text-[#4E5969] text-xs leading-5">
              <div>·当日20点前，修改加投计划，T+1 生效</div>
              <div>·当日20点后，修改加投计划，T+2 生效</div>
            </div>
          </div>
          <div className="gap-x-[11px] mt-6 w-full flex justify-between">
            <div
              onClick={() => {
                setVisible(false);
              }}
              className="flex-1 h-10 rounded-[4px] flex justify-center items-center bg-white border-[0.5px] border-[#c9cdd4] text-base font-medium leading-6 text-[#4e5969]"
            >
              取消
            </div>
            <div
              onClick={() => {
                const rate = shop.floatRange ? shop.floatRange + 1 : defaultRate;
                let errorMsg = '';
                const valid = shop.shopAddIntentDtos.some((item) => {
                  const maxAmount = +(item.initAmount * rate).toFixed(2);
                  if (item.amount > maxAmount) {
                    errorMsg = `${item.date} 加投金额不能超过${maxAmount}元`;
                    return true;
                  }
                  return false;
                });
                if (valid) {
                  Toast.show(errorMsg);
                } else {
                  setConfirmVisible(true);
                }
              }}
              className="flex-1 h-10 rounded-[4px] flex justify-center items-center bg-[#3491fa] text-base font-medium leading-6 text-white"
            >
              确认
            </div>
          </div>
        </Loading>
      </Popup>
      <ConfirmPopup
        title="参与加投计划确认"
        description="您是否要参与本月后续的加投计划？"
        visible={confirmVisible}
        setVisible={setConfirmVisible}
        onConfirmClick={() => {
          onConfirmClick?.(shop);
          setVisible(false);
        }}
        style={{
          '--z-index': '2000',
        }}
      />
    </>
  );
}
