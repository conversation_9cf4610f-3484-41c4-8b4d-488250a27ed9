.tabsContainer {
  display: flex;
  width: 100%;
  background-color: #fff;
  height: 44px;
  border-radius: 8px 8px 0 0;
  overflow: hidden;
  box-shadow: 0 1px 0 0 rgba(0, 0, 0, 0.05);
}

.tab {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  cursor: pointer;
}

.tabText {
  font-size: 16px;
  color: #86909c;
  text-align: center;
}

.activeTab {
  .tabText {
    color: #1d2129;
    font-weight: 500;
  }
}

.activeIndicator {
  position: absolute;
  bottom: 0;
  width: 40px;
  height: 3px;
  background-color: #3491fa;
  border-radius: 1.5px 1.5px 0 0;
}
