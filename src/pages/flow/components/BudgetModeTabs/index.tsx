import React from 'react';
import styles from './index.module.scss';

interface BudgetModeTabsProps {
  activeTab: 'average' | 'dynamic';
  onChange: (tab: 'average' | 'dynamic') => void;
}

const BudgetModeTabs: React.FC<BudgetModeTabsProps> = ({ activeTab, onChange }) => {
  return (
    <div className={styles.tabsContainer}>
      <div
        className={`${styles.tab} ${activeTab === 'average' ? styles.activeTab : ''}`}
        onClick={() => onChange('average')}
      >
        <span className={styles.tabText}>平均预算</span>
        {activeTab === 'average' && <div className={styles.activeIndicator} />}
      </div>
      <div
        className={`${styles.tab} ${activeTab === 'dynamic' ? styles.activeTab : ''}`}
        onClick={() => onChange('dynamic')}
      >
        <span className={styles.tabText}>动态预算</span>
        {activeTab === 'dynamic' && <div className={styles.activeIndicator} />}
      </div>
    </div>
  );
};

export default BudgetModeTabs;
