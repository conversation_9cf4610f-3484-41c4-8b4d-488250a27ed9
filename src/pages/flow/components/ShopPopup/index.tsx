import { useState } from 'react';
import closeIcon from '@src/assets/images/flow/close.png';
import { Image, PickerView, Popup } from 'antd-mobile';
import { basicColumns } from './Enum';
import styles from './index.module.scss';
export default function ShopPopup({
  visible,
  setVisible,
  setJoinFlag,
}: {
  visible: boolean;
  setVisible: (visible: boolean) => void;
  setJoinFlag: (v: number) => void;
}) {
  const [value, setValue] = useState<(string | null)[]>(['1']);
  return (
    <>
      <Popup
        visible={visible}
        onMaskClick={() => {
          setVisible(false);
        }}
        onClose={() => {
          setVisible(false);
        }}
        bodyStyle={{
          borderTopLeftRadius: '12px',
          borderTopRightRadius: '12px',
          minHeight: '38vh',
          padding: '0 12px',
        }}
      >
        <div>
          <div className="flex justify-center items-center mt-[16px]">
            <div className="text-center">
              <span style={{ fontSize: '16px', fontWeight: 500, lineHeight: '24px' }}>门店筛选</span>
              <Image
                className="absolute top-[18px] right-[16px] "
                src={closeIcon}
                width={20}
                height={20}
                onClick={() => setVisible(false)}
              />
            </div>
          </div>
          <PickerView
            columns={basicColumns}
            // value={value}
            onChange={(val: any) => {
              setValue(val[0]);
            }}
            style={{ '--height': '165px', '--item-height': '2.8rem' }}
          />
          <div className={styles.bottom__bar}>
            <div
              onClick={() => {
                setVisible(false);
              }}
              className={styles.bottom__bar__cancel}
            >
              取消
            </div>
            <div
              onClick={() => {
                setVisible(false);
                setJoinFlag(Number(value));
              }}
              className={styles.bottom__bar__confirm}
            >
              确认
            </div>
          </div>
        </div>
      </Popup>
    </>
  );
}
