import { useState } from 'react';
import closeIcon from '@src/assets/images/flow/close.png';
import { DatePickerView, Image, Popup } from 'antd-mobile';
import dayjs from 'dayjs';
import styles from './index.module.scss';

const MonthComponent = ({ setValue, initDate }: { setValue: (p: any) => void; initDate: string }) => {
  const labelRenderer = (type: string, data: number) => {
    switch (type) {
      case 'year':
        return `${data}年`;
      case 'month':
        return `${data}月`;
      case 'day':
        return `${data}日`;
      case 'hour':
        return `${data}时`;
      case 'minute':
        return `${data}分`;
      case 'second':
        return `${data}秒`;
      default:
        return data;
    }
  };
  return (
    <DatePickerView
      // {...rest}
      defaultValue={dayjs(initDate).toDate()}
      precision="month"
      renderLabel={labelRenderer}
      onChange={(val) => {
        const formattedDate = dayjs(val).format('YYYY-MM');
        setValue(formattedDate);
      }}
    />
  );
};
export default function DatePopup({
  visible,
  setVisible,
  setParams,
  initDate,
}: {
  visible: boolean;
  setVisible: (visible: boolean) => void;
  setParams: (p: any) => void;
  initDate: string;
}) {
  const [value, setValue] = useState<(string | null)[]>(['1']);
  return (
    <>
      <Popup
        visible={visible}
        onMaskClick={() => {
          setVisible(false);
        }}
        onClose={() => {
          setVisible(false);
        }}
        bodyStyle={{ borderTopLeftRadius: '12px', borderTopRightRadius: '12px', minHeight: '47vh', padding: '0 12px' }}
      >
        <div>
          <div className="flex justify-center items-center mt-4">
            <div className="text-center">
              <span style={{ fontSize: '16px', fontWeight: 600, padding: 10 }}>选择年份和月份</span>
              <Image
                className="absolute top-[18px] right-[16px] "
                src={closeIcon}
                width={20}
                height={20}
                onClick={() => setVisible(false)}
              />
            </div>
          </div>
          <MonthComponent setValue={setValue} initDate={initDate} />
          <div className={styles.bottom__bar}>
            <div
              onClick={() => {
                setVisible(false);
              }}
              className={styles.bottom__bar__cancel}
            >
              取消
            </div>
            <div
              onClick={() => {
                setParams((p: any) => ({ ...p, date: value }));
                setVisible(false);
              }}
              className={styles.bottom__bar__confirm}
            >
              确认
            </div>
          </div>
        </div>
      </Popup>
    </>
  );
}
