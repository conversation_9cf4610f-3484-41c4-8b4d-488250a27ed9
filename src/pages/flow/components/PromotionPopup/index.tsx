import { useMemo, useState } from 'react';
import { Loading } from '@src/components';
import { Input, Popup, Radio, Toast } from 'antd-mobile';
import styles from './index.module.scss';
import { BudgetMode, DynamicDataItem, DynamicDataList, ShopPromotionIntentDetail } from '../../types';
import { amountPattern, formatAmount } from '../../utils';
import CalendarInput from '../Calendar';
import ConfirmPopup from '../ConfirmPopup/index';

export default function PromotionPopup({
  visible,
  setVisible,
  data,
  value,
  // setValue,
  refreshPage,
  updatePlan,
  loading,
  title = '加入推广计划',
  dayDetail,
  dateMonth,
  updateDayDetail,
}: {
  visible: boolean;
  setVisible: (visible: boolean) => void;
  data: ShopPromotionIntentDetail;
  value: string;
  // setValue: (value: string) => void;
  refreshPage: () => Promise<any>;
  updatePlan: (params: any) => Promise<any>;
  loading: boolean;
  title: string;
  dayDetail?: DynamicDataList;
  dateMonth: string;
  updateDayDetail: () => Promise<any>;
}) {
  const [confirmVisible, setConfirmVisible] = useState(false);
  const [transPreM1, setTransPreM1] = useState(value);
  // 新增预算模式状态
  const [budgetMode, setBudgetMode] = useState<BudgetMode>(data.costType);
  // 日历数据状态
  const [dailyBudgets, setDailyBudgets] = useState<Record<string, DynamicDataItem>>(() => {
    const budgetMap: Record<string, DynamicDataItem> = {};
    (dayDetail?.yytDynamicDataList || []).forEach((item: DynamicDataItem) => {
      budgetMap[item.bidDate] = item;
    });
    return budgetMap;
  });

  // 计算动态预算总和
  const dynamicBudgetTotal = useMemo(() => {
    return Object.values(dailyBudgets).reduce((sum, item) => sum + Math.round(item.bidAmount * 100), 0) / 100;
  }, [dailyBudgets]);

  const [expanded, setExpanded] = useState(false);
  const amountColor = useMemo(() => {
    const { joinFlag, amtTransPreM1Max } = data;
    if (!joinFlag) return '#1D2129';
    if (budgetMode === BudgetMode.平均预算 && Number(transPreM1) > Number(amtTransPreM1Max)) return '#F53F3F';
    else return '#3491fa';
  }, [transPreM1, budgetMode]);

  const realTimePredictIncome = useMemo(() => {
    const budget = budgetMode === BudgetMode.平均预算 ? Number(transPreM1) : dynamicBudgetTotal;
    return (budget * Number(data.rateTraffic)).toFixed(2);
  }, [transPreM1, dynamicBudgetTotal, budgetMode]);

  // 处理日历每日预算变更
  const handleDailyBudgetChange = (date: string, value: DynamicDataItem) => {
    setDailyBudgets((prev) => ({
      ...prev,
      [date]: value,
    }));
  };
  const [year, month] = dateMonth.split('-');

  return (
    <>
      <Popup
        onClose={() => setVisible(false)}
        closeOnSwipe
        closeOnMaskClick={false}
        showCloseButton
        visible={visible}
        bodyStyle={{
          borderTopLeftRadius: '12px',
          borderTopRightRadius: '12px',
          minHeight: '40vh',
          maxHeight: '72vh',
          overflowY: 'auto',
          padding: '0 12px',
        }}
        getContainer={document.querySelector('body')}
      >
        <Loading spinning={loading}>
          <div className="flex relative items-center mt-4">
            <div className="text-xs leading-5 text-[#4e5969]">单位：元</div>
            <div className="absolute left-1/2 translate-x-[-50%]	text-base leading-6 text-[#141414] font-medium">
              {title}
            </div>
          </div>
          <div className={styles.head}>
            <div className={styles.head__plan}>
              <div className={styles.head__plan__icon} />
              {data.promotionName}
            </div>
          </div>

          {/* 新增预算模式切换 */}
          <div className={`${styles.content} ${styles.bottom__line}`}>
            <span className={styles.content__label}>预算模式</span>
            <div className={styles.budget_mode__options}>
              <Radio.Group value={budgetMode} onChange={(val) => setBudgetMode(val as BudgetMode)}>
                <div className={styles.radioGroup}>
                  <Radio value={BudgetMode.平均预算} className={styles.radio}>
                    平均
                  </Radio>
                  <Radio value={BudgetMode.动态预算} className={styles.radio}>
                    动态
                  </Radio>
                </div>
              </Radio.Group>
            </div>
          </div>

          <div className={`${styles.content} ${budgetMode === BudgetMode.动态预算 && styles.bottom__line}`}>
            <div className={styles.content__label}>建议推广金额 (当前)</div>
            <div className={styles.content__amount}>{data.amtTransPreM1}</div>
          </div>

          {budgetMode === BudgetMode.平均预算 ? (
            <div className={styles.promotion__amount}>
              <div className={styles.promotion__amount__label}>实际预算金额 (调整为)</div>
              <Input
                style={{
                  '--text-align': 'right',
                  '--color': amountColor,
                  '--font-size': '20px',
                  width: '150px',
                  fontWeight: 500,
                }}
                className={styles.promotion__amount__value}
                placeholder="请输入"
                value={transPreM1}
                onChange={(e) => {
                  const transPreM1 = formatAmount(e);
                  setTransPreM1(String(transPreM1));
                }}
                type="number"
                min={data.amtTransPreM1Min}
                pattern={amountPattern}
                inputMode={'decimal'}
              />
            </div>
          ) : (
            <>
              <div className={`${styles.content} ${styles.bottom__line}`}>
                <div className={styles.content__label}>实际预算金额 (调整为)</div>
                <div className={styles.content__amount}>{dynamicBudgetTotal}</div>
              </div>
            </>
          )}

          <div className={`${styles.content} ${styles.bottom__line}`}>
            <div className={styles.content__label}>投产比</div>
            <div className={styles.content__amount}>{data.rateTraffic}</div>
          </div>
          <div className={`${styles.content} ${styles.bottom__line}`}>
            <div className={styles.content__label}>预估推广收入</div>
            <div className={styles.content__amount}>{realTimePredictIncome}</div>
          </div>
          {/* 动态预算日历视图 */}
          {budgetMode === BudgetMode.动态预算 && (
            <div className={styles.calendar__container}>
              <CalendarInput
                values={dailyBudgets}
                onChange={handleDailyBudgetChange}
                year={+year}
                month={+month}
                minValue={data.amtTransPreM1DayMin || 0}
                expanded={expanded}
                onExpandChange={setExpanded}
              />
            </div>
          )}
          <div className={`${styles.desc} text-xs leading-5 text-[#1d2129] pl-3 pt-4 sticky bottom-16 bg-white`}>
            <div className="text-[#4E5969] text-xs leading-5">
              <div>·当日20点前，修改投流计划，T+1 生效</div>
              <div>·当日20点后，修改投流计划，T+2 生效</div>
            </div>
          </div>
          <div className={`${styles.bottom__bar} sticky bottom-16 bg-white`}>
            <div
              onClick={() => {
                setVisible(false);
              }}
              className={styles.bottom__bar__cancel}
            >
              取消
            </div>
            <div
              onClick={() => {
                if (budgetMode === BudgetMode.平均预算) {
                  if (Number(transPreM1) > data.amtTransPreM1Max) {
                    Toast.show(`门店${data.shopId}每月实际推广费用不能大于${data.amtTransPreM1Max}元`);
                    return;
                  }

                  if (Number(transPreM1) < data.amtTransPreM1Min) {
                    Toast.show(`门店${data.shopId}每月实际推广费用不能小于${data.amtTransPreM1Min}元`);
                    return;
                  }
                }
                setConfirmVisible(true);
              }}
              className={styles.bottom__bar__confirm}
            >
              确认
            </div>
          </div>
        </Loading>
      </Popup>
      <ConfirmPopup
        title="参与投流计划确认"
        description="您是否要参与本月后续的投流计划？"
        visible={confirmVisible}
        setVisible={setConfirmVisible}
        onCancelClick={() => {}}
        onConfirmClick={async () => {
          const finalBudget = budgetMode === BudgetMode.平均预算 ? Number(transPreM1) : dynamicBudgetTotal;

          await updatePlan({
            intentId: data.id,
            joinFlag: 1,
            transPreM1: finalBudget,
            optSource: 1,
            costType: budgetMode,
            ...(budgetMode === BudgetMode.动态预算
              ? {
                  dynamicDataList: Object.values(dailyBudgets).map((item) => {
                    return {
                      date: item.bidDate,
                      transPreM1: item.bidAmount,
                    };
                  }),
                }
              : {}),
          });
          Toast.show({
            icon: 'success',
            content: '操作成功',
            afterClose: async () => {
              await updateDayDetail();
              await refreshPage();
              setVisible(false);
            },
          });
        }}
        style={{
          '--z-index': '2000',
        }}
      />
    </>
  );
}
