import { memo } from 'react';
import { Switch, SwitchProps } from 'antd-mobile';

function FlowSwitch(props: SwitchProps) {
  const { style } = props;
  return (
    <Switch
      {...props}
      style={{
        '--height': '20px',
        '--width': '36px',
        '--checked-color': '#3491FA',
        ...style,
      }}
    />
  );
}
const MemoSwitch = memo(FlowSwitch);

export default MemoSwitch;
