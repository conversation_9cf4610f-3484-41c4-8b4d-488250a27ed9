import { useState } from 'react';
import closeIcon from '@src/assets/images/flow/close.png';
import { Image, PickerView, Popup } from 'antd-mobile';
import { PickerValue } from 'antd-mobile/es/components/picker-view';
import styles from './index.module.scss';

type Column = {
  label: string;
  value: string | number;
};
export default function BottomPopup({
  visible,
  setVisible,
  columns,
  title,
  onCancelClick,
  onConfirmClick,
}: {
  visible: boolean;
  setVisible: (visible: boolean) => void;
  columns: Column[][];
  title: string;
  onCancelClick?: () => void;
  onConfirmClick?: (value: PickerValue) => void;
}) {
  const [value, setValue] = useState<PickerValue>(columns[0][0]?.['value']);

  return (
    <Popup
      visible={visible}
      onMaskClick={() => {
        setVisible(false);
      }}
      onClose={() => {
        setVisible(false);
      }}
      bodyStyle={{
        borderTopLeftRadius: '12px',
        borderTopRightRadius: '12px',
        minHeight: '38vh',
        padding: '0 12px',
      }}
    >
      <div>
        <div className="flex justify-center items-center mt-[16px]">
          <div className="text-center">
            <span style={{ fontSize: '16px', fontWeight: 500, lineHeight: '24px' }}>{title}</span>
            <Image
              className="absolute top-[18px] right-[16px] "
              src={closeIcon}
              width={20}
              height={20}
              onClick={() => setVisible(false)}
            />
          </div>
        </div>
        <PickerView
          columns={columns}
          value={value}
          onChange={(val, extend) => {
            console.log('onChange', val, extend.items);
            setValue(val);
          }}
          style={{ '--height': '165px', '--item-height': '2.8rem' }}
        />
        <div className={styles.bottom__bar}>
          <div
            onClick={() => {
              onCancelClick?.();
              setVisible(false);
            }}
            className={styles.bottom__bar__cancel}
          >
            取消
          </div>
          <div
            onClick={() => {
              onConfirmClick?.(value?.[0]);
              setVisible(false);
            }}
            className={styles.bottom__bar__confirm}
          >
            确认
          </div>
        </div>
      </div>
    </Popup>
  );
}
