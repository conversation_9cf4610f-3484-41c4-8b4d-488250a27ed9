import { ReactNode } from 'react';
import { CenterPopup } from 'antd-mobile';
import styles from './index.module.scss';

export default function ConfirmPopup(props: {
  visible: boolean;
  setVisible: (visible: boolean) => void;
  onConfirmClick?: () => void;
  onCancelClick?: () => void;
  title?: ReactNode | string;
  subTitle?: ReactNode | string;
  description?: ReactNode | string;
  cancelText?: string;
  confirmText?: string;
  [props: string]: any;
}) {
  const {
    visible,
    setVisible,
    onConfirmClick,
    onCancelClick,
    title,
    subTitle,
    description,
    cancelText = '取消',
    confirmText = '确认',
    style,
    ...restProp
  } = props;
  return (
    <CenterPopup
      onClose={() => setVisible(false)}
      getContainer={document.querySelector('body')}
      closeOnMaskClick={false}
      visible={visible}
      bodyStyle={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
      }}
      className={styles.confirm__popup}
      style={{
        '--max-width': '320px',
        '--min-width': '320px',
        ...props.style,
      }}
      {...restProp}
    >
      <div className="flex flex-col items-center w-80">
        {title && (typeof title === 'string' ? <div className={styles.confirm__popup__title}>{title}</div> : title)}
        {subTitle &&
          (typeof subTitle === 'string' ? <div className={styles.confirm__popup__subTitle}>{subTitle}</div> : subTitle)}
        {description &&
          (typeof description === 'string' ? (
            <div className={styles.confirm__popup__content}>{description}</div>
          ) : (
            description
          ))}
        <div className={styles.bottom__bar}>
          <div
            onClick={() => {
              onCancelClick?.();
              setVisible(false);
            }}
            className={styles.bottom__bar__cancel}
          >
            {cancelText}
          </div>
          <div className={styles.bottom__bar__line} />
          <div
            onClick={() => {
              onConfirmClick?.();
              setVisible(false);
            }}
            className={styles.bottom__bar__confirm}
          >
            {confirmText}
          </div>
        </div>
      </div>
    </CenterPopup>
  );
}
