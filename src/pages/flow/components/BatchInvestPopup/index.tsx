import { useState } from 'react';
import { CenterPopup, Radio } from 'antd-mobile';
import styles from './index.module.scss';
import { AddIntentItem } from '../../types';

interface BatchInvestPopupProps {
  visible: boolean;
  setVisible: (visible: boolean) => void;
  selectedShops?: AddIntentItem[];
  onConfirm: (isInvest: boolean) => void;
  onCancel?: () => void;
  title?: string;
  description?: string;
  confirmText?: string;
  cancelText?: string;
  style?: React.CSSProperties;
}

export default function BatchInvestPopup({
  visible,
  setVisible,
  selectedShops = [],
  onConfirm,
  onCancel,
  title = '批量修改',
  confirmText = '确认',
  cancelText = '取消',
  style,
}: BatchInvestPopupProps) {
  const [isInvest, setIsInvest] = useState<boolean>(false);

  // 处理确认操作
  const handleConfirm = async () => {
    onConfirm(isInvest);
    setVisible(false);
  };

  // 处理取消操作
  const handleCancel = () => {
    onCancel?.();
    setVisible(false);
  };

  // 格式化显示门店列表
  const formatShopList = () => {
    if (!selectedShops || selectedShops.length === 0) return '';
    return selectedShops.map((shop) => `${shop.shopName}${shop.shopId}`).join('、');
  };

  return (
    <CenterPopup
      visible={visible}
      onClose={() => setVisible(false)}
      showCloseButton={false}
      getContainer={document.querySelector('body')}
      style={{
        '--max-width': '335px',
        '--min-width': '335px',
        '--border-radius': '12px',
        '--z-index': '1000',
        ...style,
      }}
    >
      <div className={styles.container}>
        <div className={styles.title}>{title}</div>

        {selectedShops.length > 0 && (
          <div className={styles.shopList}>
            <div className={styles.label}>选中门店：</div>
            <div className={styles.content}>{formatShopList()}</div>
            <div className={styles.divider} />
          </div>
        )}
        {
          <div className={styles.investOption}>
            <div className={styles.label}>是否加投</div>
            <Radio.Group value={isInvest ? 'yes' : 'no'} onChange={(val) => setIsInvest(val === 'yes')}>
              <div className={styles.radioGroup}>
                <Radio value="yes" className={styles.radio}>
                  是
                </Radio>
                <Radio value="no" className={styles.radio}>
                  否
                </Radio>
              </div>
            </Radio.Group>
          </div>
        }

        <div className={styles.footer}>
          <div className={styles.cancelButton} onClick={handleCancel}>
            {cancelText}
          </div>
          <div className={styles.footerLine} />
          <div className={styles.confirmButton} onClick={handleConfirm}>
            {confirmText}
          </div>
        </div>
      </div>
    </CenterPopup>
  );
}
