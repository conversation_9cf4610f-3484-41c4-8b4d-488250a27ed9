.container {
  // padding: 24px 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.title {
  text-align: center;
  padding: 24px 24px 12px;
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  color: #1d2129;
}

.shopList {
  width: 100%;
  padding: 0 24px;
}

.label {
  font-size: 15px;
  line-height: 24px;
  color: #1d2129;
}

.content {
  font-size: 15px;
  line-height: 24px;
  color: #1d2129;
  word-break: break-all;
}

.divider {
  height: 0.5px;
  background-color: #e5e6eb;
  margin: 12px 0;
}

.investOption {
  width: 100%;
  margin-bottom: 25px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 24px;
  line-height: 24px;
  color: #4e5969;
  padding: 0 24px;

  .label {
    color: #4e5969;
    height: 24px;
    line-height: 24px;
  }
}

.radioGroup {
  display: flex;
  justify-content: center;
  gap: 18px;
}

.radio {
  :global {
    .adm-radio {
      --icon-size: 20px;
      --font-size: 15px;
      --gap: 4px;
    }

    .adm-radio-content {
      color: #86909c;
      padding-left: 4px;
      --font-size: 15px;
    }
  }
}
.radioGroup {
  :global {
    .adm-radio-checked {
      .adm-radio-content {
        color: #3491fa !important;
        --font-size: 15px;
      }
    }
  }
}

.footer {
  border-top: 0.5px solid #e5e6eb;
  width: 100%;
  height: 56px;
  display: flex;
  justify-content: center;
}

// .cancelButton,
// .confirmButton {
//   flex: 1;
//   height: 48px;
//   display: flex;
//   align-items: center;
//   justify-content: center;
//   font-size: 16px;
//   cursor: pointer;
// }

.cancelButton {
  flex: 1;
  font-size: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #1d2129;
}
.footerLine {
  height: 56px;
  width: 0.5px;
  background: #e5e6eb;
}
.confirmButton {
  @extend .cancelButton;
  color: #3491fa;
}
