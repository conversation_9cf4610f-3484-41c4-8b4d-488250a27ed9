import { useState } from 'react';
import { Loading } from '@src/components';
import { useSetState } from 'ahooks';
import { Popup, Toast } from 'antd-mobile';
import { useAvailableDate } from '../../hooks';
import type { AddIntentEditParams, ShopAddIntentDetail, ShopAddIntentDetailBatchesItem } from '../../types';
import { defaultRate } from '../../utils';
import AddInvestmentDate from '../AddInvestmentDate';
import AddInvestmentInput from '../AddInvestmentInput';
import AddInvestmentValue from '../AddInvestmentValue';
import ConfirmPopup from '../ConfirmPopup/index';
export default function AddPromotionPopup({
  visible,
  setVisible,
  data,
  refreshPage,
  updatePlan,
  loading,
  title,
}: {
  visible: boolean;
  setVisible: (visible: boolean) => void;
  data: ShopAddIntentDetailBatchesItem;
  refreshPage: () => Promise<any>;
  updatePlan: (params: AddIntentEditParams) => Promise<any>;
  loading: boolean;
  title: string;
}) {
  const [confirmVisible, setConfirmVisible] = useState(false);
  const { availableDate } = useAvailableDate();

  const [addIntentMap, setAddIntentMap] = useSetState(() => {
    const map: Record<string, ShopAddIntentDetail> = {};
    data.shopAddIntentDetails.forEach((item) => {
      map[item.id] = item;
    });
    return map;
  });

  return (
    <>
      <Popup
        onClose={() => setVisible(false)}
        closeOnSwipe
        closeOnMaskClick={false}
        showCloseButton
        visible={visible}
        bodyStyle={{
          borderTopLeftRadius: '12px',
          borderTopRightRadius: '12px',
          minHeight: '40vh',
          padding: '0 12px',
        }}
        getContainer={document.querySelector('body')}
      >
        <Loading spinning={loading}>
          <div className="relative flex items-center mt-4">
            <div className="text-xs leading-5 text-[#4e5969]">单位：元</div>
            <div className="absolute font-medium left-1/2 translate-x-[-50%]	text-base leading-6 text-[#141414]">
              {title}
            </div>
          </div>
          <div className={`flex justify-between items-center mt-4`}>
            <div className="text-base font-medium leading-6 flex items-center text-[#1D2129]">
              <div className="w-1 h-4 rounded-[1px] bg-[#3491fa] mr-1" />
              {data.name}
            </div>
          </div>
          <AddInvestmentDate availableDate={availableDate} date={data.addDates} className="mt-4" />
          <div className="h-[0.5px] bg-[#E5E6EB] w-full mt-4 mb-2" />
          <AddInvestmentValue className="mt-4" />
          <div className="overflow-y-scroll max-h-44">
            {Object.values(addIntentMap).map((item, index) => {
              const { id, amount, date, initAmount } = item;
              const disabled = new Date(availableDate).getTime() > new Date(date).getTime();

              const lastIndex = Object.values(addIntentMap).length === index + 1;
              return (
                <div
                  key={id}
                  className={`${disabled ? 'h-9' : 'h-12 bg-[#F7F8FA]'}  w-full rounded-lg flex items-center justify-between px-4 ${!lastIndex && 'mb-2'}`}
                >
                  <div className={`${disabled ? 'line-through' : ''}  text-base leading-6 text-[#4E5969]`}>{date}</div>
                  {disabled ? (
                    <div className="text-base font-medium text-[#1D2129]">{item.amount}</div>
                  ) : (
                    <AddInvestmentInput
                      amount={String(amount)}
                      onChange={(amount) =>
                        setAddIntentMap((prevState) => ({
                          ...prevState,
                          [id]: { ...item, amount },
                        }))
                      }
                      disabled={disabled}
                      initAmount={initAmount}
                      rate={data.floatRange ? data.floatRange + 1 : defaultRate}
                    />
                  )}
                </div>
              );
            })}
          </div>
          <div className="h-[0.5px] bg-[#E5E6EB] w-full my-4" />

          <div className="text-xs leading-5 text-[#1d2129] ml-3 mb-9">
            <div className="text-[#4E5969] text-xs leading-5">
              <div>·当日20点前，修改加投计划，T+1 生效</div>
              <div>·当日20点后，修改加投计划，T+2 生效</div>
            </div>
          </div>
          <div className="gap-x-[11px] mt-6 w-full flex justify-between">
            <div
              onClick={() => {
                setVisible(false);
              }}
              className="flex-1 h-10 rounded-[4px] flex justify-center items-center bg-white border-[0.5px] border-[#c9cdd4] text-base font-medium leading-6 text-[#4e5969]"
            >
              取消
            </div>
            <div
              onClick={() => {
                const rate = data.floatRange ? data.floatRange + 1 : defaultRate;
                let errorMsg = '';
                const valid = Object.values(addIntentMap).some((item) => {
                  const maxAmount = +(item.initAmount * rate).toFixed(2);
                  if (item.amount > maxAmount) {
                    errorMsg = `${item.date} 加投金额不能超过${maxAmount}元`;
                    return true;
                  }
                  return false;
                });
                if (valid) {
                  Toast.show(errorMsg);
                } else {
                  setConfirmVisible(true);
                }
              }}
              className="flex-1 h-10 rounded-[4px] flex justify-center items-center bg-[#3491fa] text-base font-medium leading-6 text-white"
            >
              确认
            </div>
          </div>
        </Loading>
      </Popup>
      <ConfirmPopup
        title="参与加投计划确认"
        description="您是否要参与本月后续的加投计划？"
        visible={confirmVisible}
        setVisible={setConfirmVisible}
        onConfirmClick={async () => {
          const params = Object.values(addIntentMap).map((item) => ({
            id: item.id,
            joinFlag: 1 as const,
            amount: +item.amount,
          }));
          await updatePlan({ batchId: +data.batchId, addIntentEditDetailDtos: params });
          Toast.show({
            icon: 'success',
            content: '操作成功',
            afterClose: async () => {
              await refreshPage();
              setVisible(false);
            },
          });
        }}
        style={{
          '--z-index': '2000',
        }}
      />
    </>
  );
}
