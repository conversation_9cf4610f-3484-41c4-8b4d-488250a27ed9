import { memo } from 'react';
import { IconFont } from '@src/components';

export default memo(function AddInvestmentDate({
  date,
  className,
  availableDate,
}: {
  date: string[];
  className?: string;
  availableDate?: string;
}) {
  return (
    <div className={className}>
      <div className={`text-[13px] leading-[22px] h-[22px] text-[#4E5969] flex items-center mb-1`}>
        <IconFont type="icon-calendar" className="mr-1 text-base" />
        加投日期
      </div>
      <div className="text-sm leading-[22px] text-[#1D2129]">
        {date.map((item, index) => {
          const disabled = availableDate && new Date(availableDate).getTime() > new Date(item).getTime();
          return (
            <>
              <span className={`${disabled && 'line-through'}`} key={item}>
                {item}
              </span>
              {index !== date.length - 1 && <span>、</span>}
            </>
          );
        })}
      </div>
    </div>
  );
});
