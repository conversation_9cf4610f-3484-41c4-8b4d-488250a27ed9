import closeIcon from '@src/assets/images/flow/close.png';
import { Image, Popup } from 'antd-mobile';
import numeral from 'numeral';
export default function DescriptionPopup({
  visible,
  setVisible,
  value,
}: {
  visible: boolean;
  setVisible: (visible: boolean) => void;
  value: number;
}) {
  // console.log(value);

  return (
    <>
      <Popup
        visible={visible}
        onMaskClick={() => {
          setVisible(false);
        }}
        onClose={() => {
          setVisible(false);
        }}
        bodyStyle={{ height: '50vh', borderTopLeftRadius: '12px', borderTopRightRadius: '12px' }}
      >
        <div>
          <div className="flex justify-center items-center mt-4">
            <div className="text-center">
              <span style={{ fontSize: '16px', fontWeight: 500 }}>计算说明</span>
              <Image
                className="absolute top-[18px] right-[16px] "
                src={closeIcon}
                width={20}
                height={20}
                onClick={() => setVisible(false)}
              />
            </div>
          </div>
          <div className="flex flex-col ml-[16px] mt-4">
            <div className="mb-[16px]">
              <div style={{ fontWeight: 500, fontSize: '14px' }}>余额较低门店:</div>
              <div className="text-[14px]">当前账户金额小于 ({numeral(value).format('0, 0')}元)</div>
            </div>
            <div className="mb-[16px]">
              <div style={{ fontWeight: 500, fontSize: '14px' }}>实际推广费用:</div>
              <div className="text-[14px]">实际推广费用需大于投流月份天数 *50元，即日投流预算不得低于 50元。</div>
            </div>
            <div className="mb-[16px]">
              <div style={{ fontWeight: 500, fontSize: '14px' }}>预估投产比:</div>
              <div className="text-[14px]">预估投产比为上月日均投产比，上月无数据时取同城平均值。</div>
            </div>
            <div className="mb-[16px]">
              <div style={{ fontWeight: 500, fontSize: '14px' }}>预估推广费用:</div>
              <div className="text-[14px]">预估投产比*实际推广费用</div>
            </div>
          </div>
        </div>
      </Popup>
    </>
  );
}
