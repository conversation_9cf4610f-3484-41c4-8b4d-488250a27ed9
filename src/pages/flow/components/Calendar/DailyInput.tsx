import { memo, useState } from 'react';
import { Input } from 'antd-mobile';
import { formatAmount } from '../../utils';

/**
 * 日历每日输入框组件属性接口
 * @interface DailyInputProps
 * @property {string} date - 日期字符串
 * @property {string} amount - 输入金额
 * @property {number} [minValue] - 最小允许值，默认为0
 * @property {function} onChange - 值变化时的回调函数
 * @property {any} [props] - 其他传入的属性
 */
interface DailyInputProps {
  date: string;
  amount: string;
  minValue?: number;
  onChange: (value: number) => void;
  [props: string]: any;
}

/**
 * 日历每日输入框组件
 * 用于在日历中显示和编辑每日预算金额
 */
const DailyInput: React.FC<DailyInputProps> = ({ amount, minValue = 0, onChange, ...props }) => {
  // 内部状态，存储当前输入值
  const [value, setValue] = useState<string>(amount);

  /**
   * 保留两位小数的函数
   */
  const toFixed2 = (num: number): number => {
    return Math.round(num * 100) / 100;
  };

  return (
    <Input
      type="number"
      min={minValue}
      max={999.99}
      value={value}
      inputMode="decimal"
      style={{
        '--color': props.disabled ? '#4E5969' : '#1D2129',
        '--font-size': '14px',
        '--text-align': 'center',
        fontWeight: 500,
        height: '22px',
        lineHeight: '22px',
        backgroundColor: 'transparent',
        cursor: props.disabled ? 'not-allowed' : 'text',
      }}
      onChange={(e) => {
        // 禁用状态下不处理输入变化
        if (props.disabled) return;

        // 格式化输入值
        const val = formatAmount(e);

        // 限制最大值为999.99
        const limitedValue = Math.min(Number(val), 999.99);

        // 格式化为两位小数
        const formattedValue = toFixed2(limitedValue);

        // 更新内部状态
        setValue(String(formattedValue));
      }}
      onBlur={() => {
        // 只有当值发生变化时才触发回调
        if (value !== amount) {
          // 转换为数字
          const numValue = +value;

          // 如果值小于最小值，则设为0
          const finalValue = numValue < minValue ? 0 : numValue;

          // 更新内部状态
          setValue(String(finalValue));

          // 触发外部回调
          onChange(+finalValue);
        }
      }}
      {...props}
    />
  );
};

const MemoDailyInput = memo(DailyInput);

export default MemoDailyInput;
