import React, { memo, useEffect, useState } from 'react';
import { DotLoading } from 'antd-mobile';
import { DownOutline, UpOutline } from 'antd-mobile-icons';
import dayjs from 'dayjs';
import DailyInput from './DailyInput';
import styles from './index.module.scss';
import { DynamicDataItem } from '../../types';

interface CalendarInputProps {
  values: Record<string, DynamicDataItem>;
  onChange: (date: string, value: DynamicDataItem) => void;
  minValue?: number;
  defaultValue?: number;
  year?: number;
  month?: number;
  onExpandChange?: (isExpanded: boolean) => void;
  loading?: boolean;
  expanded?: boolean;
  disabled?: boolean;
}

const CalendarInput: React.FC<CalendarInputProps> = ({
  values,
  onChange,
  minValue = 0,
  defaultValue = 0,
  year = new Date().getFullYear(),
  month = new Date().getMonth() + 1,
  onExpandChange,
  loading = false,
  expanded,
  disabled = false,
}) => {
  const [calendarDays, setCalendarDays] = useState<Array<{ date: dayjs.Dayjs; inMonth: boolean }>>([]);

  // 生成日历数据
  useEffect(() => {
    const currentDate = dayjs(`${year}-${month}-01`);
    const firstDayOfMonth = currentDate.startOf('month');
    const lastDayOfMonth = currentDate.endOf('month');

    // 获取当月的总天数
    const daysInMonth = lastDayOfMonth.date();

    const days: Array<{ date: dayjs.Dayjs; inMonth: boolean }> = [];

    // 只添加当月的日期
    for (let i = 0; i < daysInMonth; i++) {
      days.push({
        date: firstDayOfMonth.add(i, 'day'),
        inMonth: true,
      });
    }

    setCalendarDays(days);
  }, [year, month]);

  const weekdays = ['一', '二', '三', '四', '五', '六', '日'];

  // 生成按周排列的日历数据
  const calendarWeeks = (() => {
    const weeks: Array<Array<{ date: dayjs.Dayjs; inMonth: boolean } | null>> = [];
    let currentWeek: Array<{ date: dayjs.Dayjs; inMonth: boolean } | null> = Array(7).fill(null);

    calendarDays.forEach((day) => {
      const dayOfWeek = day.date.day() || 7; // 将周日(0)转换为7
      const dayIndex = dayOfWeek - 1; // 调整为0-6索引，对应周一到周日

      // 如果当前周已经有内容且即将添加的日期是周一，则开始新的一周
      if (currentWeek.some((d) => d !== null) && dayIndex === 0) {
        weeks.push([...currentWeek]);
        currentWeek = Array(7).fill(null);
      }

      currentWeek[dayIndex] = day;

      // 如果是月份的最后一天，添加最后一周
      if (day.date.date() === calendarDays[calendarDays.length - 1].date.date()) {
        weeks.push([...currentWeek]);
      }
    });
    return weeks;
  })();

  return (
    <div className={styles.calendarContainer}>
      <div className={styles.calendarHeader}>
        <div className={styles.title}>
          每日预算配置 ({year}年{month}月)
          {loading && <DotLoading color="#1677ff" />}
        </div>
        <div className={styles.expandIcon} onClick={() => onExpandChange?.(!expanded)}>
          {expanded ? <UpOutline /> : <DownOutline />}
        </div>
      </div>

      {expanded && (
        <div className={styles.calendarWrapper}>
          {loading ? (
            <div className="flex items-center justify-center py-4">
              <DotLoading color="#1677ff" />
              <span className="ml-2 text-[#1677ff]">加载中...</span>
            </div>
          ) : (
            <>
              <div className={styles.weekdayHeader}>
                {weekdays.map((day, index) => (
                  <div key={index} className={styles.weekday}>
                    {day}
                  </div>
                ))}
              </div>

              <div className={styles.calendarGrid}>
                {calendarWeeks.map((week, weekIndex) => (
                  <React.Fragment key={weekIndex}>
                    {week.map((day, dayIndex) => {
                      if (!day) {
                        return <div key={`empty-${dayIndex}`} className={styles.emptyCell} />;
                      }

                      const dateStr = day.date.format('YYYY-MM-DD');
                      const dayValue = values[dateStr] !== undefined ? values[dateStr].bidAmount : defaultValue;
                      const dayNumber = day.date.date();

                      return (
                        <div key={dateStr} className={styles.calendarCell}>
                          <div className={styles.dayNumber}>{dayNumber}</div>
                          <DailyInput
                            disabled={disabled}
                            date={dateStr}
                            amount={dayValue}
                            minValue={minValue}
                            onChange={(value) => onChange(dateStr, { ...values[dateStr], bidAmount: value })}
                          />
                        </div>
                      );
                    })}
                  </React.Fragment>
                ))}
              </div>
            </>
          )}
        </div>
      )}
    </div>
  );
};
const MemoCalendarInput = memo(CalendarInput);
export default MemoCalendarInput;
