.calendarHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  height: 38px;
  line-height: 22px;

  .title {
    font-size: 13px;
    font-weight: 500;
    line-height: 22px;
    color: #1d2129;
  }

  .expandIcon {
    cursor: pointer;
    color: #999;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
  }
}

.calendarWrapper {
  transition: all 0.3s;
}

.weekdayHeader {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  text-align: center;
  margin-bottom: 8px;
}

.weekday {
  font-size: 13px;
  height: 22px;
  line-height: 22px;
  color: #4e5969;
  font-weight: normal;
}

.calendarGrid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  // gap: 8px 0;
}

.calendarCell {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 8px;
}

.dayN<PERSON>ber {
  font-size: 13px;
  height: 22px;
  line-height: 22px;
  text-align: center;
  color: #86909c;
}
