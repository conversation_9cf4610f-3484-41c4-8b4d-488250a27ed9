import { memo, useMemo } from 'react';
import AddIntentDate from './AddIntentDate';
import SquareCheckBox from '../components/SquareCheckBox';
import FlowSwitch from '../components/Switch';
import { AddIntentItem, AddIntentMap } from '../types';

const ShopInfo = memo(
  ({
    plan,
    setPlans,
    changeShopFn,
    keyword,
  }: {
    plan: AddIntentItem;
    setPlans: (data: AddIntentMap) => void;
    changeShopFn: (id: Array<string>) => void;
    keyword: string;
  }) => {
    const hidden = useMemo(() => {
      if (!keyword) return false;
      return !plan.shopName.includes(keyword) && plan.shopId !== keyword;
    }, [keyword]);
    const disabled = !plan.optFlag;
    return (
      <div className={`${hidden ? 'hidden' : 'block'} px-3 pt-3 mx-3 mb-3 bg-white rounded-xl`}>
        <div className="flex justify-between items-center">
          <div className="flex items-center">
            <SquareCheckBox
              disabled={disabled}
              className="mr-2"
              checked={!!plan.selected}
              onChange={(e) => {
                setPlans((prevState) => ({ ...prevState, [plan.id]: { ...plan, selected: e } }));
              }}
            />
            <div className="font-medium text-[15px] text-[#1d2129] h-6">{plan.shopName}</div>
          </div>
          <div className="flex items-center">
            <span className="text-[#3D3D3D] text-[13px] leading-[22px] font-normal mr-1">加投</span>
            <FlowSwitch
              disabled={disabled}
              checked={plan.joinFlag}
              onChange={(check) => {
                setPlans((prevState) => ({
                  ...prevState,
                  [plan.id]: { ...plan, joinFlag: check },
                }));
                changeShopFn([plan.id!]);
              }}
            />
          </div>
        </div>
        <div className="ml-8 text-[#1d2129] text-xs leading-5">ID:{plan.shopId}</div>
        <div className="pl-8">
          {plan.shopAddIntentDtos.map((item, index) => (
            <AddIntentDate
              disabled={disabled}
              joinFlag={plan.joinFlag}
              floatRange={plan.floatRange}
              key={index}
              addIntentDate={item}
              id={plan.id}
              onChange={(amount) => {
                setPlans((prevState) => {
                  const { shopAddIntentDtos } = plan;
                  shopAddIntentDtos[index].amount = +amount;
                  return {
                    ...prevState,
                    [plan.id]: { ...plan, shopAddIntentDtos },
                  };
                });
              }}
              changeShopFn={changeShopFn}
            />
          ))}
        </div>
      </div>
    );
  },
);
export default ShopInfo;
