import { memo } from 'react';
import styles from './index.module.scss';
import AddInvestmentInput from '../components/AddInvestmentInput';
import { useAvailableDate } from '../hooks';
import { ShopAddIntentItem } from '../types';
import { defaultRate } from '../utils';
function AddIntentDate({
  addIntentDate,
  onChange,
  changeShopFn,
  floatRange,
  disabled: optFlag,
  id,
  joinFlag,
}: {
  addIntentDate: ShopAddIntentItem;
  onChange: (value: string) => void;
  changeShopFn: (id: string[]) => void;
  floatRange: number;
  disabled: boolean;
  id: string;
  joinFlag: boolean;
}) {
  const { availableDate } = useAvailableDate();
  const { amount, date, initAmount } = addIntentDate;
  const amountDisabled = new Date(availableDate).getTime() > new Date(date).getTime() || optFlag || !joinFlag;
  const dateDisbaled = new Date(availableDate).getTime() > new Date(date).getTime();
  return (
    <div className={styles.addInvestment}>
      <div className={`flex items-center w-[150px] flex-nowrap`}>
        <div className="text-[13px]">加投日期：</div>
        <div className={`${dateDisbaled ? 'line-through' : ''}`}>{date}</div>
      </div>
      {amountDisabled ? (
        <div className="flex-1 font-normal text-sm text-right w-[50px]  text-[#4E5969]">{amount}</div>
      ) : (
        <AddInvestmentInput
          style={{ flex: 1, fontWeight: amountDisabled ? 400 : 500, '--font-size': '14px', '--text-align': 'right' }}
          amount={String(amount)}
          onChange={(amount) => {
            changeShopFn([id]);
            onChange(amount);
          }}
          disabled={amountDisabled}
          initAmount={initAmount}
          rate={floatRange ? floatRange + 1 : defaultRate}
          placeholder=""
          changeShopFn={changeShopFn}
        />
      )}
    </div>
  );
}
const MemoAddIntentDate = memo(AddIntentDate);
export default MemoAddIntentDate;
