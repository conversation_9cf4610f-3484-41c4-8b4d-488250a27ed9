@import "../common.scss";

$bg: #f7f8fa;
.batch__edit {
  background: $bg;
  width: 100%;
  height: 100vh;
  overflow-y: scroll;

  &__navigation {
    z-index: 10;
    width: pxToVw(375);
    height: pxToVw(44);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: pxToVw(12);
    // margin-bottom: ;
    background: white;
    margin-bottom: pxToVw(12);

    &__back {
      font-size: pxToVw(24);
    }

    &__title {
      font-size: pxToVw(16);
      font-weight: 500;
      line-height: pxToVw(24);
      text-align: center;
      color: #141414;
    }

    &__unit {
      font-size: pxToVw(12);
      line-height: pxToVw(20);
      text-align: right;
      color: #4e5969;
    }
  }

  &__input__wrap {
    padding: pxToVw(12);
    background: linear-gradient(180deg, #fff7e8 5%, #f7f8fa 100%);

    .input {
      width: pxToVw(351);
      height: pxToVw(40);
      border-radius: pxToVw(8);
      background: #ffffff;
      border: 0.5px solid #e5e6eb;
      padding-left: pxToVw(12);
      font-size: pxToVw(14);
    }
    input::-webkit-input-placeholder {
      font-size: pxToVw(12);
    }
    .search__button {
      position: absolute;
      // top: 50%;
      // transform: translateY(-50%);
      bottom: pxToVw(18);
      right: pxToVw(18);

      width: pxToVw(48);
      height: pxToVw(28);
      border-radius: pxToVw(4);
      display: flex;
      justify-content: center;
      align-items: center;
      background: #3491fa;
      color: white;
    }
  }

  &__shops {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: pxToVw(12);
    background: #f7f8fa;
    // height: 100%;
    // overflow-y: scroll;
    margin-bottom: pxToVw(80);
  }

  &__bottom {
    position: fixed;
    bottom: pxToVw(0);
    width: pxToVw(375);
    height: pxToVw(80);
    background: #ffffff;
    box-sizing: border-box;
    border-top: pxToVw(0.5) solid #e5e6eb;
    padding: pxToVw(0) pxToVw(12);

    &__operation {
      height: pxToVw(40);
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: pxToVw(13);
    }

    &__submit {
      height: pxToVw(40);
      border-radius: pxToVw(4);
      display: flex;
      justify-content: center;
      align-items: center;
      background: #3491fa;

      font-size: pxToVw(16);
      font-weight: 500;
      line-height: pxToVw(24);
      color: #ffffff;
    }
  }
}

.shop {
  width: 100%;
  border-radius: pxToVw(12);
  background: #ffffff;
  padding: pxToVw(12) pxToVw(12) pxToVw(0);
  margin-bottom: pxToVw(12);

  &__head {
    display: flex;
    justify-content: space-between;

    &__title {
      font-size: pxToVw(15);
      font-weight: 500;
      line-height: pxToVw(24);
      height: pxToVw(24);
      color: #1d2129;
    }

    &__right {
      display: flex;
      row-gap: pxToVw(4);
    }
  }

  &__id {
    font-size: pxToVw(12);
    line-height: pxToVw(20);
    color: #1d2129;
  }

  &__promotion__info {
    width: 100%;
    height: pxToVw(60);
    border-radius: pxToVw(8);
    background: #f7f8fa;
    display: flex;
    align-items: center;
    margin-bottom: pxToVw(5);

    .item {
      padding-left: pxToVw(12);
      width: pxToVw(117);
      height: pxToVw(44);

      .label {
        font-size: pxToVw(13);
        height: pxToVw(22);
        line-height: pxToVw(22);
        color: #86909c;
      }

      .value {
        font-size: pxToVw(14);
        font-weight: 500;
        height: pxToVw(22);
        line-height: pxToVw(22);
        color: #1d2129;
      }

      &:last-of-type {
        flex: 1;
      }
    }
  }

  &__balance {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: pxToVw(38);
    line-height: pxToVw(38);

    .label {
      font-size: pxToVw(14);
      color: #1d2129;
      display: flex;
      align-items: center;
    }

    .value {
      font-size: pxToVw(16);
      font-weight: 500;
    }
  }

  &__account__amount {
    @extend .shop__balance;
    border-top: pxToVw(0.5) solid #f0f0f0;
  }
}

.addInvestment {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 38px;
  border-bottom: 0.5px solid #f0f0f0;
  font-size: 14px;
  color: #4e5969;

  &:last-of-type {
    border-bottom: none;
  }
}
