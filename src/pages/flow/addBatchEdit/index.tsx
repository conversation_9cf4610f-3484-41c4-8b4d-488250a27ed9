import { memo, useEffect, useState } from 'react';
import { IconFont, Loading } from '@src/components';
import useQuerySearchParams from '@src/hooks/useQuerySearchParams';
import ShopStore from '@src/store/flow';
import { useRequest } from 'ahooks';
import { Input, NavBar, SafeArea, Toast } from 'antd-mobile';
import { PickerValue } from 'antd-mobile/es/components/picker-view';
import { LeftOutline } from 'antd-mobile-icons';
import { cloneDeep } from 'lodash';
import { useNavigate } from 'react-router-dom';
import { FixedSizeList } from 'react-window';
import styles from './index.module.scss';
import ShopInfo from './ShopInfo';
import { queryAddIntentBatch, queryAddIntentList, updateAddIntentBatch } from '../api';
import BatchEditAddPromotionPopup from '../components/BatchEditAddPromotionPopup';
import BatchInvestPopup from '../components/BatchInvestPopup';
import BottomPopup from '../components/BottomPopup';
import ConfirmPopup from '../components/ConfirmPopup';
import QuitePopup from '../components/QuitePopup';
import SquareCheckBox from '../components/SquareCheckBox';
import { useAvailableDate } from '../hooks';
import { AddIntentEditItem, AddIntentEditParams, AddIntentMap, AddIntentState } from '../types';
import { defaultRate, deviceJudgment, flowPlanId, hiddenHeader } from '../utils';

export default function AddBatchEdit() {
  const [visible, setVisible] = useState(false);
  const [visible2, setVisible2] = useState(false);
  const [selectAll, setSelectAll] = useState(false);
  const [inputVal, setInputVal] = useState('');
  const [keyword, setKeyword] = useState('');
  const [changeShopRecord, setChangeShopRecord] = useState<string[]>([]);
  const [unJoinReason, setUnJoinReason] = useState('');
  const [planVisible, setPlanVisible] = useState(false);
  const { setSelectedShopIds, selectedShopIds, shopPromotionIntentList } = ShopStore;
  const [addIntentPlan, setAddIntentPlan] = useState<AddIntentMap>({});
  const [searchParams] = useQuerySearchParams();
  const [columns, setColumns] = useState<{ label: string; value: number }[][]>([[]]);
  const [planId, setPlanId] = useState<PickerValue>(null);
  const [planName, setPlanName] = useState<PickerValue>('');
  const [addPromotionVisible, setAddPromotionVisible] = useState(false);
  const [addBatchInvestPopup, setAddBatchInvestPopup] = useState(false);
  const [initialData, setInitialData] = useState<AddIntentMap>({});
  const [status, setStatus] = useState<1 | null>(() => (searchParams.from === 'index' ? 1 : null));
  useEffect(() => {
    setTimeout(() => {
      hiddenHeader();
    }, 0);
  }, []);

  const {
    runAsync: updateAddPlan,
    loading,
  }: { runAsync: (params: AddIntentEditParams) => Promise<any>; loading: boolean } = useRequest(
    async (params) => {
      return await updateAddIntentBatch(params);
    },
    {
      manual: true,
    },
  );
  const { loading: loading1 } = useRequest(async () => {
    const res = await queryAddIntentBatch({
      shopIds: shopPromotionIntentList.map((item) => item.shopId),
      dateMonth: searchParams.dateMonth,
      platform: searchParams.platform,
    });
    const data = res.map((item) => {
      return {
        label: item.name,
        value: item.id,
      };
    });
    const month = new Date(searchParams.dateMonth).getMonth() + 1;
    data.unshift({
      label: `${month}月投流计划`,
      value: flowPlanId,
    });
    const { planId } = searchParams;
    if (planId) {
      const foundItem = data.find((item) => item.value === +planId);
      if (foundItem) {
        const { value, label } = foundItem;
        setPlanName(label);
        setPlanId(value);
      }
    } else {
      setPlanName(data[1]?.label);
      setPlanId(data[1]?.value);
    }
    setColumns([data]);
  });
  const { loading: loading2 } = useRequest(
    async () => {
      const params: { batchId: string; shopIds: string[]; status?: 1 } = {
        batchId: String(planId),
        shopIds: shopPromotionIntentList.map((item) => item.shopId),
      };
      /** 首页计划待确认进来 传参数 status = 1 标记待确认计划 */
      if (status) {
        params['status'] = status;
      }
      const res = await queryAddIntentList(params);
      /** 没有选中门店，说明是首页计划待确认入口跳转进来的，此时把当前的门店记录为选中门店 */
      !selectedShopIds.length && setSelectedShopIds(res.map((item) => item.shopId));
      /** 初始化，插入id 作为唯一索引兼容数据， 插入 selected 作为选中状态 */
      const data = res.map((item) => {
        return {
          ...item,
          id: item.shopId,
          selected: item.status === AddIntentState.待确认,
        };
      });
      const map: AddIntentMap = {};
      data.forEach((item) => {
        item.shopAddIntentMap = {};
        item.shopAddIntentDtos.forEach((shop) => {
          item.shopAddIntentMap[shop.id] = cloneDeep(shop);
        });
        map[item.id] = item;
      });
      const initialData = cloneDeep(map);
      setInitialData(initialData);
      setAddIntentPlan(map);
      setChangeShopRecord([]);
      setSelectAll(false);
      return data;
    },
    {
      ready: !!planId,
      refreshDeps: [planId],
    },
  );

  const { availableDate } = useAvailableDate();
  const navigate = useNavigate();
  const changeShopFn = (ids: string[]) => setChangeShopRecord([...new Set([...changeShopRecord, ...ids])]);

  /** 判断本次修改是否有把加投修改为未加投状态 */
  const hasUnjoin = ({ initialData, addIntentPlan }: { initialData: AddIntentMap; addIntentPlan: AddIntentMap }) => {
    const plansArr = Object.values(addIntentPlan);

    return plansArr.some((p) => {
      const { joinFlag } = initialData[p.id];
      // 初始化加投，修改后未加投
      return joinFlag && !p.joinFlag;
    });
  };

  /** 对比数据，过滤去掉未修改的数据 */
  const compareData = (initialData: AddIntentMap, addIntentPlan: AddIntentMap) => {
    const submitData = Object.values(addIntentPlan);

    const addIntentEditDetailDtos: AddIntentEditItem[] = [];
    submitData.forEach((item) => {
      const { shopAddIntentDtos, floatRange, joinFlag, shopName, optFlag } = item;
      const rate = floatRange ? floatRange + 1 : defaultRate;

      shopAddIntentDtos.forEach((shopAddIntentDto) => {
        const { amount, date } = shopAddIntentDto;
        const disabled = new Date(availableDate).getTime() > new Date(date).getTime() || !optFlag;

        // if (disabled) return;

        /** 前端过滤数据 */
        const {
          joinFlag: originalFlag,
          amount: originalAmount,
          initAmount,
        } = initialData[item.id].shopAddIntentMap[shopAddIntentDto.id];
        let errorMsg = '';

        /** 加投状态有变化，或者加投状态为已加投，且修改了推广费用 */
        // console.log(shopName, date, joinFlag !== originalFlag, joinFlag, amount, initAmount * floatRange);

        if (joinFlag !== originalFlag || joinFlag) {
          /** 前端不过滤数据，全部提交 */
          addIntentEditDetailDtos.push({
            joinFlag: joinFlag ? 1 : 0,
            amount: disabled ? originalAmount : shopAddIntentDto.amount,
            id: shopAddIntentDto.id,
          });
          const maxAmount = +(initAmount * rate).toFixed(2);
          if (amount > maxAmount) {
            errorMsg = `${shopName}日期 ${date} 加投金额不能超过${maxAmount}元`;
            throw errorMsg;
          }
        } else {
          addIntentEditDetailDtos.push({
            joinFlag: joinFlag ? 1 : 0,
            amount: originalAmount,
            id: shopAddIntentDto.id,
          });
        }
      });
    });

    return addIntentEditDetailDtos;
  };

  const isIPhone = deviceJudgment() === 'iPhone';

  const handleSubmit = async () => {
    try {
      const addIntentEditDetailDtos = compareData(initialData, addIntentPlan);
      if (!planId) return;
      await updateAddPlan({ addIntentEditDetailDtos, batchId: +planId, cancelReason: unJoinReason });
      Toast.show({
        icon: 'success',
        content: '操作成功',
        duration: 3000,
        afterClose: async () => {
          navigate('/flow/index');
        },
      });
    } catch (error: any) {
      Toast.show({
        content: error,
        duration: 3000,
      });
    }
  };
  const itemSize = Object.values(addIntentPlan)[0]
    ? 56 + 12 + Object.values(addIntentPlan)[0]['shopAddIntentDtos'].length * 38
    : 0;
  const RowRenderer = memo(({ index, style }) => {
    const item = Object.values(addIntentPlan)?.[index];
    return (
      <div style={style}>
        <ShopInfo keyword={keyword} key={item.id} plan={item} setPlans={setAddIntentPlan} changeShopFn={changeShopFn} />
      </div>
    );
  });
  return (
    <>
      <Loading spinning={loading || loading1 || loading2}>
        {!isIPhone && <div className="min-h-[2.5rem] bg-white" />}
        <NavBar
          className="bg-white"
          back={<LeftOutline fontSize={16} color="#000" />}
          backArrow={false}
          onBack={() => {
            navigate(-1);
          }}
          right={<div className={styles.batch__edit__navigation__unit}>单位：元</div>}
        >
          <span className="text-base text-[#141414] font-medium">批量调整</span>
        </NavBar>
        <div className={`${styles.batch__edit}`}>
          <div className="sticky top-0 z-10 bg-white">
            <div className={styles.batch__edit__input__wrap}>
              <div className="flex items-center mb-3">
                <div className="flex items-center h-5 text-[13px]  text-[#3491FA]" onClick={() => setPlanVisible(true)}>
                  <IconFont type="icon-swap" className="mr-1 text-base" />
                  切换
                </div>
                <div className="w-[0.5px] h-3 bg-[#C9CDD4] mx-2" />
                <div className="text-[#1D2129] text-sm font-medium">{planName}</div>
              </div>
              <Input
                className={styles.input}
                value={inputVal}
                onChange={(e) => setInputVal(e.trim())}
                placeholder="输入门店名称/ID"
                style={{
                  '--placeholder-color': '#86909C',
                }}
              />
              <div
                className={styles.search__button}
                onClick={() => {
                  setKeyword(inputVal);
                }}
              >
                搜索
              </div>
            </div>
          </div>
          <div className="mb-24">
            <FixedSizeList
              width="100%"
              height={600}
              itemCount={Object.values(addIntentPlan).length}
              itemSize={itemSize}
              itemKey={(index) => Object.values(addIntentPlan)?.[index]?.id || index}
            >
              {RowRenderer}
            </FixedSizeList>
          </div>
          <div className="fixed bottom-0 w-full h-20 bg-white border-t-[0.5] border-[#e5e6eb] px-3 z-10">
            <div className="h-10 flex justify-between items-center text-[13px]">
              <div className="flex items-center">
                <SquareCheckBox
                  checked={selectAll}
                  onChange={(e) => {
                    setAddIntentPlan(() => {
                      Object.values(addIntentPlan).forEach((p) => {
                        if (!p.optFlag) return;
                        p.selected = e;
                      });
                      return addIntentPlan;
                    });
                    setSelectAll(e);
                  }}
                />
                <span className={'text-[#3D3D3D] text-[13px] leading-[22px] font-normal ml-1'}>全选</span>
              </div>
              <div className="text-[#3491FA]">
                已调整门店：{changeShopRecord.length}/{Object.keys(addIntentPlan).length}
              </div>
            </div>
            <div className="flex gap-x-3 h-10 text-base font-medium">
              <div
                onClick={() => {
                  const selectedShops = Object.values(addIntentPlan).filter((item) => item.selected);
                  if (selectedShops.length === 0) {
                    Toast.show({
                      content: '请选择要调整的店铺',
                    });
                    return;
                  }
                  setAddBatchInvestPopup(true);
                  // setAddPromotionVisible(true);
                }}
                className="flex justify-center items-center flex-1 rounded-[4px] text-[#4E5969] border-[0.5px] border-[#C9CDD4]"
              >
                批量修改
              </div>
              <div
                onClick={() => {
                  if (!changeShopRecord?.length) {
                    return Toast.show({
                      content: '无任何调整门店',
                    });
                  }

                  const unJoin = hasUnjoin({ initialData, addIntentPlan });
                  if (unJoin) {
                    setVisible2(true);
                  } else {
                    setVisible(true);
                  }
                }}
                className="flex justify-center items-center flex-1 rounded-[4px] bg-[#3491fa] text-white"
              >
                提交修改
              </div>
            </div>
          </div>
          <SafeArea position="bottom" />
        </div>
      </Loading>
      <ConfirmPopup
        title="修改推广金额确认"
        subTitle={
          <div className="mb-2 text-base leading-6 text-[#4E5969] flex flex-col justify-center items-center">
            <div>您是否要修改本月后续的</div>
            <div>加投计划金额？</div>
          </div>
        }
        description={
          <div className="text-[#4E5969] text-xs leading-5">
            <div>·当日20点前，修改加投计划，T+1 生效</div>
            <div>·当日20点后，修改加投计划，T+2 生效</div>
          </div>
        }
        visible={visible}
        setVisible={setVisible}
        onCancelClick={() => {}}
        onConfirmClick={async () => {
          await handleSubmit();
        }}
      />

      <QuitePopup
        title="退出加投计划确认"
        subTitle={`您是否要退出【${planName}】？`}
        description={
          <div className="text-[#4E5969] text-xs leading-5">
            <div>·当日20点前，退出加投计划，T+1 生效</div>
            <div>·当日20点后，退出加投计划，T+2 生效</div>
          </div>
        }
        visible={visible2}
        setVisible={setVisible2}
        value={unJoinReason}
        setValue={setUnJoinReason}
        onConfirmClick={async () => {
          await handleSubmit();
          setUnJoinReason('');
        }}
        onCancelClick={() => {
          setUnJoinReason('');
        }}
        cancelText="取消"
        confirmText="确认"
        style={{
          minHeight: 0,
          height: '10px',
          '--border-radius	': '12',
        }}
      />
      <BottomPopup
        visible={planVisible}
        setVisible={setPlanVisible}
        columns={columns}
        title="选择计划"
        onConfirmClick={async (value) => {
          if (value === flowPlanId) {
            navigate(`/flow/batchEdit?platform=${searchParams.platform}&dateMonth=${searchParams.dateMonth}`, {
              replace: true,
            });
          } else {
            // 切换后不需要传 status
            setStatus(null);
            const name = columns[0]?.find((item) => item.value === value)?.['label'];
            setPlanId(value);
            setPlanName(name!);
          }
          setPlanVisible(false);
        }}
      />
      {addPromotionVisible && (
        <BatchEditAddPromotionPopup
          loading={loading}
          visible={addPromotionVisible}
          data={Object.values(addIntentPlan).filter((item) => item.selected)}
          setVisible={setAddPromotionVisible}
          onConfirmClick={(data) => {
            const updateIds = Object.values(addIntentPlan)
              .filter((item) => item.selected)
              .map((item) => item.id);
            changeShopFn(updateIds);
            const updateData = {};
            updateIds.forEach((id) => {
              addIntentPlan[id].joinFlag = data.joinFlag;
              addIntentPlan[id].shopAddIntentDtos.forEach((item, index) => {
                if (new Date(availableDate).getTime() > new Date(item.date).getTime()) return;

                item.amount = data.shopAddIntentDtos[index].amount;
              });
              updateData[id] = addIntentPlan[id];
            });
            setAddIntentPlan((prevState) => {
              console.log(prevState, updateData);
              return {
                ...prevState,
                ...updateData,
              };
            });
          }}
        />
      )}
      {addBatchInvestPopup && (
        <BatchInvestPopup
          selectedShops={Object.values(addIntentPlan).filter((item) => item.selected)}
          onConfirm={(boo) => {
            const updateIds = Object.values(addIntentPlan)
              .filter((item) => item.selected)
              .map((item) => item.id);
            changeShopFn(updateIds);
            const updateData = {};

            updateIds.forEach((id) => {
              addIntentPlan[id].joinFlag = boo;
              updateData[id] = addIntentPlan[id];
            });
            setAddIntentPlan((prevState) => {
              console.log(prevState, updateData);
              return {
                ...prevState,
                ...updateData,
              };
            });
          }}
          visible={addBatchInvestPopup}
          setVisible={setAddBatchInvestPopup}
        />
      )}
    </>
  );
}
