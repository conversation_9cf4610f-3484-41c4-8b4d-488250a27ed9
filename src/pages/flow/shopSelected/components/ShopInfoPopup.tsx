import { forwardRef, memo, useImperativeHandle, useState } from 'react';
import { Popup } from 'antd-mobile';
import { FixedSizeList, ListChildComponentProps } from 'react-window';

export interface ShopInfoPopupRef {
  /** 打开弹窗 */
  onOpen: () => void;
  /** 关闭弹窗 */
  onClose: () => void;
}

export default forwardRef<ShopInfoPopupRef, { selectedShopList: { shopId: string; shopName: string }[] }>(
  function ShopInfoPopup({ selectedShopList }, ref) {
    const [visible, setVisible] = useState(false);

    useImperativeHandle(ref, () => ({
      onOpen: () => {
        setVisible(true);
      },

      onClose: () => {
        setVisible(false);
      },
    }));

    const RowRenderer = memo(({ index, style }: ListChildComponentProps<typeof selectedShopList>) => {
      const item = selectedShopList?.[index];

      return (
        <div style={style}>
          <div className="text-[14px] leading-[22px] text-[#141414] font-medium">{item?.shopName ?? '-'}</div>
          <div className="text-[#86909C] text-[12px] leading-5 font-normal">门店ID：{item?.shopId ?? '-'}</div>
        </div>
      );
    });

    return (
      <Popup
        visible={visible}
        closeOnSwipe
        showCloseButton
        onClose={() => {
          setVisible(false);
        }}
        bodyStyle={{
          borderTopLeftRadius: '12px',
          borderTopRightRadius: '12px',
          height: '40vh',
        }}
      >
        <div className="relative flex items-center justify-center mt-4">
          <div className="text-base font-medium leading-6 text-[#141414]">已选择门店</div>
          {/* <div className="absolute left-1/2 translate-x-[-50%]	text-base leading-6 text-[#141414]">已选择门店</div> */}
        </div>
        <div className="flex flex-col my-4 ml-4 mr-4">
          {!!selectedShopList?.length && (
            <FixedSizeList
              width="100%"
              height={280}
              itemCount={selectedShopList.length}
              itemSize={54}
              itemKey={(index) => selectedShopList?.[index]?.shopId || index}
            >
              {RowRenderer}
            </FixedSizeList>
          )}
        </div>
      </Popup>
    );
  },
);
