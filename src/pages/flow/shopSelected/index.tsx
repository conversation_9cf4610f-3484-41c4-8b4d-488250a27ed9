import { memo, useEffect, useMemo, useRef, useState } from 'react';
import { CloseCircleOutlined, SearchOutlined } from '@ant-design/icons';
import useQuerySearchParams from '@src/hooks/useQuerySearchParams';
import ShopStore from '@src/store/flow';
import { Button, CheckList, ErrorBlock, Input, InputRef, NavBar, SearchBar, Toast } from 'antd-mobile';
import { LeftOutline } from 'antd-mobile-icons';
import classNames from 'classnames';
import { debounce } from 'lodash';
import { useNavigate } from 'react-router-dom';
import { FixedSizeList, ListChildComponentProps } from 'react-window';
import ShopInfoPopup, { type ShopInfoPopupRef } from './components/ShopInfoPopup';
import styles from './index.module.scss';
import SquareCheckBox from '../components/SquareCheckBox';
import { deviceJudgment, hiddenHeader } from '../utils';

const text13 = 'text-[13px] leading-[22px] font-normal';

export default function ShopSelected() {
  const [shopIds, setShopIds] = useState<string[]>([]);
  const inputRef = useRef<InputRef>(null);
  const [idsSearchText, setIdsSearchText] = useState<string>('');
  const [batchText, setBatchText] = useState<string[]>();
  const [searchText, setSearchText] = useState<string>('');
  const navigate = useNavigate();
  const ShopInfoPopupRef = useRef<ShopInfoPopupRef>(null);
  const [searchParams]: any = useQuerySearchParams();

  const { shopPromotionIntentList, setSelectedShopIds } = ShopStore;

  const isIPhone = deviceJudgment() === 'iPhone';

  useEffect(() => {
    setSelectedShopIds([]);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const shopList = useMemo(() => {
    return shopPromotionIntentList?.map((m) => ({ shopId: m.shopId, shopName: m.shopName }));
  }, [shopPromotionIntentList]);

  const dataSource = useMemo(() => {
    if (!shopList?.length) {
      return [];
    }

    if (batchText?.some((s) => s?.trim()?.length)) {
      return shopList?.filter((f) => batchText.includes(f.shopId));
    }

    if (searchText?.trim()?.length) {
      return shopList?.filter((f) => f.shopName?.includes(searchText));
    }

    return shopList;
  }, [batchText, searchText, shopList]);

  const handleIdsChange = useMemo(
    () =>
      debounce((text: string) => {
        setIdsSearchText(text);
      }, 300),
    [],
  );

  const handleSearchChange = useMemo(
    () =>
      debounce((text: string) => {
        setSearchText(text);
      }, 300),
    [],
  );

  const RowRenderer = memo(({ index, style }: ListChildComponentProps<typeof dataSource>) => {
    const item = dataSource?.[index];

    return (
      <CheckList.Item
        className={classNames('mt-1.5', index === 0 && styles.hidden_border_top)}
        value={item?.shopId}
        style={style}
      >
        <div className="text-[#141414] text-[14px] leading-[22px] font-normal">{item?.shopName ?? '-'}</div>
        <div className="text-[#86909C] text-[12px] leading-5 font-normal">门店ID：{item?.shopId ?? '-'}</div>
      </CheckList.Item>
    );
  });

  useEffect(() => {
    setTimeout(() => {
      hiddenHeader();
    }, 0);
  }, []);

  return (
    <div className="flex flex-col justify-between h-screen">
      {!isIPhone && <div className="min-h-[2.5rem] bg-white" />}
      <NavBar
        className="bg-white"
        back={<LeftOutline fontSize={16} color="#000" />}
        backArrow={false}
        onBack={() => {
          navigate(-1);
        }}
      >
        <span className="text-base text-[#141414] font-medium">选择门店</span>
      </NavBar>
      <div className="flex flex-col p-3 bg-white gap-y-3">
        <div className="flex border-[0.5px] border-[#E5E6EB] h-[2.5rem] rounded-lg items-center pl-3 pr-1.5 gap-x-2">
          <div className="flex w-full">
            <Input
              placeholder="请输入门店ID，使用英文逗号分隔"
              ref={inputRef}
              onChange={handleIdsChange}
              style={{
                '--font-size': '14px',
                lineHeight: '22px',
                '--placeholder-color': '#86909C',
              }}
            />
            {!!idsSearchText?.trim()?.length && (
              <div className="flex items-center justify-center">
                <CloseCircleOutlined
                  className="text-gray-300"
                  onClick={() => {
                    inputRef.current?.clear();
                    setBatchText([]);
                  }}
                />
              </div>
            )}
          </div>
          <Button
            className={classNames(text13, 'h-[1.75rem] w-[3.5rem] p-0 rounded-[.25rem]')}
            color="primary"
            onClick={() => {
              const batchShopIds = inputRef.current?.nativeElement?.value?.split(',');

              setBatchText(batchShopIds);
            }}
          >
            确认
          </Button>
        </div>
        <div>
          <div className="flex items-center">
            <div className="w-1 h-3 bg-primary rounded-[1px] mr-2" />
            <div>
              <span className={classNames(text13, 'text-[#1D2129] font-medium')}>已选择门店：</span>
              <span className="text-primary text-[14px] leading-[22px] font-medium">{shopIds.length ?? 0}</span>
            </div>
          </div>
          <div
            className={classNames(text13, 'text-[#86909C] ml-3 text-ellipsis overflow-hidden whitespace-nowrap')}
            onClick={() => {
              ShopInfoPopupRef?.current?.onOpen();
            }}
          >
            {shopList
              ?.filter((f) => shopIds.includes(f.shopId))
              .map((m) => m.shopName)
              .join('，')}
          </div>
        </div>
      </div>
      <div className="flex flex-col justify-between flex-1">
        <div className="bg-white mt-2.5 px-3 pt-[.875rem] flex-1">
          <SearchBar
            placeholder="搜索门店"
            icon={<SearchOutlined style={{ color: '#86909C' }} />}
            onChange={handleSearchChange}
            style={{
              '--height': '2.5rem',
              '--background': '#F7F8FA',
              '--placeholder-color': '#86909C',
            }}
          />
          {dataSource?.length ? (
            <CheckList
              multiple
              value={shopIds}
              onChange={(val) => {
                setShopIds(val as string[]);
              }}
              extra={(active) => {
                return <SquareCheckBox checked={active} />;
              }}
            >
              <FixedSizeList
                width="100%"
                height={shopIds?.length ? 450 : 470}
                itemCount={dataSource.length}
                itemSize={72}
                itemKey={(index) => dataSource?.[index]?.shopId || index}
              >
                {RowRenderer}
              </FixedSizeList>
            </CheckList>
          ) : (
            <div className="flex items-center justify-center h-full">
              <ErrorBlock status="empty" description="暂无数据" />
            </div>
          )}
        </div>
        <div className="flex flex-col px-3 py-2 bg-white">
          <div className="flex justify-between mb-[.5625rem]">
            <div
              className="flex items-center gap-x-1"
              onClick={() => {
                if (!dataSource?.length) {
                  return;
                }

                const isSelectedAll = dataSource?.length === shopIds.length;
                setShopIds(isSelectedAll ? [] : dataSource.map((m) => m.shopId));
              }}
            >
              <SquareCheckBox
                checked={dataSource?.length > 0 && shopIds.length === dataSource?.length}
                disabled={dataSource?.length === 0}
              />
              <span className={classNames(text13, 'text-[#3D3D3D]')}>全选</span>
            </div>
            <div
              className={classNames(text13, 'text-primary')}
              onClick={() => {
                shopIds.length > 0 && ShopInfoPopupRef?.current?.onOpen();
              }}
            >
              已选：
              {shopIds.length ?? 0}
            </div>
          </div>
          <Button
            className="text-[16px] leading-6 font-medium h-[2.5rem]"
            color="primary"
            block
            onClick={() => {
              if (!shopIds.length) {
                Toast.show({
                  content: '请选择门店',
                });
                return;
              }
              setSelectedShopIds(shopIds);
              navigate(`/flow/batchEdit?platform=${searchParams.platform}&dateMonth=${searchParams.dateMonth}`);
            }}
          >
            确定
          </Button>
        </div>
      </div>
      <ShopInfoPopup ref={ShopInfoPopupRef} selectedShopList={shopList?.filter((f) => shopIds.includes(f.shopId))} />
    </div>
  );
}
