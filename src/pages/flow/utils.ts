import H5Bridge from '@tastien/rn-bridge/lib/h5';

/**
 * 转为千分位格式金额
 */
export function formatThousandthAmount(amount: any, decimalPlaces: number = 2): string {
  // 检查输入是否为数字
  if (typeof amount !== 'number' || isNaN(amount)) {
    console.error('无效数字:', amount);
    return '-';
  }

  // 处理负数
  const isNegative = amount < 0;
  amount = Math.abs(amount);

  // 格式化数字为字符串，并保留指定的小数位数
  let formattedAmount = amount.toFixed(decimalPlaces);

  // 分离整数部分和小数部分
  const [integerPart, decimalPart] = formattedAmount.split('.');

  // 添加千分位分隔符
  const integerPartWithCommas = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');

  // 重新组合整数部分和小数部分
  formattedAmount = decimalPart ? `${integerPartWithCommas}.${decimalPart}` : integerPartWithCommas;

  // 如果是负数，添加负号
  if (isNegative) {
    formattedAmount = `-${formattedAmount}`;
  }

  return formattedAmount;
}

/** 获取当前月份的最后一天 */
export function getLastDayOfMonth() {
  const now = new Date(); // 当前日期和时间
  const month = now.getMonth() + 1; // 当前月份
  const lastDay = new Date(now.getFullYear(), month, 0); // 设置为下一个月的0号，即当前月份的最后一天
  return `${month}.${lastDay.getDate()}`; // 返回当前月份的最后一天的日期
}

/** 隐藏 rn 端头部 tabbar */
export function hiddenHeader() {
  H5Bridge.customPostMessage.customPostMessage({
    module: 'navigation',
    method: 'setOptions',
    params: {
      headerShown: false,
    },
  });
}
/** 判断设备 */
export const deviceJudgment = () => {
  const u = navigator.userAgent;
  if (u.indexOf('Android') > -1 || u.indexOf('Linux') > -1) {
    // 安卓手机
    return 'Android';
  } else if (u.indexOf('iPhone') > -1) {
    // 苹果手机
    return 'iPhone';
  } else if (u.indexOf('iPad') > -1) {
    // iPad
    return 'iPad';
  } else if (u.indexOf('Windows Phone') > -1) {
    // winphone手机
    return 'WindowsPhone';
  } else {
    return '';
  }
};

/** 保留两位小数金额正则 */
export const amountPattern = 'd+(.d{1,2})?';

/**
 * 格式化金额，保留两位小数，并去除前导零
 */
export const formatAmount = (amount: number | string) => {
  let value = String(amount);
  if (value.startsWith('0') && value !== '0') {
    value = value.replace(/^0+/, '');
  }
  // value = parseFloat(value).toFixed(2);
  return Number(value);
};

/** 投流计划 id */
export const flowPlanId = -999;

/** 默认小偏小 */
export const defaultRate = 1.3;
