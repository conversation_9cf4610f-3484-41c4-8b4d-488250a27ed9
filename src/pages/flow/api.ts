import { post } from '@src/api';
import {
  AddIntentEditParams,
  AddIntentItem,
  DynamicDataItem,
  DynamicDataList,
  PlatformEnum,
  ShopPromotionIntent,
  ShopPromotionIntentDetail,
  ShopPromotionIntentList,
} from './types';

/**
 * 获取投流列表
 */
export const queryShopPromotionIntent = (data: any) =>
  post<ShopPromotionIntentList>('/om-api/common/shopPromotionIntent/list/yyt', { data });

/**
 * 获取门店详情
 */
export const queryShopPromotionIntentDetailById = (data: {
  intentId: number;
  dateMonth: string;
  platform: '0' | '1';
}) => post<ShopPromotionIntentDetail>('/om-api/common/shopPromotionIntent/detail/yyt', { data });

/**
 * 修改投流计划
 */
export const updateShopPromotionIntent = (data: ShopPromotionIntent) =>
  post('/om-api/common/shopPromotionIntent/joinOrUnJoin/yyt', { data });

/**
 * 批量修改投流计划
 */
export const batchUpdateShopPromotionIntent = (data: { promotionList: ShopPromotionIntent[] }) =>
  post('/om-api/common/shopPromotionIntent/joinOrUnJoinBatch/yyt', { data });

/** =============================== 加投 =============================== */
interface AddIntentParams {
  shopIds: string[];
  dateMonth: string;
  platform: PlatformEnum;
}

interface AddIntentPlanItem {
  id: number;
  name: string;
  dateMonth: string;
  platform: 0 | 1;
  shopNum: number;
  totalAddAmount: number;
  addAmountDate: string;
  addDateMin: string;
  addDateMax: string;
  status: number;
  createTime: string;
  updateTime: string;
}
/**
 * 获取当前投流计划的所有加投计划
 */
export const queryAddIntentBatch = (data: AddIntentParams) =>
  post<AddIntentPlanItem[]>('/om-api/common/c/takeaway/addIntent/batch/getBatch', { data });

/**
 * 获取当前投流计划是否有待操作的加投计划
 */
export const queryToBeOperatedBatch = (data: AddIntentParams) =>
  post('/om-api/common/c/takeaway/addIntent/batch/getOptBatch', { data });

/**
 * 批量修改加流计划
 */
export const updateAddIntentBatch = (data: AddIntentEditParams) =>
  post('/om-api/common/takeaway/addIntent/edit', { data });

/**
 * 加投列表
 */
export const queryAddIntentList = (data: { batchId: string; shopIds: string[]; status?: 1 }) =>
  post<AddIntentItem[]>('/om-api/common/takeaway/addIntent/list', { data });

/**
 * 门店日维度预算详情
 */
export const queryDayIntentDetail = (data: { shopPromotionIntentId: number }) =>
  post<DynamicDataList>('/om-api/common/shopPromotionIntent/shop/day/detail', { data });

/**
 * 批量查询门店日维度预算详情
 */
export const queryBatchDayIntentDetail = (data: { shopPromotionIntentIds: number[] }) =>
  post<{ yytDynamicDataList: { shopPromotionIntentId: string; yytDynamicDataList: DynamicDataItem[] }[] }>(
    '/om-api/common/shopPromotionIntent/shop/day/batchQuery',
    { data },
  );
