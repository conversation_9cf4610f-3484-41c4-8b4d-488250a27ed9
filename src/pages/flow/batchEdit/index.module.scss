@import '../common.scss';

$bg: #f7f8fa;
.batch__edit {
  background: $bg;
  width: 100%;
  height: 100vh;
  overflow-y: scroll;

  :global {
    .adm-tabs-header {
      border-bottom: none;
    }
  }

  &__navigation {
    z-index: 10;
    // width: 375px;
    width: 100%;
    height: 44px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    // margin-bottom: ;
    background: white;
    margin-bottom: 12px;

    &__back {
      font-size: 24px;
    }

    &__title {
      font-size: 16px;
      font-weight: 500;
      line-height: 24px;
      text-align: center;
      color: #141414;
    }

    &__unit {
      font-size: 12px;
      line-height: 20px;
      text-align: right;
      color: #4e5969;
    }
  }

  &__input__wrap {
    background: linear-gradient(180deg, #ebeffc 0%, #f7f8fa 100%);
    padding: pxToVw(12);

    .input {
      width: pxToVw(351);
      height: pxToVw(40);
      border-radius: pxToVw(8);
      background: #ffffff;
      border: 0.5 solid #e5e6eb;
      padding-left: pxToVw(12);
      font-size: pxToVw(14);
    }
    input::-webkit-input-placeholder {
      font-size: pxToVw(12);
    }
    .search__button {
      position: absolute;
      right: pxToVw(18);
      // bottom: pxToVw(18);
      top: pxToVw(47);
      width: pxToVw(48);
      height: pxToVw(28);
      border-radius: pxToVw(4);
      display: flex;
      justify-content: center;
      align-items: center;
      background: #3491fa;
      color: white;
    }
  }

  &__shops {
    // display: flex;
    // flex-direction: column;
    // align-items: center;
    // padding: 12px;
    // background: #f7f8fa;
    // height: 100%;
    // overflow-y: scroll;
    margin-bottom: 80px;
  }

  &__bottom {
    position: fixed;
    bottom: 0px;
    // width: 375px;
    width: 100%;
    height: 80px;
    background: #ffffff;
    box-sizing: border-box;
    border-top: 0.5px solid #e5e6eb;
    padding: 0px 12px;

    &__operation {
      height: 40px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 13px;
    }

    &__submit {
      height: 40px;
      border-radius: 4px;
      display: flex;
      justify-content: center;
      align-items: center;
      background: #3491fa;

      font-size: 16px;
      font-weight: 500;
      line-height: 24px;
      color: #ffffff;
    }
  }
}

.shop {
  border-radius: 12px;
  background: #ffffff;
  padding: 12px 12px 0px;
  margin: 0 12px 12px;

  &__head {
    display: flex;
    justify-content: space-between;

    &__title {
      font-size: 15px;
      font-weight: 500;
      line-height: 24px;
      height: 24px;
      color: #1d2129;
    }

    &__right {
      display: flex;
      row-gap: 4px;
    }
  }

  &__id {
    font-size: 12px;
    line-height: 20px;
    color: #1d2129;
  }

  &__promotion__info {
    width: 100%;
    height: 60px;
    border-radius: 8px;
    background: #f7f8fa;
    display: flex;
    align-items: center;
    margin-bottom: 5px;

    .item {
      padding-left: 12px;
      width: 117px;
      height: 44px;

      .label {
        font-size: 13px;
        height: 22px;
        line-height: 22px;
        color: #86909c;
      }

      .value {
        font-size: 14px;
        height: 22px;
        line-height: 22px;
        color: #1d2129;
      }

      &:last-of-type {
        flex: 1;
      }
    }
  }

  &__balance {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 38px;
    line-height: 38px;

    .label {
      font-size: 14px;
      color: #4e5969;
      display: flex;
      align-items: center;
    }

    .value {
      font-size: 16px;
      font-weight: 500;
    }
  }

  &__account__amount {
    @extend .shop__balance;
    border-top: 0.5px solid #f0f0f0;
  }
}

.budget_mode {
  height: 24px;
  margin: 4px 0 8px;
}

.shop__account__amount {
  @extend .shop__balance;
}
