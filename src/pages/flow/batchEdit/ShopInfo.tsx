import { memo, useEffect, useMemo, useState } from 'react';
import { Input, Radio } from 'antd-mobile';
import styles from './index.module.scss';
import Calendar from '../components/Calendar';
import SquareCheckBox from '../components/SquareCheckBox';
import { BudgetMode, DynamicDataItem, ShopPromotionIntentDetail } from '../types';
import { amountPattern, formatAmount } from '../utils';

const ShopInfo = memo(
  ({
    plan,
    setPlans,
    changeShopFn,
    keyword,
    dateMonth,
    setDayBudget,
    dayBudget,
  }: {
    plan: ShopPromotionIntentDetail & { transPreM1: string; expanded: boolean };
    setPlans: (data: any) => void;
    changeShopFn: (shopId: string[]) => void;
    keyword: string;
    dateMonth: string;
    setDayBudget: (data: Record<string, DynamicDataItem>) => void;
    dayBudget: Record<string, DynamicDataItem>;
  }) => {
    // 解析dateMonth为年和月
    const [year, month] = useMemo(() => {
      if (dateMonth) {
        const [y, m] = dateMonth.split('-');
        return [Number(y), Number(m)];
      }
      // 默认使用当前年月
      const now = new Date();
      return [now.getFullYear(), now.getMonth() + 1];
    }, [dateMonth]);

    const [value, setValue] = useState(plan.transPreM1);
    const [budgetMode, setBudgetMode] = useState<BudgetMode>(plan.costType || BudgetMode.平均预算);
    const [dailyBudgets, setDailyBudgets] = useState<Record<string, DynamicDataItem>>(dayBudget);

    // 当预算模式或展开状态变化时，确保立即更新到父组件的plans
    useEffect(() => {
      if (budgetMode !== plan.costType) {
        setPlans({
          [plan.shopId]: {
            ...plan,
            costType: budgetMode,
          },
        });
        changeShopFn([plan.shopId]);
      }
    }, [budgetMode, plan.costType, plan.shopId, setPlans, changeShopFn]);

    const status = useMemo(() => {
      if (!plan.joinFlag) return 'disabled';
      if (Number(plan.transPreM1) > Number(plan.amtTransPreM1Max)) return 'error';
      else return 'enabled';
    }, [plan]);

    const hidden = useMemo(() => {
      if (!keyword) return false;
      return !plan.shopName.includes(keyword) && plan.shopId !== keyword;
    }, [keyword]);

    const amountColor = useMemo(() => {
      return {
        disabled: '#4E5969',
        error: '#F53F3F',
        enabled: '#1D2129',
      }[status];
    }, [status]);

    const realTimePredictIncome = useMemo(() => {
      return (Number(value) * Number(plan.rateTraffic)).toFixed(2);
    }, [value]);

    const handleBudgetModeChange = (mode: BudgetMode) => {
      setBudgetMode(mode);
      // 立即更新父组件状态
      setPlans({
        [plan.shopId]: {
          ...plan,
          costType: mode,
        },
      });
      changeShopFn([plan.shopId]);
    };

    const handleDailyBudgetChange = (date: string, value: DynamicDataItem) => {
      const newDailyBudgets = { ...dailyBudgets, [date]: value };
      setDailyBudgets(newDailyBudgets);
      setDayBudget(newDailyBudgets);
      changeShopFn([plan.shopId]);
    };

    const totalBudget = useMemo(() => {
      if (budgetMode === BudgetMode.动态预算) {
        return dailyBudgets
          ? Object.values(dailyBudgets).reduce((acc, cur) => {
              return acc + Number(cur.bidAmount) * 100;
            }, 0) / 100
          : 0;
      }
      return Number(value);
    }, [budgetMode, dailyBudgets, value]);

    // 处理日历展开/收起变化
    const handleCalendarExpandChange = (isExpanded: boolean) => {
      // 立即更新父组件状态以触发高度重新计算
      setPlans({
        [plan.shopId]: {
          ...plan,
          expanded: isExpanded,
        },
      });
    };

    return (
      <div className={`${styles.shop}`} style={{ display: hidden ? 'none' : 'block', height: hidden ? 0 : 'auto' }}>
        <div className={styles.shop__head}>
          <div className={styles.shop__head__title}>{plan.shopName}</div>
          <div className={styles.shop__head__right}>
            <div className="flex items-center">
              <SquareCheckBox
                checked={!!plan.joinFlag}
                onChange={(e) => {
                  changeShopFn([plan.shopId]);
                  setPlans({ [plan.shopId]: { ...plan, joinFlag: e ? 1 : 0 } });
                }}
              />
              <span className="text-[#3D3D3D] text-[13px] leading-[22px] font-normal ml-1">参与投流</span>
            </div>
          </div>
        </div>
        <div className={`${styles.shop__id} mb-2`}>{plan.shopId}</div>
        <div className={styles.budget_mode}>
          <Radio.Group value={budgetMode} onChange={(val) => handleBudgetModeChange(val as BudgetMode)}>
            <Radio
              style={{
                '--icon-size': '20px',
                '--font-size': '13px',
                '--gap': '4px',
                marginRight: '18px',
              }}
              value={BudgetMode.平均预算}
            >
              平均预算
            </Radio>
            <Radio
              style={{
                '--icon-size': '20px',
                '--font-size': '13px',
                '--gap': '4px',
              }}
              value={BudgetMode.动态预算}
            >
              动态预算
            </Radio>
          </Radio.Group>
        </div>
        <div className={styles.shop__promotion__info}>
          <div className={styles.item}>
            <div className={styles.label}>建议推广金额</div>
            <div className={styles.value}>{plan.amtTransPreM1}</div>
          </div>
          <div className={styles.item}>
            <div className={styles.label}>预计推广收入</div>
            <div className={styles.value}>{realTimePredictIncome}</div>
          </div>
          <div className={styles.item}>
            <div className={styles.label}>投产比</div>
            <div className={styles.value}>{plan.rateTraffic}</div>
          </div>
        </div>
        <div className={styles.shop__balance}>
          <div className={styles.label}>
            账户余额{plan.balanceLess && <div className="text-xs text-[#F53F3F]">（余额不足）</div>}
          </div>
          <div
            className={`text-sm ${Number(plan.balance) < Number(plan.transPreM1) ? 'text-[#F53F3F]' : 'text-[#4E5969]'}`}
          >
            {plan.balance}
          </div>
        </div>
        {budgetMode === BudgetMode.平均预算 ? (
          <div className={styles.shop__account__amount}>
            <div className={styles.label}>实际预算金额</div>
            <div className="flex items-center">
              <Input
                style={{
                  '--text-align': 'right',
                  '--color': amountColor,
                  '--font-size': '14px',
                  width: '200px',
                  opacity: 1,
                  fontWeight: status === 'disabled' ? 400 : 500,
                }}
                className={styles.value}
                placeholder="请输入"
                value={value}
                disabled={status === 'disabled'}
                onChange={(e) => {
                  const transPreM1 = formatAmount(e);
                  // 最多保留两位小数
                  const formattedValue = Math.round(transPreM1 * 100) / 100;
                  setValue(String(formattedValue));
                }}
                onBlur={() => {
                  if (String(value) !== String(plan.transPreM1)) {
                    // 验证预算金额是否超出限制
                    changeShopFn([plan.shopId]);
                    setPlans({
                      [plan.shopId]: {
                        ...plan,
                        transPreM1: value,
                      },
                    });
                  }
                }}
                type="number"
                min={plan.amtTransPreM1Min}
                max={999}
                pattern={amountPattern}
                inputMode="decimal"
              />
            </div>
          </div>
        ) : (
          <>
            <div className={styles.shop__account__amount}>
              <div className={styles.label}>实际预算金额</div>
              <div className={styles.value}>{totalBudget}</div>
            </div>
            <Calendar
              values={dailyBudgets}
              onChange={handleDailyBudgetChange}
              year={year}
              month={month}
              minValue={plan.amtTransPreM1DayMin || 0}
              expanded={!!plan.expanded}
              onExpandChange={handleCalendarExpandChange}
              disabled={!plan.joinFlag}
            />
          </>
        )}
      </div>
    );
  },
);

export default ShopInfo;
