import { useCallback, useEffect, useRef } from 'react';
import { BudgetMode } from '../types';

// 高度配置
const HEIGHT_CONFIG = {
  AVERAGE_BUDGET: 248, // 平均预算模式高度
  DYNAMIC_BUDGET_COLLAPSED: 286, // 动态预算但日历未展开
  DYNAMIC_BUDGET_EXPANDED: 586, // 动态预算且日历展开
  DEFAULT: 248, // 默认高度
  HIDDEN: 0, // 隐藏项的高度
};

// 创建全局高度缓存对象，用于组件之间共享
export const globalHeightCache: { current: Record<string, number> } = { current: {} };

/**
 * 计算列表项高度的钩子函数
 * @param plans 当前的所有计划
 * @param filteredPlans 当前过滤后的计划
 * @param budgetMode 当前预算模式
 * @param keyword 搜索关键字
 * @returns 高度计算函数和相关工具
 */
export function useHeightCalculation<
  T extends {
    costType: BudgetMode;
    expanded?: boolean;
    shopId: string;
    shopName: string;
  },
>(plans: Record<string, T>, filteredPlans: T[], budgetMode: BudgetMode, keyword: string) {
  // 列表引用
  const listRef = useRef<any>(null);

  // 当plans或budgetMode或keyword变化时，重置列表高度
  useEffect(() => {
    if (listRef.current) {
      // 重置所有列表项的高度
      listRef.current.resetAfterIndex(0);
    }
  }, [plans, budgetMode, keyword]);

  // 检查项目是否应该被隐藏（基于关键字搜索）
  const shouldItemBeHidden = useCallback(
    (item: T) => {
      if (!keyword) return false;
      return !item.shopName.includes(keyword) && item.shopId !== keyword;
    },
    [keyword],
  );

  // 获取特定项的高度
  const getItemHeight = useCallback(
    (index: number) => {
      const items = filteredPlans;
      if (index >= items.length) return 0;

      const item = items[index];
      if (!item) return HEIGHT_CONFIG.DEFAULT;

      // 如果项目应该被隐藏，返回0高度
      if (shouldItemBeHidden(item)) {
        return HEIGHT_CONFIG.HIDDEN;
      }

      // 根据不同状态设置高度
      if (item.costType === BudgetMode.平均预算) {
        return HEIGHT_CONFIG.AVERAGE_BUDGET;
      } else if (item.costType === BudgetMode.动态预算 && !item.expanded) {
        return HEIGHT_CONFIG.DYNAMIC_BUDGET_COLLAPSED;
      } else if (item.costType === BudgetMode.动态预算 && item.expanded) {
        return HEIGHT_CONFIG.DYNAMIC_BUDGET_EXPANDED;
      }

      return HEIGHT_CONFIG.DEFAULT;
    },
    [filteredPlans, shouldItemBeHidden],
  );

  // 重置特定索引后的高度
  const resetHeight = useCallback((index: number) => {
    if (listRef.current) {
      requestAnimationFrame(() => {
        listRef.current?.resetAfterIndex(index);
      });
    }
  }, []);

  return {
    listRef,
    getItemHeight,
    resetHeight,
    HEIGHT_CONFIG,
    shouldItemBeHidden,
  };
}
