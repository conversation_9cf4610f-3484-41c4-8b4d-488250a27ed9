import { memo, useEffect, useMemo, useRef, useState } from 'react';
import { IconFont, Loading } from '@src/components';
import useQuerySearchParams from '@src/hooks/useQuerySearchParams';
import ShopStore from '@src/store/flow';
import { useRequest, useSetState } from 'ahooks';
import { Input, NavBar, SafeArea, Tabs, Toast } from 'antd-mobile';
import { LeftOutline } from 'antd-mobile-icons';
import { useNavigate } from 'react-router-dom';
import { VariableSizeList } from 'react-window';
import styles from './index.module.scss';
import ShopInfo from './ShopInfo';
import { useHeightCalculation } from './useHeightCalculation';
import { batchUpdateShopPromotionIntent, queryAddIntentBatch, queryBatchDayIntentDetail } from '../api';
import BottomPopup from '../components/BottomPopup';
import ConfirmPopup from '../components/ConfirmPopup';
import QuitePopup from '../components/QuitePopup';
import SquareCheckBox from '../components/SquareCheckBox';
import { BudgetMode, DynamicDataItem, ShopPromotionIntentDetail } from '../types';
import { deviceJudgment, flowPlanId, hiddenHeader } from '../utils';

type ShopPromotionIntentMap = Record<string, ShopPromotionIntentDetail & { expanded?: boolean; transPreM1: string }>;
export default function BatchEdit() {
  const [budgetMode, setBudgetMode] = useState<BudgetMode>(BudgetMode.平均预算);
  const [visible, setVisible] = useState(false);
  const [selectAll, setSelectAll] = useState(false);
  const [inputVal, setInputVal] = useState('');
  const [keyword, setKeyword] = useState('');
  const [searchParams]: any = useQuerySearchParams();

  const [changeShopRecord, setChangeShopRecord] = useState<string[]>([]);
  const [unJoinReason, setUnJoinReason] = useState('');
  const [visible2, setVisible2] = useState(false);
  const { selectedShopIds, shopPromotionMap, shopFlowMap } = ShopStore;
  const [planVisible, setPlanVisible] = useState(false);
  const [columns, setColumns] = useState<{ label: string; value: number }[][]>([[]]);
  const initShopMap: any = {};

  // 存储门店的初始预算模式，用于后续过滤
  const initialCostTypes = useRef<Record<string, BudgetMode>>({});

  selectedShopIds.forEach((shopId) => {
    const shop = shopPromotionMap[shopId];
    initShopMap[shopId] = {
      id: shop.id,
      shopName: shop.shopName,
      joinFlag: shop.joinFlag,
      shopId: shop.shopId,
      amtTransPreM1: shop.amtTransPreM1,
      amtOrderTransPreM1: shop.amtOrderTransPreM1,
      amtTransPreM1Max: shop.amtTransPreM1Max,
      amtTransPreM1Min: shop.amtTransPreM1Min,
      amtTransPreM1DayMin: shop.amtTransPreM1DayMin,
      rateTraffic: shop.rateTraffic,
      balanceLess: shop.balanceLess,
      balance: shop.balance,
      transPreM1: shop.transPreM1 ? String(shop.transPreM1) : '0',
      realAmtOrderTransPreM1: shop.realAmtOrderTransPreM1,
      costType: shop.costType,
      expanded: false,
    };

    // 记录初始预算模式
    initialCostTypes.current[shopId] = shop.costType || BudgetMode.平均预算;
  });

  const { loading: batchDayLoading } = useRequest(
    async () => {
      // 获取所有需要请求的ID
      const allIds = Object.values(initShopMap).map((item: any) => item.id);

      // 分批处理，每批最多100个ID
      const batchSize = 100;
      const batches = [];

      for (let i = 0; i < allIds.length; i += batchSize) {
        const batchIds = allIds.slice(i, i + batchSize);
        batches.push(batchIds);
      }

      // 串行发送所有批次的请求，每次请求间隔300ms
      const batchResults = [];
      for (const batchIds of batches) {
        const result = await queryBatchDayIntentDetail({
          shopPromotionIntentIds: batchIds,
        });
        batchResults.push(result);

        // 添加300ms延迟，除非是最后一批
        if (batchIds !== batches[batches.length - 1]) {
          await new Promise((resolve) => setTimeout(resolve, 300));
        }
      }

      // 合并所有批次的结果
      const mergedResult = {
        yytDynamicDataList: batchResults.flatMap((res) => res.yytDynamicDataList || []),
      };

      return mergedResult;
    },
    {
      onSuccess(res) {
        const dayDetailMap: Record<string, Record<string, DynamicDataItem>> = {};
        res.yytDynamicDataList.forEach((item) => {
          const budgetMap: Record<string, DynamicDataItem> = {};
          (item?.yytDynamicDataList || [])?.forEach((item) => {
            budgetMap[item.bidDate] = item;
          });
          dayDetailMap[item.shopPromotionIntentId] = budgetMap;
        });
        setDayBudget(dayDetailMap);
      },
    },
  );
  const [dayBudget, setDayBudget] = useState<Record<string, Record<string, DynamicDataItem>>>({});

  const [plans, setPlans] = useSetState<ShopPromotionIntentMap>(initShopMap);

  useEffect(() => {
    setTimeout(() => {
      hiddenHeader();
    }, 0);
  }, []);

  const { loading, runAsync: batchUpdatePlan } = useRequest(
    async (params) => {
      await batchUpdateShopPromotionIntent(params);
    },
    { manual: true },
  );

  useRequest(async () => {
    const res = await queryAddIntentBatch({
      shopIds: selectedShopIds,
      dateMonth: searchParams.dateMonth,
      platform: searchParams.platform,
    });
    const data = res.map((item) => {
      return {
        label: item.name,
        value: item.id,
      };
    });
    const month = new Date(searchParams.dateMonth).getMonth() + 1;

    data.unshift({
      label: `${month}月投流计划`,
      value: flowPlanId,
    });
    setColumns([data]);
  });

  const initialData: ShopPromotionIntentMap = Object.freeze(initShopMap);
  const navigate = useNavigate();

  const changeShopFn = (shopIds: string[]) => setChangeShopRecord([...new Set([...changeShopRecord, ...shopIds])]);
  /** 判断本次修改是否有把投流修改为未投流状态 */
  const hasUnjoin = ({
    initialData,
    plans,
  }: {
    initialData: ShopPromotionIntentMap;
    plans: ShopPromotionIntentMap;
  }) => {
    const plansArr = Object.values(plans);

    return plansArr.some((p) => {
      const { joinFlag } = initialData[p.shopId];
      // 初始化投流，修改后未投流
      return joinFlag && !p.joinFlag;
    });
  };

  /** 对比数据，过滤去掉未修改的数据 */
  const compareData = (initialData: ShopPromotionIntentMap, plans: ShopPromotionIntentMap) => {
    const submitData = Object.values(plans);

    const promotionList: any[] = [];
    submitData.forEach((item: any) => {
      // const { joinFlag, transPreM1, costType } = initialData[item.shopId];
      /** 投流状态有变化，或者投流状态为已投流，且修改了推广费用 */

      // if (
      //   item.joinFlag !== joinFlag ||
      //   (item.joinFlag && Number(item.transPreM1) !== Number(transPreM1)) ||
      //   costType !== item.costType
      // ) {
      if (item.joinFlag) {
        const dailyBudgets = dayBudget[item.id];
        const totalBudget = Object.values(dailyBudgets).reduce((acc, cur) => {
          return acc + Number(cur.bidAmount);
        }, 0);

        const transPreM1 = item.costType === BudgetMode.动态预算 ? totalBudget : +item.transPreM1;
        const dynamicDataList =
          item.costType === BudgetMode.动态预算
            ? {
                dynamicDataList: Object.keys(dailyBudgets).map((item) => {
                  return {
                    date: item,
                    transPreM1: dailyBudgets[item].bidAmount,
                  };
                }),
              }
            : {};
        promotionList.push({
          joinFlag: item.joinFlag,
          transPreM1,
          optSource: 1,
          intentId: item.id,
          costType: item.costType,
          ...dynamicDataList,
        });
      } else {
        const dailyBudgets = dayBudget[item.id];
        const totalBudget = Object.values(dailyBudgets).reduce((acc, cur) => {
          return acc + Number(cur.bidAmount);
        }, 0);
        const transPreM1 = item.costType === BudgetMode.动态预算 ? { transPreM1: totalBudget } : {};

        const dynamicDataList =
          item.costType === BudgetMode.动态预算
            ? {
                dynamicDataList: Object.keys(dailyBudgets).map((item) => {
                  return {
                    date: item,
                    transPreM1: dailyBudgets[item].bidAmount,
                  };
                }),
              }
            : {};
        promotionList.push({
          joinFlag: item.joinFlag,
          unJoinReason,
          optSource: 1,
          intentId: item.id,
          costType: item.costType,
          ...transPreM1,
          ...dynamicDataList,
        });
      }
      // }
    });

    return promotionList;
  };
  /**
   * 校验需要提交的数据是否合法
   * @param promotionList 校验提交的数据
   * @returns boolean
   */
  const checkDataValidate = (promotionList: any[]) => {
    return promotionList.every((item) => {
      const { amtTransPreM1Min, amtTransPreM1Max, shopId } = shopFlowMap[item.intentId];
      const { joinFlag, transPreM1, costType } = item;
      if (joinFlag && costType === BudgetMode.平均预算) {
        if (transPreM1 < amtTransPreM1Min) {
          Toast.show({
            content: `门店${shopId}每月实际推广费用不能小于${amtTransPreM1Min}元`,
            duration: 2000,
          });
          return false;
        } else if (transPreM1 > amtTransPreM1Max) {
          Toast.show({
            content: `门店${shopId}每月实际推广费用不能大于${amtTransPreM1Max}元`,
            duration: 2000,
          });
          return false;
        }
      }
      return true;
    });
  };

  const isIPhone = deviceJudgment() === 'iPhone';

  const handleSubmit = async () => {
    const promotionList = compareData(initialData, plans);
    console.log('promotionList', promotionList, initialData, plans);

    if (changeShopRecord.length === 0) {
      Toast.show({
        content: '无任何调整门店',
        duration: 3000,
      });
      return;
    }

    const valid = checkDataValidate(promotionList);

    if (!valid) return;
    console.log('valid', promotionList);
    await batchUpdatePlan({ promotionList });
    Toast.show({
      icon: 'success',
      content: '操作成功',
      duration: 3000,
      afterClose: async () => {
        navigate('/flow/index');
      },
    });
  };

  // 根据当前选中的预算模式过滤门店
  const filteredPlans = useMemo(() => {
    // 根据初始化时记录的预算模式进行过滤
    return Object.entries(plans)
      .filter(([shopId]) => {
        const initialType = initialCostTypes.current[shopId];
        return budgetMode === initialType;
      })
      .map(([_, plan]) => plan);
  }, [plans, budgetMode]);

  // 统计每种预算模式的门店数量
  const modeCounts = useMemo(() => {
    const counts = {
      [BudgetMode.平均预算]: 0,
      [BudgetMode.动态预算]: 0,
    };

    Object.keys(initialCostTypes.current).forEach((shopId) => {
      const mode = initialCostTypes.current[shopId];
      counts[mode] = (counts[mode] || 0) + 1;
    });

    return counts;
  }, []);

  // 使用抽离出的高度计算hook，确保传入 keyword
  // 在BatchEdit组件中

  // 确保将keyword传递给useHeightCalculation
  const { listRef, getItemHeight, shouldItemBeHidden } = useHeightCalculation(
    plans,
    filteredPlans,
    budgetMode,
    keyword,
  );

  // 在RowRenderer中使用shouldItemBeHidden
  const RowRenderer = memo(
    ({ index, style }: { index: number; style: React.CSSProperties }) => {
      if (index >= filteredPlans.length) return null;

      const item = filteredPlans[index];
      if (!item) return null;

      // 如果项目应该被隐藏，返回null或空div
      if (shouldItemBeHidden(item)) {
        return <div style={{ ...style, height: 0 }} />;
      }

      // 获取此门店的日维度预算数据
      const data = dayBudget[item.id] || {};

      return (
        <div style={style}>
          <ShopInfo
            keyword={keyword}
            key={item.id}
            plan={item}
            dayBudget={data}
            dateMonth={searchParams.dateMonth}
            setDayBudget={(value) =>
              setDayBudget(() => {
                return { ...dayBudget, [item.id]: value };
              })
            }
            setPlans={setPlans}
            changeShopFn={changeShopFn}
          />
        </div>
      );
    },
    (prevProps, nextProps) => {
      // 优化重渲染逻辑
      return (
        prevProps.index === nextProps.index &&
        prevProps.style.top === nextProps.style.top &&
        prevProps.style.height === nextProps.style.height
      );
    },
  );

  return (
    <Loading spinning={loading || batchDayLoading}>
      {!isIPhone && <div className="min-h-[2.5rem] bg-white" />}
      <NavBar
        className="bg-white"
        back={
          <div className="flex justify-center items-center">
            <LeftOutline fontSize={16} color="#000" />
            <div className="text-[13px] ml-1">返回门店</div>
          </div>
        }
        backArrow={false}
        onBack={() => {
          navigate(-1);
        }}
        right={<div className={styles.batch__edit__navigation__unit}>单位：元</div>}
      >
        <span className="text-base text-[#141414] font-medium">批量调整</span>
      </NavBar>
      <div className={`${styles.batch__edit}`}>
        <div className="sticky top-0 bg-white">
          <div className={styles.batch__edit__input__wrap}>
            <div className="flex items-center mb-3">
              <div className="flex items-center h-5 text-[13px]  text-[#3491FA]" onClick={() => setPlanVisible(true)}>
                <IconFont type="icon-swap" className="mr-1 text-base" />
                切换
              </div>
              <div className="w-[0.5px] h-3 bg-[#C9CDD4] mx-2" />
              <div className="text-[#1D2129] text-sm font-medium">{columns?.[0]?.[0]?.label}</div>
            </div>
            <Input
              className={styles.input}
              value={inputVal}
              onChange={(e) => setInputVal(e.trim())}
              placeholder="输入门店名称/ID"
              style={{
                '--placeholder-color': '#86909C',
              }}
            />
            <div
              className={styles.search__button}
              onClick={() => {
                setKeyword(inputVal);
              }}
            >
              搜索
            </div>
          </div>
          {/* 预算模式切换Tab */}
          <Tabs
            activeLineMode="fixed"
            activeKey={String(budgetMode)}
            onChange={(value) => setBudgetMode(Number(value) as BudgetMode)}
            style={{
              '--active-line-height': '4px',
              '--fixed-active-line-width': '20px',
              '--active-title-color': '#1D2129',
              '--active-line-border-radius': '2px',
              '--title-font-size': '14px',
              fontWeight: 500,
              color: '#86909C',
              marginBottom: '12px',
              backgroundColor: '#f7f8fa',
              borderBottom: 'none',
            }}
          >
            <Tabs.Tab
              title={`${BudgetMode[2]} (${modeCounts[BudgetMode.平均预算] || 0})`}
              key={String(BudgetMode.平均预算)}
            />
            <Tabs.Tab
              title={`${BudgetMode[1]} (${modeCounts[BudgetMode.动态预算] || 0})`}
              key={String(BudgetMode.动态预算)}
            />
          </Tabs>
        </div>

        <div className={styles.batch__edit__shops}>
          <VariableSizeList
            ref={listRef}
            width="100%"
            height={530}
            itemCount={filteredPlans.length}
            itemSize={getItemHeight}
            itemKey={(index) => filteredPlans[index]?.shopId || index}
            overscanCount={3}
          >
            {RowRenderer}
          </VariableSizeList>
        </div>
        <div className={styles.batch__edit__bottom}>
          <div className={styles.batch__edit__bottom__operation}>
            <div className="flex items-center">
              <SquareCheckBox
                checked={selectAll}
                onChange={(e) => {
                  setPlans(() => {
                    Object.values(plans).forEach((p: ShopPromotionIntentDetail) => {
                      p.joinFlag = e ? 1 : 0;
                    });
                    return plans;
                  });
                  changeShopFn(Object.keys(plans));
                  setSelectAll(e);
                }}
              />
              <span className={'text-[#3D3D3D] text-[13px] leading-[22px] font-normal ml-1'}>全选</span>
            </div>
            <div className="text-[#3491FA]">
              已调整门店：{changeShopRecord.length}/{Object.keys(plans).length}
            </div>
          </div>
          <div
            onClick={() => {
              const unJoin = hasUnjoin({ initialData, plans });
              if (unJoin) {
                setVisible2(true);
              } else {
                setVisible(true);
              }
            }}
            className={styles.batch__edit__bottom__submit}
          >
            提交修改
          </div>
        </div>
        <SafeArea position="bottom" />
        <ConfirmPopup
          title="修改推广金额确认"
          subTitle={
            <div className="mb-2 text-base leading-6 text-[#4E5969] flex flex-col justify-center items-center">
              <div>您是否要修改本月后续的</div>
              <div>投流计划金额？</div>
            </div>
          }
          description={
            <div className="text-[#4E5969] text-xs leading-5">
              <div>·当日20点前，修改投流计划，T+1 生效</div>
              <div>·当日20点后，修改投流计划，T+2 生效</div>
            </div>
          }
          visible={visible}
          setVisible={setVisible}
          onCancelClick={() => {}}
          onConfirmClick={async () => {
            await handleSubmit();
          }}
        />

        <QuitePopup
          title="批量修改投流计划确认"
          subTitle="本次调整中，存在退出投流的门店。请确认是否需要退出投流～"
          description={
            <div className="text-[#4E5969] text-xs leading-5">
              <div>·当日20点前，退出投流计划，T+1 生效</div>
              <div>·当日20点后，退出投流计划，T+2 生效</div>
            </div>
          }
          visible={visible2}
          setVisible={setVisible2}
          value={unJoinReason}
          setValue={setUnJoinReason}
          onConfirmClick={async () => {
            await handleSubmit();
            setUnJoinReason('');
          }}
          onCancelClick={() => {
            setUnJoinReason('');
          }}
          cancelText="取消"
          confirmText="确认"
          style={{
            minHeight: 0,
            height: '10px',
            '--border-radius	': '12',
          }}
        />

        <BottomPopup
          visible={planVisible}
          setVisible={setPlanVisible}
          columns={columns}
          title="选择计划"
          onConfirmClick={async (value) => {
            if (value !== flowPlanId) {
              ShopStore.setShopPromotionIntentList(Object.values(plans));
              navigate(
                `/flow/addBatchEdit?platform=${searchParams.platform}&dateMonth=${searchParams.dateMonth}&planId=${value}`,
                {
                  replace: true,
                },
              );
              setPlanVisible(false);
            }
          }}
        />
      </div>
    </Loading>
  );
}
