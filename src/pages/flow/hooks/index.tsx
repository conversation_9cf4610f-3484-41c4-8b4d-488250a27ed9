import { useEffect, useState } from 'react';
import dayjs from 'dayjs';
export const useAvailableDate = () => {
  const [availableDate, setAvailableDate] = useState('');
  useEffect(() => {
    const calculateDate = () => {
      const now = new Date();
      const currentHour = now.getHours();

      // 如果当前时间在 8 点前，取T+1；8 点后取 T+2；
      const adjustedDate =
        currentHour < 20
          ? new Date(now.getTime() + 24 * 60 * 60 * 1000)
          : new Date(now.getTime() + 48 * 60 * 60 * 1000);
      const date = dayjs(adjustedDate).format('YYYY-MM-DD');

      setAvailableDate(date);
    };

    calculateDate();

    return () => {
      setAvailableDate('');
    };
  }, []);
  return { availableDate };
};
