import { useEffect, useState } from 'react';
import projectedCost from '@src/assets/images/flow/projected_cost.png';
import { Loading } from '@src/components';
import useQuerySearchParams from '@src/hooks/useQuerySearchParams';
import { userStore } from '@src/store';
import { useRequest } from 'ahooks';
import { ErrorBlock, Image, SafeArea, Toast } from 'antd-mobile';
import { LeftOutline } from 'antd-mobile-icons';
import { useNavigate } from 'react-router-dom';
import AddInvestmentPlan from './AddInvestmentPlan';
import styles from './index.module.scss';
import Plan from './Plan';
import {
  queryDayIntentDetail,
  queryShopPromotionIntentDetailById,
  updateAddIntentBatch,
  updateShopPromotionIntent,
} from '../api';
import AddPromotionPopup from '../components/AddPromotionPopup';
import PromotionPopup from '../components/PromotionPopup';
import QuitePopup from '../components/QuitePopup';
import {
  AddIntentEditParams,
  AddIntentState,
  FlowAuthorityEnum,
  PlanState,
  type ShopAddIntentDetailBatchesItem,
} from '../types';
import { deviceJudgment, hiddenHeader } from '../utils';

export default function Detail() {
  const [promotionVisible, setVisible] = useState(false);
  const [quiteVisible, setQuiteVisible] = useState(false);
  const [addIntentQuiteVisible, setAddIntentQuiteVisible] = useState(false);
  const [addPromotionVisible, setAddPromotionVisible] = useState(false);
  const [searchParams]: any = useQuerySearchParams();
  const { permissionsMap } = userStore;
  const isIPhone = deviceJudgment() === 'iPhone';
  const [quiteTitle, setQuiteTitle] = useState('');
  const [addIntentTitle, setAddIntentTitle] = useState('');
  const [quiteSubTitle, setQuiteSubTitle] = useState('');
  const [addIntentPlan, setAddIntentPlan] = useState<ShopAddIntentDetailBatchesItem | null>(null);
  const [flowTitle, setFlowTitle] = useState<string>('');
  const {
    data,
    loading: loading1,
    runAsync: refreshPage,
  } = useRequest(async () => {
    console.log({
      intentId: Number(searchParams.id),
      dateMonth: searchParams.dateMonth,
      platform: searchParams.platform,
    });

    const res = await queryShopPromotionIntentDetailById({
      intentId: Number(searchParams.id),
      dateMonth: searchParams.dateMonth,
      platform: searchParams.platform,
    });
    res.shopAddIntentDetailBatches = res?.shopAddIntentDetailBatches || [];
    return res;
  });

  const {
    data: dayDetail,
    loading: loading4,
    runAsync: updateDayDetail,
  } = useRequest(async () => await queryDayIntentDetail({ shopPromotionIntentId: searchParams.id }));

  const [unJoinReason, setUnJoinReason] = useState('');
  const { runAsync: updatePlan, loading: loading2 } = useRequest(
    async (params) => {
      return await updateShopPromotionIntent(params);
    },
    {
      manual: true,
    },
  );
  useEffect(() => {
    if (promotionVisible) {
      updateDayDetail();
    }
  }, [promotionVisible]);
  const {
    runAsync: updateAddPlan,
    loading: loading3,
  }: { runAsync: (params: AddIntentEditParams) => Promise<any>; loading: boolean } = useRequest(
    async (params) => {
      return await updateAddIntentBatch(params);
    },
    {
      manual: true,
    },
  );

  useEffect(() => {
    setTimeout(() => {
      hiddenHeader();
    }, 0);
  }, []);

  useEffect(() => {
    document.querySelector('body')?.setAttribute('style', 'background: #3491fa');
    return () => {
      document.querySelector('body')?.removeAttribute('style');
    };
  }, []);

  const navigate = useNavigate();

  if (!data) return null;
  if (!permissionsMap.has(FlowAuthorityEnum.门店投流计划详情)) {
    return <ErrorBlock status="empty" title="您没有该页面权限" description={<span>请与客服人员联系！</span>} />;
  }

  const handleConfirmQuite = async ({ type }: { type: PlanState }) => {
    if (type === PlanState.加投) {
      if (!addIntentPlan) throw Error('addIntentPlan is null');
      await updateAddPlan({
        batchId: +addIntentPlan.batchId,
        addIntentEditDetailDtos: addIntentPlan.shopAddIntentDetails.map((item) => {
          return {
            id: item.id,
            joinFlag: 0,
            amount: item.amount,
          };
        }),
        cancelReason: unJoinReason,
      });
    } else if (type === PlanState.投流) {
      await updatePlan({ intentId: data.id, joinFlag: 0, optSource: 1, unJoinReason });
    }
    setUnJoinReason('');
    Toast.show({
      icon: 'success',
      content: '操作成功',
      afterClose: async () => {
        await refreshPage();
      },
    });
  };
  const loading = loading1 || loading2 || loading3 || loading4;
  return (
    <Loading spinning={loading}>
      {!isIPhone && <div className="min-h-[2.5rem] bg-[#3491fa]" />}
      <div className={styles.flow__detail}>
        <div className={styles.flow__detail__head}>
          <div className={`${styles.flow__detail__head__navigation} relative`}>
            <div className={styles.flow__detail__head__navigation__back}>
              <LeftOutline
                onClick={() => {
                  navigate(-1);
                }}
                fontSize={16}
                color="#ffffff"
              />
            </div>
            <div className="absolute left-1/2 translate-x-[-50%] flex flex-col justify-center items-center text-white">
              <div className="text-xs font-medium leading-5 text-center">{data.shopId}</div>
              <div className="text-base font-medium leading-6 text-center">{data.shopName}</div>
            </div>
            <div className="text-xs text-white">单位：元</div>
          </div>

          <div className={styles.flow__detail__head__expense}>
            <div className="flex flex-col flex-1 ml-4">
              <div className={styles.label}>推广费用（预估当月总计）</div>
              <div className={styles.fee}>{data.realTransPreM1}</div>
            </div>
            <div className={styles.line} />
            <div className="flex flex-col flex-1 ml-3">
              <div className={styles.label}>门店推广余额</div>
              <div className={styles.fee}>{data.balance}</div>
            </div>
          </div>
          <div className={styles.flow__detail__head__plan}>
            {data.shopPromotionBriefs.map((item, index) => {
              return (
                <div key={index} className="flex justify-between mb-0.5">
                  <div>{`${index ? '+' : ''}${item.promotionName}`}</div>
                  <div className="font-medium">{item.amtTransPreM1}</div>
                </div>
              );
            })}
            {data.shopAddIntentDetailBatches
              .filter((item) =>
                [AddIntentState.待开始, AddIntentState.进行中, AddIntentState.已完成].includes(item.status),
              )
              .map((item, index) => {
                return (
                  <div key={index} className="flex justify-between mb-0.5 font-normal">
                    <div>{`+ ${item.name}`}</div>
                    <div className="font-medium">{item.totalAmount}</div>
                  </div>
                );
              })}
          </div>
        </div>
        <div className={styles.flow__detail__cost}>
          <Image className="mb-2" height={40} src={projectedCost} />
          <div className="flex justify-between mb-1.5 mx-3">
            <div className="label">{`预估消耗金额（截止${data.endDate}）`}</div>
            <div className="amount">{data.totalAmtTransPreM1}</div>
          </div>
          <div className="flex justify-between mb-1.5 mx-3">
            <div className="label">预估消耗金额（截止下周一）</div>
            <div className="amount">{data.totalAmtTransToNextMonday}</div>
          </div>
        </div>
        <div className={styles.flow__detail__parting__line}>
          <div className={styles.dashed} />
          <div className={styles.title}>计划明细</div>
        </div>
        <div className={styles.flow__detail__plans}>
          <Plan
            data={data}
            setVisible={setVisible}
            setVisible2={setQuiteVisible}
            permissionsMap={permissionsMap}
            setQuiteTitle={setQuiteTitle}
            setQuiteSubTitle={setQuiteSubTitle}
            setTitle={setFlowTitle}
          />
          {data.shopAddIntentDetailBatches.map((item) => (
            <AddInvestmentPlan
              data={item}
              setAddIntentPlan={setAddIntentPlan}
              setVisible={setAddPromotionVisible}
              setVisible2={setAddIntentQuiteVisible}
              permissionsMap={permissionsMap}
              setQuiteTitle={setQuiteTitle}
              setQuiteSubTitle={setQuiteSubTitle}
              setAddIntentTitle={setAddIntentTitle}
            />
          ))}
        </div>
        <SafeArea position="bottom" />
        {promotionVisible && (
          <PromotionPopup
            title={flowTitle}
            loading={loading}
            visible={promotionVisible}
            data={data}
            dayDetail={dayDetail}
            dateMonth={searchParams.dateMonth}
            setVisible={setVisible}
            value={data.transPreM1 ? String(data.transPreM1) : '0'}
            refreshPage={refreshPage}
            updateDayDetail={updateDayDetail}
            updatePlan={updatePlan}
          />
        )}
        {addPromotionVisible && (
          <AddPromotionPopup
            title={addIntentTitle}
            loading={loading}
            visible={addPromotionVisible}
            data={addIntentPlan!}
            setVisible={setAddPromotionVisible}
            refreshPage={refreshPage}
            updatePlan={updateAddPlan}
          />
        )}
        <QuitePopup
          type={PlanState.加投}
          title={quiteTitle}
          subTitle={quiteSubTitle}
          description={
            <div className="text-[#4E5969] text-xs leading-5">
              <div>·当日20点前，退出投流计划，T+1 生效</div>
              <div>·当日20点后，退出投流计划，T+2 生效</div>
            </div>
          }
          visible={addIntentQuiteVisible}
          setVisible={setAddIntentQuiteVisible}
          value={unJoinReason}
          setValue={setUnJoinReason}
          onConfirmClick={handleConfirmQuite}
          onCancelClick={() => {
            setUnJoinReason('');
          }}
          cancelText="取消"
          confirmText="确认退出"
          style={{
            minHeight: 0,
            height: '10px',
            '--border-radius	': '12',
          }}
        />
        <QuitePopup
          type={PlanState.投流}
          title={quiteTitle}
          subTitle={quiteSubTitle}
          description={
            <div className="text-[#4E5969] text-xs leading-5">
              <div>·当日20点前，退出投流计划，T+1 生效</div>
              <div>·当日20点后，退出投流计划，T+2 生效</div>
            </div>
          }
          visible={quiteVisible}
          setVisible={setQuiteVisible}
          value={unJoinReason}
          setValue={setUnJoinReason}
          onConfirmClick={handleConfirmQuite}
          onCancelClick={() => {
            setUnJoinReason('');
          }}
          cancelText="取消"
          confirmText="确认退出"
          style={{
            minHeight: 0,
            height: '10px',
            '--border-radius	': '12',
          }}
        />
      </div>
    </Loading>
  );
}
