import { memo } from 'react';
import styles from './index.module.scss';
import AddInvestmentDate from '../components/AddInvestmentDate';
import AddInvestmentValue from '../components/AddInvestmentValue';
import { useAvailableDate } from '../hooks';
import { AddIntentState, FlowAuthorityEnum, ShopAddIntentDetailBatchesItem } from '../types';

const planState: Record<string, { color: string; background: string }> = {
  未参与: { color: '#F53F3F', background: '#FFECE8' },
  待确认: { color: '#F53F3F', background: '#FFECE8' },
  待开始: { color: '#86909C', background: '#F2F3F5' },
  进行中: { color: '#00B42A', background: '#E8FFEA' },
  已完成: { color: '#86909C', background: '#F2F3F5' },
  已退出: { color: '#F53F3F', background: '#FFECE8' },
  已取消: { color: '#86909C', background: '#F2F3F5' },
};

const AddInvestmentPlan = ({
  data,
  setVisible,
  setVisible2,
  permissionsMap,
  setQuiteTitle,
  setQuiteSubTitle,
  setAddIntentPlan,
  setAddIntentTitle,
}: {
  data: ShopAddIntentDetailBatchesItem;
  setVisible: (visible: boolean) => void;
  setVisible2: (visible: boolean) => void;
  permissionsMap: Map<string, string>;
  setQuiteTitle: (title: string) => void;
  setQuiteSubTitle: (subTitle: string) => void;
  setAddIntentTitle: (title: string) => void;
  setAddIntentPlan: (data: ShopAddIntentDetailBatchesItem) => void;
}) => {
  const { availableDate } = useAvailableDate();
  return (
    <div className={styles.plan}>
      <div className={styles.plan__head}>
        <div
          className={styles.plan__head__tag}
          style={{
            background: planState[data.statusDesc]?.['background'],
            color: planState[data.statusDesc]?.['color'],
          }}
        >
          {AddIntentState[data.status]}
        </div>
        <div>{data.name}</div>
      </div>
      <AddInvestmentDate availableDate={availableDate} date={data.addDates} />
      <div className="h-[0.5px] bg-[#E5E6EB] w-full my-3" />
      <AddInvestmentValue className="mb-1" />
      <div className="mb-3">
        {data.shopAddIntentDetails.map((item) => {
          const disabled = new Date(availableDate).getTime() > new Date(item.date).getTime();
          return (
            <div key={item.id} className={`flex items-center justify-between mb-1 text-sm text-[#1D2129]`}>
              <div className={`${disabled && 'line-through'}`}>{item.date}</div>
              <div className="font-medium">{item.amount}</div>
            </div>
          );
        })}
      </div>
      {data.optFlag && (
        <div className={styles.plan__operation}>
          {![AddIntentState.已退出, AddIntentState.未参与].includes(data.status) && (
            <div className="flex justify-between gap-x-[11px]">
              {permissionsMap.has(FlowAuthorityEnum.加入退出加投计划) && (
                <div
                  className="flex items-center justify-center flex-1 rounded-[4px] bg-white h-10 border text-[#4e5969]"
                  style={{ border: '0.5px solid #c9cdd4' }}
                  onClick={() => {
                    setQuiteTitle('退出加投计划确认');
                    setQuiteSubTitle(`您是否要退出【${data.name}】？`);
                    setAddIntentPlan(data);
                    setVisible2(true);
                  }}
                >
                  退出计划
                </div>
              )}
              {permissionsMap.has(FlowAuthorityEnum.修改加投推广费用) && (
                <div
                  className="flex items-center justify-center flex-1 rounded-[4px] bg-[#3491FA] h-10"
                  onClick={() => {
                    setAddIntentTitle('修改推广金额');
                    setAddIntentPlan(data);
                    setVisible(true);
                  }}
                >
                  修改推广计划
                </div>
              )}
            </div>
          )}
          {permissionsMap.has(FlowAuthorityEnum.加入退出加投计划) && (
            <>
              {[AddIntentState.已退出, AddIntentState.未参与].includes(data.status) && (
                <div
                  className="bg-[#3491FA] rounded-[4px] flex justify-center items-center text-white h-10"
                  onClick={() => {
                    setAddIntentTitle('加入推广');
                    setAddIntentPlan(data);
                    setVisible(true);
                  }}
                >
                  加入计划
                </div>
              )}
            </>
          )}
        </div>
      )}
    </div>
  );
};
const MemoPlan = memo(AddInvestmentPlan);

export default MemoPlan;
