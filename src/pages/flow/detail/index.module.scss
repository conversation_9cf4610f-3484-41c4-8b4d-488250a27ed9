@import "../common.scss";

$bg: #f7f8fa;
.flow__detail {
  background: $bg;
  height: 100vh;
  overflow-y: scroll;

  .flow__detail__head {
    display: flex;
    flex-direction: column;
    align-items: center;
    background: #3491fa;

    &__mask {
      position: absolute;
      top: 0;
    }
    &__navigation {
      width: pxToVw(375);
      height: pxToVw(44);
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: pxToVw(16);
      padding: 0 pxToVw(12);
    }
    &__expense {
      width: 100%;
      height: pxToVw(54);
      color: white;
      margin-bottom: pxToVw(12);
      display: flex;
      position: relative;

      .label {
        font-size: pxToVw(13);
        font-weight: 500;
        display: flex;
        align-items: baseline;
        line-height: pxToVw(22);
      }
      .fee {
        font-size: pxToVw(24);
        font-weight: bold;
        line-height: pxToVw(32);
      }
      .line {
        position: absolute;
        left: 50%;
        width: pxToVw(0.5);
        height: pxToVw(54);
        opacity: 1;
        background: #206ccf;
      }
    }
    &__plan {
      width: pxToVw(351);
      border-radius: 0px pxToVw(12) pxToVw(12) pxToVw(12);
      /* 海蔚蓝/blue-7 */
      background: #206ccf;
      color: white;
      font-size: pxToVw(13);
      font-weight: 500;
      padding: pxToVw(12) pxToVw(12) pxToVw(10);
      margin-bottom: pxToVw(44);
    }
  }

  &__cost {
    width: pxToVw(351);
    border-radius: pxToVw(12);
    opacity: 1;
    background: #ffffff;
    color: #1d2129;
    padding-bottom: pxToVw(6);
    margin: pxToVw(-32) auto 0;

    .label {
      font-size: pxToVw(13);
      line-height: pxToVw(22);
    }
    .amount {
      font-size: pxToVw(14);
      font-weight: 500;
      line-height: pxToVw(22);
    }
  }

  &__parting__line {
    position: relative;
    margin: pxToVw(12);
    height: pxToVw(20);

    .dashed {
      position: absolute;
      top: 50%;
      width: 100%;
      height: 0;
      border-top: pxToVw(0.5) dashed #d9d9d9;
    }

    .title {
      width: pxToVw(71);
      text-align: center;
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      font-size: pxToVw(12);
      line-height: pxToVw(20);
      color: #86909c;
      background: $bg;
    }
  }

  &__plans {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
}

.plan {
  width: pxToVw(351);
  border-radius: pxToVw(12);
  background: #ffffff;
  margin-bottom: pxToVw(12);
  padding: pxToVw(12);

  &__head {
    display: flex;
    align-items: center;
    height: pxToVw(24);
    font-size: pxToVw(15);
    font-weight: 500;
    line-height: pxToVw(24);
    color: #1d2129;
    margin-bottom: pxToVw(8);

    &__tag {
      display: flex;
      justify-content: center;
      align-items: center;
      width: pxToVw(40);
      height: pxToVw(20);
      // background: #e8ffea;
      font-size: pxToVw(11);
      font-weight: normal;
      letter-spacing: 0px;
      // color: #00b42a;
      margin-right: pxToVw(4);
      border-radius: pxToVw(4);
    }
  }

  &__content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: pxToVw(4);
  }

  &__operation {
    margin-top: pxToVw(8);
    font-size: pxToVw(14);
    font-weight: 500;
    color: white;
    // height: pxToVw(40);

    .cancel {
      flex: 1;
      border-radius: pxToVw(4);
      display: flex;
      justify-content: center;
      align-items: center;
      background: #ffffff;
      border: 0.5px solid #c9cdd4;
      height: pxToVw(40);
      color: #4e5969;
      /** 禁用硬件加速 safari 浏览器border 显示不全问题 */
      -webkit-transform: translate3d(0, 0, 0);
      transform: translate3d(0, 0, 0); /* 在其他浏览器中也要使用 */
    }
  }
}
