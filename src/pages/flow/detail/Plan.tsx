import { memo } from 'react';
import styles from './index.module.scss';
import { FlowAuthorityEnum, type ShopPromotionIntentDetail } from '../types';

const planState: Record<string, { color: string; background: string }> = {
  进行中: { color: '#00B42A', background: '#E8FFEA' },
  未开始: { color: '#86909C', background: '#F2F3F5' },
  已退出: { color: '#F53F3F', background: '#FFECE8' },
  已结束: { color: '#86909C', background: '#F2F3F5' },
  未参与: { color: '#F53F3F', background: '#FFECE8' },
};

const Plan = ({
  data,
  setVisible,
  setVisible2,
  permissionsMap,
  setQuiteTitle,
  setQuiteSubTitle,
  setTitle,
}: {
  data: ShopPromotionIntentDetail;
  setVisible: (visible: boolean) => void;
  setVisible2: (visible: boolean) => void;
  permissionsMap: Map<string, string>;
  setQuiteTitle: (title: string) => void;
  setQuiteSubTitle: (subTitle: string) => void;
  setTitle: (subTitle: string) => void;
}) => {
  return (
    <div className={styles.plan}>
      <div className={styles.plan__head}>
        <div
          className={styles.plan__head__tag}
          style={{
            background: planState[data.statusDesc]?.['background'],
            color: planState[data.statusDesc]?.['color'],
          }}
        >
          {data.statusDesc}
        </div>
        <div>{data.promotionName}</div>
      </div>
      <div className={styles.plan__content}>
        <div className={styles.label}>建议推广金额</div>
        <div className={styles.amount}>{data.amtTransPreM1}</div>
      </div>
      <div className={styles.plan__content}>
        <div className={styles.label}>实际预算金额</div>
        <div className={styles.amount}>{data.transPreM1}</div>
      </div>
      <div className={styles.plan__content}>
        <div className={styles.label}>投产比</div>
        <div className={styles.amount}>{data.rateTraffic}</div>
      </div>
      <div className={styles.plan__content}>
        <div className={styles.label}>预估推广收入</div>
        <div className={styles.amount}>{data.realAmtOrderTransPreM1}</div>
      </div>
      {!(data.statusDesc === '已结束' || !data.statusDesc) && (
        <div className={styles.plan__operation}>
          {['进行中', '未开始'].includes(data.statusDesc) && (
            <div className="flex justify-between gap-x-[11px]">
              {permissionsMap.has(FlowAuthorityEnum.加入退出计划) && (
                <div
                  className="flex items-center justify-center flex-1 rounded-[4px] bg-white h-10 border text-[#4e5969]"
                  style={{ border: '0.5px solid #c9cdd4' }}
                  onClick={() => {
                    setQuiteTitle('退出投流计划确认');
                    setQuiteSubTitle('您是否要退出本月后续的投流计划？');
                    setVisible2(true);
                  }}
                >
                  退出计划
                </div>
              )}
              {permissionsMap.has(FlowAuthorityEnum.修改推广费用) && (
                <div
                  className="flex items-center justify-center flex-1 rounded-[4px] bg-[#3491FA] h-10"
                  onClick={() => {
                    setTitle('修改推广计划');
                    setVisible(true);
                  }}
                >
                  修改推广计划
                </div>
              )}
            </div>
          )}
          {permissionsMap.has(FlowAuthorityEnum.加入退出计划) && (
            <>
              {['未参与', '已退出'].includes(data.statusDesc) && (
                <div
                  className="bg-[#3491FA] rounded-[4px] flex justify-center items-center text-white h-10"
                  onClick={() => {
                    setTitle('加入推广计划');
                    setVisible(true);
                  }}
                >
                  加入计划
                </div>
              )}
            </>
          )}
        </div>
      )}
    </div>
  );
};
const MemoPlan = memo(Plan);

export default MemoPlan;
