export interface ShopPromotionIntent {
  optSource: 1; //	 操作场景 1 yyt 2 后台
  joinFlag: 0 | 1; //	 是否参与
  transPreM1?: number; //	 实际推广费用
  unJoinReason?: string; //	 退出原因
  dynamicDataList: { date: string; transPreM1: string }[];
  costType: CostType;
}

export enum BudgetMode {
  动态预算 = 1,
  平均预算,
}

export type CostType = BudgetMode;

export interface DynamicDataList {
  yytDynamicDataList: DynamicDataItem[];
}

export interface DynamicDataItem {
  bidDate: string; // 出价日期
  bidAmount: number; // 出价日期-金额
}

export type PlanStatus = '进行中' | '未开始' | '已完成' | '已退出' | '已结束' | '未参与';

export interface ShopPromotionIntentItem {
  id: number;
  brandId: number;
  date: string;
  shopId: string;
  shopName: string;
  platform: number;
  platformName: string;
  shopIdPlat: string;
  areaFlag: string;
  amtTransPreM1: number;
  rateTraffic: number;
  amtOrderTransPreM1: number;
  realAmtOrderTransPreM1: number;
  transPreM1: number;
  joinFlag: 0 | 1;
  status: number;
  sendFlag: number;
  tryTime: number;
  batchId: number;
  sourceId: number | null;
  balance: number;
  statusDesc: PlanStatus;
  shopNamePlat: string;
  pushPersonName: string | null;
  pushPersonPhone: string | null;
  amtTransPreM1Min: number;
  amtTransPreM1Max: number;
  balanceLess: boolean;
}

export interface ShopPromotionIntentList {
  totalShopNum: number;
  joinShopNum: number;
  noJoinShopNum: number;
  balanceLessShopNum: number;
  totalBalance: number;
  totalAmtTransPreM1: number;
  balanceAmountLimit: number;
  shopPromotionIntentList: ShopPromotionIntentItem[];
}

export interface ShopAddIntentDetail {
  id: number;
  date: string;
  amount: number;
  initAmount: number;
}

export interface ShopAddIntentDetailBatchesItem {
  addDates: string[];
  batchId: string;
  floatRange: number; // 金额上限浮动范围
  name: string;
  optFlag: boolean;
  shopAddIntentDetails: ShopAddIntentDetail[];
  status: number;
  statusDesc: string;
  totalAmount: number;
}
export interface ShopPromotionIntentDetail {
  id: number;
  brandId: number; // 品牌id
  date: string; // 日期 月份
  shopId: string; // 门店编码
  shopName: string; // 门店名称
  platform: 0 | 1; // 0 美团，1 饿了么
  platformName: string; // 平台名称
  shopIdPlat: string; // 第三方平台门店编码
  areaFlag: string; // 象限分布
  amtTransPreM1: number; // 预估推广费用【建议推广金额】
  rateTraffic: number; // 投放比率_配置表
  amtOrderTransPreM1: number; // 预估推广收入
  realAmtOrderTransPreM1: number; // 实时计算预估推广收入
  transPreM1: number | string; // 整月 实际推广费用
  joinFlag: number; // 是否参与统投
  sendFlag: 0 | 1 | 2; // 0 未发送 1发生成功 2发送失败
  tryTime: number; // 重试次数
  batchId: number; // 批次id
  balance: number;
  statusDesc: PlanStatus;
  amtTransPreM1DayMin: number; // 预估推广费用日纬度【可调整的最小金额】
  amtTransPreM1Min: number; // 预估推广费用【可调整的最小金额】
  amtTransPreM1Max: number; // 预估推广费用【可调整的最大金额】
  promotionName: string; // 推广计划名称【月计划】
  realTransPreM1: number; // 实时计算预估推广费用【汇总，还有后续的加投计划】
  totalAmtTransPreM1: number; // 预估消耗金额(截止12.31)
  totalAmtTransToNextMonday: number; // 预估消耗金额(截止下周一)
  shopPromotionBriefs: ShopPromotionBriefs[];
  balanceLess: boolean; // 是否余额不足
  endDate: string; // 投流月份的截止时间，格式 1.31
  shopAddIntentDetailBatches: ShopAddIntentDetailBatchesItem[]; // 加投列表
  costType: CostType;
}

export interface ShopPromotionBriefs {
  promotionName: string; // 推广计划名称
  amtTransPreM1: string; // 预估推广费用【建议推广金额】
}

export enum PlatformEnum {
  meituan = '0',
  eleme = '1',
}

export enum FlowAuthorityEnum {
  外卖推广 = '$app$flow$takeaway$spread',
  批量调整 = '$app$flow$takeaway$batchEdit',
  门店投流计划详情 = '$app$flow$takeaway$detail',
  修改推广费用 = '$app$flow$takeaway$detail$edit',
  加入退出计划 = '$app$flow$takeaway$detail$joinExit',
  修改加投推广费用 = '$app$addFlow$takeaway$detail$edit',
  加入退出加投计划 = '$app$addFlow$takeaway$detail$joinExit',
}

export enum AddIntentState {
  '待确认' = 1,
  '待开始' = 3,
  '进行中' = 4,
  // 这个状态数据库 不存在实际计算得来的
  '已完成' = 5,
  // 这个状态数据库 不存在实际计算得来的
  '已退出' = 6,
  '已取消' = 7,
  // 这个状态数据库 不存在实际计算得来的
  '未参与' = 8,
}

export interface AddIntentEditItem {
  id: number;
  joinFlag: 0 | 1;
  amount: number;
}

export interface AddIntentEditParams {
  batchId: number;
  addIntentEditDetailDtos: AddIntentEditItem[];
  cancelReason?: string;
}

export enum PlanState {
  投流,
  加投,
}

export interface ShopAddIntentItem {
  id: number;
  batchId: number;
  dateMonth: string;
  platform: number;
  date: string;
  shopName: string;
  shopId: string;
  shopIdPlat: string;
  amount: number;
  status: number;
  joinFlag: boolean;
  beforeJoinFlag: boolean;
  createTime: string;
  updateTime: string;
  createUesrId: number;
  updateUserId: number;
  initAmount: number;
}
export interface AddIntentItem {
  shopName: string;
  shopId: string;
  statusDesc: string;
  status: AddIntentState;
  joinDesc: string;
  joinFlag: boolean;
  dates: string[];
  totalAmount: number;
  shopAddIntentDtos: ShopAddIntentItem[];
  floatRange: number;
  id: string;
  selected: boolean;
  shopAddIntentMap: Record<string, ShopAddIntentItem>;
  optFlag: boolean;
}
export type AddIntentMap = Record<string, AddIntentItem>;
