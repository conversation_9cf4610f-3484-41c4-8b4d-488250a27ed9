body {
  // background-color: #f40 !important;
}

$bg: #f7f8fa;
.flow__index {
  background: $bg;
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  .flow__index__head {
    display: flex;
    flex-direction: column;
    align-items: center;
    background: #3491fa;
    height: 280px;
    margin-bottom: 25px;
    &__unit {
      color: #ffffff;
      margin-right: 10px;
      font-size: 12px;
      font-weight: normal;
    }

    &__mask {
      position: absolute;
      top: 0;
    }
    &__navigation {
      width: 375px;
      height: 44px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      &__tabs {
        font-size: 16px;
        color: #ffffff;
      }
    }
    &__expense {
      width: 100%;
      height: 54px;
      color: white;
      margin-bottom: 12px;
      display: flex;
      position: relative;

      .label {
        font-size: 13px;
        font-weight: 500;
        display: flex;
        align-items: baseline;
        line-height: 22px;
      }
      .fee {
        font-size: 24px;
        font-weight: bold;
        line-height: 32px;
      }
      .line {
        position: absolute;
        left: 50%;
        width: 0.5px;
        height: 54px;
        opacity: 1;
        background: #206ccf;
      }
    }
    &__shop {
      border-radius: 0px 12px 12px 12px;
      background: #206ccf;
      color: white;
      font-size: 13px;
      font-weight: 500;
      padding: 12px 12px 10px;
      margin: 0 auto;
    }
  }

  &__cost {
    // border-radius: 12px;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
    opacity: 1;
    color: #1d2129;
    padding-bottom: 6px;
    margin: 0 12px 0;
    z-index: 1;

    .label {
      font-size: 13px;
      line-height: 22px;
    }
    .amount {
      font-size: 14px;
      font-weight: 500;
      line-height: 22px;
    }
  }

  &__parting__line {
    position: relative;
    margin: 12px;
    height: 20px;

    .dashed {
      position: absolute;
      top: 50%;
      width: 100%;
      height: 0;
      border-top: 0.5px dashed #d9d9d9;
    }

    .title {
      width: 71px;
      text-align: center;
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      font-size: 12px;
      line-height: 20px;
      color: #86909c;
      background: $bg;
    }
  }

  &__shops {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
}

.shop {
  border-radius: 12px;
  background: #ffffff;
  padding: 12px 12px 0px 12px;
  margin: 0.625rem 0.75rem;
  height: 160px;

  &__head {
    display: flex;
    align-items: center;
    height: 24px;
    font-size: 15px;
    font-weight: 500;
    line-height: 24px;
    color: #1d2129;
    justify-content: space-between;

    &__tag {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 40px;
      height: 20px;
      background: #e8ffea;
      font-size: 11px;
      font-weight: normal;
      letter-spacing: 0px;
      color: #00b42a;
      margin-right: 4px;
      margin-left: 4px;
      border-radius: 4px;
    }
    &__Notag {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 40px;
      height: 20px;
      background: #f2f3f5;
      font-size: 11px;
      font-weight: normal;
      letter-spacing: 0px;
      color: #4e5969;
      margin-right: 4px;
      margin-left: 4px;
      border-radius: 4px;
    }
  }

  &__content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    // margin-bottom: 4px;
  }

  &__operation {
    margin-top: 8px;
    font-size: 14px;
    font-weight: 500;
    color: white;
    // height: 40px;

    .cancel {
      flex: 1;
      border-radius: 4px;
      display: flex;
      justify-content: center;
      align-items: center;
      background: #ffffff;
      border: 0.5px solid #c9cdd4;
      height: 40px;
      color: #4e5969;
    }
  }
}

//  .ant-tabs .ant-tabs-top .ant-tabs-mobile .custom-tabs .css-dev-only-do-not-override-1okl62o .ant-tabs-nav ::before {
//   border-bottom: 1px solid #3491fa !important
// }

// .custom-tabs .ant-tabs-nav-wrap::before .ant-tabs-nav-list .ant-tabs-ink-bar .ant-tabs-ink-bar-animated{
//   background: #fff !important
// }
