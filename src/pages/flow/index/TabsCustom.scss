.custom-tabs .ant-tabs-ink-bar {
  visibility: visible; /* 确保下划线可见 */
  background-color: #fff; /* 设置下划线颜色 */
  height: 2px; /* 设置下划线高度 */
  width: auto; /* 根据内容宽度自动调整 */
  transition: width 0.3s; /* 添加动画效果 */
}

/* 隐藏默认的下划线槽 */
.custom-tabs .ant-tabs-nav::before {
  display: none; /* 或者使用 visibility: hidden; */
}

/* 为激活状态添加自定义下划线 */
.custom-tabs .ant-tabs-tab-active {
  position: relative;
}

.custom-tabs .ant-tabs-tab-active::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 2px; /* 设置下划线高度 */
  // background-color: #ffffff; /* 设置下划线颜色 */
  transform: translateY(50%); /* 根据需要调整下划线位置 */
  transition: transform 0.3s; /* 添加动画效果 */
  visibility: visible; /* 确保下划线可见 */
}

.ant-checkbox-input:focus + .ant-checkbox-inner {
  box-shadow: none !important;
}
