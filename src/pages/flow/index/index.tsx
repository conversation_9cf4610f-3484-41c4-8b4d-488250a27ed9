import { memo, useEffect, useMemo, useState } from 'react';
import { InfoCircleOutlined } from '@ant-design/icons';
import caretDownBlack from '@src/assets/images/flow/caret-down-black.png';
import caretDown from '@src/assets/images/flow/caret-down.png';
import chevronRight from '@src/assets/images/flow/chevron-right.png';
import elemIcon from '@src/assets/images/flow/elem_Icon.png';
import meituanIcon from '@src/assets/images/flow/meituan_icon.png';
import monthCard from '@src/assets/images/flow/month_card.png';
import straightLine from '@src/assets/images/flow/straight_line.png';
import { IconFont, Loading } from '@src/components';
import useQuerySearchParams from '@src/hooks/useQuerySearchParams';
import { userStore } from '@src/store';
import { H5Bridge } from '@tastien/rn-bridge';
import { useRequest } from 'ahooks';
import { Checkbox } from 'antd';
import { But<PERSON>, ErrorBlock, Image, NavBar, Tabs } from 'antd-mobile';
import { LeftOutline } from 'antd-mobile-icons';
import dayjs from 'dayjs';
import ReactECharts from 'echarts-for-react';
import numeral from 'numeral';
import { useNavigate } from 'react-router-dom';
import { FixedSizeList } from 'react-window';
import styles from './index.module.scss';
import shopStore from '../../../store/flow';
import { queryShopPromotionIntent, queryToBeOperatedBatch } from '../api';
import DatePopup from '../components/DatePopup';
import DescriptionPopup from '../components/DescriptionPopup';
import ShopPopup from '../components/ShopPopup';
import './TabsCustom.scss';
import { FlowAuthorityEnum, PlatformEnum } from '../types';
import { deviceJudgment, hiddenHeader } from '../utils';

function formatDate(dateStr: string) {
  const [year, month] = dateStr?.split('-');

  return `${year}年${Number(month)}月`;
}

const Shop = ({
  Item,
  permissionsMap,
  dateMonth,
}: {
  Item: any;
  permissionsMap: Map<string, string>;
  dateMonth: string;
}) => {
  const navigate = useNavigate();
  return (
    <div className={styles.shop}>
      <div className={styles.shop__head}>
        <div className="flex items-center">
          <div>{Item?.shopName}</div>
          {Item?.joinFlag ? (
            <div className={styles.shop__head__tag}>统投</div>
          ) : (
            <div className={styles.shop__head__Notag}>未统投</div>
          )}
        </div>
        {permissionsMap.has(FlowAuthorityEnum.加入退出计划) && (
          <div
            className="text-[#4E5969] text-[12px] flex items-center "
            onClick={() => {
              navigate(`/flow/detail?id=${Item.id}&platform=${Item?.platform}&dateMonth=${dateMonth}`);
            }}
          >
            <span className="p-1 text-[12px]">查看详情</span>
            <Image width={16} height={16} src={chevronRight} />
          </div>
        )}
      </div>
      <div className="mb-[4px]">
        <div className={'text-[#1D2129] text-[12px]'}>{Item?.shopId}</div>
      </div>
      <div className="flex flex-wrap -mx-2">
        <div className="w-1/3 pl-[10px] pr-[12px] mb-[8px]">
          <div className="text-[#86909C] flex whitespace-no-wrap">
            <span>建议推广金额</span>
          </div>
          <div className=" text-[14px] " style={{ fontWeight: 500 }}>
            {Item?.amtTransPreM1}
          </div>
        </div>
        <div className="w-1/3 pl-[10px] pr-[12px] mb-[8px]">
          <div className="text-[#86909C] flex whitespace-no-wrap">预计推广收入</div>
          <div className=" text-[14px] " style={{ fontWeight: 500 }}>
            {Item?.realAmtOrderTransPreM1}
          </div>
        </div>
        <div className="w-1/3 pl-[10px] pr-[12px] mb-[8px]">
          <div className="text-[#86909C] flex whitespace-no-wrap">投产比</div>
          <div className=" text-[14px] " style={{ fontWeight: 500 }}>
            {Item?.rateTraffic}
          </div>
        </div>
        <div className="w-1/3 pl-[10px] pr-[12px] mb-[8px]">
          <div className="text-[#86909C] flex whitespace-no-wrap">实际预算金额</div>
          <div className=" text-[14px] " style={{ fontWeight: 500 }}>
            {Item?.transPreM1}
          </div>
        </div>
        <div className="w-1/3 pl-[10px] pr-[12px] mb-[8px]">
          <div className="text-[#86909C] flex whitespace-no-wrap">账户余额</div>
          <div className=" text-[14px] " style={{ fontWeight: 500 }}>
            {Item?.balance}
          </div>
        </div>
      </div>
    </div>
  );
};

const MyChartComponent = ({ option }: { option: any }) => {
  const handleChartReady = (echartsInstance: any) => {
    // 禁用所有鼠标事件
    echartsInstance.getZr().off('mousedown'); // 移除之前的事件监听器
    echartsInstance.getZr().on('mousedown', (event: any) => {
      event.stop(); // 阻止事件冒泡
    });

    // 禁用其他鼠标事件
    ['click', 'dblclick', 'mousemove', 'mouseover', 'mouseout'].forEach((eventType) => {
      echartsInstance.getZr().off(eventType);
      echartsInstance.getZr().on(eventType, (event: any) => {
        event.stop();
      });
    });

    // 禁用所有交互事件
    echartsInstance.off('click');
    echartsInstance.off('dblclick');
    echartsInstance.off('mouseover');
    echartsInstance.off('mouseout');
  };

  return (
    <>
      <ReactECharts
        option={option}
        style={{ height: 145, top: -10 }}
        notMerge
        lazyUpdate
        onChartReady={handleChartReady}
      />
    </>
  );
};
export default function Index() {
  const navigate = useNavigate();
  const [visible, setVisible] = useState(false);
  const [dateVisible, setDateVisible] = useState(false);
  const [messageVisible, setMessageVisible] = useState(false);
  const [searchParams]: any = useQuerySearchParams();
  const [activeKey, setActiveKey] = useState<'0' | '1'>(searchParams?.platform || PlatformEnum.meituan);
  const [params, setParams] = useState({
    platform: searchParams?.platform ? +searchParams?.platform : +PlatformEnum.meituan,
    date: searchParams?.date ? searchParams?.date : dayjs().format('YYYY-MM'),
  });
  const [pieData, setPieData] = useState<{ value: number; name: string; itemStyle: { color: string } }[]>([]);
  const [joinFlag, setJoinFlag] = useState(-100); // null 表示不进行过滤
  const [balanceLess, setBalanceLess] = useState<boolean | null>(null); // null 表示不进行过滤
  const { permissionsMap } = userStore;
  const [shopIds, setShopIds] = useState<string[]>([]);
  // 获取当前月份
  const currentMonth = dayjs().format('YYYY-MM');

  const isIPhone = deviceJudgment() === 'iPhone';

  // 展示的日期
  const displayDate = params.date || currentMonth;

  useEffect(() => {
    setTimeout(() => {
      hiddenHeader();
    }, 0);
  }, []);

  const option = useMemo(() => {
    const data = pieData.map((item) => ({
      value: item.value || 0,
      name: item.name,
      itemStyle: item.itemStyle,
    }));

    return {
      title: {
        text: '本月配置',
        left: '16%',
        top: '52%',
        textStyle: {
          color: '#86909C',
          fontSize: 13,
          fontWeight: 'normal',
        },
      },
      tooltip: {
        trigger: 'none',
        show: false,
      },
      legend: {
        orient: 'vertical',
        left: '50%',
        itemWidth: 8,
        itemHeight: 8,
        itemGap: 20,
        top: '25%',
        selectedMode: false,
        data: [
          { name: '门店总数', icon: 'rect' },
          { name: '参与投流门店', icon: 'rect' },
          { name: '余额不足门店', icon: 'rect' },
        ],
        formatter(name: string) {
          const dataMap: { [key: string]: number } = {
            门店总数: pieData.find((d) => d.name === '门店总数')?.value || 0,
            参与投流门店: pieData.find((d) => d.name === '参与投流门店')?.value || 0,
            余额不足门店: pieData.find((d) => d.name === '余额不足门店')?.value || 0,
          };
          const paddingMap: { [key: string]: string } = {
            门店总数: ' '.repeat(17),
            参与投流门店: ' '.repeat(10),
            余额不足门店: ' '.repeat(10),
          };
          const value = dataMap[name];
          return [`{name|${name}}${paddingMap[name]}`, `{value|${value}}`].join('');
        },
        textStyle: {
          rich: {
            name: {
              fontSize: 13,
              color: '#4E5969',
            },
            value: {
              fontSize: 14,
              fontWeight: 'bold',
              color: '#1D2129',
            },
          },
        },
      },
      series: [
        {
          name: '门店配置',
          type: 'pie',
          radius: ['60%', '80%'],
          center: ['25%', '60%'],
          avoidLabelOverlap: false,
          label: {
            show: false,
          },
          clickable: false, // 禁止点击
          data,
        },
      ],
    };
  }, [pieData]);

  const { data, loading } = useRequest(
    async () => {
      const res = await queryShopPromotionIntent(params);
      setPieData([
        { value: res?.joinShopNum, name: '参与投流门店', itemStyle: { color: '#A871E3' } },
        { value: res?.balanceLessShopNum, name: '余额不足门店', itemStyle: { color: '#F9CC45' } },
        { value: res?.totalShopNum, name: '门店总数', itemStyle: { color: '#F2F3F5' } },
      ]);
      shopStore.setShopPromotionIntentList(res.shopPromotionIntentList || []);
      setShopIds(res.shopPromotionIntentList?.map((item) => item.shopId) || []);
      return res;
    },
    {
      refreshDeps: [activeKey, params.date],
    },
  );

  const { data: shops } = useRequest(
    () => {
      if (!shopIds.length) return null;
      return queryToBeOperatedBatch({
        shopIds,
        dateMonth: displayDate,
        platform: +activeKey,
      });
    },
    {
      // ready: !!shopIds.length,
      refreshDeps: [shopIds],
    },
  );

  // 过滤数据
  const filteredData = useMemo(() => {
    let filteredList = data?.shopPromotionIntentList || [];
    if (joinFlag !== -100) {
      filteredList = filteredList.filter((item: any) => item.joinFlag === joinFlag);
    }
    // 过滤余额不足门店
    if (balanceLess === true) {
      filteredList = filteredList.filter((item: any) => item.balanceLess === balanceLess);
    }
    return filteredList;
  }, [data, joinFlag, balanceLess]);

  // 定义 Row 组件为 memo 包装的组件以优化性能
  const Row = memo(({ index, style }: { index: number; style: React.CSSProperties }) => {
    const item = filteredData[index]; // 获取当前项
    return (
      <div style={{ ...style }}>
        <Shop Item={item} permissionsMap={permissionsMap} dateMonth={displayDate} />
      </div>
    );
  });
  if (!permissionsMap.has(FlowAuthorityEnum.外卖推广)) {
    return <ErrorBlock status="empty" title="您没有该页面权限" description={<span>请与客服人员联系！</span>} />;
  }
  if (!data) return null;
  return (
    <Loading spinning={loading}>
      {!isIPhone && <div className="min-h-[2.5rem] bg-[#3491fa]" />}
      <div className={styles.flow__index}>
        <NavBar
          className="flex justify-between w-full p-2 text-white z-[1] mb-4"
          back={<LeftOutline fontSize={16} color="#ffffff" />}
          backArrow={false}
          onBack={() => {
            H5Bridge.navigator.goBack();
          }}
          right={<div className={styles.flow__index__head__unit}>单位：元</div>}
        >
          <Tabs
            activeKey={activeKey}
            onChange={(value) => {
              setActiveKey(value);
              setParams((p) => ({ ...p, platform: +value }));
            }}
            style={{
              '--active-line-color': '#FFFFFF',
            }}
          >
            {[
              { name: '美团外卖', value: PlatformEnum.meituan, icon: meituanIcon },
              { name: '饿了么', value: PlatformEnum.eleme, icon: elemIcon },
            ].map((m) => (
              <Tabs.Tab
                key={m.value}
                title={
                  <div
                    style={{
                      opacity: activeKey === m.value ? 1 : 0.7,
                    }}
                    className="flex items-center justify-center pr-2 text-white"
                  >
                    <img src={m.icon} alt={m.name} width={24} height={24} />
                    <span className="text-base">{m.name}</span>
                  </div>
                }
              />
            ))}
          </Tabs>
        </NavBar>
        <div className={`bg-[#3491fa] w-full  absolute top-0 z-0 ${isIPhone ? 'h-[237px]' : 'h-[280px]'}`} />
        <div className={styles.flow__index__cost}>
          {shops && (
            <div
              onClick={() => {
                navigate(`/flow/addBatchEdit?platform=${activeKey}&dateMonth=${displayDate}&from=index`);
              }}
              className="w-full h-6 rounded-[40px] bg-[#F7BA1E] flex justify-center items-center mb-2 text-white"
            >
              <IconFont type="icon-error-circle-filled" className="mr-1 text-xs" />
              计划待确认
            </div>
          )}
          <div
            style={{
              backgroundImage: `url(${monthCard})`,
              // height: 250,
              backgroundSize: '100% 100%',
            }}
          >
            <div className="flex items-center justify-between ">
              <div className="flex items-center  pt-[8px] pl-[3px]">
                <div className="mr-[3px] text-white text-[12px] ml-[8px]" style={{ fontWeight: 500 }}>
                  本月策略
                </div>
                <div className="text-white text-[12px]   " onClick={() => setDateVisible(true)}>
                  {formatDate(displayDate)}
                  <DatePopup
                    visible={dateVisible}
                    setVisible={setDateVisible}
                    setParams={setParams}
                    initDate={params?.date}
                  />
                </div>
                <Image width={16} height={16} src={caretDown} />
              </div>
              <div style={{ marginRight: 15, color: '#86909C', marginTop: 5 }}>
                <InfoCircleOutlined className="mr-1" />
                <span
                  onClick={() => {
                    setMessageVisible(true);
                  }}
                  className="text-[12px]"
                >
                  计算说明
                </span>
                <DescriptionPopup
                  visible={messageVisible}
                  value={data.balanceAmountLimit}
                  setVisible={setMessageVisible}
                />
              </div>
            </div>
            <div className="flex items-center justify-around" style={{ marginTop: '26px' }}>
              <div className="flex flex-col items-center">
                <div className=" text-[#4E5969] text-13">预估费用</div>
                <div className="mt-1 text-[24px] text-[#1D2129] font-bold">
                  {numeral(data?.totalAmtTransPreM1).format('0,  0.00')}
                </div>
              </div>
              <div className="flex flex-col items-center">
                <div className=" text-[#4E5969] text-[13px]">余额</div>
                <div className="mt-1 text-[24px] text-[#1D2129] font-bold">
                  {numeral(data?.totalBalance).format('0, 0.00')}
                </div>
              </div>
            </div>
            <div>
              <Image src={straightLine} style={{ marginTop: 12 }} />
            </div>
            <MyChartComponent option={option} />
          </div>
        </div>
        <div
          style={{
            padding: '16px 12px 0px',
            margin: '0 auto',
            width: '100%',
          }}
          className="flex items-center justify-between"
        >
          <div className="flex items-center justify-center">
            <div className="flex items-center justify-center" onClick={() => setVisible(true)}>
              <span className="pl-1">{joinFlag === -100 ? '全部门店' : joinFlag === 1 ? '统投' : ' 未统投'}</span>
              <Image width={16} height={16} src={caretDownBlack} />
            </div>
            <div className="h-[16px] w-[0px] border-[0.5px] border-[#C9CDD4]  mr-[8px] ml-[8px] " />
            <Checkbox
              onChange={(i) => {
                setBalanceLess(i.target.checked);
              }}
            >
              余额不足
            </Checkbox>
          </div>
          {permissionsMap.has(FlowAuthorityEnum.批量调整) && (
            <Button
              color="primary"
              onClick={() => {
                navigate(`/flow/shopSelected?platform=${activeKey}&dateMonth=${displayDate}`);
              }}
              style={{ width: '72px', height: '28px', fontSize: '12px', padding: '0' }}
            >
              批量调整
            </Button>
          )}
        </div>
        {filteredData?.length ? (
          <FixedSizeList
            height={500}
            width="100%"
            itemSize={172}
            itemCount={filteredData.length}
            itemKey={(index) => filteredData[index]?.id || index}
          >
            {Row}
          </FixedSizeList>
        ) : (
          <div className="flex items-center justify-center h-4/6">
            <ErrorBlock status="empty" description="暂无数据" />
          </div>
        )}
      </div>
      <ShopPopup visible={visible} setVisible={setVisible} setJoinFlag={setJoinFlag} />
    </Loading>
  );
}
