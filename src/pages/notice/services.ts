import { get } from '@src/api';

export interface INoticeDetail {
  id: number;
  title: string;
  sort: number;
  detail: string;
  published: boolean;
  startTime: string;
  endTime: string;
  operatorName: string;
  roleTypeList: string[];
  createdTime: string;
  updatedTime: string;
  groupIdList: number[];
}

/**
 * @description 获取公告详情
 * @param {object} params
 * @param {number} params.id 公告id
 * @returns
 */
export const fetchNoticeDetail = ({ id }: { id: number }) => {
  return get<INoticeDetail>(`om-api/announcement/v2/detail/${id}`, {});
};
