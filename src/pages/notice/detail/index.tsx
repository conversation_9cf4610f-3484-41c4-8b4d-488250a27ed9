import { useLocation } from 'react-router-dom';
import { fetchNoticeDetail } from '../services';
import { useRequest } from 'ahooks';
import { ImageViewer, Loading } from 'antd-mobile';
import dayjs from 'dayjs';
import { useEffect, useRef, useState } from 'react';
import { SlidesRef } from 'antd-mobile/es/components/image-viewer/slides';

function useParams<T = any>() {
  const location = useLocation();
  if (!location.search.includes('?')) return;

  return location.search
    .split('?')[1]
    .split('&')
    .reduce((acc, cur) => {
      const [key, value] = cur.split('=');
      return { ...acc, [decodeURIComponent(key)]: decodeURIComponent(value) };
    }, {}) as T;
}

export default function Notice() {
  const params = useParams<{ id: string }>();
  const picList: string[] = [];
  const [previewImage, setPreviewImage] = useState<string>('');
  const contentRef = useRef<HTMLDivElement>(null);
  const imageViewerRef = useRef<SlidesRef>(null);

  const { data, loading } = useRequest(
    async () => {
      const res = await fetchNoticeDetail({ id: Number(params?.id) ?? 0 });
      return res;
    },
    {
      refreshDeps: [params?.id],
      onError: (error) => {
        console.log('error :>> ', error);
      },
    },
  );

  useEffect(() => {
    if (contentRef?.current) {
      const imgElements = contentRef.current.getElementsByTagName('img');
      for (const img of imgElements) {
        picList.push(img.getAttribute('src') || '');
        img.addEventListener('click', () => {
          setPreviewImage(img.getAttribute('src') || '');
        });
      }
      const index = picList.findIndex((item) => item === previewImage);
      index > -1 && imageViewerRef?.current?.swipeTo(index);
    }
  }, [picList]);

  return (
    <>
      <div className="bg-white min-h-screen max-h-screen w-full overflow-y-auto flex-1">
        {loading ? (
          <div className="w-full h-screen flex justify-center items-center">
            <Loading />
          </div>
        ) : (
          <div>
            <div className="px-4 pt-4">
              <div className="font-bold text-2xl">{data?.title ?? ''}</div>
              <div className="flex justify-end my-4">
                <div className="break-words">
                  {dayjs(data?.startTime).format('YYYY-MM-DD HH:mm:ss') || ''}
                </div>
              </div>
            </div>
            <div className="w-full px-2 mb-4">
              <div ref={contentRef} dangerouslySetInnerHTML={{ __html: data?.detail ?? '' }} />
              <ImageViewer.Multi
                ref={imageViewerRef}
                images={picList}
                visible={!!previewImage}
                onClose={() => setPreviewImage('')}
              />
            </div>
          </div>
        )}
      </div>
    </>
  );
}
