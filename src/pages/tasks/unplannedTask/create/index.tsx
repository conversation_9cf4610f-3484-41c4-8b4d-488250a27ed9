import { FC, useEffect, useState } from 'react';
import { Loading } from '@src/components';
import PageContainer from '@src/components/PageContainer';
import useQuerySearchParams from '@src/hooks/useQuerySearchParams';
import checklistSelectStore from '@src/store/checklistSelectStore';
import { H5Bridge } from '@tastien/rn-bridge';
import { useRequest } from 'ahooks';
import { Select } from 'antd';
import { Button, DatePicker, Form, Picker, Space, Toast } from 'antd-mobile';
import { PickerValue } from 'antd-mobile/es/components/picker';
import dayjs from 'dayjs';
import debounce from 'lodash/debounce';
import { stringify } from 'qs';
import { NavigateOptions, useLocation, useNavigate } from 'react-router-dom';
import SelectButton from '../../patrolTask/components/SelectButton';
import { createUnplannedTask, getPermissionTagSelectList, getSimpleChecklists, TCreateTaskData } from '../api';
import ChecklistSelectCard from '../components/ChecklistSelect';

const today = dayjs().toDate();

type TSearchParams = [TCreateTaskData, (params: TCreateTaskData | Function, options?: NavigateOptions) => void];

type TFormData = {
  taskDate: string;
  sheetWeightList: any[];
  permissionLabelIds?: number[];
};

const Create: FC = () => {
  const [params, setParams] = useQuerySearchParams() as TSearchParams;
  const subType = params?.subType;
  const [form] = Form.useForm<TFormData>();
  const location = useLocation();
  const navigate = useNavigate();

  const { data: TagOptionData, loading: optionLoading } = useRequest(
    async () => {
      const res = await getPermissionTagSelectList();

      return (
        res?.map((item: { name: string; id: number }) => {
          return {
            label: item?.name,
            value: item?.id?.toString(),
          };
        }) || []
      );
    },
    { refreshDeps: [] },
  );
  console.log(TagOptionData, '=TagOptionData');

  const [dataPicker, setDataPicker] = useState<{
    visible: boolean;
    formName?: string;
    min?: Date;
  }>({
    visible: false,
  });

  const [pickerProps, setPickerProps] = useState<{
    visible: boolean;
    index?: number;
    value?: PickerValue;
  }>({
    visible: false,
  });

  const { data: checklists } = useRequest(async () => {
    const res = await getSimpleChecklists({ planType: 'PATROL' });

    return res;
  });
  useEffect(() => {
    const formatToday = dayjs().format('YYYY-MM-DD');
    const newParams = {
      ...params,
      taskDate: params.taskDate || formatToday,
      permissionLabelIds: params?.permissionLabelIds,
    };
    setParams(newParams, { replace: true });

    form?.setFieldsValue(newParams);
  }, []);

  const toChecklistSelect = (index?: number) => {
    const { pathname } = location;
    checklistSelectStore.setFromUrl(pathname);
    checklistSelectStore.setChecklists(checklists ?? []);
    navigate(
      `/tasks/checklist/select?${stringify({
        name: 'sheetWeightList',
        index,
      })}`,
      { state: params },
    );
  };

  const [loading, setLoading] = useState(false);

  const submit = debounce((val: any) => {
    console.log(val, '=val');
    setLoading(true);
    try {
      createUnplannedTask(val)
        ?.then(({ successCount, failCount }) => {
          setLoading(false);
          Toast.show(`${successCount || 0}个巡检任务创建成功,${failCount || 0}个巡检任务创建失败`);

          setTimeout(async () => {
            await H5Bridge.customPostMessage.customPostMessage({
              module: 'navigation',
              method: 'navigateTo',
              params: {
                pathname: 'Task',
                data: { queryType: 'ONLY_PATROL_TASK' },
              },
            });
          }, 500);
        })
        .catch(() => {
          setLoading(false);
        });
    } catch (error) {
      setLoading(false);
    }
  }, 500);

  return (
    <Loading spinning={loading} maskClickable={false}>
      <PageContainer
        footer={
          <div className="py-2 px-3 bg-[#fff] ">
            <Button
              color="primary"
              fill="solid"
              block
              // @ts-ignore
              onClick={() => {
                const { taskDate, sheetWeightList, shopIds } = params;
                const { permissionLabelIds } = form?.getFieldsValue();
                if (!permissionLabelIds?.length) {
                  Toast.show('请先选择权限标签');
                  return;
                }
                if (!taskDate) {
                  Toast.show('请先选择任务日期');
                  return;
                }
                if (sheetWeightList?.length > 0) {
                  const weightSheets = sheetWeightList?.filter(({ weight }) => Number(weight) !== 0);
                  if (weightSheets?.length > 0 && weightSheets?.length < sheetWeightList?.length) {
                    Toast.show('权重填写一个，其他的都要填写');
                    return;
                  }

                  if (weightSheets?.length === sheetWeightList?.length) {
                    let sum: number = 0;
                    weightSheets?.forEach(({ weight }) => {
                      sum += Number(weight);
                    });
                    if (sum !== 100) {
                      Toast.show('检查表权重必须为100%');
                      return;
                    }
                  }
                  const onlyChecklistIds: number[] = [];
                  if (
                    sheetWeightList?.some(({ workSheet }) => {
                      const { value } = workSheet;
                      if (onlyChecklistIds.includes(value)) {
                        return true;
                      } else {
                        onlyChecklistIds.push(value);
                        return false;
                      }
                    })
                  ) {
                    Toast.show('检查表重复');
                    return;
                  }
                } else {
                  Toast.show('请先选择检查表');
                  return;
                }

                submit({
                  taskDate,
                  shopIds,
                  subType: !!subType ? subType : 'NORMAL',
                  permissionLabelIds: permissionLabelIds?.map((item) => +item),
                  sheetWeightList:
                    // 检查表为一项时，手动设置权重为100%
                    sheetWeightList.length === 1
                      ? sheetWeightList.map(({ workSheet }) => ({
                          workSheetId: workSheet.value,
                          weight: '100',
                        }))
                      : sheetWeightList.map(({ workSheet, weight }) => {
                          return { workSheetId: workSheet.value, weight };
                        }),
                });
              }}
            >
              确认
            </Button>
          </div>
        }
      >
        <div className="h-full bg-[#f5f5f5]">
          <Form className="tdm-form" layout="horizontal" form={form}>
            <Space
              direction="vertical"
              className="w-full"
              style={{
                '--gap-vertical': '0.625rem',
              }}
            >
              <Form.Item name="taskDate" label="选择日期">
                <SelectButton
                  placeholder="请选择"
                  onClick={() => {
                    setDataPicker({
                      visible: true,
                      min: today,
                      formName: 'taskDate',
                    });
                  }}
                />
              </Form.Item>
              <Form.Item
                name="permissionLabelIds"
                label="权限标签"
                rules={[
                  {
                    required: true,
                    message: '请选择权限标签',
                  },
                ]}
              >
                <Select
                  allowClear
                  showSearch
                  style={{ width: 200 }}
                  loading={optionLoading}
                  onChange={(value) => {
                    setParams({ ...params, permissionLabelIds: value }, { replace: true });
                  }}
                  mode="multiple"
                  placeholder="请选择权限标签"
                  filterOption={(val, opt: any) => {
                    return opt?.label?.toLowerCase()?.includes(val?.toLowerCase()) || false;
                  }}
                  options={TagOptionData || []}
                />
              </Form.Item>
              <ChecklistSelectCard
                label="选择检查表"
                name="sheetWeightList"
                values={params?.sheetWeightList}
                onReselect={(index: number) => {
                  toChecklistSelect(index);
                }}
                onDelete={(index: number) => {
                  const newSearchParams = { ...params };
                  newSearchParams['sheetWeightList'].splice(index, 1);
                  console.log(newSearchParams, '=newSearchParams111');

                  form?.setFieldsValue(newSearchParams);
                  setParams(newSearchParams, { replace: true });
                }}
                onSetWeight={(index) => {
                  setPickerProps({
                    visible: true,
                    index,
                    value: params?.sheetWeightList?.[index].weight,
                  });
                }}
                onCreate={() => {
                  toChecklistSelect();
                }}
              />
            </Space>
          </Form>
          <DatePicker
            title="时间选择"
            visible={dataPicker?.visible}
            onClose={() => {
              setDataPicker({ visible: false });
            }}
            min={dataPicker?.min}
            onConfirm={(val) => {
              if (dataPicker?.formName && val) {
                const value = dayjs(val).format('YYYY-MM-DD');
                form.setFieldValue(dataPicker?.formName, value);
                setParams(
                  () => {
                    return { ...params, taskDate: value };
                  },
                  {
                    replace: true,
                  },
                );
              }
            }}
          />
        </div>
        <Picker
          columns={[
            [{ value: '0', label: '不设权重' }].concat(
              Array.from({ length: 100 }).map((_, index: number) => {
                return {
                  value: (index + 1).toString(),
                  label: `${index + 1}%`,
                };
              }),
            ),
          ]}
          visible={pickerProps?.visible}
          onClose={() => {
            setPickerProps({ visible: false });
          }}
          value={[pickerProps?.value as any]}
          onConfirm={(val) => {
            const { index } = pickerProps;
            const newSearchParams = { ...params };
            if (typeof index !== 'number') return;
            newSearchParams.sheetWeightList[index].weight = val;
            setParams(newSearchParams, { replace: true });
            form.setFieldsValue(newSearchParams);
          }}
        />
      </PageContainer>
    </Loading>
  );
};

export default Create;
