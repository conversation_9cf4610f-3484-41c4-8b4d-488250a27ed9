import { get, post } from '@src/api';
import { QueryKey, useInfiniteQuery } from '@tanstack/react-query';

export type TALlShopData = {
  groupId: number; // 组织id
  pageNum: number; // 页码
  pageSize: number; // 每页大小
} & Partial<{
  /** true 权限内 false 权限外 */
  limit: boolean;
  longitude: number; // 经度
  latitude: number; // 纬度
  shopIds: string[]; // 门店id列表
  shopName: string; // 门店名称
  /** 是否过滤没有负责人的门店 true过滤 false不过滤 */
  // needHead: boolean;
}>;

/** 门店状态枚举 */
export enum ShopStatusEnum {
  筹备中 = 1,
  营业中 = 2,
  歇业中 = 3,
  闭店中 = 4,
  待营业 = 8,
}

export interface IShopListItem {
  shopId: string;
  shopName: string;
  type: number;
  /**
   * 1:筹备中、2:营业中、3:停业中、4:闭店中、8:待营业
   */
  shopStatus: ShopStatusEnum;
  address: string;
  shopTel: string;
  latitude: number;
  longitude: number;
  /** 距离 返回的是 米 */
  distance: number;
  headUserId?: number;
  headNickname?: string;
  headMobile?: string;
  bindCamera: number;
  hikvisionStoreId?: any;
  // 是否有员工信息
  hasEmployee: boolean;
}

/**
 * @description 创建巡检计划外任务-获取全部门店列表
 * @param {TALlShopData} data
 * @param {number} data.pageNum 页码
 * @param {number} data.pageSize 每页大小
 * @param {boolean} data.limit 是否权限内
 * @param {boolean} data.needHead 是否过滤没有负责人的门店
 * @returns
 */
export const getALLShops = (data: TALlShopData) => {
  // return post<PageRes<IShopListItem>>('/cc-api/gzt/getLimitShop', {
  return post<{
    pages: number;
    data: IShopListItem[];
    total: number;
  }>('/om-api/common/getLimitInOutShop', {
    data,
  });
};

export const useQueryAllShops = (
  queryKey: QueryKey,
  {
    currentPoint,
    ...rest
  }: Omit<TALlShopData, 'pageNum'> & Partial<{ currentPoint: { latitude: number; longitude: number } }>,
) => {
  return useInfiniteQuery({
    queryKey,
    queryFn: ({ pageParam = 1 }) => getALLShops({ ...currentPoint, ...rest, pageNum: pageParam }),
    initialPageParam: 1,
    enabled: !!currentPoint,
    getNextPageParam: (lastPage, allPages) => {
      const hasNext = rest.pageSize * allPages.length < lastPage.total;
      if (hasNext) {
        return allPages.length + 1;
      }
      return;
    },
  });
};

// 创建巡检计划外任务-检测是否有其他门店 弃用
// export const hasCompanyOther = () => post(`/yy-api/shop/applet/has-company-other`, {});

/** 类型枚举 1 代表组织 2 代表门店 */
export enum TypeEnum {
  组织 = 1,
  门店 = 2,
}

export enum TypeTagEnum {
  组织 = 'ORGANIZATION',
  门店 = 'SHOP',
}

export interface IMineChildren {
  id: number;
  /** 1 代表组织 2 代表门店 */
  type: TypeEnum;
  name: string;
  shopId?: any;
  shopType?: any;
  shopStatus?: any;
  parentId?: number;
  children: IMineChildren[];
  typeTag: TypeTagEnum;
}

export interface IMineTreeData {
  id: number;
  type: TypeEnum;
  name: string;
  shopId?: string;
  shopType?: any;
  shopStatus?: ShopStatusEnum;
  parentId?: any;
  children: IMineChildren[];
  // 类型标记 根节点代表组织
  typeTag: TypeTagEnum;
}

/** 创建巡检计划外任务-获取我的门店组织数据 */
export const getMineOrganizationTree = () =>
  // post<TreeData>('om-api/common/principal-tree', { data: { type: 'ORGANIZATION' } });
  post<IMineTreeData[]>('cc-api/gzt/shop/getGroupShopTree', {
    data: { groupType: 2, privilegeCode: 1 },
  });

export interface ShopInfo {
  id: number;
  type: number;
  shopId: string;
  shopName: string;
  shopStatus?: any;
  limit: boolean;
  createTime?: string;
  fightId: number;
  fightName?: string;
  city?: string;
  bindCamera: number;
  hikvisionStoreId?: any;
  hikvisionStoreName?: string;
  headUserId?: number;
  headNickname?: string;
  headMobile?: string;
  fsUserId?: number;
  distance?: any;
  typeTag: TypeTagEnum.门店; // 类型标记 shopInfos 数组里面的都为门店
}

export interface IOtherChildren {
  id: number;
  name: string;
  parentId: number;
  limit: boolean;
  headUserId?: number;
  headNickname?: string;
  headMobile?: string;
  fsUserId?: number;
  shopInfos?: ShopInfo[];
  children?: IOtherChildren[];
  typeTag: TypeTagEnum.组织; // 类型标记 Children 数组里面的都为组织
}

export interface IOtherTreeData {
  id: number;
  name: string;
  parentId: number;
  limit: boolean;
  headUserId?: number;
  headNickname?: string;
  headMobile?: string;
  fsUserId?: number;
  shopInfos?: ShopInfo[];
  children: IOtherChildren[];
  // 类型标记
  typeTag: TypeTagEnum.组织;
}

type TOtherOrganizationTreeData = {
  /** 是否需要门店信息 */
  needShop?: boolean;
};

/** 创建巡检计划外任务-获取其他门店组织数据 */
export const getOtherOrganizationTree = ({ needShop }: TOtherOrganizationTreeData) =>
  // get(`/yy-api/resource/tree/company-other`, {});
  get<IOtherTreeData>(`om-api/common/principal-tree/all?needShop=${needShop}`);

export type TCreateTaskData = {
  taskDate: string; // 计划日期
  shopIds: string[]; // 门店id列表
  subType: string; // 巡检类型
  sheetWeightList: any[];
  permissionLabelIds?: number[]; // 权限标签id列表
};
// 获取权限标签
export const getPermissionTagSelectList = async () => await get(`om-api/corp/permission-label/list/by-permission`, {});
// 创建巡检计划外任务
export const createUnplannedTask = (data: TCreateTaskData) =>
  // post(`/yy-api/v1/patrol/task/add`, { data });
  post('om-api/corp/patrol/task/add', { data });

type TChecklistData = {
  planType: string;
  labelIds?: number[];
  sheetName?: string;
};

export interface IChecklistItem {
  id: number;
  sheetName: string;
  type: 'PATROL';
}

// 获取巡检任务信息
export const getSimpleChecklists = (data: TChecklistData) =>
  post<IChecklistItem[]>('om-api/corp/worksheet/effective/list/simple', {
    data,
  });

// 获取歇业记录
export const getRecords = (shopId?: string) => {
  return get(`om-api/common/temp/shop/close/info/${shopId}`);
};
