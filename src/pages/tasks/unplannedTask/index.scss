.patrol-unplanned-task {
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
  height: 100%;

  &-filter {
    background: #fff;
    padding: 0 12px;

    .adm-dropdown-popup-body {
      border-radius: 0;
    }

    .adm-space-item {
      padding-left: 16px;
      .adm-radio-content {
        border-top: 1px solid #f0f0f0;
      }
      &:first-child {
        .adm-radio-content {
          border-top: 0;
        }
      }
    }
    .adm-radio {
      width: 100%;
    }
    .adm-radio-content {
      padding: 12px 0;
      flex: auto;
      margin-left: 8px;
      font-size: 14px;
      line-height: 22px;
      color: #5e5e5e;
    }
  }

  &-body {
    background-color: #fff;
    margin-top: 10px;
    flex: 1;
    overflow-y: scroll;
  }

  .adm-tabs {
    background: #fff;
  }

  .adm-tabs-tab {
    font-size: 14px;
    line-height: 22px;
  }

  .unplanned-error-block {
    margin-top: 40px;
  }

  &-checkbox {
    .adm-checkbox-icon {
      margin-top: 2px;
    }
  }

  &-query-dropdown {
    background: none;
    .adm-dropdown-item-title {
      font-size: 14px;
      line-height: 22px;
      color: #5e5e5e;
      padding: 9px 0 8px 0;
    }
    .adm-dropdown-item-title-arrow {
      font-size: 16px;

      display: flex;
      align-items: center;
      margin-bottom: 2px;
    }
  }
}
