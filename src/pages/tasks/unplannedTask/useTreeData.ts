import { useRequest } from 'ahooks';
import {
  getMineOrganizationTree,
  getOtherOrganizationTree,
  IMineChildren,
  IMineTreeData,
  IOtherTreeData,
  TypeEnum,
  TypeTagEnum,
} from './api';
import { typeTagMap } from '.';
import { useMemo } from 'react';

// 添加标记
function addMark(arr: any[], typeTag: TypeTagEnum) {
  return arr.map((item) => ({ ...item, typeTag }));
}

// 递归处理组织和门店
function dig(data: Array<IMineTreeData | IOtherTreeData>, isInterior?: boolean): any[] {
  // 权限内
  if (isInterior) {
    return (data as IMineChildren[])?.map((item) => ({
      ...item,
      children: item?.children?.length
        ? dig(
            item.children.map((v: IMineChildren) => ({ ...v, typeTag: typeTagMap[v.type] })) || [],
            isInterior,
          )
        : [],
    }));
  }
  // 权限外
  return (data as IOtherTreeData[])?.map((item) => {
    // children 代表组织
    const children = item?.children?.length ? dig(addMark(item.children, TypeTagEnum.组织)) : [];
    // shopInfos 代表门店
    const shopInfos = item?.shopInfos?.length ? dig(addMark(item.shopInfos, TypeTagEnum.门店)) : [];
    return {
      ...item,
      children: children.concat(shopInfos),
    };
  });
}

export type TreeData = {
  value: string;
  label: string;
  parentId?: string;
  allChild?: string[];
  children?: string[];
};

type TProps = {
  /** 是否为权限内 */
  isInterior: boolean;
};

export default function useTreeData({ isInterior }: TProps) {
  // 权限内组织数据
  const { data: mineOrgs } = useRequest(
    async () => {
      const res = await getMineOrganizationTree();
      // 长度为1 并且类型为组织
      if (res?.length === 1 && res[0]?.type === TypeEnum.组织) {
        return { ...res[0], typeTag: TypeTagEnum.组织 };
      }
      // 返回的如果是多个数组类型 在外面包一层 根组织
      return {
        id: 0,
        name: '全部',
        type: 1,
        typeTag: TypeTagEnum.组织,
        children: res?.map((v) => ({ ...v, typeTag: typeTagMap[v.type] })) || [],
      };
    },
    {
      refreshDeps: [],
    },
  );

  // 权限外组织数据
  const { data: otherOrgs } = useRequest(
    async () => {
      const res = await getOtherOrganizationTree({ needShop: true });
      // 最外层 标识为组织
      res.typeTag = TypeTagEnum.组织;
      return res;
    },
    {
      refreshDeps: [],
    },
  );

  const { treeData, highestOrgId } = useMemo(() => {
    const map: { [key: string]: TreeData } = {};
    let highestOrgId: string = '';

    const loopOrgs = (nodes: any[], parentId?: string) => {
      let allChild: any[] = [];
      const children = nodes?.map(
        ({ id, typeTag, children: treeSet, name, shopId, shopName, headNickname }) => {
          const value = typeTag === TypeTagEnum.门店 ? `${typeTag}-${shopId}` : `${typeTag}-${id}`;
          const label =
            typeTag === TypeTagEnum.门店
              ? `${shopId} ${shopName || name}`
              : `${name}${headNickname ? `  (${headNickname})` : ''}`;
          map[value] = { value: value, label: label, parentId };
          let children: any[] = [];

          let allKeys: any = [];
          if (treeSet?.length > 0) {
            const { children: sub, allChild: subAll } = loopOrgs(treeSet, value);
            children = children.concat(sub);
            allKeys = subAll;
            allChild = Array.from(new Set(allChild.concat(subAll.concat(sub))));
          }

          map[value].allChild = Array.from(new Set(allKeys.concat(children)));

          map[value].children = children;

          return value;
        },
      );
      return { children, allChild };
    };

    if (mineOrgs && isInterior) {
      loopOrgs(dig([mineOrgs], isInterior));
      highestOrgId = `${mineOrgs.typeTag}-${mineOrgs.id}`;
    } else if (otherOrgs && !isInterior) {
      loopOrgs(dig([otherOrgs]));
      highestOrgId = `${otherOrgs.typeTag}-${otherOrgs.id}`;
    }

    return {
      treeData: map,
      highestOrgId,
    };
  }, [isInterior, mineOrgs, otherOrgs]);

  return {
    /** 组织树数据 */
    treeData,
    /** 最高组织id */
    highestOrgId,
  };
}
