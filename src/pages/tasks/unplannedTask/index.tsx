import { FC, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import emptyPng from '@src/assets/images/task-empty.png';
import { IconFont, Loading } from '@src/components';
import PageContainer from '@src/components/PageContainer';
import { Button, Checkbox, ErrorBlock, InfiniteScroll, List, Popover, PullToRefresh, Tabs, Toast } from 'antd-mobile';
import { isNull, isString, omit, omitBy } from 'lodash';
import { stringify } from 'qs';
import './index.scss';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { TALlShopData, TypeEnum, TypeTagEnum, useQueryAllShops } from './api';
import { renderPage } from './components/renderPage';
import ShopItem from './components/ShopItem';
import { getOrgOrShopId } from './components/ShopTreeSelected';
import TypeFilters, { TypeFiltersRef } from './components/TypeFilters';
import useTreeData from './useTreeData';

enum TabsType {
  我的门店 = 'Mine',
  其他门店 = 'Other',
}

export const typeTagMap: { [key: string]: TypeTagEnum } = {
  [TypeEnum.组织]: TypeTagEnum.组织,
  [TypeEnum.门店]: TypeTagEnum.门店,
};

export type TQueryParams = TALlShopData & Partial<{ currentPoint: { latitude: number; longitude: number } }>;

const UnplannedTask: FC = () => {
  const [searchParams] = useSearchParams();
  const subType = searchParams.get('subType');
  const [tabKey, setTabKey] = useState<string>(TabsType.我的门店);
  const navigate = useNavigate();
  const [queryParams, setQueryParams] = useState<TQueryParams>(() => ({
    groupId: 0,
    pageNum: 1,
    pageSize: 30,
    filterShopStatus: ['CLOSE'],
    // currentPoint: {
    //   latitude: 37.421948,
    //   longitude: -122.083977,
    // },
  }));
  const [shopIds, setShopIds] = useState<string[]>([]);
  const typeFiltersRef = useRef<TypeFiltersRef>(null);

  const { treeData, highestOrgId } = useTreeData({
    isInterior: tabKey === TabsType.我的门店,
  });

  const {
    data: allShops,
    hasNextPage,
    fetchNextPage,
    refetch,
    isLoading,
    isFetching,
  } = useQueryAllShops(['allShopInfo', queryParams], {
    limit: tabKey === TabsType.我的门店,
    ...omit(queryParams, 'pageNum'),
    // needHead: true,
  });
  const data = useMemo(() => {
    return allShops?.pages?.flatMap((item) => item?.data)?.filter((ele) => ele?.hasEmployee) || [];
  }, [allShops]);

  const loadMore = async () => {
    await fetchNextPage();
  };

  // 监听tab是否切换 切换时清空搜索条件
  useEffect(() => {
    if (!highestOrgId) return;
    setQueryParams((pre) => ({
      ...pre,
      pageNum: 1,
      shopName: undefined,
      shopIds: undefined,
      // 赋值组织树初始最高id
      groupId: +getOrgOrShopId(highestOrgId),
    }));
  }, [tabKey, highestOrgId]);

  const postMsgGetLocation = () => {
    // 通知APP获取定位
    if ((window as any)?.['ReactNativeWebView']?.['postMessage']) {
      // 安卓
      (window as any)['ReactNativeWebView']['postMessage'](JSON.stringify({ getLocation: true }));
    } else {
      // ios
      window.parent.postMessage(JSON.stringify({ getLocation: true }), '*');
    }
  };
  useEffect(() => {
    postMsgGetLocation();
    function handleEvent(e: Event & { data?: string }) {
      if (e.data && isString(e.data)) {
        // 监听定位回调格式：{"location":{"longitude":116.37296,"latitude":39.91095}}
        const result = JSON.parse(e.data);
        if (result?.location) {
          setQueryParams({
            ...queryParams,
            currentPoint: { ...result?.location },
          });
        }
      }
    }
    document.addEventListener('message', handleEvent); // 安卓端监听
    window.addEventListener('message', handleEvent); // ios端监听
    return () => {
      document.removeEventListener('message', handleEvent);
      window.addEventListener('message', handleEvent);
    };
  }, []);

  const renderBody = useCallback(() => {
    if (!data?.length && tabKey === TabsType.其他门店) {
      return (
        <ErrorBlock
          className="tdm-error-block unplanned-error-block"
          image={emptyPng}
          title={false}
          style={{
            '--image-height': '160px',
            height: '70vh',
          }}
          description={
            <p>
              您拥有当前分公司所有门店权限
              <br />
              请移步【我的门店】进行选择
            </p>
          }
        />
      );
    }

    if (data?.length > 0) {
      return (
        <div>
          <List>
            {data.map((item) => {
              return (
                <List.Item key={item.shopId} className="tdm-list-item">
                  <Checkbox
                    className="w-full tdm-checkbox items-start patrol-unplanned-task-checkbox"
                    checked={shopIds?.includes(item.shopId)}
                    onChange={(val: boolean) => {
                      if (val) {
                        setShopIds(shopIds.concat([item.shopId]));
                      } else {
                        setShopIds(shopIds.filter((id) => id !== item?.shopId));
                      }
                    }}
                  >
                    <ShopItem dataSource={item} />
                  </Checkbox>
                </List.Item>
              );
            })}
          </List>
          <InfiniteScroll loadMore={loadMore} hasMore={hasNextPage} />
        </div>
      );
    } else {
      return (
        <ErrorBlock
          className="tdm-error-block unplanned-error-block"
          image={emptyPng}
          title={false}
          style={{
            '--image-height': '160px',
            height: '70vh',
          }}
          description="暂无门店，请联系总部管理员"
        />
      );
    }
  }, [tabKey, data, shopIds, hasNextPage]);

  return (
    <Loading spinning={isFetching || isLoading}>
      <PageContainer
        footer={
          <div className="py-2 px-3 bg-[#fff] border-t border-solid border-[#F0F0F0]">
            <Button
              color="primary"
              fill="solid"
              block
              onClick={() => {
                navigate(`create?${stringify(omitBy({ shopIds, subType }, isNull))}`);
              }}
              disabled={shopIds.length === 0}
            >
              {shopIds.length === 0 ? '请先选择门店' : `加入任务(${shopIds.length})`}
            </Button>
          </div>
        }
      >
        <PullToRefresh
          onRefresh={async () => {
            postMsgGetLocation();
            if (!queryParams?.currentPoint?.latitude) {
              Toast.show({ icon: 'fail', content: '请先刷新获取当前位置' });
            } else {
              const res = await refetch();
              if (res.isSuccess) {
                Toast.show({ icon: 'success', content: '刷新成功' });
              } else {
                Toast.show({ icon: 'fail', content: '刷新失败' });
              }
            }
          }}
        >
          <div className="patrol-unplanned-task">
            {!!!subType && (
              <Tabs
                activeKey={tabKey}
                onChange={(key) => {
                  setTabKey(key);
                  typeFiltersRef.current?.clearSearchBar();
                  typeFiltersRef.current?.clearFilterData();
                }}
              >
                <Tabs.Tab title="我的门店" key={TabsType.我的门店} />
                <Tabs.Tab
                  title={
                    <span className="flex items-center">
                      其他门店
                      <Popover
                        content={
                          <span className="text-xs leading-5">
                            所属分公司内，
                            <br />
                            当前账号无权限的门店
                          </span>
                        }
                        mode="dark"
                        className="tdm-popover"
                        trigger="click"
                        placement="bottom"
                      >
                        <IconFont
                          type="icon-warning-circle"
                          className="text-base ml-[2px] leading-none"
                          onClick={(e) => {
                            e.stopPropagation();
                          }}
                        />
                      </Popover>
                    </span>
                  }
                  key={TabsType.其他门店}
                />
              </Tabs>
            )}
            <TypeFilters
              ref={typeFiltersRef}
              setQueryParams={setQueryParams}
              treeData={treeData}
              highestOrgId={highestOrgId}
            />
            <div className="patrol-unplanned-task-body">{renderBody()}</div>
          </div>
        </PullToRefresh>
      </PageContainer>
    </Loading>
  );
};

export default renderPage(UnplannedTask);
