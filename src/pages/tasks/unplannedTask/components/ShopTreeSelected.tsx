import { memo, useRef, useState, useImperativeHandle, forwardRef } from 'react';
import OrganizationDropdown, { TDropdownRef } from './OrganizationDropdown';
import { TreeData } from '../useTreeData';
import { Button } from 'antd-mobile';
import { TypeTagEnum } from '../api';

export const getOrgOrShopId = (target: string) => {
  return target.split('-')[1];
};

export type TShopTreeSelectedRef = {
  /** 清除过滤数据 */
  clearFilterData: () => void;
};

type TProps = {
  /** 组织树数据 */
  treeData: { [key: string]: TreeData };
  /** 最高组织id */
  highestOrgId: string;
  /** 点击选择回调 */
  onSelect: (values: {
    /** 组织id */
    groupId: number;
    /** 门店id */
    shopIds?: string[];
  }) => void;
};

export default memo(
  forwardRef<TShopTreeSelectedRef, TProps>(function ShopTreeSelected(
    { treeData, highestOrgId, onSelect },
    ref,
  ) {
    const orgDropDownRef = useRef<TDropdownRef>(null);
    const [filterData, setFilterData] = useState<{
      viewId?: string;
      selectId?: string;
    }>({});

    useImperativeHandle(ref, () => ({
      clearFilterData: () => setFilterData({}),
    }));

    const selectOrg = (selectId: string) => {
      // SHOP --> 门店
      if (selectId.includes(TypeTagEnum.门店)) {
        const groupId = +getOrgOrShopId(highestOrgId);
        const shopIds = [getOrgOrShopId(selectId)];
        onSelect({ groupId, shopIds });
        // ORGANIZATION --> 组织
      } else if (selectId.includes(TypeTagEnum.组织)) {
        const groupId = +getOrgOrShopId(selectId);
        onSelect({ groupId });
      }
    };

    return (
      <OrganizationDropdown
        title={
          treeData[filterData?.selectId || filterData?.viewId || highestOrgId]?.label || '全部门店'
        }
        className="my-2"
        ref={orgDropDownRef}
      >
        <ul>
          {treeData[filterData?.viewId || highestOrgId] && (
            <li className="flex justify-between w-full px-4 py-[10px] items-center">
              <div>{filterData?.viewId === highestOrgId ? '全部门店' : '全部'}</div>
              <div>
                {treeData[filterData?.viewId || highestOrgId]?.parentId && (
                  <Button
                    size="mini"
                    onClick={() => {
                      setFilterData({
                        ...filterData,
                        viewId: treeData[filterData?.viewId || highestOrgId]?.parentId,
                      });
                    }}
                  >
                    返回上级
                  </Button>
                )}
                <Button
                  size="mini"
                  color="primary"
                  className="ml-2"
                  onClick={() => {
                    const target = treeData[filterData?.viewId || highestOrgId];
                    const selectId = target?.value;
                    setFilterData({
                      ...filterData,
                      selectId,
                    });
                    orgDropDownRef?.current?.close();
                    selectOrg(selectId);
                  }}
                >
                  选择
                </Button>
              </div>
            </li>
          )}
          {treeData[filterData.viewId || highestOrgId]?.children?.map((key: string) => {
            return (
              <li key={key} className="flex justify-between w-full px-4 py-[10px] items-center">
                <div>{treeData[key].label}</div>
                <div className="shrink-0">
                  {!!treeData[key]?.children?.length && (
                    <Button
                      size="mini"
                      onClick={() => {
                        setFilterData({
                          ...filterData,
                          viewId: treeData[key].value,
                        });
                      }}
                    >
                      查看下级
                    </Button>
                  )}
                  <Button
                    size="mini"
                    color="primary"
                    className="ml-2"
                    onClick={() => {
                      const selectId = treeData[key].value;
                      setFilterData({
                        ...filterData,
                        selectId,
                      });
                      orgDropDownRef?.current?.close();
                      selectOrg(selectId);
                    }}
                  >
                    选择
                  </Button>
                </div>
              </li>
            );
          })}
        </ul>
      </OrganizationDropdown>
    );
  }),
);
