import { FC, useRef } from 'react';
import './index.scss';
import { IconFont } from '../utils';
import { Button, Form, Modal } from 'antd-mobile';
import SelectButton from '@src/pages/tasks/patrolTask/components/SelectButton';

interface ChecklistSelectCardProps {
  label?: string;
  onCreate?: () => void;
  onReselect?: (index: number) => void;
  onDelete?: (index: number) => void;
  onSetWeight?: (index: number) => void;
  values: any;
  name: string;
}

const ChecklistSelectCard: FC<ChecklistSelectCardProps> = ({
  label,
  onCreate,
  values,
  name,
  onReselect,
  onDelete,
  onSetWeight,
}) => {
  const deleteModalRef = useRef<any>(null);

  return (
    <>
      <div className="checklist-select-card">
        <h5 className="checklist-select-title">{label}</h5>
        <div className="checklist-select-list">
          <Form.Array name={name}>
            {(fields: any) => {
              return fields.map(({ index }: any) => (
                <div className="pl-4 flex items-center ">
                  {fields.length > 1 && (
                    <IconFont
                      type="icon-minus-circle1"
                      className="text-[20px] text-[#F53F3F] mr-2 shrink-0"
                      onClick={() => {
                        // remove(index)
                        deleteModalRef.current = Modal.show({
                          content: (
                            <div>
                              <p className="text-center text-base font-medium text-[#141414]">
                                是否确定删除【{values?.[index]?.workSheet?.label}】检查表
                              </p>
                              <div className="flex mt-6">
                                <Button
                                  className="w-1/2 text-[#5E5E5E] mr-3"
                                  onClick={() => {
                                    deleteModalRef.current.close();
                                  }}
                                >
                                  取消
                                </Button>
                                <Button
                                  color="primary"
                                  className="w-1/2"
                                  onClick={() => {
                                    onDelete?.(index);
                                    deleteModalRef.current.close();
                                  }}
                                >
                                  删除
                                </Button>
                              </div>
                            </div>
                          ),
                          bodyClassName: 'tdm-modal-body',
                        });
                      }}
                    />
                  )}
                  <div className="flex items-center w-full justify-between py-3 pr-4 patrol-task-list-item">
                    <span
                      className="text-[#5E5E5E] text-sm leading-[22px] w-[220px] whitespace-nowrap text-ellipsis overflow-hidden"
                      onClick={() => {
                        onReselect?.(index);
                      }}
                    >
                      {values?.[index]?.workSheet?.label}
                    </span>
                    {
                      <Form.Item name={[index, 'weight']} noStyle>
                        <SelectButton
                          className="shrink-0 text-sm"
                          transform={(val: any) => {
                            return val !== '0' ? val + '%' : '不设权重';
                          }}
                          onClick={() => {
                            onSetWeight?.(index);
                          }}
                        />
                      </Form.Item>
                    }
                  </div>
                </div>
              ));
            }}
          </Form.Array>
        </div>
        <div className="checklist-select-operation">
          <span className="flex items-center text-sm leading-6 cursor-pointer" onClick={onCreate}>
            <IconFont type="icon-plus-circle1" className="text-2xl leading-none mr-2 mt-[-2px]" />
            添加检查表
          </span>
        </div>
      </div>
    </>
  );
};

export default ChecklistSelectCard;
