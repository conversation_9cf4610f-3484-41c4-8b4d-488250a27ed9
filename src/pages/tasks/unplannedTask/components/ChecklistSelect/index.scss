.checklist-select {
  &-card {
    background-color: #fff;
    padding: 12px 0;
    display: flex;
    flex-direction: column;
    .adm-radio {
      margin-top: 12px;
    }
    .adm-radio-icon {
      width: 20px;
      height: 20px;
      // border-width: 2px;
    }
    .adm-radio-content {
      font-size: 14px;
      font-weight: 400;
      line-height: 22px;
      color: #5e5e5e;
    }
    .adm-list-card {
      margin: 0;
      .adm-list-body {
        border-radius: 0;
        border: 0;
      }
    }
  }
  &-title {
    padding: 0 16px;
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
    color: #141414;
  }

  &-list {
    // border-top: 0.5px solid #f0f0f0;
    margin-top: 12px;
    .adm-list {
      &:first-child {
        border-top: 0.5px solid #f0f0f0;
      }
    }
    .adm-list-item-content {
      display: block;
      border-top: 0;
      padding-right: 0;
    }
    .adm-list-item-content-main {
      flex: none;
      padding: 0;
    }
    .adm-form-list-operation {
      text-align: left;
      border-top: 0.5px solid #f0f0f0;
    }
    .adm-list-body-inner {
      margin: 0;
    }
  }
  &-operation {
    border-top: 0.5px solid #f0f0f0;
    color: #00bbb4;
    padding: 12px 0 0 16px;
  }
}
