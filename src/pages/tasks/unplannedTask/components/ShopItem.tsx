import { FC, useState } from 'react';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { IconFont } from '@src/components';
import ShopRecords from './Records';
import { IShopListItem, ShopStatusEnum } from '../api';

interface ShopItemProps {
  dataSource: IShopListItem;
}
const ShopItem: FC<ShopItemProps> = ({ dataSource }) => {
  const [visible, setVisible] = useState(false);
  const { shopId, shopName, shopStatus, longitude, latitude, headNickname, headMobile, address, distance } = dataSource;

  return (
    <div>
      <div className="flex items-center justify-between">
        <div className="flex flex-row items-center">
          <span className="text-[#141414] text-base font-medium">
            {shopId} {shopName}
            {shopStatus && <span className="text-[#858585]">【{ShopStatusEnum?.[shopStatus]}】</span>}
          </span>
          {shopStatus === ShopStatusEnum.歇业中 && (
            <div
              onClick={() => {
                setVisible(true);
              }}
            >
              <QuestionCircleOutlined />
            </div>
          )}
        </div>
        {latitude && longitude && distance && (
          <span className="bg-[rgba(78, 89, 105, 0.05)] border border-[#C9CDD4] border-solid text-xs leading-5 rounded-sm px-1 text-[#4E5969]">
            {(distance / 1000).toFixed(2)}
            km
          </span>
        )}
      </div>
      <div className="my-1 text-[#5E5E5E] flex items-start">
        <IconFont type="icon-user-1" className="text-base leading-none mr-1 pt-[3px]" />
        <span className="text-sm leading-[22px]">
          <span className="mr-2">{headNickname || '无'}</span>
          {'('}
          <span>{headMobile || '无'}</span>
          {')'}
        </span>
      </div>
      <div className=" text-[#5E5E5E] flex items-start">
        <IconFont type="icon-location-1" className="text-base  leading-none mr-1 pt-[3px]" />
        <span className="text-sm leading-[22px]">{address || '无'}</span>
      </div>

      <ShopRecords
        shopName={`${shopName}-${ShopStatusEnum?.[shopStatus]}`}
        visible={visible}
        onClosed={() => setVisible(false)}
        shopId={shopId}
      />
    </div>
  );
};

export default ShopItem;
