import { ReactNode } from 'react';
import { Outlet, useOutlet } from 'react-router-dom';

export const renderPage = (
  Component: React.FunctionComponent,
  options?: {
    ContextComponent: React.FunctionComponent<{ children: ReactNode }>;
  },
) => {
  return () => {
    const outlet = useOutlet();

    const renderNode = outlet ? <Outlet /> : <Component />;
    if (options?.ContextComponent) {
      const { ContextComponent } = options;
      return <ContextComponent>{renderNode}</ContextComponent>;
    } else {
      return renderNode;
    }
  };
};
