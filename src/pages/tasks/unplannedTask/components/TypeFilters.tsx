import { IconFont } from '@src/components';
import { Dropdown, DropdownRef, InputRef, Radio, SearchBar, Space } from 'antd-mobile';
import { forwardRef, memo, useCallback, useImperativeHandle, useRef, useState } from 'react';
import ShopTreeSelected, { TShopTreeSelectedRef } from './ShopTreeSelected';
import { TQueryParams } from '..';
import { debounce } from 'lodash';
import { TreeData } from '../useTreeData';

enum QueryType {
  No = 'no',
  Name = 'name',
}

const QueryTypeCN: Record<QueryType, string> = {
  [QueryType.No]: '门店编号',
  [QueryType.Name]: '门店名称',
};

export type TypeFiltersRef = {
  /** 清空搜索框 */
  clearSearchBar: () => void;
  /** 清空组织树选择筛选数据 */
  clearFilterData: () => void;
};

type TProps = {
  setQueryParams: React.Dispatch<React.SetStateAction<TQueryParams>>;
  treeData: { [key: string]: TreeData };
  highestOrgId: string;
};

export default memo(
  forwardRef<TypeFiltersRef, TProps>(function TypeFilters(
    { setQueryParams, treeData, highestOrgId },
    ref,
  ) {
    const [queryType, setQueryType] = useState<QueryType>(QueryType.Name);
    const containerRef = useRef<HTMLDivElement>(null);
    const queryDropDownRef = useRef<DropdownRef>(null);
    const searchBarRef = useRef<InputRef>(null);
    const shopTreeSelectedRef = useRef<TShopTreeSelectedRef>(null);

    const onSelect = ({ groupId, shopIds }: { groupId: number; shopIds?: string[] }) => {
      // 选择门店
      if (shopIds?.length) {
        setQueryParams((pre) => ({ ...pre, groupId, shopIds, pageNum: 1 }));
      } else {
        // 选择组织
        setQueryParams((pre) => ({ ...pre, groupId, shopIds: undefined, pageNum: 1 }));
      }
    };

    const handleSearch = useCallback(
      debounce((shopName: string) => {
        setQueryParams((pre) => ({
          ...pre,
          shopName: shopName?.trim()?.length ? shopName.trim() : undefined,
          pageNum: 1,
        }));
      }, 500),
      [],
    );

    useImperativeHandle(ref, () => ({
      clearSearchBar: () => searchBarRef.current?.clear(),
      clearFilterData: () => shopTreeSelectedRef.current?.clearFilterData(),
    }));

    return (
      <div className="patrol-unplanned-task-filter">
        <div
          className="rounded-lg bg-[#FAFAFA] px-2 leading-6 flex items-center mt-2"
          ref={containerRef}
        >
          <Dropdown
            ref={queryDropDownRef}
            getContainer={containerRef?.current}
            className="patrol-unplanned-task-query-dropdown"
            arrow={<IconFont type="icon-chevron-down" className="text-[#5E5E5E]" />}
          >
            <Dropdown.Item key="queryType" title={QueryTypeCN[queryType]}>
              <div className="bg-[#fff]">
                <Radio.Group
                  onChange={(val: any) => {
                    setQueryType(val);
                    queryDropDownRef?.current?.close();
                    searchBarRef?.current?.clear();
                  }}
                  value={queryType}
                >
                  <Space
                    direction="vertical"
                    className="w-full"
                    style={{
                      '--gap-vertical': '0px',
                    }}
                  >
                    <Radio value={QueryType.Name}>{QueryTypeCN[QueryType.Name]}</Radio>
                    <Radio value={QueryType.No}>{QueryTypeCN[QueryType.No]}</Radio>
                  </Space>
                </Radio.Group>
              </div>
            </Dropdown.Item>
          </Dropdown>
          <div className="w-[1px] h-[22px] bg-[#DCDCDC] mx-3"></div>
          <SearchBar
            ref={searchBarRef}
            placeholder={queryType === QueryType.Name ? '请输入门店名称' : '请输入门店编号'}
            onChange={(val) => {
              handleSearch(val);
            }}
            style={{
              '--background': 'transparent',
            }}
          />
        </div>
        <ShopTreeSelected
          ref={shopTreeSelectedRef}
          treeData={treeData}
          highestOrgId={highestOrgId}
          onSelect={onSelect}
        />
      </div>
    );
  }),
);
