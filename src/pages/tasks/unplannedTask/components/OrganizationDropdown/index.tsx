import {
  forwardRef,
  ReactNode,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import './index.scss';
import { IconFont } from '@src/components';
import classNames from 'classnames';

interface TProps {
  hasEllipsis?: boolean;
  title: string;
  className?: string;
  children?: ReactNode;
}

export type TDropdownRef = { close: () => void };

const OrganizationDropdown = forwardRef<TDropdownRef, TProps>(
  ({ title, className, children, hasEllipsis }, ref) => {
    const [unfold, setUnfold] = useState<boolean>(false); // 是否展开  默认不展开
    const selectRef = useRef<HTMLDivElement>(null);

    useImperativeHandle(
      ref,
      () => ({
        close: () => {
          setUnfold(false);
        },
      }),
      [],
    );

    return (
      <div className={classNames('organization-dropdown', className)}>
        <div
          ref={selectRef}
          className={`organization-dropdown-select text-primary ${hasEllipsis && 'w-24 flex justify-center  items-center'}  `}
          onClick={() => {
            setUnfold(!unfold);
          }}
        >
          <span>{title}</span>
          <IconFont
            type="icon-chevron-down"
            className={classNames('organization-dropdown-select-arrow', {
              'organization-dropdown-select-arrow-unfold': unfold,
            })}
          />
        </div>
        <div
          className="organization-dropdown-popup"
          style={{
            display: unfold ? 'block' : 'none',
            top: selectRef?.current?.offsetTop
              ? selectRef?.current?.offsetTop + selectRef?.current?.offsetHeight
              : 0,
          }}
        >
          <div
            className="organization-dropdown-popup-mask"
            onClick={() => {
              setUnfold(false);
            }}
          />
          <div className="organization-dropdown-popup-content">{children}</div>
        </div>
      </div>
    );
  },
);

export default OrganizationDropdown;
