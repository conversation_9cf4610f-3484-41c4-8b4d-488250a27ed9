import React, { useEffect } from 'react';
import { IPopup } from '@src/components/IPopup';
import { useRequest } from 'ahooks';
import { List } from 'antd-mobile';
import { getRecords } from '../api';

interface Props {
  shopId?: string;
  visible: boolean;
  onClosed: () => void;
  shopName?: string;
}

const ShopRecords: React.FC<Props> = ({ visible, shopId, onClosed, shopName }) => {
  const { data, runAsync } = useRequest(() => getRecords(shopId), {
    manual: true,
  });

  useEffect(() => {
    if (visible) {
      runAsync();
    }
  }, [visible]);

  return (
    <IPopup visible={visible} title={shopName || ''} destroyOnClose onClose={onClosed}>
      <List>
        <List.Item>
          歇业时段：{data?.closeStarTime} - {data?.closeEndTime}
        </List.Item>
      </List>
    </IPopup>
  );
};

export default ShopRecords;
