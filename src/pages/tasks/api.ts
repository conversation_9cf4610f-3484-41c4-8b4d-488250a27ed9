import { get, post } from '@src/api';
import { Issue, ReportDetailItem, SelfReportIssueList, SelfTaskInfoItem } from '@src/common/api.type';
import { getBrandId } from '@src/utils/tokenUtils';
import {
  AcceptanceTaskPayload,
  CommentSubmitPayload,
  createTaskPayload,
  EditProcessTaskPayload,
  geTaskCompleteInfoResp,
  getNormalTaskListResp,
  getNormalTaskPlanListPayload,
  getNormalTaskPlanListResp,
  getSOPTypeTreeResp,
  getTaskDetailResp,
  getTaskTypeTreeResp,
  getUnderTaskSelfPlanListResp,
  getZTUserPagesResp,
  ModifyTaskStatusPayload,
  patrolTaskListResp,
  ProcessTaskPayload,
  QueryKillListParams,
  QueryTaskListParams,
  SelfTaskListAll,
  ShopListRes,
  sopInfo,
  TaskCountResponse,
  TaskExecuteConfirmResp,
  TaskInvestRate,
  TaskListItem,
  TaskTypeListItem,
  TUpload,
  TUploadGalleryParams,
  TUploadParams,
  TWatermark,
} from './api.type';
import { DiagnosticInfo, ReportDetailInfo } from '../patrol/api.type';

export type ConfigKey =
  | 'ALLOW_ALBUM'
  | 'CHECK_IN_DISTANCE'
  | 'REPORT_AUTO_CONFIRM_DAYS'
  | 'MUST_CHECK_IN'
  | 'ISSUE_FIX_DEFAULT_DAYS'
  | 'REPORT_SHOW_PASS_RATE'
  | 'REPORT_VIDEO_TIME_LIMIT'
  | 'REPORT_VIDEO_TIME_LIMITCheck'
  | 'IN_STORE_INSPECTION_NOT_QUALIFIED_ISSUE_ITEM_DEFAULT_DEADLINE'
  | 'IN_STORE_INSPECTION_RECTIFY_REJECT_AFTER_DEADLINE'
  | 'REPORT_VIDEO_NOT_QUALIFIED_ISSUE_ITEM_DEFAULT_RECTIFY_DEADLINE'
  | 'REPORT_VIDEO_RECTIFY_REJECT_RECTIFY_AFTER_DEADLINE'
  | 'SELF_INSPECTION_RECTIFICATION_PERIOD'
  | 'ISSUE_FIX_DEFAULT_DAYS'
  | 'SELF_INSPECTION_RECTIFY_REJECT_AFTER_DEADLINE';
/** 获取中台用户数据列表 */
export const getZTUserPages = async (data: {
  keyword?: string;
  pageNo: number;
  pageSize: number;
  type: number;
  withPermission: boolean;
  userIds?: number[];
}) =>
  post<getZTUserPagesResp>('/cc-api/gzt/rest/system/user/page', {
    data,
  });

/** 创建任务 */
export const createTask = async (data: createTaskPayload) =>
  post('/om-api/common/routine/task/createTask', {
    data,
  });

/** 查询所有人员账号id集合 */
export const getAllUserIDs = async (data: {
  keyword?: string;
  pageNo: number;
  pageSize: number;
  type?: number;
  withPermission: boolean;
}) => post('/cc-api/gzt/rest/system/userIds', { data });

/** 查询已选处理人 */
// export const getDetailPageByIds = async (data: {
//   keyword?: string;
//   pageNum: number;
//   pageSize: number;
//   userIds: number[];
// }) => post('/api/rest/system/user/detailPageByIds', { data });

/** 查询任务详情 */
export const getTaskDetail = async (taskUserId: string) =>
  get<getTaskDetailResp>(`/om-api/common/routine/task/getTaskDetail/${taskUserId}`, {});
/** 查询人员账号信息列表 */
export const getUserList = async (keyword?: string) =>
  post(`/cc-api/user/list`, {
    data: {
      userName: keyword,
      status: 'true',
      groupIds: [],
      shopIds: [],
      brandId: getBrandId(),
      bizTypes: [],
      pageNo: 1,
      pageSize: 9999,
    },
  });
// 根据ids查询人员信息详情
export const detailPageByIds = async (userIds?: number[]) =>
  post(`/cc-api/user/list`, {
    data: {
      userIds,
      status: 'true',
      groupIds: [],
      shopIds: [],
      brandId: getBrandId(),
      bizTypes: [],
      pageNo: 1,
      pageSize: 9999,
    },
  });
/** 查询基础任务列表 */
export const querySimpleTaskList = (data?: any) => post('/om-api/common/routine/task/querySimpleTaskList', { data });
/** 查询任务列表 */
export const getTaskList = async (data: QueryTaskListParams) =>
  post<TaskListItem[]>('/om-api/common/routine/task/queryTaskList', { data });
/** 查询任务列表-分页 */
export const getTaskListPage = async (data: QueryTaskListParams) =>
  post<{ data: TaskListItem[] }>('/om-api/common/routine/task/queryTaskPage', {
    data,
  });

export enum CNStatus {
  待开始 = 'WAIT',
  进行中 = 'RUNNING',
  已完成 = 'COMPLETE',
  待分配 = 'NEW',
  已关闭 = 'CLOSE',
  中止 = 'SUSPEND',
}
// export enum disinfectionTypeStatus {
//   FIRST_STRUCTURE = 'FIRST_STRUCTURE',
//   SECOND_STRUCTURE = 'SECOND_STRUCTURE',
// }
export enum disinfectionTypeStatus {
  FIRST_STRUCTURE = 'FIRST_STRUCTURE',
  SECOND_STRUCTURE = 'SECOND_STRUCTURE',
  DAILY = 'DAILY',
  EXIGENCY = 'EXIGENCY',
}
export type TKillItem = {
  arriveTime: string;
  createTime: string;
  createUserId: number;
  createUserName: string;
  managerUserName: string;
  managerUserPhone: string;
  createUserPhone: string;
  disinfectionCompanyId: any;
  disinfectionCompanyName: any;
  disinfectionType: disinfectionTypeStatus;
  id: number;
  processList: any;
  reportCheckCount: number;
  reportIssueCount: number;
  reportNoReformCount: number;
  reportPassed: string;
  reportScore: number;
  reportStatus: string;
  shopId: string;
  shopName: string;
  taskClosedTime: string;
  taskStartTime: string;
  taskStatus: CNStatus;
  worksheetList: any;
};

/** 查询消杀任务列表-分页 */
// export const getKillListPage = async (data: QueryKillListParams) =>
export const getKillListPage = async (data: QueryKillListParams) =>
  post<{ data: TKillItem[] }>('/om-api/corp/disinfection/corp-page', {
    data,
  });
// /common/disinfection/company/list/simple?enable=true&filterNoEmployee=true
/** 获取消杀公司 */
export const getKillCompany = async () =>
  get('/om-api/common/disinfection/company/list/simple', {
    params: {
      enable: true,
      filterNoEmployee: true,
    },
  });
/** 获取消杀公司的巡检人 */
export const getKillCompanyUser = async () => get('/om-api/common/disinfection/company/employee/list/simple', {});
/** 查询任务数量 */
export const getTaskCount = () => get<TaskCountResponse>('/om-api/common/routine/task/getTaskCountMap', {});
/** 查询待验收任务数量 */
export const getTaskWaitAcceptanceCount = () =>
  get<TaskCountResponse>('/om-api/common/routine/task/getTaskWaitAcceptanceCount', {});
/** 查询进行中任务数量 */
export const getProcessingTaskCount = () =>
  get<TaskCountResponse>('/om-api/common/routine/task/getTaskProcessingCountMap', {});

/** 获取任务类型列表 */
export const getTaskTypeList = () => post<TaskTypeListItem[]>('/om-api/common/task/type/queryList', { data: {} });

/** 点评内容 */
export const commentSubmit = (data: CommentSubmitPayload) =>
  post<TaskTypeListItem[]>('/om-api/common/routine/task/scheduleComment', {
    data,
  });
/** 接受、拒绝、终止任务 */
export const modifyTaskStatus = (data: ModifyTaskStatusPayload) =>
  post<TaskTypeListItem[]>('/om-api/common/routine/task/receiveTask', { data });

/** 验收任务（验收、驳回） */
export const acceptanceTask = (data: AcceptanceTaskPayload) =>
  post<TaskTypeListItem[]>('/om-api/common/routine/task/acceptanceTask', {
    data,
  });

/** 处理任务 */
export const processTask = (data: ProcessTaskPayload) => post('/om-api/common/routine/task/processTask', { data });
// 获取任务进度
export const getTaskSchedule = (taskUserId: number) =>
  get(`/om-api/common/routine/task/getTaskScheduleValue/${taskUserId}`, {});
/** 修改任务进度 */
export const editProcessTask = (data: EditProcessTaskPayload) =>
  post('/om-api/common/routine/task/modTaskSchedule', { data });
/** 查询任务类型树列表 */
export const getTaskTypeTree = async () =>
  post<getTaskTypeTreeResp>('/om-api/common/task/type/queryTree', {
    data: {
      // 获取全部启用的类型
      status: 1,
    },
  });

/** 查询一二级sop类型 */
export const getSOPTypeTree = async () => post<getSOPTypeTreeResp>('/api/rest/manage/sop/getSopTypeTree', {});
// 根据二级sop类型ID和任务id获取可选sop信息
export const getSOPList = async (data: { sopTypeId: number; taskTypeId: number }) =>
  post<{
    sopInfoReps: sopInfo[];
  }>('/api/rest/manage/sop/getSopInfo', { data });
// 根据任务类型获取sopType和sopInfo信息
export const getSopTypeTreeByTaskTypeId = (taskTypeId: number) =>
  get(`/om-api/common/task/type/getListByTaskTypeId/${taskTypeId}`, {});

// 根据二级sop类型ID和任务id获取可选sop信息
export const getSopInfoByTaskTypeId = async (data: { taskTypeId: number }) =>
  post<{
    sopInfoReps: sopInfo[];
  }>('/api/rest/manage/sop/getSopInfoByTaskTypeId', { data });

// 修改任务截止时间
export const setTaskDeadline = async (taskUserId: string, taskDeadline: string) =>
  post('/om-api/common/routine/task/modTaskDeadline', {
    data: { taskUserId, taskDeadline },
  });
// 自检任务详情-基本信息
export const getSelfTaskDetail = async (data: { planId: number; shopId: number }) =>
  post<SelfTaskInfoItem>('/om-api/v1/patrol/self/task/info', {
    data,
  });
// 自检任务详情-列表
export const getSelfTaskList = async (data: { planId: number; shopId: number }) =>
  post<SelfTaskListAll>('/om-api/app/self/task/list', { data });

// 查看报告详情
export const getQueryReportDetail = async (data: {
  reportId: number;
  notFilledItemHandleType?: 'SET_FULL_SCORE' | 'SET_NOT_APPLY';
}) => post<ReportDetailItem>('/om-api/v1/patrol/report/review', { data });
// 整改反馈详情
// export const queryIssueList = async (data: QueryIssueListParams) => {
//   const list = await post<Issue[]>('/om-api/v1/patrol/report/issue/my/list', { data });
//   return list.map((e) => {
//     let buttonEnums: Array<
//       | 'AUDIT_PASS'
//       | 'FOLLOW_PROBLEM'
//       | 'REJECT_PROBLEM'
//       | 'SELF_AUDIT_PASS'
//       | 'SELF_FOLLOW_PROBLEM'
//       | 'SELF_REJECT_PROBLEM'
//     > = [];
//     if (e.status === Status.unReviewed) {
//       buttonEnums = ['AUDIT_PASS', 'REJECT_PROBLEM'];
//     }
//     if (e.status === Status.process) {
//       buttonEnums = ['FOLLOW_PROBLEM'];
//     }
//     return {
//       ...e,
//       buttonEnums,
//     };
//   });
// };
// 公司员工权限外时整改反馈详情。
export const getOutQueryIssueList = (data: { reportId?: number }) => {
  return post<Issue[]>('/om-api/v1/patrol/report/issues', { data });
};

// 获取自检报告的详情整改项（督导）
export const getPAListBySelfDetail = async (data: { taskId: number }) => {
  const list = await get<SelfReportIssueList>(`/om-api/common/selfReport/issue/${data.taskId}/list`, { data });

  // return digSelfReportIssue(list.selfReportIssueMiniProgramDTOS);
  return list.selfReportIssueMiniProgramDTOS || [];
};
export enum PROCESS_TYPE_ENUM {
  REFORM_SUBMITTED,
  SUPERVISOR_REJECTED,
  COMPLETED,
}
export interface SelfReportIssueAppletProcesses {
  selfReportIssueProcesses: {
    createByName: string;
    createUserAvatar: string;
    createdAt: string;
    feedbackResources: {
      id: string;
      type: 'IMG' | 'VEDIO' | 'PDF';
      url: string;
    }[];
    feedbackText: string;
    id: number;
    processType: PROCESS_TYPE_ENUM;
    selfReportIssueId: 0;
  }[];
}

// 为了适配现有组件，在这里处理数据
const digReportIssueDetail = (value: SelfReportIssueAppletProcesses['selfReportIssueProcesses']) => {
  return value.map((item) => ({
    submitedName: item.createByName,
    submitedAvatar: item.createUserAvatar,
    type: PROCESS_TYPE_ENUM[item.processType],
    submitTime: item.createdAt,
    issueImages: item.feedbackResources.filter(Boolean),
    issueRemark: item.feedbackText,
  }));
};

export const getSelfReportIssueDetail = async (data: { id: number }) => {
  const list = await get(`/om-api/selfReport/issue/applet/${data.id}/processes`, {
    data,
  });

  return digReportIssueDetail(list.selfReportIssueProcesses);
};
// 审核自检任务通过
export const examineSelfTask = async (data: { reportId: number }) =>
  await post('/om-api/patrol/self/confirm', { data });

// 更新巡检任务数据
export const updateInspectTask = async (data: { id: number; reason?: string; imgUrls?: string[]; status: number }) =>
  await post('/om-api/v1/patrol/issue/save', { data });

// 更新自检任务数据
export const updateSelfTask = async (data: { text?: string; images?: string[]; reportId: number }) =>
  await post('/om-api/patrol/self/reject-rectification', { data });

// 报告整改反馈批量审核通过
export const batchExaminePatrol = (data: { reportId: number; issueIds: number[] }) =>
  post('/om-api/patrol/issues/batchPass', { data });
// /api/patrol/issues/batchPass
// 报告整改反馈批量审核通过
export const batchExamineSelf = (data: { reportIssueIds: number[] }) =>
  post('/om-api/common/selfReport/issue/confirm/batch', { data });
// /api/common/selfReport/issue/confirm/batch
// 我处理-巡检计划-列表
// export const getNormalTaskPlanList = async (
//   data: getNormalTaskPlanListPayload,
//   options?: { [key: string]: any },
// ) =>
//   post<getNormalTaskPlanListResp>('/om-api/app/normal-task-plan/list', {
//     data,
//     ...(options || {}),
//   });
// 我处理-巡检-列表
export const getNormalTaskPlanList = async (data: getNormalTaskPlanListPayload, options?: { [key: string]: any }) =>
  post<getNormalTaskPlanListResp>('/om-api/corp/task-center/patrol/task/list-by-user', {
    data,
    ...(options || {}),
  });

// 我管辖-巡检-列表
export const getUnderTaskPlanList = async (data: getNormalTaskPlanListPayload, options?: { [key: string]: any }) =>
  post<getNormalTaskPlanListResp>('/om-api/corp/task-center/patrol/task/list-by-shop', {
    data,
    ...(options || {}),
  });
// 执行时段确认分页列表
export const getTaskExecuteConfirmList = async (data: any) =>
  post<TaskExecuteConfirmResp>('/om-api/corp/patrol/task/food-safety-arrive-shop/exe-period/confirm/page-list', {
    data,
  });
// 执行时段确认接口
export const submitExecuteConfirm = async (data: { taskId: number; exePeriodConfirmType: string }) =>
  post('/om-api/corp/patrol/task/food-safety-arrive-shop/exe-period/confirm', {
    data,
  });

// 我管辖-自检计划-列表
// /api/corp/task-center/self/task/list
export const getUnderTaskSelfPlanList = async (data: getNormalTaskPlanListPayload, options?: { [key: string]: any }) =>
  post<getUnderTaskSelfPlanListResp>('/om-api/corp/task-center/self/task/list', {
    data,
    ...(options || {}),
  });
export const getNormalTaskWorkerList = async (data: getNormalTaskPlanListPayload, options?: { [key: string]: any }) =>
  post<getNormalTaskPlanListResp>('/om-api/app/normal-task-plan/list-worker-info', {
    data,
    ...(options || {}),
  });
// 查询任务完成情况
export const geTaskCompleteInfo = async (params: { planId: number; filterPermission: boolean; userId?: number }) =>
  get<geTaskCompleteInfoResp>('/om-api/app/normal-task-plan/complete/info', {
    params: {
      ...params,
      filterPermission: params?.filterPermission,
    },
  });
// 我处理-巡检任务-列表
export const getNormalTaskList = async (data: any, options?: { [key: string]: any }) =>
  post<getNormalTaskListResp>('/om-api/app/out/plan/task', {
    data,
    ...(options || {}),
  });
// 我管辖-巡检计划-列表
// export const getUnderTaskPlanList = async (
//   data: getNormalTaskPlanListPayload,
//   options?: { [key: string]: any },
// ) =>
//   post<any>('/om-api/app/normal-task-plan/worker/info', {
//     data,
//     ...(options || {}),
//   });

// // 我管辖-自检计划-列表
// export const getUnderTaskSelfPlanList = async (
//   data: getNormalTaskPlanListPayload,
//   options?: { [key: string]: any },
// ) =>
//   post<getUnderTaskSelfPlanListResp>('/om-api/app/self/task/plan/list', {
//     data,
//     ...(options || {}),
//   });
// 我管辖-自检任务-列表
export const getUnderTaskSelfCheckingList = async (
  data: getNormalTaskPlanListPayload,
  options?: { [key: string]: any },
) =>
  post<getUnderTaskSelfPlanListResp>('/om-api/app/self/task/plan/list/once', {
    data,
    ...(options || {}),
  });

// 我管辖-巡检任务-列表
export const getUnderTaskSelfList = async (data: any, options?: { [key: string]: any }) =>
  post<getNormalTaskListResp>('/om-api/app/out/plan/task/byUser', {
    data,
    ...(options || {}),
  });
// 查询巡检任务计划下相关巡检人任务明细列表
export const getPatrolTaskList = async (data: any, options?: { [key: string]: any }) => {
  return post<patrolTaskListResp>('/om-api/app/normal-task-plan/task/list', {
    data,
    ...(options || {}),
  });
};
// 我我派发-巡检任务计划下相关巡检人任务明细列表
export const getPatrolTaskListByPlan = async (data: any, options?: { [key: string]: any }) => {
  return post<patrolTaskListResp>('/om-api/app/normal-task-plan/task/list-by-plan', {
    data,
    ...(options || {}),
  });
};

// 获取食安检查表0：代表自检相关的检查表、1：代表巡检相关的检查表
export const getWorkSheet = async (taskType: 0 | 1, options?: { [key: string]: any }) =>
  get<
    {
      id: number;
      sheetName: string;
    }[]
  >('/om-api/workSheet/app/food/safety', {
    params: {
      taskType,
    },
    ...(options || {}),
  });
/**
 * 获取角色列表
 * roleCategory 1:公司 2:门店
 */

export const getRoleList = async (params: { roleCategory: number }) =>
  get<{ id: number; name: string }[]>('/om-api/common/role/list', {
    params,
  });
// 通过计划名称模糊查询巡检计划
export const getTaskIdByTaskName = async (word: string, options?: { [key: string]: any }) =>
  get<
    {
      id: number;
      planName: string;
      createTime: string;
    }[]
  >('/om-api/normal/plan/task-name-worksheet', {
    params: {
      word,
    },
    ...(options || {}),
  });
export const getSelfTaskIdByTaskName = async (word: string, selfPlanType: string, options?: { [key: string]: any }) =>
  post<
    {
      id: number;
      planName: string;
    }[]
  >('/om-api/app/self/planName', {
    data: {
      pageNo: 1,
      pageSize: 100,
      planName: word,
      selfPlanType,
    },
    ...(options || {}),
  });

// 获取人员信息
export const getAllUserBase = async (options?: { [key: string]: any }) =>
  post<any>('/om-api/user/queryAllUserBase', {
    data: {
      authPermission: false,
    },
    ...(options || {}),
  });

export const getShopDetail = async (params: { shopId: number }) =>
  get<ShopListRes>('/om-api/patrol/shop', {
    params,
  });

export const queryChecklistDetail = (data: {
  taskId: number;
  notFilledItemHandleType?: string | number | undefined;
  filterHasApply?: boolean;
}) => {
  return post<ReportDetailInfo>(`/om-api/common/patrol/report/detail`, {
    data,
  });
};
// 考试情况详情(公司员工)
export const getExamSituation = async (taskId: number | string) =>
  get<{ confirm: boolean; remark: string }>(
    `/om-api/corp/patrol/task/food-safety/arrive-shop/${taskId}/exam-situation/detail`,
    {},
  );
// 考试情况详情(门店员工)
export const getExamSituationShop = async (taskId: number | string) =>
  get<{ confirm: boolean; remark: string }>(
    `/om-api/shop/patrol/task/food-safety/arrive-shop/${taskId}/exam-situation/detail`,
    {},
  );
// 考试情况确认
export const submitExamConfirm = (data: { taskId: number; confirm: boolean; remark?: string }) => {
  return post(`/om-api/corp/patrol/task/food-safety/arrive-shop/exam-situation/confirm`, {
    data,
  });
};
export const getTaskPhotos = async (params: { taskId: number; taskType: string }) =>
  get('/om-api/common/task/gallery', {
    params,
  });
// 查询诊断信息
export const getDiagnosticInfo = async (params: { taskId: number }) =>
  get<DiagnosticInfo>('/om-api/common/diagnostic/info/find-by-task-id', {
    params,
  });
export const uploadImage = async (data: TUploadParams, watermark: TWatermark) => {
  const url = {
    none: '/om-api/common/file/upload/image',
    default: '/om-api/common/file/upload/image/watermark',
    company: '/om-api/common/file/upload/image/watermark/company',
    extra: `/om-api/common/file/upload/image/watermarkOfShop`,
  }[watermark];

  return post<TUpload>(url, {
    headers: {
      'Role-Type': 'CORP',
    },
    data,
  });
};
// 获取原食安稽核报告(公司员工)
export const getParentReportId = async (taskId: number) =>
  get<{ existReport: boolean; parentId: number }>(
    `/om-api/corp/patrol/task/food-safety/arrive-shop/${taskId}/parent-report-id`,
    {},
  );
// 获取原食安稽核报告(门店员工)
export const getParentReportIdShop = async (taskId: number) =>
  get<{ existReport: boolean; parentId: number }>(
    `/om-api/shop/patrol/task/food-safety/arrive-shop/${taskId}/parent-report-id`,
    {},
  );

export const uploadVideo = async (data: TUploadParams) => post('/om-api/common/file/upload/video/watermark', { data });

export const uploadGalleryImage = async (data: TUploadGalleryParams) =>
  post('/om-api/common/task/gallery/picture', { data });

export const uploadGalleryVideo = async (data: TUploadGalleryParams) =>
  post('/om-api/common/task/gallery/video', { data });
// 获取巡检计划信息
export const getPatrolPlanInfo = () =>
  get('/om-api/app/normal/plan/statistics-by-user', {
    ignoreUnAuth: true,
  });

// 获取巡检任务信息
export const getPatrolTaskInfo = () =>
  get('/om-api/task/normal/statistics-by-user', {
    ignoreUnAuth: true,
  });

export async function queryConfiglist() {
  return get<{ key: ConfigKey; value: string | number }[]>('/om-api/common/system/config/all', {});
}

export async function queryPlanDetails(planId: string) {
  return get(`/om-api/corp/patrol/plan/quick-info?planId=${planId}`, {});
}
// 查询投流任务费用
export async function queryShopInvestRate(planId?: string) {
  return get(`/om-api/common/routine/task/third-party/additional-info?id=${planId}`, {});
}
// 更新投流任务费用
export const updateInvestRate = async (data: TaskInvestRate) =>
  post('/om-api/common/routine/task/third-party/flow/actual-cost', { data });
