import { SelfTaskStatusEnum } from '@src/common/enum';
import {
  CheckWayEnum,
  KillTaskStatusEnum,
  shopTypeEnum,
  TaskStatusEnum,
  TaskTypeEnum,
  yytTaskPlanStatusEnum,
  yytTaskStatusEnum,
} from './enum';
import { TASK_STATUS } from '../patrol/enum';

export type createTaskPayload = {
  taskTitle: string;
  taskInfo?: string;
  // 任务处理人ID列表
  taskPrincipalIds: number[];
  // 抄送人ID列表
  taskRecipientsIds?: number[];
  // 任务截止时间;
  taskDeadline: number;
  // 任务到期提醒，0不提醒，1提醒，默认提醒
  taskExpRemind: 0 | 1;
  // 任务类型ID
  taskTypeId: number;
  // sop表的ID列表
  sopIds?: number[];
  // 附件列表
  fileEntities?: {
    bucketName?: string;
    fileName: string;
    objectName: string;
  }[];
  checkSwitch?: 0 | 1; // 任务验收开关 0关闭，1开启
};

export type taskFileList = {
  id: number;
  fileUrl?: string;
  fileName?: string;
}[];
export type commentItemRespItem = {
  content: string;
  createTime: string;
  createUserId: number;
  creatorName: string;
};
export type getTaskDetailResp = {
  taskUserId: number;
  taskId: number;
  taskTitle: string;
  taskInfo: string;
  taskPrincipal: ZTUserPagesItem; // 任务处理人
  taskCreator: ZTUserPagesItem; // 任务创建人
  taskUpdateTime: string;
  taskDeadline: string; // 任务截止时间
  taskType: { id: number; taskType: string; comment: string };
  taskExpRemind: number; // 任务到期提醒
  updateAttachRequired: number;
  finishAttachRequired: number;
  sopInfoList: {
    id: number;
    name: string;
    fileUrl?: string;
    fileName?: string;
  }[]; // Sop关联列表
  recipientsList: ZTUserPagesItem[]; // 抄送人列表
  taskFileList: taskFileList; // 附件列表
  taskStatus: number;
  taskScheduleList: {
    id: number;
    taskUserId: number;
    scheduleValue: number;
    scheduleMark?: string;
    createTime: string;
    createUserId: number;
    scheduleFileList: taskFileList;
    scheduleCommentRespList: commentItemRespItem[];
  }[]; // 任务进度列表
  taskOperationLogList: {
    id: number;
    taskUserId: number;
    operation: string;
    reason?: string;
    createTime: string;
    updateTime: string;
  }[]; // 操作记录列表
  checkSwitch?: 0 | 1;
  tripartiteTaskType?: string;
};

export type QueryTaskListParams = {
  queryType: TaskTypeEnum;
  sortBy?: any;
  taskStatus: TaskStatusEnum | undefined;
  taskTypeIds?: number[];
  pageNum: number;
  pageSize: number;
};
export type QueryKillListParams = {
  hasCount: boolean;
  queryType: TaskTypeEnum;
  sortBy?: any;
  worksheetIds?: number[];
  disinfectionCompanyIds?: number[];
  taskStatus: KillTaskStatusEnum | undefined;
  taskTypeIds?: number[];
  pageNum: number;
  pageSize: number;
};

export type TaskListItem = {
  taskCreatorName: string;
  taskDeadline: string;
  taskId: number;
  taskPrincipalName: string;
  taskInfo: string;
  taskTitle: string;
  taskStatus: TaskStatusEnum;
  taskUserId: number;
  taskType: string;
  taskUpdateTime: string | null;
  scheduleValue: number;
  finishAttachRequired: 0 | 1;
  updateAttachRequired: 0 | 1;
  tripartiteTaskType?: string;
};

export type TaskCountResponse = {
  principal: number;
  under: number;
  waiting: number;
  created: number;
  recipients: number;
};

export type TaskTypeListItem = {
  id: number;
  taskType: string;
};

export type ModifyTaskStatusPayload = {
  remark?: string;
  taskStatus: TaskStatusEnum.InProgress | TaskStatusEnum.Rejected | TaskStatusEnum.Terminated;
  taskUserId: number;
};
export type AcceptanceTaskPayload = {
  remark?: string;
  taskStatus: TaskStatusEnum.Completed | 6; // 3:验收通过、6:驳回
  taskUserId: number;
};
export type CommentSubmitPayload = {
  content: string;
  scheduleId: number;
  taskUserId: number;
};
export type ProcessTaskPayload = {
  fileEntityList?: { fileName: string; objectName: string }[];
  remark: string;
  scheduleValue: number;
  taskUserId: number;
};
export type EditProcessTaskPayload = {
  fileEntityList?: { fileName: string; objectName: string }[];
  remark: string;
  scheduleId: number;
  scheduleValue: number;
  taskUserId: number;
};
export type ZTUserPagesItem = {
  userId: number;
  phone: string;
  userName: string;
  nickName: string;
  name: string;
  status: string;
  updateTime: string;
  roleItems: {
    roleId: number;
    userId: number;
    string: number;
    roleName: string;
    name: string;
    description: string;
    sort: number;
  }[];
  groupItems: { id: number; parentId: number; name: string }[];
  shopItems: { shopId: string; shopName: string; userId: number }[];
};
export type getZTUserPagesResp = {
  current: number;
  pages: number;
  result: ZTUserPagesItem[];
  searchCount: boolean;
  size: number;
  total: number;
};

export type taskTypeItem = {
  id: number;
  comment: string; // 类型描述
  taskType: string;
  children: taskTypeItem[];
};
export type getTaskTypeTreeResp = taskTypeItem[];

export type SOPTypeTreeItem = {
  id: number;
  name: string;
  sopInfoReps: SOPTypeTreeItem[];
  children: SOPTypeTreeItem[];
};
export type getSOPTypeTreeResp = SOPTypeTreeItem[];

export type sopInfo = {
  id: number;
  name: string;
  sopFiles?: taskFileList;
};

export type SelfTaskListItem = {
  endDate: number;
  reportDetail: {
    issuesCount: number;
    noReformCount: number;
    reportScore: number;
    submitUserId: number;
    passed: number;
    reportId: number;
    submitUserName: string;
    taskName: string;
  };
  taskStatus: SelfTaskStatusEnum;
  startDate: number;
};

export type SelfTaskListAll = {
  total: number;
  data: SelfTaskListItem[];
};
export type TSignType = 'SYSTEM' | 'SIGN_NEITHER' | 'SIGN_IN_OUT' | 'SIGN_IN';

export type QueryTaskListItem = {
  taskId: number;
  shopId: number;
  taskDate: string;
  shopName: string;
  shopNo: string;
  shopAddress: string;
  createTime: string;
  signed: 0 | 1;
  signType: TSignType;
  shopLat: number;
  shopLog: number;
  createByAvatar: string;
  createByName: string;
  executeUserId: number;
  createById: number;
  shopPhone: string;
  status: TASK_STATUS;
  cross?: boolean;
  MUST_CHECK_IN?: number;
  /**
   * 签离状态
   */
  leaveStatus: 0 | 1;
  shopManagerName: string;
  checkListIds: number[];
  taskType: '0:自检 1:巡检';
  startTime: '点击巡店任务正式开始时间，格式：2021-03-01 10:00:00';
  subType: 1 | 0; // 任务子类型，0：常规巡检/自检，1：视频云巡检 v1.8新增
  submitReport?: boolean; // 签离并提交报告
  summaryText?: string;
  reportId?: string;
  notFilledItemHandleType?: 'SET_FULL_SCORE' | 'SET_NOT_APPLY';
};
export type CorporationConfig = {
  ALLOW_ALBUM: 0 | 1;
  CHECK_IN_DISTANCE: number;
  REPORT_AUTO_CONFIRM_DAYS: number;
  MUST_CHECK_IN: number;
  ISSUE_FIX_DEFAULT_DAYS: number;
  REPORT_SHOW_PASS_RATE: string;
};
export type getNormalTaskPlanListPayload = {
  pageNo: number;
  pageSize: number;
  planType?: number;
  status?: string;
  fromStartTime?: string;
  toEndTime?: string;
  userId?: number;
  planId?: number;
};
export type getNormalTaskPlanListItem = {
  id: number;
  planId: number;
  userId?: number;
  status: yytTaskPlanStatusEnum;
  planType: CheckWayEnum;
  createUserId: number;
  name: string;
  startDate: number;
  endDate: number;
  patrolUserName?: string;
  createType: string;
};
export type getNormalTaskPlanListResp = {
  total: number;
  data?: getNormalTaskPlanListItem[];
};

export type geTaskCompleteInfoResp = {
  planId: number;
  totalShopCount: number;
  patrolShopCount: number;
  passReportCount: number;
  failReportCount: number;
};
export type ExecuteConfirmItem = {
  taskId: number;
  taskType:
    | 'NORMAL'
    | 'VIDEO'
    | 'CROSS'
    | 'FOOD_SAFETY_NORMAL'
    | 'FOOD_SAFETY_VIDEO'
    | 'DIAGNOSTIC'
    | 'FOOD_SAFETY_ARRIVE_SHOP'; // NORMAL-到店巡检(权限内),VIDEO-视屏云巡检,CROSS-到店巡检(权限外) FOOD_SAFETY_ARRIVE_SHOP-食安稽核到店辅导
  createTime: string;
  type: string;
  exePeriodConfirmStatus: string;
  taskStatus: string;
  shopId: string;
  taskName: string;
  shopName: string;
  shopAddress: string;
  shopContact: string;
  inspectorName: string;
  executionPeriodBeginTime: string;
  executionPeriodEndTime: string;
  level: 'UNQUALIFIED_SHOP' | 'RETEST_SHOP'; // 不合格门店|复检门店
};
export type TaskExecuteConfirmResp = {
  pages: number;
  data: ExecuteConfirmItem[];
  size: number;
  total: number;
};

export type getNormalTaskList = {
  signed: 0 | 1;
  signType: 'SYSTEM' | 'SIGN_NEITHER' | 'SIGN_IN_OUT' | 'SIGN_IN';
  leaveStatus?: 0 | 1;
  taskId: number;
  taskName: string;
  status: yytTaskStatusEnum;
  subType: CheckWayEnum;
  cross: boolean;
  userId: number;
  taskDate: number;
  userName: string;
  phoneNumber: string;
  simpleShop: {
    id: number;
    name: string;
    type: keyof shopTypeEnum; // 加盟类型
    address: string;
  };
  reportDetail: {
    reportId: number; // 报告id
    reportStatus: number; // 报告状态
    reportScore: number; // 报告得分
    passed: number; // 报告是否通过 1：通过 / 0：未通过
    issueCount: number; // 问题项
    noReformCount: number; // 未整改项
  };
};

export type getNormalTaskListResp = getNormalTaskList[];

export type patrolTaskListItem = {
  taskId: number;
  shopId: number;
  reportId: number;
  shopName: string;
  shopNo: string;
  taskDate: string;
  createByName: string;
  createByAvatar: string;
  shopAddress: string;
  shopLat: number;
  shopLog: number;
  shopManagerId: number;
  shopManagerName: string;
  shopPhone: string;
  headMobile: string;
  status: number;
  statusDesc: string;
  taskType: number;
  createTime: string;
  signed: 0 | 1;
  shopType: 'JOIN' | 'DIRECT';
  subType: 0 | 1;
  signType: 'SYSTEM' | 'SIGN_NEITHER' | 'SIGN_IN_OUT' | 'SIGN_IN';
  cross: boolean;
  leaveStatus?: number | undefined;
};
export type patrolTaskListResp = patrolTaskListItem[];
export type getUnderTaskSelfPlanListItem = {
  userId: number;
  name: string;
  waitStartCount: number; // 待开始任务计划数量
  processCount: number; // 进行中任务计划数量
  completeCount: number; // 已结束任务计划数量
  cancelCount: number; // 已取消任务计划数量
  patrolUserId: number; // 巡检人id
  patrolUserName: string;
};
export type getUnderTaskSelfPlanListResp = getUnderTaskSelfPlanListItem[];
export type GeoLocation = {
  longitude: number;
  latitude: number;
  address?: string;
  title?: string;
};

export type PlanShopInfo = GeoLocation & {
  shopId: number;
  shopName: string;
  shopNo: string;
  shopManagerName: string;
  shopManagerPhone: string;
  shopAddress: string;
  necessaryFlag: number; // 是否是必检门店，1：是 0：否
  shopType: 'DIRECT' | 'JOIN';
  /**
   * 其实是这种格式，但是不好排序 '2021-07-03'
   */
  lastPatrolTime: number; // 上次巡检日期
  lastPatrolScore: 98; // 上次巡检得分
  hasPatrol: 1; // 1：本次已巡检 0：本次未巡检
  deviceCount?: number;
  deviceStatus?: number;
  /**
   * 中台门店状态
   * PREPARING.筹备中
   * OPEN.营业中
   * OFFLINE.停业中
   * CLOSE.闭店中
   * TO_BE_OPENED.待营业
   
   */
  shopStatus?: string;
};
export type ShopListRes = {
  shopId: number;
  shopName: string;
  shopNo: string;
  latitude: number;
  longitude: number;
  shopManagerName: string;
  shopManagerPhone: string;
  shopAddress: string;
  lastPatrolTime: string; // 上次巡检时间
  lastPatrolScore: number; // 上次巡检得分
  deviceCount: number; // 摄像头数量
  deviceStatus: number;
};
export type CheckItem = {
  checkListTypeId: number;
  itemId: number;
  itemSOP: string;
  style?: number;
  // hasApply?: boolean;
  score?: number;
  itemRemark?: string;
  imageURLS: {
    url: string;
    type: 'IMG' | 'VIDEO';
    id?: string;
  }[];
  reformLimit?: number;
  necessaryFlag: 0 | 1; // 必检项
  fineFlag: 0 | 1; // 罚款项
  redLine: 0 | 1; // S项
  deductType: 'CATEGORY' | 'WORKSHEET' | 'REPORT'; // S项扣分类型，CATEGORY：检查表分类，WORKSHEET：检查表，REPORT：最终报告
  deductRatio: 80; // S项扣分比例
  standard?: string;
  fullScore: number;
  sopId?: number;
  hasApply: boolean;
  qualified: boolean | 1 | 0;
  notQualifiedMustUpload?: number;
  qualifiedMustUpload?: number;
  scoreType: 1 | 2; // 1：得分项，2：扣分项
  copyUsers: {
    id: number;
    name: string;
  }[];
  shopRemark?: string;
  watchId: number | null;
  watchName: string | null;
  ALLOW_ALBUM: 0 | 1 | 2; // 0：跟随系统设置，1：允许，2：不允许 v2.3新增字段
  selectReasons: string[] | null;
  reasons?: string[];
  nonconformityReasons: string[];
  mustChooseNonconformityReasons: boolean;
};
export type ChecklistCategory = {
  groupTypeId: number;
  groupTypeName: string;
  itemCount: number;
  filledCount: number;
  children: CheckItem[];
};
export type ChecklistItem = {
  checkListId: number;
  checkListName: string;
  itemCount?: number;
  filledCount?: number;
  actualTotalScore: number;
  importance?: number;
  children?: ChecklistCategory[];
};
export type ChecklistData = {
  data: ChecklistItem[];
  taskId: number;
  shopId: number;
  cross: boolean;
  signType?: string;
  timeType?: number;
  repeatType: any;
  cornDay?: {
    startTime: string;
    day: number;
    hour: number;
  };
  cornWeek?: {
    startTime: string;
    day: number;
    hour: number;
    weeks: number[];
  };
  cornMonth?: {
    startTime: string;
    day: number;
    hour: number;
    days: number[];
  };
  cronType?: 'CYCLE_DAY' | 'CYCLE_WEEK' | 'CYCLE_MONTH';
  period?: string;
  shopName: string;
  taskName?: string;
  expiredTime: string;
  beginTime: string;
  summaryText?: string;
  shopSummary?: string;
  subType?: 1 | 0;
};
export type UpdateChecklistItem = {
  hasApply?: boolean;
  checkListId: number;
  checkListTypeId: number;
  patrolWorksheetItemId: number;
  qualified?: 0 | 1 | boolean;
  score?: number;
  itemRemark?: string;
  images?: any;
  reformLimit?: number;
  shopRemark?: string;
  reasons?: any[];
  copyUserIds?: number[];
};
export type UpdateChecklistParams = {
  taskId: number;
  checkLists: UpdateChecklistItem[];
};
export type TUploadParams = {
  bucket: string;
  key: string;
  originName: string;
  remark?: string;
  userId?: number;
  watermark?: string;
};
export type TUploadGalleryParams = TUploadParams & {
  taskId?: number;
  waterMarkContent?: string;
  taskType?: string;
  shopId?: string;
  address?: string;
  latitude?: number;
  longitude?: number;
};
export enum TWatermark {
  none = 'none',
  default = 'default',
  company = 'company',
  extra = 'extra',
}
export type TUpload = {
  contentType: string;
  fileType: string;
  id: string;
  name: string;
  url: string;
};

export type TaskInvestRate = {
  id?: number;
  actualCost?: number;
};
