.iAdmTabs {
  :global {
    .adm-tabs-tab-list {
      color: #58595b;
    }

    .adm-tabs-tab-active {
      font-weight: 500;
    }

    .adm-tabs-tab-list {
      margin: 0 8px;
    }

    .adm-tabs-tab-wrapper {
      padding: 0 8px;
    }

    .adm-tabs-tab {
      padding: 16px 0;
      line-height: 16px;
    }
    .adm-tabs-content {
      padding: 0;
    }

    .adm-tabs-header {
      // border-bottom: 0;
      min-width: 100vw;
    }
  }
}

.iAdmTabs2 {
  :global {
    .adm-tabs-tab-list {
      color: #58595b;
    }

    .adm-tabs-tab-active {
      font-weight: 500;
    }

    .adm-tabs-tab-list {
      margin: 0 8px;
    }

    .adm-tabs-tab-wrapper {
      padding: 0 8px;
    }

    .adm-tabs-tab {
      padding: 16px 0;
    }

    // .adm-tabs-header {
    //   border-bottom: 0;
    // }
  }
}
.iAdmTabs3 {
  :global {
    .adm-tabs-tab-list {
      color: #58595b;
    }

    .adm-tabs-tab-active {
      font-weight: 500;
    }

    .adm-tabs-tab-list {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(56px, 1fr));
      margin: 0 8px;
    }

    .adm-tabs-tab-wrapper {
      padding: 0 8px;
    }

    .adm-tabs-tab {
      padding: 11px 0;
    }

    // .adm-tabs-header {
    //   border-bottom: 0;
    // }
  }
}

.ISteps {
  :global {
    .adm-step-status-finish .adm-step-icon-dot {
      outline-width: 4px;
      outline-color: #b3ebe9;
      outline-style: solid;
    }

    .adm-step-status-finish {
      --line-to-next-color: var(--adm-color-border);
    }

    .adm-step-status-wait .adm-step-icon-dot {
      width: 10px;
      height: 10px;
    }

    .adm-step-content {
      padding-bottom: 20px;
      margin-bottom: 20px;

      border-bottom: 1px solid rgba(0, 0, 0, 0.03);

      .adm-step-title {
        line-height: 1.25rem !important;
      }
    }

    .adm-step:last-child {
      .adm-step-content {
        margin-bottom: 16px;
        border-bottom: 0;
      }
    }
  }
}

.ISteps2 {
  :global {
    .adm-step-status-finish .adm-step-icon-dot {
      outline-width: 4px;
      outline-color: #b3ebe9;
      outline-style: solid;
    }

    .adm-step-status-finish {
      --line-to-next-color: var(--adm-color-border);
    }

    .adm-step-status-wait .adm-step-icon-dot {
      width: 10px;
      height: 10px;
    }

    .adm-step-content {
      padding-bottom: 0.8125rem !important;

      .adm-step-title {
        line-height: 1.25rem !important;
      }
    }

    .adm-steps-vertical .adm-step .adm-step-content {
      padding-bottom: 20px;
    }
  }
}

.Cards {
  :global {
    .adm-card-header-title {
      width: 100%;
    }
  }
}

.summarycontent {
  background: #fafafa;
  border-radius: 4px;
}

.operationArea {
  display: flex;
  justify-content: space-between;
  margin-top: 12px;

  > span {
    width: 42%;
    border-radius: 999px;
    line-height: 28px;
    border: 1px solid;
    text-align: center;
    cursor: pointer;
  }

  .pass {
    color: #378BFF;
    border-color: #378BFF;

    &.active {
      color: #fff;
      background-color: #378BFF;
    }
  }

  .fail {
    color: red;
    border-color: red;

    &.active {
      color: #fff;
      background-color: red;
    }
  }
}

.remarkArea {
  width: 100%;
  background-color: #f6f9fa;
  padding: 5px 10px;
}

.WfullPopup {
  :global {
    .adm-popup-body {
      width: 100%;
    }
  }
}
