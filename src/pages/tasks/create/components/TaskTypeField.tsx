import { useState } from 'react';
import { IPopup } from '@src/components/IPopup';
import { CustomerFormFieldWrapper } from '@src/components/CustomerFormFieldWrapper';
import { Radio } from 'antd-mobile';
import cn from 'classnames';
import { useRequest } from 'ahooks';
import { getTaskTypeTree } from '../../api';
import { taskTypeItem } from '../../api.type';
import { IconFont } from '@src/components';

interface TaskTypeFieldProps {
  value?: any;
  onChange?: any;
}

export const TaskTypeField = ({ value, onChange }: TaskTypeFieldProps) => {
  const [visible, setVisible] = useState(false);

  const [taskTypeList, setTaskTypeList] = useState<Map<string, taskTypeItem[]>>();

  const [typeLevel1, setTypeLevel1] = useState<string>('');

  useRequest(getTaskTypeTree, {
    onSuccess: (data) => {
      // 二级
      const _map = new Map();
      data.forEach((element) => {
        _map.set(element.taskType, element.children);
      });
      setTaskTypeList(_map);
      setTypeLevel1(_map.keys().next().value);
    },
  });
  return (
    <CustomerFormFieldWrapper
      value={value?.taskType}
      onShow={() => {
        setVisible(true);
      }}
      placeholder="请选择(必选)"
    >
      <IPopup
        visible={visible}
        onClose={() => {
          setVisible(false);
        }}
        title="任务类型"
        closeOnMaskClick
      >
        {!!taskTypeList && (
          <div className="flex h-[455px] text-13 leading-[13px]">
            <div className="w-[150px] bg-[#F8F8F8] flex flex-col overflow-y-scroll shrink-0">
              {Array.from(taskTypeList.keys()).map((key) => {
                return (
                  <div
                    onClick={() => {
                      setTypeLevel1(key);
                    }}
                    className={cn('p-4', {
                      'bg-white': key === typeLevel1,
                    })}
                    key={key}
                  >
                    {key}
                  </div>
                );
              })}
            </div>
            <div className="grow flex flex-col overflow-y-scroll ">
              {!!taskTypeList.get(typeLevel1)?.length ? (
                taskTypeList.get(typeLevel1)?.map((item) => {
                  return (
                    <div
                      onClick={() => {
                        setVisible(false);
                        onChange?.(item);
                      }}
                      className="flex justify-between items-center p-4"
                      key={item.id}
                    >
                      <span className="truncate">{item.taskType}</span>
                      <Radio
                        style={{
                          '--icon-size': '19px',
                        }}
                        checked={item?.id === value?.id}
                      />
                    </div>
                  );
                })
              ) : (
                <div className="flex flex-col items-center pt-[68px] h-full gap-4">
                  <IconFont type="icon-zanwushuju" className="text-[90px]  text-[#DCDCDC]" />
                  <span className="text-[#858585] text-xs">该分类暂无明细</span>
                </div>
              )}
            </div>
          </div>
        )}
      </IPopup>
    </CustomerFormFieldWrapper>
  );
};
