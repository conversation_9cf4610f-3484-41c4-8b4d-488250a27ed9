import { useMemo, useState } from 'react';
import { Checkbox, Popup } from 'antd-mobile';
import { ZTUserPagesItem } from '../../api.type';

interface PersonnelCheckboxItemProps<T> {
  type?: 'add' | 'remove';
  checkboxStatus?: boolean;
  data: T;
  onAdd?: (params: T) => void;
  onDelete?: (params: T) => void;
}

export const PersonnelCheckboxItem = ({
  type = 'add',
  data,
  checkboxStatus,
  onAdd,
  onDelete,
}: PersonnelCheckboxItemProps<ZTUserPagesItem>) => {
  const [showType, setShowType] = useState<'group' | 'shop' | undefined>();
  const [visible, setVisible] = useState(false);

  const items = useMemo(() => {
    if (showType === 'group') {
      return data.groupItems;
    } else if (showType === 'shop') {
      return data.shopItems;
    } else {
      return [];
    }
  }, [showType, data]);

  return (
    <>
      <div className="p-4 text-sm">
        <div className="flex items-center overflow-x-hidden justify-between">
          <div className="flex items-center justify-center">
            {type === 'add' ? (
              <Checkbox
                checked={checkboxStatus}
                onClick={() => {
                  type === 'add' && onAdd?.(data);
                }}
              />
            ) : (
              <button
                onClick={() => {
                  onDelete?.(data);
                }}
                type="button"
                className="rounded-[.125rem] text-xs leading-[.8125rem] text-grey px-1 py-[.1875rem] shrink-0 border border-solid border-[#979797]"
              >
                移除
              </button>
            )}
            <span className="text-dark text-14 font-medium grow truncate ml-3.5">{data.name}</span>
            <div className="ml-2 px-1 py-[2px] rounded-[2px] bg-[#378BFF]/10 text-[#378BFF] mr-1 max-w-[80px] truncate shrink-0 text-xs">
              {data.roleItems?.[0]?.name}
            </div>
          </div>
          <div
            onClick={() => {
              setShowType('group');
              setVisible(true);
            }}
            className="text-[#151515] text-13 leading-[13px] truncate"
          >
            {data.groupItems?.map((item) => item.name).join('、')}
          </div>
        </div>
        <div
          onClick={() => {
            setShowType('shop');
            setVisible(true);
          }}
          className="mt-2 text-[#ADADAD] truncate ml-9"
        >
          {data?.shopItems?.length ? data.shopItems.map((item) => item.shopName).join('；') : '无'}
        </div>
      </div>
      <Popup
        visible={visible}
        showCloseButton
        bodyClassName="rounded-t-lg h-1/2"
        onMaskClick={() => {
          setVisible(false);
          setShowType(undefined);
        }}
        onClose={() => {
          setVisible(false);
          setShowType(undefined);
        }}
      >
        <div className="px-4 py-2 text-base text-black font-medium border-b border-[#F0F0F0]">
          <span>{showType === 'group' ? '所属战区' : '所属门店'}</span>
        </div>
        <div className="px-4 flex flex-col py-3 overflow-y-scroll">
          {items.length ? (
            items.map((item) => (
              <span key={'shopId' in item ? item.shopId : item.id} className="text-sm font-medium">
                {'shopName' in item ? item.shopName : item.name}
              </span>
            ))
          ) : (
            <span className="text-sm font-medium">无</span>
          )}
        </div>
      </Popup>
    </>
  );
};
