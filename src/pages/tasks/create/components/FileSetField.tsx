import { useState } from 'react';
import { IPopup } from '@src/components/IPopup';
import { CustomerFormFieldWrapper } from '@src/components/CustomerFormFieldWrapper';
import { Radio, Space } from 'antd-mobile';
import { has, keys } from 'lodash';
interface FileSetFieldProps {
  value?: any;
  onChange?: any;
}
export const setInit = {
  updateAttachRequired: undefined,
  finishAttachRequired: undefined,
};
export const FileSetField = ({ value, onChange }: FileSetFieldProps) => {
  const [visible, setVisible] = useState(false);
  return (
    <CustomerFormFieldWrapper
      value={keys(setInit).every((o) => has(value, o)) ? '已设置' : ''}
      onShow={() => {
        setVisible(true);
      }}
      placeholder="未设置(必选)"
    >
      <IPopup
        visible={visible}
        onClose={() => {
          setVisible(false);
        }}
        title="附件上传设置"
        closeOnMaskClick
      >
        <div className="px-4 py-8">
          <div className="flex justify-between items-center">
            <div className="text-base">更新进度时</div>
            <Radio.Group
              onChange={(e) => {
                onChange?.({
                  ...value,
                  updateAttachRequired: e,
                });
              }}
            >
              <Space direction="horizontal">
                <Radio value={1}>必填</Radio>
                <Radio value={0}>选填</Radio>
              </Space>
            </Radio.Group>
          </div>
          <div className="flex justify-between items-center mt-4">
            <div className="text-base">完成任务时</div>
            <Radio.Group
              onChange={(e) => {
                onChange?.({
                  ...value,
                  finishAttachRequired: e,
                });
              }}
            >
              <Space direction="horizontal">
                <Radio value={1}>必填</Radio>
                <Radio value={0}>选填</Radio>
              </Space>
            </Radio.Group>
          </div>
        </div>
      </IPopup>
    </CustomerFormFieldWrapper>
  );
};
