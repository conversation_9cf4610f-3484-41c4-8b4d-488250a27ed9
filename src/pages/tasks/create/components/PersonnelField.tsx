import { useEffect, useRef, useState } from 'react';
import { CustomerFormFieldWrapper } from '@src/components/CustomerFormFieldWrapper';
import styles from '@src/components/IDropdown/index.module.scss';
import { IPopup, IPopupPage } from '@src/components/IPopup';
import { useSelections, useUpdateEffect } from 'ahooks';
import { Button, Checkbox, Dropdown, DropdownRef, InfiniteScroll, Radio, SearchBar, Toast } from 'antd-mobile';
import { DownOutline, UpOutline } from 'antd-mobile-icons';
import cn from 'classnames';
import _ from 'lodash';
import containerStyles from './index.module.scss';
import { PersonnelCheckboxItem } from './PersonnelCheckboxItem';
import { getAllUserIDs, getZTUserPages } from '../../api';
import { ZTUserPagesItem } from '../../api.type';
import '@src/pages/tasks/patrolTask/components/ChecklistSelect/index.scss';

export enum PersonnelFieldEnum {
  '处理人',
  '抄送人',
}

interface PersonnelFieldProps {
  value?: any;
  onChange?: any;
  type: PersonnelFieldEnum;
  id: string;
  withPermission: boolean;
}
const queryUserByOption = [
  {
    label: '姓名',
    value: 1,
  },
  {
    label: '角色',
    value: 2,
  },
  {
    label: '组织',
    value: 3,
  },
];

export const PersonnelField = ({ value, onChange, id, type, withPermission }: PersonnelFieldProps) => {
  const [visible, setVisible] = useState(false);

  const [isSwitchCkecked, setIsSwitchCkecked] = useState(false);
  const [SelectedVisible, setSelectedVisible] = useState(false);
  const [queryUserBy, setQueryUserBy] = useState<1 | 2 | 3>(1);
  const [list, setList] = useState<ZTUserPagesItem[]>([]);
  const [resUserIDList, setResUserIDList] = useState<number[]>([]);
  const [resUserIDListAll, setResUserIDListAll] = useState<number[]>([]);
  const [resUserIDListByKeyWord, setResUserIDListByKeyWord] = useState<number[]>([]);
  const [searchList, setSearchList] = useState<ZTUserPagesItem[]>([]);
  const searchKeyRef = useRef('');
  const searchKeySelectedRef = useRef('');
  const [insideSearchKey, setInsideSearchKey] = useState('');
  const [hasMore, setHasMore] = useState(true);
  const [hasMoreSelected, setHasMoreSelected] = useState(true);
  const pageNumRef = useRef(0);
  const pageNumSelectedRef = useRef(0);
  const ref = useRef<DropdownRef>(null);
  const [userChecked, setUserChecked] = useState<ZTUserPagesItem[]>([]);
  const { selected, isSelected, toggle, setSelected } = useSelections(
    // list.map((item) => item.userId),
    list.length !== 0 ? list.map((item) => item.userId) : searchList.map((item) => item.userId),

    [],
  );
  useEffect(() => {
    if (visible) {
      // 更新选中态
      // setSelected(value?.userChecked.map((o: any) => o.userId) || []);
      setSelected(value?.resUserIDList.map((o: any) => o) || []);
      setUserChecked(value?.userChecked || []);
      setIsSwitchCkecked(isSwitchCkecked);
      if (resUserIDListAll.length !== value?.userChecked.length) {
        setIsSwitchCkecked(false);
      }
    }
  }, [visible]);
  const isSameObject = (a: ZTUserPagesItem, b: ZTUserPagesItem) => {
    return a.userId === b.userId;
  };
  const checkArray = (arr1: number[], arr2: number[]) => {
    if (!!arr1.length && !!arr2.length) {
      return arr1.every((element) => arr2.includes(element));
    } else {
      return false;
    }
  };
  const loadMore = async () => {
    pageNumRef.current++;
    if (!!searchKeyRef.current) {
      const res = await getZTUserPages({
        withPermission,
        pageNo: pageNumRef.current,
        pageSize: 50,
        keyword: searchKeyRef.current,
        type: queryUserBy,
      });
      const resUserIDs = await getAllUserIDs({
        withPermission,
        pageNo: pageNumRef.current,
        pageSize: 50,
        keyword: searchKeyRef.current,
        type: queryUserBy,
      });
      setResUserIDListByKeyWord(resUserIDs);
      setSearchList((pre) => [...pre, ...res!.result]);
      setHasMore(res!.result.length > 0);
    } else {
      const res = await getZTUserPages({
        withPermission,
        pageNo: pageNumRef.current,
        pageSize: 50,
        type: queryUserBy,
      });
      const resUserIDs = await getAllUserIDs({
        withPermission,
        pageNo: pageNumRef.current,
        pageSize: 50,
        type: queryUserBy,
      });
      if (resUserIDListAll.length === 0) {
        setResUserIDListAll((pre) => _.uniq([...pre, ...resUserIDs]));
      }
      setList((pre) => [...pre, ...res!.result]);
      setHasMore(res!.result.length > 0);
    }
  };
  const getSelectedMen = async () => {
    pageNumSelectedRef.current++;
    if (selected.length > 0) {
      const res = await getZTUserPages({
        withPermission,
        pageNo: pageNumSelectedRef.current,
        pageSize: 50,
        keyword: searchKeySelectedRef.current,
        type: queryUserBy,
        userIds: selected,
      });
      setUserChecked((pre) => _.uniqBy([...pre, ...res!.result], 'userId'));
      setHasMoreSelected(res!.result.length > 0);
    }
  };

  useUpdateEffect(() => {
    if (searchKeyRef.current === '') {
      setResUserIDList(resUserIDListAll);
      if (resUserIDListAll.length !== selected.length) {
        setIsSwitchCkecked(false);
      }
    } else {
      if (checkArray(resUserIDListByKeyWord, selected)) {
        setIsSwitchCkecked(true);
      } else {
        setIsSwitchCkecked(false);
      }
      setResUserIDList(resUserIDListByKeyWord);
    }
  }, [searchKeyRef.current]);
  useEffect(() => {
    if (resUserIDListByKeyWord.length !== 0 && checkArray(resUserIDListByKeyWord, selected)) {
      setIsSwitchCkecked(true);
    }
    if (!!resUserIDListAll.length && resUserIDListAll.length === selected.length) {
      setIsSwitchCkecked(true);
    }
    if (searchKeyRef.current !== '' && !checkArray(resUserIDListByKeyWord, selected)) {
      setIsSwitchCkecked(false);
    }
  }, [resUserIDListByKeyWord, selected]);
  useUpdateEffect(() => {
    pageNumRef.current = 0;
    setList([]);
    setSearchList([]);
    setHasMore(true);
    loadMore();
  }, [queryUserBy]);
  return (
    <CustomerFormFieldWrapper
      value={(value?.userChecked || []).map((item: ZTUserPagesItem) => item.name).join('、')}
      onShow={() => {
        setVisible(true);
      }}
      placeholder={PersonnelFieldEnum[type] === '处理人' ? '未选择(必选)' : '未选择'}
      // overflowedText={`等${isSwitchCkecked ? value?.resUserIDList.length : value?.userChecked?.length
      //   }位${PersonnelFieldEnum[type]}`}
      overflowedText={`等${selected?.length}位${PersonnelFieldEnum[type]}`}
      // overflowedText={`等${value?.userChecked?.length}位${PersonnelFieldEnum[type]}`}
      // overflowedText={`等${value?.resUserIDList?.length}位${PersonnelFieldEnum[type]}`}
    >
      <IPopupPage
        visible={visible}
        DocumentTitle={`选择${PersonnelFieldEnum[type]}`}
        footer={
          <div className="flex">
            <div className="flex justify-between flex-1 pl-[15px] pr-5">
              <div className="flex justify-between items-center text-sm pr-4">
                <Checkbox
                  checked={isSwitchCkecked}
                  onChange={() => {
                    if (!isSwitchCkecked) {
                      if (searchKeyRef.current === '') {
                        setSelected(resUserIDListAll);
                      } else {
                        setSelected((pre) => _.uniq([...pre, ...resUserIDListByKeyWord]));
                      }
                      list.length === 0
                        ? setUserChecked((pre) => _.uniqBy([...pre, ...searchList], 'userId'))
                        : setUserChecked((pre) => _.uniqBy([...pre, ...list], 'userId'));
                    } else {
                      if (searchKeyRef.current === '') {
                        setSelected([]);
                      } else {
                        setSelected((pre) => _.differenceWith(pre, resUserIDListByKeyWord));
                      }
                      list.length === 0
                        ? setUserChecked((pre) => _.differenceWith(pre, searchList, isSameObject))
                        : setUserChecked((pre) => _.differenceWith(pre, list, isSameObject));
                    }
                    setIsSwitchCkecked(!isSwitchCkecked);
                  }}
                />
                <div className="px-2">全选</div>
              </div>
              <div
                onClick={() => {
                  if (!!selected.length) {
                    setSelectedVisible(true);
                    pageNumSelectedRef.current = 0;
                    setUserChecked([]);
                    setHasMoreSelected(true);
                    // getSelectedMen();
                  }
                }}
                className="pt-3 pb-[.375rem] flex flex-col justify-between"
              >
                <div className="flex items-center text-sm gap-[.375rem]">
                  {/* 已选{PersonnelFieldEnum[type]}: */}
                  已选:
                  <span className="text-primary text-base font-medium">
                    {selected.length > 9999 ? '9999+' : selected.length}人
                  </span>
                  <UpOutline className="text-primary text-base" />
                </div>
                <span className="text-xs text-[#979797]">
                  点击查看已选
                  {PersonnelFieldEnum[type]}
                </span>
              </div>
            </div>
            <Button
              onClick={() => {
                setVisible(false);
                onChange?.({ userChecked, resUserIDList: selected });
                // onChange?.({ userChecked });
              }}
              color="primary"
              fill="solid"
              style={{ padding: '0 2rem', '--border-radius': '0px' }}
            >
              确认添加
            </Button>
          </div>
        }
        onClose={() => {
          setVisible(false);
          setSelectedVisible(false);
        }}
      >
        <div className="px-4 py-3 sticky top-0 left-0 bg-white ">
          <div className="bg-[#f5f5f5] flex items-center rounded-lg">
            <Dropdown
              ref={ref}
              className={cn('border-r bg-transparent px-4', styles.IDropdown)}
              getContainer={document.getElementById(id)}
            >
              <Dropdown.Item
                key="person"
                title={<div>{queryUserByOption.find((o) => o.value === queryUserBy)?.label}</div>}
                arrow={<DownOutline className="text-xs" />}
              >
                <div className="checklist-select-filter-type">
                  <Radio.Group
                    value={queryUserBy}
                    onChange={(val: any) => {
                      setQueryUserBy(val);
                      ref.current?.close();
                    }}
                  >
                    {queryUserByOption.map((o) => (
                      <Radio key={o.value} value={o.value}>
                        {o.label}
                      </Radio>
                    ))}
                  </Radio.Group>
                </div>
              </Dropdown.Item>
            </Dropdown>
            <SearchBar
              // placeholder="搜索处理人"
              placeholder={`搜索${PersonnelFieldEnum[type]}`}
              style={{
                '--height': '40px',
                flexGrow: 1,
              }}
              onSearch={(val) => {
                pageNumRef.current = 0;
                searchKeyRef.current = val;
                setList([]);
                setSearchList([]);
                setHasMore(true);
                loadMore();
              }}
              onClear={() => {
                pageNumRef.current = 0;
                searchKeyRef.current = '';
                setList([]);
                setSearchList([]);
                setHasMore(true);
                loadMore();
              }}
              onFocus={() => {
                ref.current?.close();
              }}
            />
          </div>
        </div>
        <div className={cn('divide-y divide-[#F6F6F6] text-sm', containerStyles.IDropdownContainer)} id={id}>
          {(list.length === 0 ? searchList : list).map((item) => {
            return (
              <PersonnelCheckboxItem
                data={item}
                key={item.userId}
                type="add"
                checkboxStatus={isSelected(item.userId)}
                onAdd={(user) => {
                  if (userChecked.length > 999) {
                    Toast.show({
                      content: '最多选择999位人员',
                    });
                    return;
                  }
                  if (isSelected(user.userId)) {
                    // 移除选中
                    setIsSwitchCkecked(false);
                    setResUserIDList((pre) => pre.filter((item) => item !== user.userId));
                    setUserChecked((pre) => pre.filter((item) => item.userId !== user.userId));
                  } else {
                    // 选中
                    setUserChecked((pre) => [...pre, { ...user }]);
                    setResUserIDList((pre) => [...pre, user.userId]);
                  }
                  toggle(user.userId);
                }}
              />
            );
          })}
          <InfiniteScroll loadMore={loadMore} hasMore={hasMore} />
        </div>
      </IPopupPage>
      <IPopup
        visible={SelectedVisible}
        onClose={() => {
          setInsideSearchKey('');
          setSelectedVisible(false);
        }}
        title={`已选择${PersonnelFieldEnum[type]}`}
        closeOnMaskClick
        bodyStyle={{
          height: '75vh',
        }}
        destroyOnClose
      >
        <div className="px-4 py-3 sticky top-0 left-0 bg-white">
          <SearchBar
            // placeholder="搜索处理人"
            placeholder={`搜索${PersonnelFieldEnum[type]}`}
            style={{
              '--height': '40px',
            }}
            onSearch={(val) => {
              searchKeySelectedRef.current = val;
              pageNumSelectedRef.current = 0;
              setInsideSearchKey(val);
              getSelectedMen();
            }}
            onClear={() => {
              searchKeySelectedRef.current = '';
              pageNumSelectedRef.current = 0;
              setInsideSearchKey('');
              getSelectedMen();
            }}
          />
        </div>
        <div className="divide-y divide-[#F6F6F6] text-sm">
          {userChecked
            .filter((item) => {
              if (!!insideSearchKey) {
                const regex = new RegExp(insideSearchKey, 'i');
                const result = (item.name || '').match(regex);
                return !!result;
              }
              return true;
            })
            .map((item) => {
              return (
                <PersonnelCheckboxItem
                  data={item}
                  key={item.userId}
                  type="remove"
                  onDelete={(user) => {
                    setUserChecked((pre) => pre.filter((item) => item.userId !== user.userId));
                    toggle(user.userId);
                  }}
                />
              );
            })}
          <InfiniteScroll loadMore={getSelectedMen} hasMore={hasMoreSelected} />
        </div>
      </IPopup>
    </CustomerFormFieldWrapper>
  );
};
