import { CustomerFormFieldWrapper } from '@src/components/CustomerFormFieldWrapper';
import { IPopup, IPopupPage } from '@src/components/IPopup';
import { useRequest, useSelections } from 'ahooks';
import { Button, Checkbox, ImageViewer, Toast } from 'antd-mobile';
import { UpOutline } from 'antd-mobile-icons';
import { useEffect, useState } from 'react';
import { getSopTypeTreeByTaskTypeId } from '../../api';
import { getSOPTypeTreeResp, sopInfo } from '../../api.type';
import cn from 'classnames';
import { IconFont } from '@src/components';
import PdfViewer from '@src/components/PdfViewer';
import VideoViewer from '@src/components/VideoViewer';

interface SopCheckboxItemProps<T> {
  type?: 'add' | 'remove';
  checkboxStatus?: boolean;
  data: T;
  onAdd?: (params: T) => void;
  onDelete?: (params: T) => void;
}

const SopCheckboxItem = ({
  type = 'add',
  data,
  checkboxStatus,
  onAdd,
  onDelete,
}: SopCheckboxItemProps<any>) => {
  const [pdfPreviewUrl, setPdfPreviewUrl] = useState('');
  const [pdfPreviewVisible, setPdfPreviewVisible] = useState(false);
  const [videoPreviewUrl, setVideoPreviewUrl] = useState('');
  const [videoPreviewVisible, setVideoPreviewVisible] = useState(false);

  return (
    <div
      onClick={() => {
        type === 'add' && onAdd?.(data);
      }}
    >
      <div className="text-sm leading-none text-[#5E5E5E] flex justify-between items-center">
        <div className="overflow-hidden">
          <div className={cn('flex flex-col gap-3', { 'p-4': type === 'remove' })}>
            {type === 'remove' && (
              <span className="truncate">
                {(data as sopInfo & { parentName: string })?.parentName ||
                  (data as any)?.sopTypeName}
              </span>
            )}
            <span
              className={cn('truncate', {
                'text-primary': type === 'remove',
              })}
              onClick={() => {
                if (type === 'remove') {
                  if (data.fileName?.endsWith('.pdf')) {
                    setPdfPreviewVisible(true);
                    setPdfPreviewUrl(data.fileUrl || '');
                  } else if (data.fileName?.endsWith('.mp4')) {
                    setVideoPreviewVisible(true);
                    setVideoPreviewUrl(data.fileUrl || '');
                  } else {
                    ImageViewer.show({ image: data.fileUrl });
                  }
                }
              }}
            >
              {data.name}
              {type === 'remove' && '.sop'}
            </span>
          </div>
        </div>
        {type === 'add' ? (
          <Checkbox checked={checkboxStatus}></Checkbox>
        ) : (
          <button
            onClick={() => {
              onDelete?.(data);
            }}
            type="button"
            className="mr-4 rounded-[.125rem] text-xs leading-[.8125rem] text-grey px-1 py-[.1875rem] shrink-0 border border-solid border-[#979797]"
          >
            移除
          </button>
        )}
      </div>
      <PdfViewer
        visible={pdfPreviewVisible}
        url={pdfPreviewUrl}
        onClose={() => setPdfPreviewVisible(false)}
      />
      <VideoViewer
        visible={videoPreviewVisible}
        url={videoPreviewUrl}
        onClose={() => setVideoPreviewVisible(false)}
      />
    </div>
  );
};

interface SOPFieldProps {
  value?: any;
  onChange?: any;
  taskTypeId?: any;
}

export const SOPField = ({ value, onChange, taskTypeId }: SOPFieldProps) => {
  const [visible, setVisible] = useState(false);
  const [SelectedVisible, setSelectedVisible] = useState(false);
  const [treeTypeList, setTreeTypeList] = useState<Map<string, getSOPTypeTreeResp>>();
  const [typeLevel1, setTypeLevel1] = useState<string>('');
  const [sopChecked, setSopChecked] = useState<{ id: number; name: string; fileUrl: string }[]>([]);
  const [openCollapseKey, setOpenCollapseKey] = useState<string[]>([]);

  const {} = useRequest(
    async () => {
      if (!taskTypeId) throw null;
      const res = await getSopTypeTreeByTaskTypeId(taskTypeId?.id);
      if (res?.length === 1 && res[0].sopContentDTOS?.length === 1) {
        onChange?.(res[0].sopContentDTOS); //仅展示所关联的一个sop，且默认选中
      }
      const _map = new Map();
      res?.forEach((item: any) => {
        _map.set(item.categoryName, item.sopContentDTOS);
      });
      setTreeTypeList(_map);
      setTypeLevel1(_map.keys().next().value);
    },
    {
      refreshDeps: [taskTypeId],
    },
  );
  const { selected, isSelected, toggle, setSelected } = useSelections([]);
  useEffect(() => {
    if (visible) {
      // 更新选中态
      setSelected(value?.map((o: any) => o.id) || []);
      setSopChecked(value || []);
    }
  }, [visible]);
  useEffect(() => {
    const showItem =
      treeTypeList?.get(typeLevel1)?.map((v) => {
        return v?.id?.toString();
      }) || [];
    setOpenCollapseKey(showItem);
  }, [typeLevel1, treeTypeList]);
  // 在任务类型变化的时候，清空任务sop和选中列表，并且刷新列表
  useEffect(() => {
    onChange?.(undefined);
    setSelected([]);
    setSopChecked([]);
    setOpenCollapseKey([]);
  }, [taskTypeId?.id]);

  console.log(treeTypeList?.get(typeLevel1));

  return (
    <CustomerFormFieldWrapper
      value={(value || []).map((item: sopInfo) => item.name).join('、')}
      onShow={() => {
        if (!!taskTypeId) {
          setVisible(true);
        } else {
          Toast.show({
            content: '请先选择任务类型',
          });
        }
      }}
      placeholder="未选择"
      overflowedText={`等${value?.length}个SOP`}
    >
      <IPopupPage
        visible={visible}
        DocumentTitle="添加SOP"
        footer={
          <div className="flex">
            <div
              onClick={() => {
                setSelectedVisible(true);
              }}
              className="flex-grow pt-3 pl-4 pb-[.375rem] flex flex-col justify-between"
            >
              <div className="flex items-center text-sm gap-[.375rem]">
                已选SOP:
                <span className="text-primary text-base font-medium">{selected.length}个</span>
                <UpOutline className="text-primary text-base" />
              </div>
              <span className="text-xs text-[#979797]">点击查看已选SOP</span>
            </div>
            <Button
              onClick={() => {
                setVisible(false);
                onChange?.(sopChecked);
              }}
              color="primary"
              fill="solid"
              style={{ padding: '19px 41px', '--border-radius': '0px' }}
            >
              确认添加
            </Button>
          </div>
        }
        onClose={() => {
          setVisible(false);
          setSelectedVisible(false);
        }}
      >
        {!!treeTypeList && (
          <div className="flex h-full">
            <div className="w-[124px] bg-[#F6F6F6] h-full shrink-0 overflow-y-scroll">
              <div className="flex flex-col">
                {Array.from(treeTypeList.keys()).map((key) => (
                  <div
                    className={cn('text-dark text-sm leading-[20px] p-4', {
                      'bg-white': key === typeLevel1,
                    })}
                    onClick={() => {
                      setTypeLevel1(key);
                    }}
                    key={key}
                  >
                    {key}
                  </div>
                ))}
              </div>
            </div>
            <div className="grow overflow-y-scroll h-full">
              {(treeTypeList.get(typeLevel1) || []).length === 0 ? (
                <div className="flex flex-col items-center pt-[68px] h-full gap-4">
                  <IconFont type="icon-zanwushuju" className="text-[90px]  text-[#DCDCDC]" />
                  <span className="text-[#858585] text-xs">该分类暂无明细</span>
                </div>
              ) : (
                <div className="p-5 flex flex-col  h-full gap-5">
                  {treeTypeList.get(typeLevel1)?.map((item) => (
                    <SopCheckboxItem
                      data={item}
                      key={item.id}
                      type="add"
                      checkboxStatus={isSelected(item.id as never)}
                      onAdd={(sop) => {
                        if (sopChecked.length > 99) {
                          Toast.show({
                            content: '最多选择99个sop',
                          });
                          return;
                        }
                        if (isSelected(sop.id as never)) {
                          // 移除选中
                          setSopChecked((pre) => pre.filter((item) => item.id !== sop.id));
                        } else {
                          // 选中
                          setSopChecked((pre) => [...pre, { ...sop, parentName: `${typeLevel1}` }]);
                        }
                        toggle(sop.id as never);
                      }}
                    />
                  ))}
                </div>
              )}
            </div>
          </div>
        )}
      </IPopupPage>
      <IPopup
        visible={SelectedVisible}
        onClose={() => {
          setSelectedVisible(false);
        }}
        title="已选SOP"
        subTitle="点击sop可预览"
        closeOnMaskClick
        bodyStyle={{
          height: '75vh',
        }}
      >
        <div className="divide-y divide-[#F6F6F6] text-sm ">
          {sopChecked.length === 0 ? (
            <div className="flex flex-col items-center pt-[68px] h-full gap-4">
              <IconFont type="icon-zanwushuju" className="text-[122px]  text-[#DCDCDC]" />
              <span className="text-[#858585] text-xs">暂无已选SOP</span>
            </div>
          ) : (
            sopChecked.map((item) => {
              return (
                <SopCheckboxItem
                  data={item}
                  key={item.id}
                  type="remove"
                  onDelete={(sop) => {
                    setSopChecked((pre) => pre.filter((item) => item.id !== sop.id));
                    toggle(sop.id as never);
                  }}
                />
              );
            })
          )}
        </div>
      </IPopup>
    </CustomerFormFieldWrapper>
  );
};
