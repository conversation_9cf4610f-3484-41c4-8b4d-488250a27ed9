import { RefObject, useMemo, useRef } from 'react';
import { IconFont, Loading } from '@src/components';
import { CustomerFormItemWrapper } from '@src/components/CustomerFormItemWrapper';
import H5Bridge from '@tastien/rn-bridge/lib/h5';
import { useRequest } from 'ahooks';
import { Button, DatePicker, DatePickerRef, Form, Popover, Switch, TextArea, Toast } from 'antd-mobile';
import cn from 'classnames';
import dayjs from 'dayjs';
import { debounce, has, keys } from 'lodash';
import { FileSetField, setInit } from './components/FileSetField';
import { PersonnelField, PersonnelFieldEnum } from './components/PersonnelField';
import { SOPField } from './components/SOPField';
import { TaskTypeField } from './components/TaskTypeField';
import styles from './index.module.scss';
import { createTask } from '../api';
import { createTaskPayload } from '../api.type';
import { Uploader } from '../components/ProgressPopup';

export default function CreateIndex() {
  const [form] = Form.useForm();
  const taskDeadline = Form.useWatch('taskDeadline', form);
  const taskTypeId = Form.useWatch('taskTypeId', form);
  const DatePickerRef = useRef<any>();

  const { run, loading } = useRequest((data: createTaskPayload) => createTask(data), {
    manual: true,
    onSuccess: () => {
      Toast.show({
        icon: 'success',
        content: '创建成功',
        duration: 1000,
        afterClose: async () => {
          await H5Bridge.customPostMessage.customPostMessage({
            module: 'navigation',
            method: 'navigateTo',
            params: {
              pathname: 'Task',
              data: { queryType: 'ONLY_ROUTINES_TASK' },
            },
          });
        },
      });
    },
  });

  const handleSubmit = useMemo(
    () =>
      debounce((val) => {
        run(val);
      }, 500),
    [run],
  );

  return (
    <Loading spinning={loading} maskClickable={false}>
      <div className="flex flex-col h-full">
        <Form
          layout="horizontal"
          onFinish={(val) => {
            const { taskPrincipalIds, taskDeadline, taskExpRemind, taskTypeId, fileEntities, checkSwitch, fileSet } =
              val;
            if (!keys(setInit).every((o) => has(fileSet, o))) {
              return Toast.show({
                content: '请选择附件上传设置',
              });
            }
            handleSubmit({
              ...val,
              taskPrincipalIds: taskPrincipalIds.resUserIDList,
              taskRecipientsIds: val?.taskRecipientsIds?.resUserIDList || [],
              taskDeadline: dayjs(taskDeadline).format('YYYY-MM-DD HH:mm'),
              taskExpRemind: taskExpRemind ? 1 : 0,
              taskTypeId: taskTypeId.id,
              sopIds: (val?.sopIds || []).map((item: any) => item.id),
              finishAttachRequired: fileSet?.finishAttachRequired || 0,
              updateAttachRequired: fileSet?.updateAttachRequired || 0,
              fileEntities:
                fileEntities &&
                fileEntities.map((file: any) => {
                  return {
                    fileName: file.name,
                    objectName: file.response.key,
                  };
                }),
              checkSwitch: checkSwitch ? 1 : 0,
            });
          }}
          onFinishFailed={() => {
            Toast.show({
              content: '请完成所有必填/必选项',
            });
          }}
          form={form}
          requiredMarkStyle="none"
          hasFeedback={false}
          className={`flex-grow ${styles.IForm}`}
        >
          <Form.Item name="taskTitle" label="任务标题" rules={[{ required: true, message: '请填写任务标题', max: 60 }]}>
            <TextArea
              placeholder="请填写任务标题(必填)"
              rows={1}
              autoSize={{ minRows: 1, maxRows: 4 }}
              style={{
                '--text-align': 'right',
                '--font-size': '15px',
                '--color': '#58595B',
              }}
              maxLength={60}
            />
          </Form.Item>
          <Form.Item name="taskInfo" label="任务描述">
            <TextArea
              placeholder="请填写任务描述"
              rows={1}
              autoSize={{ minRows: 1 }}
              style={{
                '--text-align': 'right',
                '--font-size': '15px',
                '--color': '#58595B',
              }}
              maxLength={100}
            />
          </Form.Item>
          <CustomerFormItemWrapper
            name="taskPrincipalIds"
            label="任务处理人"
            rules={[{ required: true, message: '请选择任务处理人' }]}
          >
            <PersonnelField type={PersonnelFieldEnum['处理人']} id="taskPrincipalIds" withPermission={true} />
          </CustomerFormItemWrapper>
          <CustomerFormItemWrapper name="taskRecipientsIds" label="任务抄送人">
            <PersonnelField type={PersonnelFieldEnum['抄送人']} id="taskRecipientsIds" withPermission={false} />
          </CustomerFormItemWrapper>
          <Form.Item
            name="taskDeadline"
            label="任务截止时间"
            trigger="onConfirm"
            onClick={(_, datePickerRef: RefObject<DatePickerRef>) => {
              datePickerRef.current?.open();
            }}
            rules={[{ required: true }]}
            childElementPosition="right"
          >
            <DatePicker min={dayjs().toDate()} ref={DatePickerRef} precision="minute">
              {(value) =>
                value ? dayjs(value).format('YYYY/MM/DD HH:mm') : <span className="text-[#CCCCCC]">未选择(必选)</span>
              }
            </DatePicker>
          </Form.Item>
          <div className="px-4 mb-4 flex gap-3">
            {[3, 5, 7].map((o) => {
              return (
                <button
                  type="button"
                  key={o}
                  onClick={() => {
                    form.setFieldsValue({
                      taskDeadline: dayjs().add(o, 'day').set('hour', 18).set('minute', 0).toDate(),
                    });
                  }}
                  className={cn(
                    `p-[6px] text-sm leading-[14px] rounded  flex-shrink-0 ${
                      dayjs(taskDeadline).diff(dayjs().startOf('day'), 'day') === o
                        ? 'bg-primary/10 text-primary'
                        : 'bg-[#F3F3F3] text-grey'
                    } `,
                  )}
                >
                  {o}天后截止
                </button>
              );
            })}
            <button
              onClick={() => {
                DatePickerRef.current.open();
              }}
              type="button"
              className="p-[6px] text-sm leading-[14px] rounded bg-[#F3F3F3] text-grey flex-shrink-0 "
            >
              自选时间
            </button>
          </div>
          <Form.Item
            name="taskExpRemind"
            label={
              <div className="flex items-center h-full">
                <span className="shrink-0">任务到期提醒</span>
                <Popover content={'在任务到期前7/3/1天前通过飞书消息提醒'} trigger="click" placement="top" mode="dark">
                  <IconFont type="icon-changjianwentixiangguanwenti" className="ml-1" style={{ color: '#9C9C9C' }} />
                </Popover>
              </div>
            }
            childElementPosition="right"
            valuePropName="checked"
            initialValue={true}
          >
            <Switch />
          </Form.Item>
          <CustomerFormItemWrapper
            name="taskTypeId"
            label="任务类型"
            rules={[{ required: true, message: '请填写任务类型' }]}
          >
            <TaskTypeField />
          </CustomerFormItemWrapper>
          <CustomerFormItemWrapper name="sopIds" label="任务SOP">
            <SOPField taskTypeId={taskTypeId} />
          </CustomerFormItemWrapper>
          <Form.Item
            layout="horizontal"
            name="fileEntities"
            valuePropName="fileList"
            getValueFromEvent={(e) => e.fileList}
            label={<span className="text-dark text-15">任务附件</span>}
          >
            <Uploader />
          </Form.Item>
          <CustomerFormItemWrapper
            name="fileSet"
            label="附件上传设置"
            rules={[{ required: true, message: '请选择附件上传设置' }]}
          >
            <FileSetField />
          </CustomerFormItemWrapper>
          <Form.Item
            name="checkSwitch"
            label={
              <div className="flex items-center h-full">
                <span className="shrink-0">任务验收设置</span>
                <Popover content={'开启时，任务完成后需要上级进行任务验收'} trigger="click" placement="top" mode="dark">
                  <IconFont type="icon-changjianwentixiangguanwenti" className="ml-1" style={{ color: '#9C9C9C' }} />
                </Popover>
              </div>
            }
            childElementPosition="right"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
        </Form>
        <div className="p-3 bg-white">
          <Button
            onClick={() => {
              form.submit();
            }}
            color="primary"
            fill="solid"
            block
            className="h-[45px] text-base"
          >
            创建任务
          </Button>
        </div>
      </div>
    </Loading>
  );
}
