import { useEffect, useState } from 'react';
import { IPopup } from '@src/components/IPopup';
import { useRequest } from 'ahooks';
import { Button, Checkbox, Loading, Toast } from 'antd-mobile';
import { getDisinfectionCompanyList, reAssignTask } from './api';

interface Props {
  visible?: boolean;
  onClosed: () => void;
  taskId?: number;
  disinfectionCompanyId?: number;
  delReportFn?: () => void;
}

function SelectActionSheet({ visible, onClosed, taskId, disinfectionCompanyId, delReportFn }: Props) {
  const [selected, setSelected] = useState<any>(0);

  const { data, refresh, loading } = useRequest(getDisinfectionCompanyList, { manual: true });

  const { runAsync: onReAssignTask } = useRequest(reAssignTask, { manual: true });

  useEffect(() => {
    if (visible) {
      setSelected(disinfectionCompanyId);
      refresh();
    }
  }, [visible]);

  const onSubmit = async () => {
    try {
      if (selected === disinfectionCompanyId) {
        Toast.show('相同消杀公司无需重新分配');
        return;
      }

      if (!selected) {
        Toast.show('请选择消杀公司');
        return;
      }

      await onReAssignTask({
        taskId,
        newDisinfectionCompanyId: selected,
        oldDisinfectionCompanyId: disinfectionCompanyId,
      });

      delReportFn?.();

      onClosed();
    } catch (error) {
      console.log('🚀 ~ onSubmit ~ error:', error);
    }
  };

  return (
    <IPopup
      title="请选择消杀公司"
      visible={visible}
      onClose={onClosed}
      bodyStyle={{
        borderTopLeftRadius: '8px',
        borderTopRightRadius: '8px',
        overflowY: 'scroll',
        height: '60vh',
      }}
      showCloseButton
      footer={
        <div className="flex justify-between p-3">
          <Button onClick={onSubmit} className="w-full" color="primary">
            确定
          </Button>
        </div>
      }
    >
      <div className="flex flex-col gap-y-2 px-3">
        {loading ? (
          <div className="flex items-center justify-center">
            <Loading />
          </div>
        ) : (
          data?.map((item: any) => (
            <div
              onClick={() => setSelected(item.id)}
              key={item.id}
              className="flex items-center justify-between gap-y-2 py-3"
            >
              <div>{item.name}</div>
              <Checkbox checked={selected === item.id} onChange={() => setSelected(item.id)} />
            </div>
          ))
        )}
      </div>
    </IPopup>
  );
}

export default SelectActionSheet;
