import { get, post, put } from '@src/api';

export const rejectExigencyDisinfectionTask = (data: {
  taskId: number;
  reasonInfo: { reason: string; remark?: string };
}) => put('/om-api/corp/disinfection/reject', { data });

// 获取消杀公司列表
export const getDisinfectionCompanyList = () =>
  get('/om-api/common/disinfection/company/list/simple?enable=true&filterNoEmployee=true', {});

// 重新分配 /api/corp/disinfection/reAssignTask
export const reAssignTask = (data: {
  taskId?: number;
  newDisinfectionCompanyId?: number;
  oldDisinfectionCompanyId?: number;
}) =>
  post('/om-api/corp/disinfection/reAssignTask', {
    data,
  });

// 取消任务
export const cancelTask = (data: {
  taskId?: number;
  reasonInfo: {
    reason: string;
    remark?: string;
  };
}) => post('/om-api/corp/disinfection/cancel', { data });
