/* eslint-disable react-refresh/only-export-components */
import { type ReactNode, useState } from 'react';
import AuthorityEnum from '@src/common/authority';
import { formatScore } from '@src/pages/mission/components/core';
import { userStore } from '@src/store';
import { H5Bridge } from '@tastien/rn-bridge';
import { useRequest } from 'ahooks';
import { message, Space } from 'antd';
import classNames from 'classnames';
import dayjs from 'dayjs';
import { observer } from 'mobx-react';
import { rejectExigencyDisinfectionTask } from './api';
import CancelPopup from './CancelPopup';
import { EmergencyCard } from './EmergencyCard';
import { RejectPopup } from './RejectPopup';
import SelectActionSheet from './SelectActionSheet';
import { CNStatus, disinfectionTypeStatus, TKillItem } from '../api';
import { IconFont } from '../unplannedTask/components/utils';

const statusTagMap = {
  WAIT: <div className="text-base text-[#378BFF]">待开始</div>,
  RUNNING: <div className="text-base text-[#6CCD5F]">进行中</div>,
  COMPLETE: <div className="text-base text-[#3DB86D]">已完成</div>,
  NEW: <div className="text-base text-[#FF7B29]">待分配</div>,
  SUSPEND: <div className="text-base text-[#EA0000]">中止</div>,
  CLOSE: <div className="text-base text-[#EA0000]">已关闭</div>,
  REJECT: <div className="text-base text-[#EA0000]">已驳回</div>,
  CANCEL: <div className="text-base text-[#EA0000]">已取消</div>,
};

export enum disinfectionTypeValue {
  FIRST_STRUCTURE = '一道结构消杀',
  SECOND_STRUCTURE = '二道结构消杀',
  DAILY = '日常消杀',
  EXIGENCY = '紧急消杀',
}
export enum disinfectionProcessType {
  'NEW_TASK' = '创建',
  'SUBMIT_TASK' = '提交',
}
export enum statusValueMap {
  WAIT = 'WAIT',
  RUNNING = 'RUNNING',
  COMPLETE = 'COMPLETE',
  NEW = 'NEW',
  SUSPEND = 'NSUSPENDEW',
  CLOSE = 'CLOSE',
  ALL = 'ALL',
}

type TDailyDisinfectionItem = {
  label: string;
  key: any;
  format?: string;
  render?: (data: any) => ReactNode;
};

const dailyDisinfectionInfoList: TDailyDisinfectionItem[] = [
  { label: '排班时间', key: 'arriveTime', format: 'YYYY-MM-DD' },
  {
    label: '打卡时间',
    key: 'signTime',
    render: (entity: any) => {
      return entity?.disinfectionSignProcess?.createTime || entity?.disinfectionSignProcessList?.[0]?.createTime ? (
        <div>
          {dayjs(
            entity?.disinfectionSignProcess?.createTime || entity?.disinfectionSignProcessList?.[0]?.createTime,
          ).format('YYYY-MM-DD HH:mm:ss')}
          {!!entity?.taskOverdue && (
            <span className="ml-2 bg-[#F10505] text-white rounded-md p-1 text-xs">逾期到店</span>
          )}
        </div>
      ) : (
        '-'
      );
    },
  },
  { label: '消杀公司', key: 'disinfectionCompanyName' },
];

type TDailyDisinfectionInfoProps = {
  data: any;
  /** 是否显示排班时间 默认显示 */
  isShowPlanningTime?: boolean;
};

export function DailyDisinfectionInfo({ data, isShowPlanningTime = true }: TDailyDisinfectionInfoProps) {
  return (
    <Space direction="vertical">
      {dailyDisinfectionInfoList
        .filter((item) => isShowPlanningTime || item.key !== 'arriveTime')
        .map((item) => (
          <div className="flex items-center" key={item.key}>
            <span className=" text-[#858585] text-xs leading-[12px] flex items-center">
              {item.label}：
              {item?.render
                ? item.render(data)
                : item.format
                  ? dayjs(data?.[item.key]).format(item.format)
                  : data?.[item.key]}
            </span>
          </div>
        ))}
    </Space>
  );
}

type TProps = {
  hasPermission?: Boolean; // 用于判断是否是权限内还是权限外的页面用
  // info: TPatrolItem;
  info: TKillItem;
  //   roleType: ERoleType;
  delReportFn: () => void;
};

const AuthorityWrap: React.FC<{ permission: string; children: ReactNode }> = observer(({ permission, children }) => {
  const { permissionsMap } = userStore;
  if (!permissionsMap.has(permission)) {
    return <></>;
  }
  return children;
});

export function StructKillCard({ info, delReportFn }: TProps) {
  const [rejectVisible, setRejectVisible] = useState(false);

  const [cancelPopupVisible, setCancelPopupVisible] = useState(false);

  const [selectActionSheetVisible, setSelectActionSheetVisible] = useState(false);

  const { run: rejectTask } = useRequest(rejectExigencyDisinfectionTask, {
    manual: true,
    onSuccess: () => {
      setRejectVisible(false);
      message.success('驳回成功');
      delReportFn?.();
    },
  });

  return (
    <div className=" mb-2">
      {info.taskStatus === CNStatus['已完成'] ? (
        <div
          className=" bg-white pt-4 pb-2"
          onClick={() => {
            H5Bridge.navigator.push({
              pathname: 'ReportDetail',
              title: ' ',
              message: { id: info?.id },
            });
          }}
        >
          <div className=" px-4">
            <div>
              <div className=" flex justify-between items-center">
                <div className=" text-xs">
                  <div className="bg-[#378BFF] text-[#FFFFFF]  px-2 rounded py-1">
                    {disinfectionTypeValue[info.disinfectionType]}
                  </div>
                </div>
                {statusTagMap[info.taskStatus]}
              </div>
              <div className=" flex justify-between items-center">
                <div className=" text-base font-semibold">
                  {info.shopId} {info.shopName}
                </div>
              </div>
              <div className=" text-sm text-[#858585]">
                {/* {dayjs(info.reportDate).format('MM/DD')}{' '}
                  {subTypeText[info.subType]} */}
              </div>
              {/* <div className=" text-sm text-[#858585]">{info.planName}</div> */}
            </div>
            <div className=" flex justify-between items-center">
              <div className=" text-[#858585]">检查得分</div>
              <div className=" text-[#141414] font-medium text-2xl">{formatScore(info.reportScore)}</div>
            </div>
            <div className=" flex-col justify-between items-center bg-[#fafafa] px-2 py-2">
              {info.processList?.length > 0 &&
                info.processList.map(
                  (processItem: { operatorName: string; operateAction: string; createTime: string }) => {
                    return (
                      <div className="pt-2">
                        {processItem.operatorName && (
                          <div className="flex items-center">
                            <span className=" text-[#858585] text-xs leading-[12px]">
                              {processItem.operatorName} {dayjs(processItem.createTime).format('MM-DD HH:mm:ss')}{' '}
                              {
                                disinfectionProcessType[
                                  processItem.operateAction as keyof typeof disinfectionProcessType
                                ]
                              }
                            </span>
                          </div>
                        )}
                      </div>
                    );
                  },
                )}
              {(info.disinfectionType === disinfectionTypeStatus.DAILY ||
                info.disinfectionType === disinfectionTypeStatus.EXIGENCY) && (
                <div className="pt-1 mt-1">
                  <DailyDisinfectionInfo data={info} isShowPlanningTime={false} />
                </div>
              )}
            </div>
            <div className=" flex items-center justify-between my-2">
              <div className=" flex flex-col justify-center items-center w-full">
                <div className=" text-[#378BFF] text-base">{info.reportCheckCount}个</div>
                <div className=" text-[#858585]">检查项</div>
              </div>
              <div className=" flex flex-col justify-center items-center  w-full">
                <div className=" text-[#378BFF] text-base">{info.reportIssueCount}个</div>
                <div className=" text-[#858585]">问题项</div>
              </div>
              {/* {showNoReformCount() && (
                <div className=" flex flex-col justify-center items-center  w-full">
                  <div
                    className={classNames(' text-base', {
                      'text-[#EA0000]': !!info.reportNoReformCount,
                      'text-[#378BFF]': info.reportNoReformCount === 0,
                    })}
                  >
                    {info.reportNoReformCount}个
                  </div>
                  <div className=" text-[#858585]">未整改项</div>
                </div>
                )} */}
              <div className=" flex flex-col justify-center items-center  w-full">
                <div
                  className={classNames(' text-base', {
                    'text-[#EA0000]': !!info?.reportNoReformCount,
                    'text-[#378BFF]': info?.reportNoReformCount === 0,
                  })}
                >
                  {info?.reportNoReformCount}个
                </div>
                <div className=" text-[#858585]">未整改项</div>
              </div>
            </div>
            <div className=" w-full border border-t border-[#f7f7f7]" />
            <div className=" text-13 leading-[21px] text-85 flex justify-between items-center py-1">
              <div className=" flex mt-1 flex-wrap gap-1">
                {info.worksheetList?.map((item: any) => (
                  <div className="rounded-sm border border-[#DCDCDC] px-2 mr-2" key={item.id}>
                    {item.worksheetName}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="bg-white pb-2">
          <div
            className=" bg-white pt-4 pb-1"
            onClick={() => {
              H5Bridge.navigator.push({
                pathname: 'DisinfectionTaskDetail',
                title: ' ',
                message: { id: info.id },
              });
            }}
          >
            <div className=" px-4">
              <div>
                <div className=" flex justify-between items-center">
                  <div className=" text-xs">
                    <div className="bg-[#378BFF] text-[#FFFFFF]  px-2 rounded py-1">
                      {disinfectionTypeValue[info.disinfectionType]}
                    </div>
                  </div>
                  {statusTagMap[info.taskStatus]}
                </div>
                <div className=" flex justify-between items-center">
                  <div className=" text-base font-semibold">
                    {info.shopId} {info.shopName}
                  </div>
                </div>
              </div>

              <div className=" flex justify-between items-center bg-[#fafafa] px-2 py-2 mt-2">
                {info.disinfectionType === disinfectionTypeStatus.DAILY ? (
                  <DailyDisinfectionInfo data={info} />
                ) : info.disinfectionType === disinfectionTypeStatus.EXIGENCY ? (
                  <EmergencyCard data={info} />
                ) : (
                  <div>
                    <div className="flex items-center">
                      <span className=" text-[#858585] text-xs leading-[12px]">
                        项目经理: {info.managerUserName}{' '}
                        <span
                          className="text-primary text-sm ml-2"
                          onClick={() => {
                            window.location.href = `tel:${info?.managerUserPhone}`;
                          }}
                        >
                          <IconFont type="icon-rongqi" />
                        </span>
                      </span>
                    </div>
                    <div className="flex items-center mt-2">
                      <span className=" text-[#858585] text-xs leading-[12px]">
                        消杀公司: {info.disinfectionCompanyName ? info.disinfectionCompanyName : '暂未分配'}{' '}
                      </span>
                    </div>
                    <div className="flex items-center mt-2">
                      <span className=" text-[#858585] text-xs leading-[12px]">
                        到店时间：
                        {dayjs(info.arriveTime).format('YYYY-MM-DD HH:mm:ss')}{' '}
                      </span>
                    </div>
                  </div>
                )}
              </div>
            </div>
            <div className=" w-full  px-4 py-2 mt-2">
              <div className=" text-[#378BFF] text-sm">
                创建时间：{dayjs(info.createTime).format('YYYY-MM-DD HH:mm:ss')}
              </div>
              {info.taskStatus === CNStatus['待分配'] && (
                <div className="flex justify-end mt-2 gap-2">
                  <AuthorityWrap permission={AuthorityEnum.紧急消杀_驳回}>
                    <div
                      className="border border-[#C4C4C4] text-[#58595B] text-xs px-3.5 rounded py-1.5"
                      onClick={(e) => {
                        e.stopPropagation();
                        setRejectVisible(true);
                      }}
                    >
                      驳回
                    </div>
                  </AuthorityWrap>
                  <div
                    className="border border-[#C4C4C4] text-[#58595B] text-xs px-3.5 rounded py-1.5"
                    onClick={(e) => {
                      e.stopPropagation();
                      H5Bridge.navigator.push({
                        pathname: 'OnSiteAllotDisinfectionFirm',
                        title: ' ',
                        message: { id: info.id, shopId: info?.shopId },
                      });
                    }}
                  >
                    去分配
                  </div>
                </div>
              )}
            </div>
          </div>
          {info.taskStatus === CNStatus['待开始'] && (
            <div className="flex flex-row justify-end items-end gap-x-3 mr-3">
              <div
                onClick={() => setSelectActionSheetVisible(true)}
                className="border border-[#C4C4C4] text-[#58595B] text-xs px-3.5 rounded py-1.5"
              >
                重新分配
              </div>
              <div
                onClick={() => setCancelPopupVisible(true)}
                className="border border-[#C4C4C4] text-[#58595B] text-xs px-3.5 rounded py-1.5"
              >
                取消任务
              </div>
            </div>
          )}
        </div>
      )}
      <RejectPopup
        visible={rejectVisible}
        onClose={() => setRejectVisible(false)}
        onSuccess={(reasonInfo) => {
          rejectTask({
            taskId: info.id,
            reasonInfo,
          });
        }}
      />

      <SelectActionSheet
        visible={selectActionSheetVisible}
        onClosed={() => setSelectActionSheetVisible(false)}
        taskId={info?.id}
        disinfectionCompanyId={info.disinfectionCompanyId}
        delReportFn={delReportFn}
      />

      <CancelPopup
        visible={cancelPopupVisible}
        onClose={() => setCancelPopupVisible(false)}
        taskId={info?.id}
        delReportFn={delReportFn}
      />
    </div>
  );
}
