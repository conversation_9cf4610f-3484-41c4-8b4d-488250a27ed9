import { useEffect } from 'react';
import { IPopup } from '@src/components/IPopup';
import { useRequest } from 'ahooks';
import { Button, Form, Radio, Space, TextArea } from 'antd-mobile';
import { cancelTask } from './api';

interface Props {
  visible: boolean;
  onClose: () => void;
  taskId: number;
  delReportFn?: () => void;
}

const CancelPopup = ({ visible, onClose, taskId, delReportFn }: Props) => {
  const { runAsync: cancelTaskAsync } = useRequest(cancelTask, { manual: true });

  const [form] = Form.useForm();

  useEffect(() => {
    form.resetFields();
  }, [visible]);

  const onSubmit = async () => {
    try {
      const values = await form.validateFields();

      await cancelTaskAsync({
        taskId,
        reasonInfo: {
          reason: values.reason,
          remark: values.remark,
        },
      });

      delReportFn?.();

      form.resetFields();

      onClose();
    } catch (error) {
      console.log(error);
    }
  };

  return (
    <IPopup
      title="紧急消杀任务取消"
      visible={visible}
      onClose={onClose}
      footer={
        <div className="flex justify-between p-3">
          <Button onClick={onSubmit} type="submit" className="w-full" color="primary">
            确定
          </Button>
        </div>
      }
    >
      <Form form={form}>
        <Form.Item name="reason" label="取消原因" required rules={[{ required: true, message: '请选择取消原因' }]}>
          <Radio.Group>
            <Space className="mt-2" direction="vertical">
              <Radio value="误操作">误操作</Radio>
              <Radio value="消杀已处理">消杀已处理</Radio>
              <Radio value="分配错误">分配错误</Radio>
            </Space>
          </Radio.Group>
        </Form.Item>
        <Form.Item name="remark" label="备注">
          <TextArea className="h-[120px] mt-3" placeholder="请输入备注" />
        </Form.Item>
      </Form>
    </IPopup>
  );
};

export default CancelPopup;
