import { IPopup } from '@src/components/IPopup';
import type { SystemConfigItem } from '@src/hooks/useShopConfig/api';
import { useQueryClient } from '@tanstack/react-query';
import { Button, Form, Radio, Space, TextArea } from 'antd-mobile';

interface RejectPopupProps {
  visible: boolean;
  onClose: () => void;
  onSuccess: (values: any) => void;
}

export const RejectPopup = ({ visible, onClose, onSuccess }: RejectPopupProps) => {
  const queryClient = useQueryClient();
  const [form] = Form.useForm();
  const data = queryClient.getQueryData(['SystemConfig']) as SystemConfigItem[];
  const rejectOptions = JSON.parse(
    data?.find((item) => item.key === 'EXIGENCY_DISINFECTION_TASK_REJECT_REASONS')?.value || '[]',
  );

  return (
    <IPopup
      title="紧急消杀请求驳回"
      visible={visible}
      onClose={onClose}
      bodyStyle={{ height: '75vh' }}
      footer={
        <div className="bg-white flex gap-x-3 p-3">
          <Button
            color="primary"
            fill="outline"
            block
            className="h-[45px] text-base"
            onClick={() => {
              onClose();
            }}
          >
            取消
          </Button>
          <Button
            color="primary"
            fill="solid"
            block
            className="h-[45px] text-base"
            onClick={() => {
              form.submit();
            }}
          >
            确认
          </Button>
        </div>
      }
      position="bottom"
    >
      <Form
        form={form}
        onFinish={(values) => {
          onSuccess?.(values);
        }}
      >
        <Form.Item label="驳回原因" name="reason" rules={[{ required: true, message: '请选择驳回原因' }]}>
          <Radio.Group>
            <Space direction="vertical">
              {rejectOptions.map((item: string) => (
                <Radio key={item} value={item}>
                  {item}
                </Radio>
              ))}
            </Space>
          </Radio.Group>
        </Form.Item>
        <Form.Item label="备注" name="remark">
          <TextArea placeholder="请输入备注" maxLength={50} />
        </Form.Item>
      </Form>
    </IPopup>
  );
};
