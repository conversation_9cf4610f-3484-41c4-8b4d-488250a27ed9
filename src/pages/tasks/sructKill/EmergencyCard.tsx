import { Space } from 'antd';
import dayjs from 'dayjs';

interface EmergencyCardProps {
  data: any;
}

const renderSignTime = (entity: any) => {
  return entity?.disinfectionSignProcess?.createTime || entity?.disinfectionSignProcessList?.[0]?.createTime ? (
    <div>
      {dayjs(
        entity?.disinfectionSignProcess?.createTime || entity?.disinfectionSignProcessList?.[0]?.createTime,
      ).format('YYYY-MM-DD HH:mm:ss')}
      {!!entity?.taskOverdue && <span className="ml-2 bg-[#F10505] text-white rounded-md p-1 text-xs">逾期到店</span>}
    </div>
  ) : (
    '-'
  );
};

export const EmergencyCard = ({ data }: EmergencyCardProps) => {
  const rejectInfo = JSON.parse(
    data?.processList?.find((item: any) => item.operateAction === 'REJECT_TASK')?.remark || '{}',
  );
  const rejectTime = data?.processList?.find((item: any) => item.operateAction === 'REJECT_TASK')?.createTime;
  return (
    <Space direction="vertical">
      <div className="flex items-center">
        <span className=" text-[#858585] text-xs leading-[12px] flex items-center">创建人：{data?.createUserName}</span>
      </div>
      <div className="flex items-center">
        <span className=" text-[#858585] text-xs leading-[12px] flex items-center">
          打卡时间：{renderSignTime(data)}
        </span>
      </div>
      <div className="flex items-center">
        <span className=" text-[#858585] text-xs leading-[12px] flex items-center">
          消杀公司：{data?.disinfectionCompanyName || '-'}
        </span>
      </div>
      {rejectInfo?.reason && <div className="text-xs text-[#858585]">驳回原因：{rejectInfo?.reason}</div>}
      {rejectInfo?.remark && <div className="text-xs text-[#858585]">驳回备注：{rejectInfo?.remark}</div>}
      {rejectTime && (
        <div className="text-xs text-[#858585]">驳回时间：{dayjs(rejectTime).format('YYYY-MM-DD HH:mm:ss')}</div>
      )}
    </Space>
  );
};
