import { Loading } from '@src/components';
import useQuerySearchParams from '@src/hooks/useQuerySearchParams';
import { getPatrolReportLogs, queryPatrolReportDetail } from '@src/pages/patrol/api';
import { StepType } from '@src/pages/reportDetail/patrol/basicInfo/operateLogPopup';
import { getCycleJobList, suspendCycleJob } from '@src/pages/selfCheckComment/api';
import { useRequest } from 'ahooks';
import { Toast } from 'antd-mobile';
import dayjs from 'dayjs';
import { ItemCard } from './ItemCard';

export default function CircularRectificationIndex() {
  const [{ taskId }] = useQuerySearchParams();

  const { data: reportInfo, loading: reportInfoLoading } = useRequest(async () => {
    return queryPatrolReportDetail({ taskId: +taskId });
  });

  const { data: logData, loading: logDataLoading } = useRequest(async () => {
    return getPatrolReportLogs(taskId);
  });

  const {
    data: cycleJobList,
    loading: cycleJobListLoading,
    refresh: refreshCycleJobList,
  } = useRequest(async () => {
    return getCycleJobList(taskId);
  });

  const { run: suspendCycleJobRun, loading: suspendCycleJobLoading } = useRequest(
    async (reason: string, id: number) => {
      return suspendCycleJob({ id, reason });
    },
    {
      manual: true,
      onSuccess: () => {
        Toast.show({
          content: '中止成功',
        });
        refreshCycleJobList();
      },
    },
  );
  return (
    <Loading spinning={cycleJobListLoading || suspendCycleJobLoading || reportInfoLoading || logDataLoading}>
      <div className="h-full flex flex-col bg-[#F6F6F6] gap-2">
        <div className="bg-white flex flex-col p-4 gap-2">
          <h3>
            {reportInfo?.shopId} {reportInfo?.shopName}
          </h3>
          <div className="bg-[#EEEEEE] px-4 py-2 rounded flex flex-col gap-2 text-[#AEAEAE] text-[13px] leading-[13px]">
            <span>{reportInfo?.taskName}</span>
            {logData?.map((item: any) => {
              return (
                <span>
                  {item?.oprUserName} {item?.createTime ? dayjs(item?.createTime).format('MM-DD HH:mm') : '-'}{' '}
                  {StepType['PATROL'][item?.operationType as keyof (typeof StepType)['PATROL']]}
                </span>
              );
            })}
          </div>
        </div>
        <div className="flex-1 overflow-y-auto flex flex-col gap-2">
          {cycleJobList?.map((c: any) => <ItemCard key={c?.id} data={c} onSuspend={suspendCycleJobRun} />)}
        </div>
      </div>
    </Loading>
  );
}
