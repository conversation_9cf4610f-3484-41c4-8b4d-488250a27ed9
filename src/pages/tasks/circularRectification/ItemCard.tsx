import { useState } from 'react';
import { FilePrev } from '@src/pages/patrol/checkList/FilePrev';
import dayjs from 'dayjs';
import { CheckItemAttributeTag, CycleJobRectifyCycleTypeCN, CycleJobStatus, CycleJobStatusCN } from './enum';
import TextareaPopup from '../components/TextareaPopup';

interface ItemCardProps {
  data: any;
  onSuspend: (reason: string, id: number) => void;
}

export const ItemCard = ({ data, onSuspend }: ItemCardProps) => {
  const [rejectPopupVisible, setRejectPopupVisible] = useState(false);

  const unqualifiedReasons = [
    ...(data?.otherReason ? [data.otherReason] : []),
    ...(data?.unqualifiedReasons?.map((item: any) => item.reason) || []),
  ];

  return (
    <>
      <div className="bg-white p-4 flex flex-col gap-2">
        <div className="flex gap-1 flex-wrap">
          {data?.accentedTermTags?.map((item: any) => (
            <div key={item}>{CheckItemAttributeTag[item as keyof typeof CheckItemAttributeTag]}</div>
          ))}
        </div>
        <div className="flex justify-between">
          <span>{data?.name}</span>
          <span>{CycleJobStatusCN[data?.jobStatus as keyof typeof CycleJobStatusCN]}</span>
        </div>
        {(data?.content || unqualifiedReasons?.length > 0 || !!data?.images?.length) && (
          <div className="bg-[#EEEEEE] py-1 px-2 flex flex-col gap-2">
            {data?.content && <span>{data?.content}</span>}
            {unqualifiedReasons?.length > 0 && (
              <div className="flex flex-wrap gap-x-2">
                {unqualifiedReasons?.map((item: any) => {
                  return <a className="text-blue-500">#{item}</a>;
                })}
              </div>
            )}
            {!!data?.images?.length && <FilePrev file={data?.images} />}
          </div>
        )}
        <div className="flex justify-between">
          <div className="flex flex-col gap-2">
            <span>
              循环频次：
              <span className="text-[#AAAAAA]">
                {CycleJobRectifyCycleTypeCN[data?.rectifyCycleType as keyof typeof CycleJobRectifyCycleTypeCN]}
              </span>
            </span>
            {data?.cycleEndTime && (
              <span>
                截止时间：<span className="text-[#AAAAAA]">{dayjs(data?.cycleEndTime).format('YYYY年MM月DD日')}</span>
              </span>
            )}
            {data?.jobStatus === CycleJobStatus.已中止 && (
              <>
                {data?.suspendReason && (
                  <span className="break-all">
                    中止原因：
                    <span className="text-[#AAAAAA]">{data?.suspendReason}</span>
                  </span>
                )}
                {data?.suspendTime && (
                  <span>
                    中止时间：
                    <span className="text-[#AAAAAA]">{dayjs(data?.suspendTime).format('YYYY年MM月DD日 HH:mm:ss')}</span>
                  </span>
                )}
                {data?.operationUser?.nickname && (
                  <span>
                    操作人 ：<span className="text-[#AAAAAA]">{data?.operationUser?.nickname}</span>
                  </span>
                )}
              </>
            )}
          </div>
          {data?.jobStatus === CycleJobStatus.进行中 && (
            <button
              className="w-[72px] h-[30px] text-[#747578] text-xs leading-3 border border-[#eeeeee] rounded"
              onClick={(e) => {
                e.stopPropagation();
                setRejectPopupVisible(true);
              }}
            >
              中止循环
            </button>
          )}
        </div>
      </div>
      <TextareaPopup
        visible={rejectPopupVisible}
        title="中止循环整改理由"
        okText="确认提交"
        onClose={() => setRejectPopupVisible(false)}
        onOk={(reason: string) => {
          onSuspend?.(reason, data?.id);
          setRejectPopupVisible(false);
        }}
        maxLength={150}
        placeholder="请填写中止循环整改的理由，最多150个字"
      />
    </>
  );
};
