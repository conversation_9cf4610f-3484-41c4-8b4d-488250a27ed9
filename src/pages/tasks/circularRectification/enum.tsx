import type { ReactNode } from 'react';
import { Tag } from 'antd';

export enum CheckItemAttribute {
  S项 = 'RED_LINE',
  必检项 = 'NECESSARY',
  M项 = 'YELLOW',
  关键项 = 'KEY',
  罚款项 = 'PENALTY',
  阳性指标项 = 'POSITIVE',
}

export const CheckItemAttributeTag: Record<CheckItemAttribute, ReactNode> = {
  [CheckItemAttribute.S项]: <Tag color="#e23c39">S项</Tag>,
  [CheckItemAttribute.必检项]: <Tag color="green">必检项</Tag>,
  [CheckItemAttribute.M项]: <Tag color="warning">M项</Tag>,
  [CheckItemAttribute.关键项]: <Tag color="blue">关键项</Tag>,
  [CheckItemAttribute.罚款项]: <Tag color="volcano">罚款项</Tag>,
  [CheckItemAttribute.阳性指标项]: <Tag color="pink">阳性指标项</Tag>,
};
export enum CycleJobStatus {
  进行中 = 'PROCESSING',
  已中止 = 'SUSPENDED',
  已结束 = 'FINISHED',
  已删除 = 'DELETED',
}

export const CycleJobStatusCN: Record<CycleJobStatus, string> = {
  [CycleJobStatus.进行中]: '进行中',
  [CycleJobStatus.已中止]: '已中止',
  [CycleJobStatus.已结束]: '已结束',
  [CycleJobStatus.已删除]: '已删除',
};

export enum CycleJobRectifyCycleType {
  每日一次 = 'EVERY_DAY',
  每周一次 = 'EVERY_WEEK',
}

export const CycleJobRectifyCycleTypeCN: Record<CycleJobRectifyCycleType, string> = {
  [CycleJobRectifyCycleType.每日一次]: '每日一次',
  [CycleJobRectifyCycleType.每周一次]: '每周一次',
};
