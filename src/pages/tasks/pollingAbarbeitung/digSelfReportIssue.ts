import { roleTypeIsManage } from '@src/utils/tokenUtils';
import dayjs from 'dayjs';
enum Status {
  all = -1,
  process = 1,
  unReviewed = 2,
  overdue = 3,
  completed = 4,
}
type SelfReportIssuePageResult = {
  copyRecipientNames: string;
  createAt: string;
  expirationAt: string;
  createUserName: string;
  createUserAvatar: string;
  id: number;
  issue_images: string[];
  rectificationPeriod: string;
  shopName: string;
  status: 'CREATED' | 'REFORM_SUBMITTED' | 'SUPERVISOR_REJECTED' | 'COMPLETED' | 'PAST_DUE';
  suggestion: string;
  workSheetItemName: string;
  workSheetItemLabels: ('RED_LINE' | 'NECESSARY' | 'YELLOW' | 'KEY' | 'PENALTY')[];
  workSheetCategoryName: string;
  worksheetName: string;
};

enum PROCESS_TYPE_ENUM {
  REFORM_SUBMITTED,
  SUPERVISOR_REJECTED,
  COMPLETED,
}
export interface SelfReportIssueAppletProcesses {
  selfReportIssueProcesses: {
    createByName: string;
    createUserAvatar: string;
    createdAt: string;
    feedbackResources: {
      id: string;
      type: 'IMG' | 'VEDIO' | 'PDF';
      url: string;
    }[];
    feedbackText: string;
    id: number;
    processType: PROCESS_TYPE_ENUM;
    selfReportIssueId: 0;
  }[];
}

interface SelfReportIssuePage {
  pages: number;
  result: SelfReportIssuePageResult[];
  total: number;
}
const digReportIssueDetail = (value: SelfReportIssueAppletProcesses['selfReportIssueProcesses']) => {
  return value.map((item) => ({
    submitedName: item.createByName,
    submitedAvatar: item.createUserAvatar,
    type: PROCESS_TYPE_ENUM[item.processType],
    submitTime: item.createdAt,
    issueImages: item.feedbackResources.filter(Boolean),
    issueRemark: item.feedbackText,
  }));
};

// 为了适配现有组件，在这里处理数据
export const digSelfReportIssue = (
  data: SelfReportIssuePage['result'] | SelfReportIssuePageResult,
  config?: { hasChildren: boolean },
) => {
  const filter = (item: any) => {
    const status = (
      {
        CREATED: Status.process,
        SUPERVISOR_REJECTED: Status.process,
        REFORM_SUBMITTED: Status.unReviewed,
        PAST_DUE: Status.overdue,
        COMPLETED: Status.completed,
      } as any
    )[item.status];
    let buttonEnums: any[] = [];
    let belongsMe = false;

    if (!roleTypeIsManage() && status === Status.unReviewed) {
      buttonEnums = ['AUDIT_PASS', 'REJECT_PROBLEM'];
      belongsMe = true;
    }
    if (roleTypeIsManage() && status === Status.process) {
      buttonEnums = ['FOLLOW_PROBLEM'];
      belongsMe = true;
    }

    const workSheetItemLabels = {
      redLine: item.workSheetItemLabels?.includes('RED_LINE') ? 1 : 0,
      fineFlag: item.workSheetItemLabels?.includes('PENALTY') ? 1 : 0,
      necessaryFlag: item.workSheetItemLabels?.includes('NECESSARY') ? 1 : 0,
      keyItem: item.workSheetItemLabels?.includes('KEY') ? 1 : 0,
      yellowItem: item.workSheetItemLabels?.includes('YELLOW') ? 1 : 0,
    };

    const sumbimitedTime = item.createAt || item.createTime;
    const elapsedMinute =
      item.completedAt && sumbimitedTime ? dayjs(item.completedAt).diff(dayjs(sumbimitedTime), 'minute') : 0;
    const getElapsedTime = (minutes: number) => {
      const day = Math.floor(minutes / 1440);
      const dayStr = day ? `${day} 天 ` : '';
      const hour = Math.floor((minutes - day * 1440) / 60);
      const hourStr = hour ? `${hour} 小时 ` : '';
      const minute = minutes - day * 1440 - hour * 60;
      const minuteStr = minute ? `${minute} 分钟 ` : '';
      return dayStr + hourStr + minuteStr;
    };
    const elapsedTime = getElapsedTime(elapsedMinute);

    const values = {
      ...item,
      submitUserId: 0,
      issuesCount: 0,
      noReformCount: 0,
      reportId: item.id,
      itemRemark: item.suggestion,
      itemName: item.workSheetItemName ? [item.workSheetItemName] : [],
      itemImages: item.issueResources || item.itemImages || [],
      worksheetNames: item.workSheetItemName ? [item.workSheetItemName] : [],
      submitedUserName: item.createUserName,
      submitedUserAvatar: item.createUserAvatar,
      deadline: item.expirationAt,
      sumbimitedTime,
      //   shopId: 0,
      reformStatus: status,
      // status,
      statusEnum: item.status,
      copyUsers: item.copyRecipientNames ? item.copyRecipientNames.map((e: string) => ({ nickname: e })) : [],
      checkListName: item.worksheetName ? item.worksheetName : item.workSheetItemName,
      checkListTypeName: item.workSheetCategoryName || item.worksheetCategoryName,
      buttonEnums,
      belongsMe,
      elapsedTime,
      labels: item.workSheetItemLabels || item.labels,
      ...workSheetItemLabels,
      taskPlanName: item.taskPlanName,
      selectReasons: item.nonconformityReasons,
    };

    if (config?.hasChildren) {
      values['children'] = digReportIssueDetail(item.selfReportIssueProcesses);
    }

    return values;
  };
  return Array.isArray(data) ? data.map(filter) : filter(data);
};

// 只用作处理报告详情-整改详情数据处理
export const pollingAbarbeitungTab = (
  data: SelfReportIssuePage['result'] | SelfReportIssuePageResult,
  config?: { hasChildren: boolean },
) => {
  const filter = (item: any) => {
    const status = (
      {
        CREATED: Status.process,
        SUPERVISOR_REJECTED: Status.process,
        REFORM_SUBMITTED: Status.unReviewed,
        PAST_DUE: Status.overdue,
        COMPLETED: Status.completed,
      } as any
    )[item.status];
    let buttonEnums: any[] = [];
    let belongsMe = false;

    if (!roleTypeIsManage() && status === Status.unReviewed) {
      buttonEnums = ['AUDIT_PASS', 'REJECT_PROBLEM'];
      belongsMe = true;
    }
    if (roleTypeIsManage() && status === Status.process) {
      buttonEnums = ['FOLLOW_PROBLEM'];
      belongsMe = true;
    }

    const workSheetItemLabels = {
      redLine: item.workSheetItemLabels?.includes('RED_LINE') ? 1 : 0,
      fineFlag: item.workSheetItemLabels?.includes('PENALTY') ? 1 : 0,
      necessaryFlag: item.workSheetItemLabels?.includes('NECESSARY') ? 1 : 0,
      keyItem: item.workSheetItemLabels?.includes('KEY') ? 1 : 0,
      yellowItem: item.workSheetItemLabels?.includes('YELLOW') ? 1 : 0,
    };

    const sumbimitedTime = item.createAt || item.createTime;
    const elapsedMinute =
      item.completedAt && sumbimitedTime ? dayjs(item.completedAt).diff(dayjs(sumbimitedTime), 'minute') : 0;
    const getElapsedTime = (minutes: number) => {
      const day = Math.floor(minutes / 1440);
      const dayStr = day ? `${day} 天 ` : '';
      const hour = Math.floor((minutes - day * 1440) / 60);
      const hourStr = hour ? `${hour} 小时 ` : '';
      const minute = minutes - day * 1440 - hour * 60;
      const second = minutes - day * 1440 - hour * 60 - minutes * 60;
      const minuteStr = minute ? `${minute} 分钟 ` : `${second}秒`;
      // const minuteStr = minute ? minute + ' 分钟 ' : '1分钟';

      return dayStr + hourStr + minuteStr;
    };
    const elapsedTime = getElapsedTime(elapsedMinute);

    const values = {
      ...item,
      submitUserId: 0,
      issuesCount: 0,
      noReformCount: 0,
      reportId: item.id,
      itemRemark: item.suggestion,
      itemName: item.workSheetItemName ? [item.workSheetItemName] : [],
      itemImages: item.issueResources || item.itemImages || [],
      worksheetNames: item.workSheetItemName ? [item.workSheetItemName] : [],
      submitedUserName: item.createUserName,
      submitedUserAvatar: item.createUserAvatar,
      deadline: item.expirationAt,
      sumbimitedTime,
      //   shopId: 0,
      selfStatus: status,
      status,
      statusEnum: item.status,
      copyUsers: item.copyRecipientNames ? item.copyRecipientNames.map((e: string) => ({ nickname: e })) : [],
      // checkListName: item.worksheetName ? item.worksheetName : item.workSheetItemName,
      // checkListTypeName: item.workSheetCategoryName || item.worksheetCategoryName,
      buttonEnums,
      belongsMe,
      elapsedTime,
      labels: item.workSheetItemLabels || item.labels,
      ...workSheetItemLabels,
      taskPlanName: item.taskPlanName,
      selectReasons: item.nonconformityReasons,
    };

    if (config?.hasChildren) {
      values['children'] = digReportIssueDetail(item.selfReportIssueProcesses);
    }

    return values;
  };
  return Array.isArray(data) ? data.map(filter) : filter(data);
};
