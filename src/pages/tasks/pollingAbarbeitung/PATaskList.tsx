import { useMemo, useState } from 'react';
import robotPng from '@src/assets/images/robot.png';
import { DateFilter, IconFont } from '@src/components';
// import { DateFilter } from '../components/DateFilter';
import { ShopTree } from '@src/components/ShopTree';
import useQuerySearchParams from '@src/hooks/useQuerySearchParams';
import { ERoleType } from '@src/pages/mission';
import { DZSelect } from '@src/pages/mission/components/Select';
import { UnderPatrolFilter } from '@src/pages/mission/components/UnderPatrolFilter';
import { roleTypeIsManage } from '@src/utils/tokenUtils';
import { useRequest } from 'ahooks';
import { Checkbox, SpinLoading } from 'antd-mobile';
import classNames from 'classnames';
import dayjs from 'dayjs';
import { getPAListByAI, getPAListByNormalPolling, getPAListBySelfPolling } from './api';
import { getStatusName } from './dataMap';
import { unifyData } from './dataRelay';
import { digSelfReportIssue } from './digSelfReportIssue';
import { InfiniteScrollBuilder } from '../components/InfiniteScrollBuilder';
import IssueCardShop from '../components/IssueList/IssueCardShop';

enum taskType {
  AIPolling = 'AIPolling',
  SelfPolling = 'SelfPolling',
  NormalPolling = 'NormalPolling',
}

enum taskVideoNormalType {
  Normal = 'NORMAL_AND_CROSS',
  Video = 'VIDEO',
  FOOD_SAFETY_NORMAL = 'FOOD_SAFETY_NORMAL',
  FOOD_SAFETY_VIDEO = 'FOOD_SAFETY_VIDEO',
}

const requestMap: any = {
  [taskType.AIPolling]: getPAListByAI,
  [taskType.SelfPolling]: getPAListBySelfPolling,
  [taskType.NormalPolling]: getPAListByNormalPolling,
};

export default function PATaskList() {
  const [searchParams]: any = useQuerySearchParams();
  const { pollingType, NormalType, shopId } = searchParams;
  // const shopIds = 'TS224'
  const [searchParamsByAI, setSearchParamsByAI] = useState<any>({
    copyRecipient: false,
    groupId: null,
    shopIds: shopId ? [shopId] : undefined,
    pageNo: 1,
    pageSize: 5,
    start: dayjs().subtract(1, 'months').toISOString(),
    end: dayjs().toISOString(),
    word: '',
    status: roleTypeIsManage() ? 'UNAUDITED' : 'UNRECTIFIED',
  });
  const [searchParamsByNormal, setSearchParamsByNormal] = useState<any>({
    groupId: null,
    onlyMe: true,
    onlyMeSubmitted: !searchParams.noOnlyMeSubmitted,
    pageNo: 1,
    pageSize: 10,
    // shopIds: undefined,
    shopIds: shopId ? [shopId] : undefined,
    startTime: dayjs().subtract(1, 'months').toISOString(),
    endTime: dayjs().toISOString(),
    patrolTaskSubTypeByApp: taskVideoNormalType[NormalType],
    status: roleTypeIsManage() ? 2 : 1,
  });
  const [searchParamsBySelf, setSearchParamsBySelf] = useState<any>({
    groupId: null,
    onlyLookCopyMe: false,
    onlyMeSubmitted: !searchParams.noOnlyMeSubmitted,
    shopIds: shopId ? [shopId] : undefined,
    pageNo: 1,
    pageSize: 5,
    beginDate: dayjs().subtract(1, 'months').toISOString(),
    endDate: dayjs().toISOString(),
    statues: roleTypeIsManage() ? ['REFORM_SUBMITTED'] : ['CREATED', 'SUPERVISOR_REJECTED'], // 这是实际传参
    status: roleTypeIsManage() ? 'REFORM_SUBMITTED' : 'CREATED', // 垃圾传参（只处理前端状态筛选）
  });
  const { run: runByNormal, loading: loadingNormal } = useRequest(
    async () => {
      const res: any = await getPAListByNormalPolling(searchParamsByNormal); // 巡检整改列表
      console.log('NormalPollingDAta', res);

      return res || [];
    },
    {
      manual: true,
    },
  );
  const { run: runByAI, loading: loadingAI } = useRequest(
    async () => {
      const res: any = await getPAListByAI(searchParamsByAI); // AI整改列表
      console.log('datares', res);

      return res.data || [];
    },
    {
      manual: true,
    },
  );
  const { run: runBySelf, loading: loadingSelf } = useRequest(
    async () => {
      const res: any = await getPAListBySelfPolling(searchParamsBySelf); // 自检整改列表
      return digSelfReportIssue(res.result) || [];
    },
    { manual: true },
  );
  const paramMap: any = {
    [taskType.AIPolling]: searchParamsByAI,
    [taskType.SelfPolling]: searchParamsBySelf,
    [taskType.NormalPolling]: searchParamsByNormal,
  };
  const loadingMap: any = {
    [taskType.AIPolling]: loadingAI,
    [taskType.SelfPolling]: loadingSelf,
    [taskType.NormalPolling]: loadingNormal,
  };
  const paramDate: any = {
    [taskType.AIPolling]: [dayjs(searchParamsByAI.start).toDate(), dayjs(searchParamsByAI.end).toDate()],
    [taskType.SelfPolling]: [dayjs(searchParamsBySelf.beginDate).toDate(), dayjs(searchParamsBySelf.endDate).toDate()],
    [taskType.NormalPolling]: [
      dayjs(searchParamsByNormal.startTime).toDate(),
      dayjs(searchParamsByNormal.endTime).toDate(),
    ],
  };
  const DZMap: any = {
    [taskType.AIPolling]: [
      {
        name: '全部',
        key: undefined as any,
      },
      {
        name: '待整改',
        key: 'UNRECTIFIED',
      },
      {
        name: '待审核',
        key: 'UNAUDITED',
      },
      {
        name: '已整改',
        key: 'RECTIFIED',
      },
      {
        name: '已过期',
        key: 'OVERDUE',
      },
    ],
    [taskType.SelfPolling]: [
      {
        name: '全部',
        key: undefined as any,
      },
      {
        name: '待整改',
        // key: ['CREATED', 'SUPERVISOR_REJECTED'],
        key: 'CREATED',
      },
      {
        name: '待审核',
        key: 'REFORM_SUBMITTED',
      },
      {
        name: '已整改',
        key: 'COMPLETED',
      },
      {
        name: '已过期',
        key: 'PAST_DUE',
      },
      {
        name: '已作废',
        key: 'INVALID',
      },
    ],
    [taskType.NormalPolling]: [
      {
        name: '全部',
        key: undefined as any,
      },
      {
        name: '待整改',
        key: 1,
      },
      {
        name: '待审核',
        key: 2,
      },
      {
        name: '已整改',
        key: 4,
      },
      {
        name: '已过期',
        key: 3,
      },
      {
        name: '已作废',
        key: 5,
      },
      {
        name: '已逾期',
        key: 6,
      },
    ],
  };
  const DZValueMap: any = {
    [taskType.AIPolling]: searchParamsByAI,
    // [taskType.SelfPolling]: { ...searchParamsBySelf, status: searchParamsBySelf.statues },
    [taskType.SelfPolling]: searchParamsBySelf,
    [taskType.NormalPolling]: searchParamsByNormal,
  };
  const runMap: any = {
    [taskType.AIPolling]: runByAI,
    [taskType.SelfPolling]: runBySelf,
    [taskType.NormalPolling]: runByNormal,
  };
  const [filterVisible, setFilterVisible] = useState(false);
  const statusText = useMemo(() => {
    let statusParam: any;

    if (pollingType === 'AIPolling') {
      statusParam = searchParamsByAI;
    }

    if (pollingType === 'SelfPolling') {
      statusParam = {
        ...searchParamsBySelf,
        status: searchParamsBySelf.statues[0],
      };
    }

    if (pollingType === 'NormalPolling') {
      statusParam = searchParamsByNormal;
    }

    console.log('statusParam', statusParam);

    return getStatusName(statusParam?.status);
  }, [searchParamsByAI, searchParamsBySelf, searchParamsByNormal, pollingType]);

  return (
    <div className="flex flex-col grow h-0 ">
      <div className="shrink-0">
        <div
          className=" flex items-center bg-white my-2 px-4 overflow-x-auto"
          style={{
            width: window.innerWidth,
          }}
        >
          <>
            <DateFilter
              noBg={true}
              value={paramDate[pollingType]}
              onChange={(e: any) => {
                if (pollingType === 'AIPolling') {
                  setSearchParamsByAI((p: any) => ({
                    ...p,
                    start: dayjs(e[0]).startOf('day').toISOString(),
                    end: dayjs(e[1]).endOf('day').toISOString(),
                  }));
                }

                if (pollingType === 'SelfPolling') {
                  setSearchParamsBySelf((p: any) => ({
                    ...p,
                    beginDate: dayjs(e[0]).startOf('day').toISOString(),
                    endDate: dayjs(e[1]).endOf('day').toISOString(),
                  }));
                }

                if (pollingType === 'NormalPolling') {
                  setSearchParamsByNormal((p: any) => ({
                    ...p,
                    startTime: dayjs(e[0]).startOf('day').toISOString(),
                    endTime: dayjs(e[1]).endOf('day').toISOString(),
                  }));
                }
              }}
              key="month"
              type="date"
              className="mr-6 flex-shrink-0"
            />
            {!!shopId ? null : (
              <ShopTree
                isInPermission
                onSelectChange={(params) => {
                  setTimeout(() => {
                    console.log('NormalPolling12345678', params);
                    if (pollingType === 'AIPolling') {
                      setSearchParamsByAI((p: any) => ({
                        ...p,
                        groupId: params.groupId,
                        shopIds: params.shopId?.length > 0 ? params.shopId : undefined,
                      }));
                    }
                    if (pollingType === 'SelfPolling') {
                      setSearchParamsBySelf((p: any) => ({
                        ...p,
                        groupId: params.groupId,
                        shopIds: params.shopId?.length > 0 ? params.shopId : undefined,
                      }));
                    }
                    if (pollingType === 'NormalPolling') {
                      console.log('NormalPolling50', params);
                      setSearchParamsByNormal((p: any) => ({
                        ...p,
                        groupId: params?.groupId,
                        shopIds: params.shopId?.length > 0 ? params.shopId : undefined,
                      }));
                    }
                  }, 50);
                }}
              />
            )}

            <DZSelect
              className=" ml-6 flex-shrink-0"
              showText={statusText}
              value={DZValueMap[pollingType]?.status!}
              renderSource={DZMap[pollingType]}
              onChange={(val) => {
                console.log('vvvvvvvvv', val);

                if (pollingType === 'AIPolling') {
                  // 字符串
                  setSearchParamsByAI({
                    ...searchParamsByAI,
                    status: val as any,
                  });
                }

                if (pollingType === 'SelfPolling') {
                  // 字符串
                  setSearchParamsBySelf({
                    ...searchParamsBySelf,
                    statues: val === 'CREATED' ? [val, 'SUPERVISOR_REJECTED'] : val ? [val] : [],
                  });
                }

                if (pollingType === 'NormalPolling') {
                  // 数字
                  setSearchParamsByNormal({
                    ...searchParamsByNormal,
                    status: val as any,
                  });
                }
              }}
            />
            <button
              className={classNames(
                `text-sm flex items-center leading-[14px] flex-shrink-0 ${
                  filterVisible ? 'text-[#141414]' : 'text-[#5E5E5E]'
                } focus:outline-none`,
              )}
              onClick={() => {
                setFilterVisible(true);
              }}
            >
              <div className="w-[1px] h-5 bg-black/[0.03] mr-[18px]" />
              筛选
              <IconFont type="icon-a-1111-copy" className="ml-1 text-xs text-[#B8B8B8]" />
            </button>
          </>
        </div>
        {pollingType !== 'AIPolling' && (
          <div
            className="bg-white py-2 flex px-4 gap-x-3 border-b border-black/[0.03] relative z-[1]"
            id="filterDrawerContainer"
          >
            <Checkbox
              onChange={() => {
                if (pollingType === 'SelfPolling') {
                  setSearchParamsBySelf({
                    ...searchParamsBySelf,
                    onlyLookCopyMe: !searchParamsBySelf.onlyLookCopyMe,
                  });
                }

                if (pollingType === 'NormalPolling') {
                  setSearchParamsByNormal({
                    ...searchParamsByNormal,
                    onlyMe: !searchParamsByNormal.onlyMe,
                  });
                }
              }}
              style={{
                '--icon-size': '18px',
              }}
            >
              <span className="text-sm leading-[14px] text-[#5E5E5E]">看抄送给我的</span>
            </Checkbox>

            <Checkbox
              checked={
                pollingType === 'SelfPolling'
                  ? searchParamsBySelf.onlyMeSubmitted
                  : searchParamsByNormal.onlyMeSubmitted
              }
              onChange={() => {
                if (pollingType === 'SelfPolling') {
                  setSearchParamsBySelf({
                    ...searchParamsBySelf,
                    onlyMeSubmitted: !searchParamsBySelf.onlyMeSubmitted,
                  });
                }

                if (pollingType === 'NormalPolling') {
                  setSearchParamsByNormal({
                    ...searchParamsByNormal,
                    onlyMeSubmitted: !searchParamsByNormal.onlyMeSubmitted,
                  });
                }
              }}
              style={{
                '--icon-size': '18px',
              }}
            >
              <span className="text-sm leading-[14px] text-[#5E5E5E]">看我提交的</span>
            </Checkbox>
          </div>
        )}
        {loadingMap[pollingType] ? (
          <SpinLoading className="mx-auto my-4" />
        ) : (
          <div className="flex flex-col gap-[10px] pt-2 px-2 rounded-lg">
            <InfiniteScrollBuilder
              key="TaskCard"
              searchParams={paramMap[pollingType]}
              api={requestMap[pollingType]}
              renderChildren={(data) => {
                // digSelfReportIssue(data.result)
                console.log('12345data', data);

                // const realData = pollingType === 'SelfPolling' ? digSelfReportIssue(data) : unifyData(data, pollingType)
                const realData = unifyData(pollingType, data);

                console.log('12345realData', realData);

                return realData?.map((o: any, idx: any) => {
                  const dataAI = (data: any) => {
                    const {
                      submitedUserName,
                      deadline,
                      processes,
                      copyUsers,
                      shopName,
                      // shopNo,
                      status,
                      workSheetName,
                      workSheetCategoryName,
                      labels,
                      itemImages,
                      itemRemark,
                      sumbimitedTime,
                      id,
                      shopId,
                      workSheetItemName,
                      rectifyMustUpload,
                    } = data;
                    const last = processes?.[processes?.length - 1];

                    let elapsedTime = '';

                    if (last?.type === 'COMPLETED') {
                      // 如果最后一条数据为审核通过，则计算耗时
                      // eslint-disable-next-line prefer-destructuring
                      const first = processes?.[0];
                      const stamp = dayjs(last?.sumbimitedTime).valueOf() - dayjs(first?.sumbimitedTime).valueOf(); // 时间差
                      const hourStamp = 60 * 60 * 1000;
                      const dayStamp = 24 * hourStamp;
                      const day = Math.floor(stamp / dayStamp);
                      const hour = Math.ceil((stamp % dayStamp) / hourStamp);

                      elapsedTime = [day, '天', hour, '小时'].join('');
                    }

                    return {
                      elapsedTime,
                      deadline,
                      id,
                      processes: (processes || []).filter(({ type }: any) => {
                        return type !== 'CREATED';
                      }),
                      copyUsers,
                      shopName,
                      shopNo: shopId,
                      shopId,
                      status,
                      workSheetName,
                      workSheetCategoryName,
                      labels,
                      itemImages,
                      itemRemark,
                      workSheetItemName, // AI检查项内容
                      submitedUserName,
                      sumbimitedTime,
                      submitedUserAvatar: robotPng,
                      itemName: workSheetItemName,
                      rectifyMustUpload,
                    };
                  };

                  return (
                    <IssueCardShop
                      key={`${o.issueId || o.reportId}_${idx}`}
                      className="mb-4"
                      data={pollingType === 'AIPolling' ? dataAI(o) : o}
                      onRefesh={runMap[pollingType]}
                      pollingType={pollingType}
                    />
                  );
                });
              }}
            />
          </div>
        )}
      </div>
      {/* 筛选框 */}
      <UnderPatrolFilter
        isSelfCheck={pollingType === 'SelfPolling'}
        value={DZValueMap[pollingType]}
        open={filterVisible}
        onClose={() => setFilterVisible(false)}
        onChange={(value: Record<string, any>) => {
          if (pollingType === 'AIPolling') {
            setSearchParamsByAI((pre: any) => ({
              ...pre,
              ...value,
              worksheetId: value.workSheetId,
            })); // 兼容后端传参
          }

          if (pollingType === 'SelfPolling') {
            setSearchParamsBySelf((pre: any) => ({
              ...pre,
              ...value,
              taskPlanId: value.taskIds,
              keyword: value.word,
              worksheetId: value.workSheetId,
            }));
          }

          if (pollingType === 'NormalPolling') {
            setSearchParamsByNormal((pre: any) => ({
              ...pre,
              ...value,
              normalTaskPlanId: value.planId,
              // keyword: value.word,
              worksheetId: value.workSheetId,
            }));
          }

          setFilterVisible(false);
        }}
        roleType={roleTypeIsManage() ? ERoleType.店长 : ERoleType.督导}
        showPlan={pollingType === 'AIPolling' ? false : true}
        showWorkSheet
        showQuestion
        showTask={!(pollingType === 'SelfPolling') && searchParams?.NormalType === 'Normal'}
      />
    </div>
  );
}
