import { useMemo, useRef, useState } from 'react';
import { Loading } from '@src/components';
import PageContainer from '@src/components/PageContainer';
import useQuerySearchParams from '@src/hooks/useQuerySearchParams';
import { queryPatrolReportDetail } from '@src/pages/patrol/api';
import { ReportDetailInfo } from '@src/pages/patrol/api.type';
import { getCycleJobCount, getSelfReportReview } from '@src/pages/selfCheckComment/api';
import { roleTypeIsManage } from '@src/utils/tokenUtils';
import { useRequest } from 'ahooks';
import { Button, Checkbox, Dialog, SpinLoading, Tabs, Toast } from 'antd-mobile';
import { RightOutline } from 'antd-mobile-icons';
import cn from 'classnames';
import { useNavigate } from 'react-router-dom';
import { getPAListByNormalPolling } from './api';
import { unifyData } from './dataRelay';
import { pollingAbarbeitungTab } from './digSelfReportIssue';
import styles from './index.module.scss';
import {
  batchExaminePatrol,
  batchExamineSelf,
  examineSelfTask,
  getPAListBySelfDetail,
  updateInspectTask,
} from '../api';
import IssueList from '../components/IssueList';
// report操作类型map
// eslint-disable-next-line react-refresh/only-export-components
export const reportOperatorTypeMap = {
  REFORM_SUBMIT: '提交整改',
  REFORM_PASS: '审核通过',
};
export default function DetailIndex() {
  enum Status {
    all = -1,
    process = 1,
    unReviewed = 2,
    overdue = 3,
    completed = 4,
    AUDIT_OVERDUE = 6,

    // //自检的
    // CREATED = 1,
    // SUPERVISOR_REJECTED = 1,
  }
  const [searchParams] = useQuerySearchParams<{
    taskId: string;
    pollingType: string;
    cross: string;
    isDAILY: boolean;
  }>();
  const { taskId, pollingType, cross, isDAILY } = searchParams;
  const [filter, setFilter] = useState<{ activeTab: Status; onlyMe: boolean }>({
    activeTab: -1,
    onlyMe: false,
  });
  const navigate = useNavigate();
  const { data: reportInfo, loading } = useRequest(async () => {
    // eslint-disable-next-line no-throw-literal
    if (!taskId) throw null;
    return pollingType === 'SelfPolling' ? getSelfReportReview(taskId) : queryPatrolReportDetail({ taskId: +taskId });
  });

  const { data: cycleJobCount, loading: cycleJobCountLoading } = useRequest(
    async () => {
      return getCycleJobCount(taskId);
    },
    {
      ready: !!roleTypeIsManage(),
    },
  );

  const {
    data = [],
    run: queryList,
    loading: listLoading,
  } = useRequest(
    // async (taskId) => {
    async () => {
      // eslint-disable-next-line no-throw-literal
      if (!taskId) throw null;
      if (pollingType === 'SelfPolling') {
        return await getPAListBySelfDetail({ taskId: +taskId });
      } else {
        return await getPAListByNormalPolling({
          reportId: +taskId,
          // onlyMe: false,
          onlyMe: true,
          pageNo: 1,
          pageSize: 100,
        });
      }
      // return cross === 'true'
      //   ? await getOutQueryIssueList({
      //     reportId: +reportId,
      //   })
      //   : queryIssueList({ reportId: +reportId, onlyMe: false, pageNo: 1, pageSize: 100 });
    },
  );
  console.log('取到的数据', data);
  const examineDialogRef = useRef<any>();
  const { loading: examineLoading, run: examine } = useRequest(
    async (data) => {
      if (pollingType === 'SelfPolling') {
        return await examineSelfTask({ reportId: data?.reportId });
      } else {
        return await updateInspectTask({ id: data?.issueId, status: 2 });
      }
    },
    {
      manual: true,
      onSuccess: () => {
        Toast.show('审核通过');
        queryList();
      },
    },
  );

  const { loading: batchExamineLoading, run: batchExamine } = useRequest(
    async () => {
      if (pollingType === 'SelfPolling') {
        console.log('examineList', examineList);
        const reportIssueIds = examineList?.map(({ id }: any) => id);
        return await batchExamineSelf({ reportIssueIds });
      } else {
        // eslint-disable-next-line no-throw-literal
        if (!taskId) throw null;
        const issueIds = examineList?.map(({ issueId }: any) => issueId);
        return await batchExaminePatrol({ reportId: +taskId, issueIds });
      }
    },
    {
      manual: true,
      onSuccess: () => {
        examineDialogRef.current.close();
        Toast.show('审核通过');
        queryList();
      },
    },
  );

  const { tabs, belongsMeCount } = useMemo(() => {
    const count = {
      [Status.process]: 0,
      [Status.unReviewed]: 0,
      [Status.completed]: 0,
      [Status.AUDIT_OVERDUE]: 0,
    };

    const belongsMeCount = {
      [Status.all]: 0,
      [Status.process]: 0, // 待整改
      [Status.unReviewed]: 0, // 待审核
      [Status.completed]: 0, // 已整改
      [Status.AUDIT_OVERDUE]: 0, // 已逾期
    };

    // 巡检的
    const dataToTab = pollingType === 'SelfPolling' ? pollingAbarbeitungTab(data) : data;
    console.log('dataToTab', dataToTab);

    for (let i = 0, len = dataToTab?.length; i < len; i++) {
      const { status, belongsMe } = dataToTab[i];
      console.log('status11111', status);

      (count as any)[status] = (count as any)[status] + 1;
      // 过期当做待整改
      if (status === Status.overdue || status === 'PAST_DUE') {
        count[Status.process] += 1;
      }

      if (belongsMe) {
        if (status === Status.overdue) {
          belongsMeCount[Status.process] += 1;
        }
        belongsMeCount[Status.all] += 1;
        (belongsMeCount as any)[status] += 1;
      }
    }
    // 自检的
    console.log('count[Status.process]', count[Status.process]);

    const tabs = [
      {
        tab: `全部(${dataToTab.length ? dataToTab.length : 0})`,
        key: Status.all,
      },
      { tab: `待整改(${count[Status.process]})`, key: Status.process },
      { tab: `待审核(${count[Status.unReviewed]})`, key: Status.unReviewed },
      { tab: `已整改(${count[Status.completed]})`, key: Status.completed },
      { tab: `已逾期(${count[Status.AUDIT_OVERDUE]})`, key: Status.AUDIT_OVERDUE },
    ];

    return {
      tabs: isDAILY ? tabs.filter((v) => v.key !== Status.unReviewed) : tabs,
      belongsMeCount,
      // count,
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data]);
  // filteredList兼容根据
  const filteredList = useMemo(() => {
    const tofilterData = pollingType === 'SelfPolling' ? pollingAbarbeitungTab(data) : data;
    const real = tofilterData
      .filter((v) => {
        console.log('iii哈哈哈哈', v);
        return (
          (!filter.onlyMe || v.belongsMe) &&
          (+filter.activeTab === Status.all ||
            v.status === +filter.activeTab ||
            (v.status === Status.overdue && +filter.activeTab === Status.process))
        );
      })
      .map((e) => {
        return {
          ...e,
          status: pollingType === 'SelfPolling' ? e.statusEnum : e.status,
        };
      });
    console.log('real', real);

    return real;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filter, data]);
  // 待审核数量
  const examineList = useMemo(() => {
    console.log('filterstatusdata', data);
    return (data || []).filter(({ status }: any) => {
      console.log('filterstatus', status);
      return pollingType === 'SelfPolling' ? status === 'REFORM_SUBMITTED' : status === 2;
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data]);
  console.log('examineList111111111', examineList);
  return (
    <PageContainer
      footer={
        !(reportInfo as ReportDetailInfo)?.cross &&
        examineList.length > 0 &&
        roleTypeIsManage() && (
          <div className="py-2 px-3 bg-[#fff]">
            <Button
              block
              color="primary"
              onClick={() => {
                examineDialogRef.current = Dialog.show({
                  content: (
                    <div>
                      <div className="text-base font-medium text-center">
                        有{examineList.length}个问题项将被审核通过
                      </div>
                      <div className="mt-6 flex">
                        <Button
                          onClick={() => {
                            examineDialogRef.current.close();
                          }}
                          className="text-base py-2 rounded-md mr-3 flex-1"
                        >
                          取消
                        </Button>
                        <Button
                          color="primary"
                          className="text-base py-2 rounded-md flex-1"
                          onClick={() => {
                            batchExamine();
                          }}
                        >
                          确定
                        </Button>
                      </div>
                    </div>
                  ),
                });
              }}
              loading={batchExamineLoading}
            >
              一键审核通过
            </Button>
          </div>
        )
      }
    >
      <Loading spinning={loading}>
        <div className="h-full flex flex-col gap-y-2">
          <div className="flex-grow gap-y-2.5 flex flex-col overflow-y-scroll">
            <div className="bg-white text-sm leading-[14px] p-4 pb-[22px] text-grey ">
              <div className="ellipsis-2 text-base leading-[22px] font-medium text-[#141414]">
                {!!reportInfo?.shopId && <span className="mr-2">{reportInfo?.shopId}</span>}
                <span>{reportInfo?.shopName}</span>
              </div>
              <div className="flex justify-between bg-[#f6f6f6] px-2 py-3  text-[#858585] mt-3 rounded-lg">
                <span className="text-sm  leading-[14px]">{reportInfo?.taskName}</span>
              </div>
              {/* {(reportInfo?.status === 2 || reportInfo?.status === 3) && (
                <div className="flex items-center mt-[14px]">
                  <Image
                    width={24}
                    height={24}
                    style={{ borderRadius: '100%' }}
                    src={reportInfo?.submitUserAvatar}
                  />
                  <span className="ml-2 text-xs leading-6 text-[#5E5E5E]">
                    {reportInfo?.submitUserName} 提交报告 {reportInfo?.submitTime}
                  </span>
                </div>
              )}
              {reportInfo?.status === 3 && (
                <div className="mt-1">
                  {reportInfo.confirmUserId === 0 ? (
                    <div className="flex justify-start items-center">
                      <span
                        className="sh-icon sh-icon-diannao text-primary h-full"
                        style={{ fontSize: '24px' }}
                      ></span>
                      <span className="ml-2 text-text-minor text-sm ">
                        店长未在规定时间内确认报告，系统 {reportInfo?.taskDate} 自动确认
                      </span>
                    </div>
                  ) : (
                    <div className="flex items-center">
                      <Image
                        width={24}
                        height={24}
                        style={{ borderRadius: '100%' }}
                        src={reportInfo?.confirmUserAvatar}
                      />
                      <span className="ml-2  text-xs leading-6 text-[#5E5E5E] ">
                        {reportInfo?.confirmUserName} 确认报告 {reportInfo?.reportDate}
                      </span>
                    </div>
                  )}
                </div>
              )}

              {reportInfo?.operateList?.map((item, index) => {
                return (
                  <div className="flex items-center mt-2" key={index}>
                    <Image
                      width={24}
                      height={24}
                      style={{ borderRadius: '100%' }}
                      // style={{ borderRadius: 32 }}
                      // className="w-6"
                      src={item?.operateUserAvatar}
                    />
                    <span className="ml-2 text-text-minor text-xs text-[#5E5E5E]">
                      {item?.operateUserName} {(reportOperatorTypeMap as any)[item?.operateType]}
                      {item?.operateTime}
                    </span>
                  </div>
                );
              })} */}
            </div>
            {cycleJobCount > 0 && (
              <div className="bg-white p-4 mt-[10px]">
                <div
                  className="flex"
                  onClick={() => {
                    navigate(`/tasks/circularRectification?taskId=${taskId}`);
                  }}
                >
                  <div className="flex-1 text-base">循环整改项</div>
                  <div className="flex items-center text-[#AAAAAA]">
                    共{cycleJobCount}个
                    <RightOutline className="text-[#c5c5c5] mt-1 text-sm " />
                  </div>
                </div>
              </div>
            )}
            <div className="bg-white">
              <div className=" text-sm leading-[14px] pt-4 text-grey">
                <div className="flex justify-between px-4 ">
                  <div className=" text-base leading-[16px] font-medium text-[#141414]">{'整改明细'}</div>
                  <Checkbox
                    onChange={() => {
                      setFilter({ ...filter, onlyMe: !filter.onlyMe });
                    }}
                    style={{
                      '--icon-size': '18px',
                    }}
                  >
                    <span className="text-sm leading-[14px] text-[#5E5E5E]">
                      我处理的({(belongsMeCount as any)[filter.activeTab]})
                    </span>
                  </Checkbox>
                </div>
                <div className="mt-[12px] bg-white items-center border-[#eeeeee]">
                  <Tabs
                    defaultActiveKey={'-1'}
                    className="w-full text-[#858585] font-medium"
                    onChange={(v) => {
                      setFilter({ ...filter, activeTab: v });
                    }}
                    activeLineMode="fixed"
                    style={{
                      '--active-line-height': '4px',
                      '--fixed-active-line-width': '24px',
                      '--active-title-color': '#141414',
                    }}
                  >
                    {tabs.map((item) => (
                      <Tabs.Tab
                        title={
                          <div
                            className={cn('leading-4 text-sm', {
                              [styles.activeColor]: item.key === filter.activeTab,
                            })}
                          >
                            {item.tab}
                          </div>
                        }
                        key={item.key}
                        // className="pl-10 pr-0"
                      />
                    ))}
                  </Tabs>
                </div>
              </div>
            </div>
            {listLoading ? (
              <SpinLoading className="mx-auto my-4" />
            ) : (
              <div className="pt-0 px-2 rounded-lg">
                <IssueList
                  isShop={true}
                  cross={cross}
                  dataSource={pollingType === 'SelfPolling' ? filteredList : unifyData(pollingType, filteredList)} // 此处自检数据为已处理过 不再处理
                  onChange={() => {}}
                  examineLoading={examineLoading}
                  onExamine={(data: any) => {
                    examine(data);
                  }}
                  pollingType={pollingType}
                  onRefesh={queryList}
                />
              </div>
            )}
          </div>
        </div>
      </Loading>
    </PageContainer>
  );
}
