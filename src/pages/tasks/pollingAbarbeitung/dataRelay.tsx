import { digSelfReportIssue } from './digSelfReportIssue';

// 巡检、自检、AI巡检 相关数据转换为统一组件数据
enum CustomEnum {
  待整改 = 1,
  待审核 = 2,
  已过期 = 3,
  已整改 = 4,
  已逾期 = 6,
}
enum NormalRecordEnum {
  'CREATED',
  'SUPERVISOR_REJECTED',
  'COMPLETED',
}
const enumMapping: Record<number, string> = {
  [CustomEnum.待整改]: 'UNRECTIFIED',
  [CustomEnum.待审核]: 'UNAUDITED',
  [CustomEnum.已整改]: 'COMPLETED',
  [CustomEnum.已过期]: 'OVERDUE',
  [CustomEnum.已逾期]: 'AUDIT_OVERDUE',
};
const mapEnumValue = (value: number) => {
  return enumMapping[value] || 'Unknown';
};

export const unifyData = (pollingType: string, data: any) => {
  if (pollingType === 'NormalPolling') {
    return data?.map((item: any) => {
      console.log('iiiiii', item);
      return {
        ...item,
        rectifyMustUpload: item.reformMustUpload,
        status: mapEnumValue(item.status),
        id: item.issueId,
        workSheetItemName: item.itemName,
        workSheetName: item.checkListName,
        workSheetCategoryName: item?.checkListTypeName,
        labels: item.tags,
        processes: item?.children?.map((e: any) => {
          return {
            ...e,
            text: e.issueRemark,
            submitedUserName: e.submitedName,
            submitedUserAvatar: e.submitedAvatar,
            type: NormalRecordEnum[e.type], // 0：提交整改反馈 1：xxx驳回 2：审核通过
          };
        }),
      };
    });
  }
  if (pollingType === 'AIPolling') {
    // return digSelfReportIssue(data);
    return data;
  }
  if (pollingType === 'SelfPolling') {
    return digSelfReportIssue(data);
  }
};
