export type AIPAListParams = {
    start?: string;
    end?: string;
    groupId: number;
    pageNo: number;
    pageSize: number;
    copyRecipient: boolean;
    shopIds: any
    worksheetId?: number
};
export type aiRectifyParams = {
    reportId: number;
    text?: string;
    images?: any[]
}
export type selfRectifyParams = {
    id: number;
    text?: string;
    images?: any[]
}
export type getSelfPAListParams = {
    start?: string;
    end?: string;
    groupId?: number;
    pageNo: number;
    pageSize: number;
    statues: string[];
    // onlyLookCopyMe: boolean;
    // onlyMeSubmitted:boolean;
    // shopIds: any
};
export type getNormalPAListParams = {
    start?: string;
    end?: string;
    onlyMe: boolean;
    onlyMeSubmitted?: boolean;
    groupId?: number;
    pageNo: number;
    pageSize: number;
    shopIds?: any;
    reportId?: number;
};