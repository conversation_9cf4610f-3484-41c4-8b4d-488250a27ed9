import { useEffect, useMemo, useState } from 'react';
import robotPng from '@src/assets/images/robot.png';
import { DateFilter, IconFont } from '@src/components';
import { ShopTree } from '@src/components/ShopTree';
import useQuerySearchParams from '@src/hooks/useQuerySearchParams';
import { ERoleType } from '@src/pages/mission';
import { PollingTypeSelect } from '@src/pages/mission/components/PollingTypeSelect';
import { TaskCenterFilter } from '@src/pages/mission/components/TaskCenterFilter';
import { roleTypeIsManage } from '@src/utils/tokenUtils';
import { useRequest } from 'ahooks';
import { Checkbox, SpinLoading, Tabs } from 'antd-mobile';
import classNames from 'classnames';
import dayjs from 'dayjs';
import _ from 'lodash';
import {
  getPAListByAI,
  getPAListByNormalPolling,
  getPAListBySelfPolling,
} from './api';
import { unifyData } from './dataRelay';
import { digSelfReportIssue } from './digSelfReportIssue';
import { InfiniteScrollBuilder } from '../components/InfiniteScrollBuilder';
import IssueCardShop from '../components/IssueList/IssueCardShop';

enum taskType {
  AIPolling = 'AIPolling',
  SelfPolling = 'SelfPolling',
  NormalPolling = 'NormalPolling',
}

const requestMap = {
  [taskType.AIPolling]: getPAListByAI,
  [taskType.SelfPolling]: getPAListBySelfPolling,
  [taskType.NormalPolling]: getPAListByNormalPolling,
};

export default function PATaskList() {
  const [searchParams, setSearchParams]: any = useQuerySearchParams();
  //   const { detailReturn } = searchParams;
  const { shopId, detailReturn } = searchParams;
  // const shopId = 'TS224';
  //   const shopId = 'M429';
  const [searchParamsByAI, setSearchParamsByAI] = useState<any>({
    copyRecipient: false,
    groupId: /[a-zA-Z]/.test(shopId) ? null : Number(shopId),
    shopIds: /[a-zA-Z]/.test(shopId) ? [shopId] : [],
    // pageNo: 1,
    pageSize: 5,
    start: dayjs().subtract(1, 'months').toISOString(),
    end: dayjs().toISOString(),
    word: '',
    status: roleTypeIsManage() ? 'UNAUDITED' : 'UNRECTIFIED',
  });
  const [searchParamsByNormal, setSearchParamsByNormal] = useState<any>({
    groupId: /[a-zA-Z]/.test(shopId) ? null : Number(shopId),
    onlyMe: true,
    // pageNo: 1,
    pageSize: 10,
    shopIds: /[a-zA-Z]/.test(shopId) ? [shopId] : [],
    startTime: dayjs().subtract(1, 'months').toISOString(),
    endTime: dayjs().toISOString(),
    status: roleTypeIsManage() ? 2 : 1,
  });
  const [searchParamsBySelf, setSearchParamsBySelf] = useState<any>({
    groupId: /[a-zA-Z]/.test(shopId) ? null : Number(shopId),
    onlyLookCopyMe: false,
    shopIds: /[a-zA-Z]/.test(shopId) ? [shopId] : [],
    // pageNo: 1,
    pageSize: 10,
    beginDate: dayjs().subtract(1, 'months').toISOString(),
    endDate: dayjs().toISOString(),
    statues: roleTypeIsManage()
      ? ['REFORM_SUBMITTED']
      : ['CREATED', 'SUPERVISOR_REJECTED'], // 这是实际传参
    status: roleTypeIsManage() ? 'REFORM_SUBMITTED' : 'CREATED', // 垃圾传参（只处理前端状态筛选）
  });
  const DZValueMap = {
    [taskType.AIPolling]: searchParamsByAI,
    [taskType.SelfPolling]: searchParamsBySelf,
    [taskType.NormalPolling]: searchParamsByNormal,
  };
  const initialPollingType =
    localStorage.getItem('pollingType') || 'SelfPolling';
  const initialActiveKey =
    localStorage.getItem('activeKey') || 'REFORM_SUBMITTED';
  const initialNormalType = localStorage.getItem('normalType') || '';
  const initialPollingTypeValue =
    localStorage.getItem('pollingTypeValue') || 'SelfPolling';
  const [pollingType, setPollingType] = useState<string>(initialPollingType);
  const [activeKey, setActiveKey] = useState<string>(initialActiveKey);
  const [normalType, setNormalType] = useState<string>(initialNormalType);
  const [pollingTypeValue, setPollingTypeValue] = useState<string>(
    initialPollingTypeValue,
  );

  useEffect(() => {
    localStorage.setItem('pollingType', pollingType);
    localStorage.setItem('activeKey', activeKey);
    localStorage.setItem('normalType', normalType);
    localStorage.setItem('pollingTypeValue', pollingTypeValue);
  }, [pollingType, activeKey, normalType, pollingTypeValue]);
  useEffect(() => {
    if (detailReturn === 'true') {
      // 说明是从详情页返回的场景
    } else {
      localStorage.removeItem('pollingType');
      localStorage.removeItem('activeKey');
      localStorage.removeItem('normalType');
      localStorage.removeItem('pollingTypeValue');
      setPollingType('SelfPolling');
      setActiveKey('REFORM_SUBMITTED');
      setNormalType('');
      setPollingTypeValue('SelfPolling');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const { run: runByNormal, loading: loadingNormal } = useRequest(
    async () => {
      const res: any = await getPAListByNormalPolling(searchParamsByNormal); // 巡检整改列表

      console.log('NormalPollingDAta', res);

      return res || [];
    },
    {
      manual: true,
    },
  );
  const { run: runByAI, loading: loadingAI } = useRequest(
    async () => {
      const res: any = await getPAListByAI(searchParamsByAI); // AI整改列表

      console.log('datares', res);

      return res.data || [];
    },
    {
      manual: true,
    },
  );
  const { run: runBySelf, loading: loadingSelf } = useRequest(
    async () => {
      const res: any = await getPAListBySelfPolling(searchParamsBySelf); // 自检整改列表

      return digSelfReportIssue(res.result) || [];
    },
    { manual: true },
  );
  const paramMap = {
    [taskType.AIPolling]: searchParamsByAI,
    [taskType.SelfPolling]: searchParamsBySelf,
    [taskType.NormalPolling]: searchParamsByNormal,
  };
  const loadingMap = {
    [taskType.AIPolling]: loadingAI,
    [taskType.SelfPolling]: loadingSelf,
    [taskType.NormalPolling]: loadingNormal,
  };
  const paramDate = {
    [taskType.AIPolling]: [
      dayjs(searchParamsByAI.start).toDate(),
      dayjs(searchParamsByAI.end).toDate(),
    ],
    [taskType.SelfPolling]: [
      dayjs(searchParamsBySelf.beginDate).toDate(),
      dayjs(searchParamsBySelf.endDate).toDate(),
    ],
    [taskType.NormalPolling]: [
      dayjs(searchParamsByNormal.startTime).toDate(),
      dayjs(searchParamsByNormal.endTime).toDate(),
    ],
  };
  const DZMap = {
    [taskType.AIPolling]: [
      {
        name: '全部',
        key: undefined as any,
      },
      {
        name: '待整改',
        key: 'UNRECTIFIED',
      },
      {
        name: '待审核',
        key: 'UNAUDITED',
      },
      {
        name: '已整改',
        key: 'RECTIFIED',
      },
      {
        name: '已过期',
        key: 'OVERDUE',
      },
    ],
    [taskType.SelfPolling]: [
      {
        name: '全部',
        key: undefined as any,
      },
      {
        name: '待整改',
        key: 'CREATED',
      },
      {
        name: '待审核',
        key: 'REFORM_SUBMITTED',
      },
      {
        name: '已整改',
        key: 'COMPLETED',
      },
      {
        name: '已过期',
        key: 'PAST_DUE',
      },
    ],
    [taskType.NormalPolling]: [
      {
        name: '全部',
        key: undefined as any,
      },
      {
        name: '待整改',
        key: '1',
      },
      {
        name: '待审核',
        key: '2',
      },
      {
        name: '已整改',
        key: '4',
      },
      {
        name: '已过期',
        key: '3',
      },
    ],
  };

  const runMap = {
    [taskType.AIPolling]: runByAI,
    [taskType.SelfPolling]: runBySelf,
    [taskType.NormalPolling]: runByNormal,
  };
  const [filterVisible, setFilterVisible] = useState(false);
  const statusText = useMemo(() => {
    enum normalTypeEnum {
      NORMAL_AND_CROSS = '到店巡检',
      VIDEO = '视频巡检',
      FOOD_SAFETY_NORMAL = '食安线下稽核',
      FOOD_SAFETY_VIDEO = '食安线上稽核',
    }
    enum pollingTypeEnum {
      SelfPolling = '门店自检',
      AIPolling = 'AI巡检',
    }
    return pollingType === 'NormalPolling'
      ? normalTypeEnum[normalType]
      : pollingTypeEnum[pollingType];
  }, [pollingType, normalType]);

  useEffect(() => {
    console.log('223323pollingType', pollingType);
    setActiveKey(
      pollingType === 'NormalPolling'
        ? DZValueMap[pollingType]?.status.toString()
        : DZValueMap[pollingType]?.status!,
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pollingType]);

  return (
    <div className="flex flex-col grow h-0 ">
      <div className="shrink-0">
        <div>
          <div>
            <Tabs
              onChange={(key) => {
                setActiveKey(key);

                if (pollingType === 'AIPolling') {
                  setSearchParamsByAI({
                    ...searchParamsByAI,
                    status: key as any,
                  });
                }

                if (pollingType === 'SelfPolling') {
                  setSearchParamsBySelf({
                    ...searchParamsBySelf,
                    statues:
                      key === 'CREATED'
                        ? [key, 'SUPERVISOR_REJECTED']
                        : key
                          ? [key]
                          : [],
                  });
                }

                if (pollingType === 'NormalPolling') {
                  setSearchParamsByNormal({
                    ...searchParamsByNormal,
                    status: key as any,
                  });
                }
              }}
              activeKey={activeKey}
              className="bg-white"
            >
              {DZMap[pollingType].map(({ key, name }) => (
                <Tabs.Tab title={name} key={key} />
              ))}
            </Tabs>
          </div>
          <div
            className="pt-2 flex justify-between items-center bg-white border-b-[0.0625rem] border-[#00000005] px-4 overflow-x-auto"
            style={{
              width: window.innerWidth,
            }}
          >
            <>
              <DateFilter
                noBg={true}
                value={paramDate[pollingType]}
                onChange={(e) => {
                  if (pollingType === 'AIPolling') {
                    setSearchParamsByAI((p: any) => ({
                      ...p,
                      start: dayjs(e[0]).startOf('day').toISOString(),
                      end: dayjs(e[1]).endOf('day').toISOString(),
                    }));
                  }

                  if (pollingType === 'SelfPolling') {
                    setSearchParamsBySelf((p: any) => ({
                      ...p,
                      beginDate: dayjs(e[0]).startOf('day').toISOString(),
                      endDate: dayjs(e[1]).endOf('day').toISOString(),
                    }));
                  }

                  if (pollingType === 'NormalPolling') {
                    setSearchParamsByNormal((p: any) => ({
                      ...p,
                      startTime: dayjs(e[0]).startOf('day').toISOString(),
                      endTime: dayjs(e[1]).endOf('day').toISOString(),
                    }));
                  }
                }}
                key="month"
                type="date"
                // className="mb-2"
              />
              <div className="">
                {/* overflow-hidden text-ellipsis */}
                {/* <div className="w-2 flex justify-center  items-center "> */}
                {/* <div>大方很</div> */}
                <ShopTree
                  hasEllipsis={true}
                  isInPermission
                  shopId={shopId}
                  onSelectChange={(params) => {
                    setTimeout(() => {
                      setSearchParams((p: any) => ({
                        ...p,
                        shopId: params.groupId
                          ? params.groupId
                          : params.shopId[0],
                      }));
                      console.log('NormalPolling12345678', params);
                      setSearchParamsByAI((p: any) => ({
                        ...p,
                        groupId: params.groupId,
                        shopIds: params.shopId?.length > 0 ? params.shopId : [],
                      }));
                      setSearchParamsBySelf((p: any) => ({
                        ...p,
                        groupId: params.groupId,
                        shopIds: params.shopId?.length > 0 ? params.shopId : [],
                      }));
                      setSearchParamsByNormal((p: any) => ({
                        ...p,
                        groupId: params.groupId,
                        shopIds: params.shopId?.length > 0 ? params.shopId : [],
                      }));
                      // }
                    }, 50);
                  }}
                />
              </div>

              <PollingTypeSelect
                className={`flex text-sm leading-[14px] rounded  text-primary px-2 py-[3px]`}
                showText={statusText}
                value={pollingTypeValue}
                renderSource={[
                  {
                    name: '门店自检',
                    key: 'SelfPolling',
                  },
                  {
                    name: '到店巡检',
                    key: `NormalPolling+NORMAL_AND_CROSS`,
                  },
                  {
                    name: '视频巡检',
                    key: `NormalPolling+VIDEO`,
                  },
                  {
                    name: 'AI巡检',
                    key: 'AIPolling',
                  },
                  {
                    name: '食安线下稽核',
                    key: `NormalPolling+FOOD_SAFETY_NORMAL`,
                  },
                  {
                    name: '食安线上稽核',
                    key: `NormalPolling+FOOD_SAFETY_VIDEO`,
                  },
                ]}
                onChange={(val) => {
                  setPollingTypeValue(val);

                  const valKey = val.split('+');

                  console.log('vvvvvvvvvvalKey', valKey);
                  setPollingType(valKey[0]);

                  if (valKey[0] === 'NormalPolling') {
                    setNormalType(valKey[1]);
                    console.log('searchParamsByNormal', searchParamsByNormal);
                    setSearchParamsByNormal((p: any) => ({
                      ...p,
                      patrolTaskSubTypeByApp: valKey[1],
                    }));
                  }
                }}
              />
              <button
                className={classNames(
                  `text-sm  flex  items-center leading-[14px] flex-shrink-0 ${
                    filterVisible ? 'text-[#141414]' : 'text-[#5E5E5E]'
                  } focus:outline-none`,
                )}
                onClick={() => {
                  setFilterVisible(true);
                }}
              >
                <div className="w-[1px] h-5 bg-black/[0.03] mr-[18px]" />
                筛选
                <IconFont
                  type="icon-a-1111-copy"
                  className="ml-1 text-xs text-[#B8B8B8]"
                />
              </button>
            </>
          </div>
        </div>

        {pollingType !== 'AIPolling' && (
          <div
            className="bg-white py-2 flex justify-between px-4 border-b border-black/[0.03] relative z-[1]"
            id="filterDrawerContainer"
          >
            <Checkbox
              onChange={() => {
                if (pollingType === 'SelfPolling') {
                  setSearchParamsBySelf({
                    ...searchParamsBySelf,
                    onlyLookCopyMe: !searchParamsBySelf.onlyLookCopyMe,
                  });
                }

                if (pollingType === 'NormalPolling') {
                  setSearchParamsByNormal({
                    ...searchParamsByNormal,
                    onlyMe: !searchParamsByNormal.onlyMe,
                  });
                }
              }}
              style={{
                '--icon-size': '18px',
              }}
            >
              <span className="text-sm leading-[14px] text-[#5E5E5E]">
                看抄送给我的
              </span>
            </Checkbox>
          </div>
        )}
        {loadingMap[pollingType] ? (
          <SpinLoading className="mx-auto my-4" />
        ) : (
          <div className="flex flex-col gap-[10px] pt-2 px-2 rounded-lg">
            <InfiniteScrollBuilder
              key={pollingType}
              searchParams={paramMap[pollingType]}
              api={requestMap[pollingType]}
              renderChildren={(data) => {
                const realData = _.uniqBy(unifyData(pollingType, data), 'id');

                return realData?.map((o: any, idx: any) => {
                  const dataAI = (data: any) => {
                    const {
                      submitedUserName,
                      deadline,
                      processes,
                      copyUsers,
                      shopName,
                      status,
                      workSheetName,
                      workSheetCategoryName,
                      labels,
                      itemImages,
                      itemRemark,
                      sumbimitedTime,
                      id,
                      shopId,
                      workSheetItemName,
                      rectifyMustUpload,
                    } = data;
                    const last = processes && processes[processes?.length - 1];

                    let elapsedTime = '';

                    if (last?.type === 'COMPLETED') {
                      // 如果最后一条数据为审核通过，则计算耗时
                      // eslint-disable-next-line prefer-destructuring
                      const first = processes[0];
                      const stamp =
                        dayjs(last?.sumbimitedTime).valueOf() -
                        dayjs(first?.sumbimitedTime).valueOf(); // 时间差
                      const hourStamp = 60 * 60 * 1000;
                      const dayStamp = 24 * hourStamp;
                      const day = Math.floor(stamp / dayStamp);
                      const hour = Math.ceil((stamp % dayStamp) / hourStamp);

                      elapsedTime = [day, '天', hour, '小时'].join('');
                    }

                    return {
                      elapsedTime,
                      deadline,
                      id,
                      processes: (processes || []).filter(({ type }) => {
                        return type !== 'CREATED';
                      }),
                      copyUsers,
                      shopName,
                      shopNo: shopId,
                      status,
                      workSheetName,
                      workSheetCategoryName,
                      labels,
                      itemImages,
                      itemRemark,
                      workSheetItemName, // AI检查项内容
                      submitedUserName,
                      sumbimitedTime,
                      submitedUserAvatar: robotPng,
                      itemName: workSheetItemName,
                      rectifyMustUpload,
                    };
                  };

                  return (
                    <IssueCardShop
                      key={`${o.issueId || o.reportId}_${idx}`}
                      className="mb-4"
                      shopId={shopId}
                      data={pollingType === 'AIPolling' ? dataAI(o) : o}
                      onRefesh={runMap[pollingType]}
                      pollingType={pollingType}
                    />
                  );
                });
              }}
            />
          </div>
        )}
      </div>
      {/* 筛选框 */}
      <TaskCenterFilter
        isSelfCheck={pollingType === 'SelfPolling'}
        // value={searchParamsByAI}
        value={DZValueMap[pollingType]}
        open={filterVisible}
        onClose={() => setFilterVisible(false)}
        onChange={(value: Record<string, any>) => {
          console.log('vaslue11', value);

          if (pollingType === 'AIPolling') {
            setSearchParamsByAI((pre: any) => ({
              ...pre,
              ...value,
            })); // 兼容后端传参
          }

          if (pollingType === 'SelfPolling') {
            setSearchParamsBySelf((pre: any) => ({
              ...pre,
              ...value,
              // taskPlanId: value.taskIds,
              // keyword: value.word,
            }));
          }

          if (pollingType === 'NormalPolling') {
            setSearchParamsByNormal((pre: any) => ({
              ...pre,
              ...value,
              normalTaskPlanId: value.planId,
            }));
          }

          setFilterVisible(false);
        }}
        roleType={roleTypeIsManage() ? ERoleType.店长 : ERoleType.督导}
        showTask={pollingType === 'AIPolling' ? false : true}
        showWorkSheet
        showQuestion
      />
    </div>
  );
}
