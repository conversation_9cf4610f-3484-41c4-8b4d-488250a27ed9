import { useMemo } from 'react';
import { Loading } from '@src/components';
import useQuerySearchParams from '@src/hooks/useQuerySearchParams';
import { useRequest } from 'ahooks';
import { ErrorBlock } from 'antd-mobile';
import { getRectificationDetailByRoutine, getRectificationDetailBySelf } from './services';
import IssueCardShop from '../../components/IssueList/IssueCardShop';
import { unifyData } from '../dataRelay';

enum TaskTypeEnum {
  SELF = 'SELF',
  PATROL = 'PATROL',
}

const pollingTypeMap: Record<string, string> = {
  [TaskTypeEnum.SELF]: 'SelfPolling',
  [TaskTypeEnum.PATROL]: 'NormalPolling',
};

export default function Detail() {
  const [searchParams] = useQuerySearchParams<{ taskType: string; taskId: string }>();
  const { taskId, taskType } = searchParams;

  const pollingType = useMemo(() => {
    return pollingTypeMap[taskType];
  }, [taskType]);

  const { data, loading, refresh, error } = useRequest(
    async () => {
      const action = {
        SELF: getRectificationDetailBySelf,
        PATROL: getRectificationDetailByRoutine,
      }[taskType as TaskTypeEnum];

      const res = await action(+taskId);

      if (taskType === 'PATROL') {
        return { ...res, id: res?.issueId };
      }

      return res;
    },
    {
      ready: !!taskId,
      refreshDeps: [taskId, taskType],
    },
  );

  const dataSource = useMemo(() => {
    if (data) {
      return unifyData(pollingType, [data]);
    } else {
      return [];
    }
  }, [data, pollingType]);

  if (loading) {
    return (
      <div className="flex flex-1 justify-center items-center">
        <Loading spinning={loading} />
      </div>
    );
  }

  if (!data) {
    return <ErrorBlock description={error?.message || '暂无数据'} />;
  }

  return (
    <IssueCardShop
      className="mb-4"
      data={dataSource?.[0] ?? []}
      onRefesh={refresh}
      pollingType={pollingType}
      isNotFold
      hiddenOperation
    />
  );
}
