import robotPng from '@src/assets/images/robot.png';
import dayjs from 'dayjs';

export const dataAI = (data: any) => {
  const {
    submitedUserName,
    deadline,
    processes,
    copyUsers,
    shopName,
    // shopNo,
    status,
    workSheetName,
    workSheetCategoryName,
    labels,
    itemImages,
    itemRemark,
    sumbimitedTime,
    id,
    shopId,
    workSheetItemName,
    rectifyMustUpload,
  } = data;
  const last = processes?.[processes?.length - 1];

  let elapsedTime = '';

  if (last?.type === 'COMPLETED') {
    // 如果最后一条数据为审核通过，则计算耗时
    // eslint-disable-next-line prefer-destructuring
    const first = processes?.[0];
    const stamp = dayjs(last?.sumbimitedTime).valueOf() - dayjs(first?.sumbimitedTime).valueOf(); // 时间差
    const hourStamp = 60 * 60 * 1000;
    const dayStamp = 24 * hourStamp;
    const day = Math.floor(stamp / dayStamp);
    const hour = Math.ceil((stamp % dayStamp) / hourStamp);

    elapsedTime = [day, '天', hour, '小时'].join('');
  }

  return {
    elapsedTime,
    deadline,
    id,
    processes: (processes || []).filter(({ type }: any) => {
      return type !== 'CREATED';
    }),
    copyUsers,
    shopName,
    shopNo: shopId,
    shopId,
    status,
    workSheetName,
    workSheetCategoryName,
    labels,
    itemImages,
    itemRemark,
    workSheetItemName, // AI检查项内容
    submitedUserName,
    sumbimitedTime,
    submitedUserAvatar: robotPng,
    itemName: workSheetItemName,
    rectifyMustUpload,
  };
};
