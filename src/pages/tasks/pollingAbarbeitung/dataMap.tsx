export enum taskType {
    AIPolling = 'AIPolling',
    SelfPolling = 'SelfPolling',
    NormalPolling = 'NormalPolling',
}
export const DZMap = {
    [taskType.AIPolling]: [
        {
            name: '全部',
            key: undefined as any,
        },
        {
            name: '待整改',
            key: 'UNRECTIFIED',
        },
        {
            name: '待审核',
            key: 'UNAUDITED',
        },
        {
            name: '已整改',
            key: 'RECTIFIED',
        },
        {
            name: '已过期',
            key: 'OVERDUE',
        },
    ],
    [taskType.SelfPolling]: [
        {
            name: '全部',
            key: undefined as any,
        },
        {
            name: '待整改',
            key: ["CREATED", "SUPERVISOR_REJECTED"],
        },
        {
            name: '待审核',
            key: ["REFORM_SUBMITTED"],
        },
        {
            name: '已整改',
            key: ["COMPLETED"],
        },
        {
            name: '已过期',
            key: ["PAST_DUE"],
        },
    ],
    [taskType.NormalPolling]:
        [
            {
                name: '全部',
                key: undefined as any,
            },
            {
                name: '待整改',
                key: 1,
            },
            {
                name: '待审核',
                key: 2,
            },
            {
                name: '已整改',
                key: 4,
            },
            {
                name: '已过期',
                key: 3,
            },
        ],
}
export const statusMap = {
    // 自检
    'CREATED': '待整改',
    'SUPERVISOR_REJECTED': '待整改',
    'REFORM_SUBMITTED': '待审核',
    'COMPLETED': '已整改',
    'PAST_DUE': '已过期',

    // AI巡检
    'UNRECTIFIED': '待整改',
    'UNAUDITED': '待审核',
    'RECTIFIED': '已整改',
    'OVERDUE': '已过期',

    // 常规巡检
    1: '待整改',
    2: '待审核',
    3: '已过期',
    4: '已整改'
};

export const getStatusName = (status: string | number) => {
    // if (Array.isArray(status)) {
    //     console.log('statusMap', status.map((s: string) => statusMap[s] || '状态'));
    //     return status.map((s: string) => statusMap[s] || '状态');
    // } else
    if (statusMap.hasOwnProperty(status)) {
        return statusMap[status];
    } else {
        return '状态';
    }
};

//自检整改，常规巡检，AI巡检数据需要处理成统一的格式
//数据转换

enum NormalRecordEnum {
    'CREATED',
    'SUPERVISOR_REJECTED',
    'COMPLETED'
}
export enum CustomEnum {
    待整改 = 1,
    待审核 = 2,
    已过期 = 3,
    已整改 = 4,
}
export const enumMapping: Record<number, string> = {
    [CustomEnum.待整改]: 'UNRECTIFIED',
    [CustomEnum.待审核]: 'UNAUDITED',
    [CustomEnum.已整改]: 'COMPLETED',
    [CustomEnum.已过期]: 'OVERDUE'
};
export const mapEnumValue = (value: number) => {
    return enumMapping[value] || 'Unknown';
}
export const PATaskListUnifyData = (data: any, pollingType: string) => {
    if (pollingType === 'NormalPolling') {
        return data?.map((item: any) => {
            return {
                ...item,
                status: mapEnumValue(item.status),
                id: item.issueId,
                workSheetName: item.checkListName,
                workSheetCategoryName: item?.checkListTypeName,
                labels: item.tags,
                processes: item?.children.map((e: any) => {
                    return {
                        ...e,
                        submitedUserName: e.submitedName,
                        submitedUserAvatar: e.submitedAvatar,
                        type: NormalRecordEnum[e.type]    //0：提交整改反馈 1：xxx驳回 2：审核通过
                    }
                }),
            }
        })
    }
    // if (pollingType === 'AIPolling') {

    //     return digSelfReportIssue(data);
    // }
}