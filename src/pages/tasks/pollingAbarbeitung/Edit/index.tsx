import { FC, useState } from 'react';
import { IconFont } from '@src/components';
import PageContainer from '@src/components/PageContainer';
import useQuerySearchParams from '@src/hooks/useQuerySearchParams';
import SystemImageUpload from '@src/pages/patrol/checkList/components/systemImageUpload';
import { roleTypeIsManage } from '@src/utils/tokenUtils';
import { useRequest } from 'ahooks';
import { Button, Form, Space, TextArea, Toast } from 'antd-mobile';
import dayjs from 'dayjs';
import { useNavigate } from 'react-router-dom';
// import { ImgUploader } from '../../components/imageUpload';
import {
  addPollingNormal,
  addPollingSelf,
  aiAnomalyReject,
  aiRecognitionReject,
  aiRectify,
  aiRectifyReject,
  overruleSelf,
} from '../api';
// eslint-disable-next-line react-refresh/only-export-components
export enum OperationTypeEdit {
  FeedbackAI = 'FeedbackAI', // AI门店整改反馈
  ErrorAI = 'ErrorAI', // AI识别错误
  AnomalyAI = 'AnomalyAI', // AI识别错误的督导驳回
  OverruleAI = 'OverruleAI', // 督导整改反馈驳回
  OverruleSelf = 'OverruleSelf', // 自检整改督导驳回
  AddPollingSelf = 'AddPollingSelf', // 自检整改提交
  AddOverruleNormal = 'AddPollingNormal', // 常规巡检整改提交  常规巡检督导驳回
  // rectification
}
const requestMap = {
  [OperationTypeEdit.FeedbackAI]: aiRectify, // 整改反馈
  [OperationTypeEdit.ErrorAI]: aiRecognitionReject, // ai识别错误驳回
  [OperationTypeEdit.AnomalyAI]: aiAnomalyReject, // ai识别错误驳回
  [OperationTypeEdit.OverruleAI]: aiRectifyReject, // 督导整改反馈驳回 对应FeedbackAI的整改反馈
  [OperationTypeEdit.OverruleSelf]: overruleSelf, // 自检督导驳回
  [OperationTypeEdit.AddPollingSelf]: addPollingSelf, // 门店侧提交整改
  [OperationTypeEdit.AddOverruleNormal]: addPollingNormal, // 门店侧提交整改
};
const Edit: FC = () => {
  const [isFormValid, setIsFormValid] = useState(roleTypeIsManage() ? true : false);
  const [upLoading, setUpLoading] = useState<boolean>(false);
  // const updateIsFormValid = (newValue: boolean) => {
  //   setIsFormValid(newValue);
  // };
  const [form] = Form.useForm();
  const [searchParams]: any = useQuerySearchParams();
  console.log('searchParams', searchParams);
  const navigate = useNavigate();
  const { loading, run: save } = useRequest(
    async () => {
      const fields = await form.validateFields();
      console.log(fields, '=fields');
      const imgUrls: string[] | undefined = fields?.imgUrls?.map((response: any) => response?.id);
      const paramsObjByAI = {
        images: imgUrls,
        text: fields.reason,
        reportId: searchParams.reportId,
      };
      const paramsObjBySelf = {
        images: imgUrls,
        text: fields.reason,
        id: searchParams.reportId,
      };
      const paramsObjByNormal = {
        imgUrls,
        reason: fields.reason, // 常规巡检是reason
        id: searchParams.reportId,
        status: +searchParams.status,
      };
      let paramsObj = {};
      switch (searchParams.operationType) {
        case 'FeedbackAI':
          paramsObj = paramsObjByAI;
          break;
        case 'ErrorAI':
          paramsObj = paramsObjByAI;
          break;
        case 'OverruleAI':
          paramsObj = paramsObjByAI;
          break;
        case 'AnomalyAI':
          paramsObj = paramsObjByAI;
          break;
        case 'OverruleSelf':
          paramsObj = paramsObjBySelf;
          break;
        case 'AddPollingSelf':
          paramsObj = paramsObjBySelf;
          break;
        case 'AddPollingNormal':
          paramsObj = paramsObjByNormal;
          break;
        default:
          break;
      }
      return await requestMap[searchParams?.operationType as OperationTypeEdit](paramsObj as any);
    },
    {
      manual: true,
      onSuccess: () => {
        Toast.show('提交成功');
        navigate(-1);
        // searchParams.shopId !== 'undefined'
        //   ? navigate(`/tasks/pollingAbarbeitung/paTaskList/shop?shopId=${searchParams.shopId}&detailReturn=${true}`)
        //   : navigate(-1);
        (window as any).ReactNativeWebView.postMessage(
          JSON.stringify({
            action: 'refreshRectificationTaskList',
          }),
        );
      },
    },
  );
  return (
    <PageContainer
      footer={
        <div className="py-2 px-3 bg-[#fff]">
          <Button
            color="primary"
            block
            disabled={!isFormValid || loading || upLoading}
            // disabled={false}
            loading={loading}
            onClick={() => {
              save();
            }}
          >
            提交
          </Button>
        </div>
      }
    >
      <Space
        direction="vertical"
        className="w-full text-[#141414]"
        style={{
          '--gap-vertical': '10px',
        }}
      >
        <div className=" bg-[#fff]">
          {searchParams?.auditDeadlineTime && (
            <div className="text-sm text-gray-500 p-4">
              驳回后，门店整改时间截止为
              <span className="text-red-500">{dayjs(searchParams?.auditDeadlineTime).format('MM月DD日 HH时mm分')}</span>
              ，请追踪门店在此期限前完成整改
            </div>
          )}
          <Form
            layout="vertical"
            form={form}
            onValuesChange={(_changedValues, allValues) => {
              const hasReason = !!allValues.reason;
              const hasImages = allValues.imgUrls && allValues.imgUrls.length > 0;
              setIsFormValid(hasReason || hasImages);
            }}
          >
            <Form.Item name="reason" label={<div className="text-base text-[#141414] font-medium">详细说明</div>}>
              <div>
                <TextArea
                  className="bg-[#FAFAFA] p-1"
                  placeholder="输入详细的情况说明，最多140字"
                  style={{
                    '--font-size': '14px',
                    '--color': '#B8B8B8',
                    height: '80px',
                  }}
                  maxLength={140}
                />
              </div>
            </Form.Item>

            <Form.Item
              label={<div className="text-base text-[#141414] font-medium">照片</div>}
              name="imgUrls"
              rules={[
                {
                  required: searchParams.rectifyMustUpload === 'true' && !roleTypeIsManage(),
                  message: '请上传照片',
                },
              ]}
            >
              <SystemImageUpload
                isAlbumUpImg={true}
                setUpLoading={setUpLoading}
                shopId={searchParams?.shopId}
                FileUploadType="PATROL"
              />
            </Form.Item>
          </Form>
        </div>
        <div className="py-3 px-4 flex justify-between text-base bg-[#fff] leading-[23px]">
          <span className="flex items-center">
            <IconFont type="icon-calendar-user" className="text-2xl leading-none mr-1 text-[#378BFF] mt-[-1px]" />
            <span className="mt-[1px]">处理人</span>
          </span>
          <span className="mt-[1px]">{searchParams?.watchName}</span>
        </div>
      </Space>
    </PageContainer>
  );
};

export default Edit;
