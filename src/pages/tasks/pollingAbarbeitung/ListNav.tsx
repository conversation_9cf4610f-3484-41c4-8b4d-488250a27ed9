import { List } from 'antd-mobile'
import { useNavigate } from 'react-router';
export default () => {
  const navigate = useNavigate();
  type TransferTab = {
    label: string;
    url: string;
    key: string;
  };
  const TransferTabs: TransferTab[] = [
    { label: '门店自检整改任务', url: '/subpackages/pages/issues/self/index', key: 'self' },
    { label: '到店巡检整改任务', url: '/subpackages/pages/issues/routine/index', key: 'routine' },
    { label: '视频巡检整改任务', url: '/subpackages/pages/issues/routine/index', key: 'routine' },
    { label: 'AI巡检整改任务', url: '/tasks/pollingAbarbeitung/paTaskList', key: 'ai' },
  ];
  return (
    <>
      <List>
        {TransferTabs?.map(({ label, url, key }: TransferTab) => {
          return (
            <List.Item onClick={() => {
              navigate(url);
             }}>
              {label}
            </List.Item>
          )
        })}
      </List>
    </>
  )
}