import { get, post } from '@src/api';
import {
  AIPAListParams,
  aiRectifyParams,
  getNormalPAListParams,
  getSelfPAListParams,
  selfRectifyParams,
} from './api.type';
// 获取AI巡检整改跟进列表
export const getPAListByAI = (data: AIPAListParams) => post<any>(`/om-api/ai/patrol/report/applet-list`, { data });
// 门店AI整改反馈
export const aiRectify = (data: aiRectifyParams) =>
  post<any>(`/om-api/ai/patrol/report/submit-rectification`, { data });
// 门店AI识别错误
export const aiRecognitionReject = (data: aiRectifyParams) =>
  post<any>('/om-api/ai/patrol/report/anomaly-identification', { data });
//   export const aiRecognitionError = (data: aiRectifyParams) => {
//     post<any>('patrol/ai/anomaly-identification', { data });
//   };
// AI识别错误的督导驳回
export const aiAnomalyReject = (data: aiRectifyParams) =>
  post<any>('/om-api/ai/patrol/report/reject-anomaly-identification', { data });
export const aiRectifyReject = (data: aiRectifyParams) => {
  post<any>('/om-api/ai/patrol/report/reject-rectification', { data });
};
// 督导AI整改审核通过
export const passAI = (data: { reportId: number }) => post<any>('/om-api/ai/patrol/report/confirm', { data });

// 食安稽核去学习
export const toStudy = (data: { userStudyDetailId: number }) =>
  post<any>('/om-api/shop/patrol/issue/user-study', { data });

// 查询门店员工学习情况列表
export const getStudyList = (id: number | string) => {
  return get<{ userId: number; nickname: string; phone: number; status: string }[]>(
    `/om-api/common/patrol/issue/study/user-study-list`,
    { params: { id } },
  );
};
// 学习项目列表
export const getProjectList = (id: number | string) => {
  return get<{ studyProjectId: number; studyProjectName: string; studyStatus: string }[]>(
    `/om-api/common/patrol/issue/study/user-study-project-list`,
    { params: { id } },
  );
};
// 获取自检巡检整改跟进列表
export const getPAListBySelfPolling = (data: getSelfPAListParams) =>
  post<any>(`/om-api/common/selfReport/issue/miniProgram/page`, { data });

// 自检整改督导驳回
export const overruleSelf = (data: selfRectifyParams) =>
  post<any>(`/om-api/common/patrol/self/reject-rectification`, { data });

// 自检整改督导审核通过
export const passSelf = (data: { id: number }) => post<any>('/om-api/common/patrol/self/confirm', { data });
// 门店整改反馈
export const addPollingSelf = (data: selfRectifyParams) =>
  post<any>(`/om-api/common/patrol/self/submit-rectification`, { data });

// 获取常规巡检整改跟进列表
export const getPAListByNormalPolling = (data: getNormalPAListParams) =>
  post<any>(`/om-api/patrol/issues/issue/my/list`, { data });

// 门店整改反馈
export const addPollingNormal = (data: { id: number; reason?: string; imgUrls?: any[]; status: number }) =>
  post<any>(`/om-api/patrol/issues/confirmIssuesItem`, { data });

// 自检整改任务卡片展开
export const expendSelf = (data: number) => get<any>(`/om-api/common/selfReport/issue/${data}/detail`);
