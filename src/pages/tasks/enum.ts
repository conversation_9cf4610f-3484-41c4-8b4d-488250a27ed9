export enum TaskStatusEnum {
  /** 待确认 0 */
  Waiting,
  /** 进行中 1 */
  InProgress,
  /** 已拒绝 2 */
  Rejected,
  /** 已完成 3 */
  Completed,
  /** 已终止 4 */
  Terminated,
  /** 已延期 5 */
  Delayed,
  /** 全部 6 */
  All,
  /** 待开始 7 */
  WaitToStart,
  /** 已结束 8 */
  End,
  /** 已取消 9 */
  Cancel,
  /** 去审核 examine */
  EXAMINE = 'examine',
  /** 待验收 */
  waitForCheckig = -1,
}

export enum KillTaskStatusEnum {
  /** 待分配  */
  NEW = 'NEW',
  /** 待开始 */
  WAIT = 'WAIT',
  /** 进行中 */
  RUNNING = 'RUNNING',
  /** 中止 */
  SUSPEND = 'SUSPEND',
  /**  已关闭 */
  CLOSE = 'CLOSE',
  /** 已完成 */
  COMPLETE = 'COMPLETE',
  /** 全部  */
  ALL = 'ALL',
  /** 已驳回 */
  REJECT = 'REJECT',
  /** 已取消 */
  CANCEL = 'CANCEL',
}
export enum KillTaskTypeEnum {
  /** 一道结构消杀  */
  FIRST_STRUCTURE = 'FIRST_STRUCTURE',
  /** 二道结构消杀 */
  SECOND_STRUCTURE = 'SECOND_STRUCTURE',
  /** 日常消杀 */
  DAILY = 'DAILY',
  /** 紧急消杀 */
  EXIGENCY = 'EXIGENCY',
  /** 全部  */
  ALL = 'ALL',
}

export enum TaskTypeEnum {
  /** 待确认的 */
  Waiting = 'waiting',
  /** 我处理的 */
  Principal = 'principal',
  /** 我派发的 */
  Created = 'created',
  /** 抄送我的 */
  Recipients = 'recipients',
  /** 我管辖的 */
  Under = 'under',
}
export enum WorkTypeEnum {
  /** 常规任务 */
  NormalWork = 'normalWork',
  /** 巡检 */
  Patrol = 'patrol',
  /** 自检 */
  Self = 'self',
  /** 消杀 */
  StructKill = 'structKill',
  /* 紧急消杀 */
  EmergencyStructKill = 'emergencyStructKill',
}
export enum WorkTimeTypeEnum {
  /** 日任务 */
  Day = 'day',
  /** 周任务 */
  Week = 'week',
  /** 月任务 */
  Month = 'month',
}
export const taskStatusOptions = [
  { label: '全部任务', value: TaskStatusEnum.All },
  { label: '进行中', value: TaskStatusEnum.InProgress },
  { label: '已完成', value: TaskStatusEnum.Completed },
  { label: '已拒绝', value: TaskStatusEnum.Rejected },
  { label: '已终止', value: TaskStatusEnum.Terminated },
  { label: '待验收', value: TaskStatusEnum.waitForCheckig },
];
export const killTaskStatusOptions = [
  { label: '全部任务', value: KillTaskStatusEnum.ALL },
  { label: '待分配', value: KillTaskStatusEnum.NEW },
  { label: '待开始', value: KillTaskStatusEnum.WAIT },
  { label: '进行中', value: KillTaskStatusEnum.RUNNING },
  { label: '中止', value: KillTaskStatusEnum.SUSPEND },
  { label: '已关闭', value: KillTaskStatusEnum.CLOSE },
  { label: '已完成', value: KillTaskStatusEnum.COMPLETE },
  { label: '已驳回', value: KillTaskStatusEnum.REJECT },
  { label: '已取消', value: KillTaskStatusEnum.CANCEL },
];
export const EmergencyStructKillTaskStatusOptions = [
  { label: '全部任务', value: KillTaskStatusEnum.ALL },
  { label: '待开始', value: KillTaskStatusEnum.WAIT },
  { label: '进行中', value: KillTaskStatusEnum.RUNNING },
  { label: '已完成', value: KillTaskStatusEnum.COMPLETE },
];

export const killTaskTypeOptions = [
  { label: '全部类型', value: KillTaskTypeEnum.ALL },
  { label: '一道结构消杀', value: KillTaskTypeEnum.FIRST_STRUCTURE },
  { label: '二道结构消杀', value: KillTaskTypeEnum.SECOND_STRUCTURE },
  // { label: '日常消杀', value: KillTaskTypeEnum.DAILY },
];
// 巡检计划状态枚举
export enum yytTaskPlanStatusEnum {
  待开始 = 'WAIT_START',
  进行中 = 'PROCESS',
  已结束 = 'COMPLETED',
  已取消 = 'CANCELED',
  待确认 = 'NOT_CONFIRMED',
  已确认 = 'CONFIRMED',
  待审核 = '等接口',
  执行确认中 = '等接口',
  转派中 = 'TRANSFERRING',
  执行修改审核 = 'EXE_MODIFY_AUDIT',
}
// 门店状态
export enum shopStatusEnum {
  筹备中 = 'PREPARING',
  营业中 = 'OPEN',
  歇业中 = 'OFFLINE',
  闭店中 = 'CLOSE',
  待营业 = 'TO_BE_OPENED',
}

export enum shopStatusEnumCN {
  'PREPARING' = '筹备中',
  'OPEN' = '营业中',
  'OFFLINE' = '歇业中',
  'CLOSE' = '闭店中',
  'TO_BE_OPENED' = '待营业',
}
// 自检任务枚举
export enum selfPatrolStatusEnum {
  待点评 = 1,
  已点评,
  待点评1,
  已点评1,
  // 已结束 = 'COMPLETED',
  // 已取消 = 'CANCELED',
}
// 巡检任务状态枚举
export enum yytTaskStatusEnum {
  待开始 = 1,
  进行中,
  已完成,
  已过期,
  已取消,
}
export enum yytTaskStatusCNToEnEnum {
  '待开始' = 'NOT_READY',
  '进行中' = 'PROCESS',
  '已完成' = 'COMPLETED',
  '已过期' = 'EXPIRED',
  '已取消' = 'CANCELED',
  '待确认' = 'NOT_CONFIRMED',
  '已确认' = 'CONFIRMED',
  '转派中' = 'TRANSFERRING',
  '执行修改审核' = 'EXE_MODIFY_AUDIT',
}
export const yytTaskStatusENToCNEnum: { [key: string]: string } = {
  NOT_READY: '待开始',
  PROCESS: '进行中',
  COMPLETED: '已完成',
  EXPIRED: '已过期',
  CANCELED: '已取消',
};
// 巡检方式
export enum CheckWayEnum {
  '到店巡检',
  '视频云巡检',
  '食安线下稽核',
  '食安线上稽核',
  '诊断巡检',
  '食安稽核到店辅导',
}
export enum CheckWayENToCNEnum {
  'NORMAL' = '到店巡检',
  'VIDEO' = '视频云巡检',
  'FOOD_SAFETY_NORMAL' = '食安线下稽核',
  'FOOD_SAFETY_VIDEO' = '食安线上稽核',
  'DIAGNOSTIC' = '诊断巡检',
  'FOOD_SAFETY_ARRIVE_SHOP' = '食安稽核到店辅导',
}

export enum CheckWayCNToEnEnum {
  '到店巡检' = 'NORMAL',
  '视频云巡检' = 'VIDEO',
  '食安线下稽核' = 'FOOD_SAFETY_NORMAL',
  '食安线上稽核' = 'FOOD_SAFETY_VIDEO',
  '诊断巡检' = 'DIAGNOSTIC',
  '食安稽核到店辅导' = 'FOOD_SAFETY_ARRIVE_SHOP',
}
export enum AlertColorEnum {
  '倔强青铜',
  '秩序白银',
  '尊贵铂金',
}
export enum ColorCNToEnEnum {
  '倔强青铜' = 'ORANGE',
  '秩序白银' = 'RED',
  '尊贵铂金' = 'YELLOW',
}
// 任务权限
export enum TaskPermissionEnum {
  '权限内',
  '权限外',
}
export enum TaskPermissionBooleanEnum {
  'false',
  'true',
}
// 门店类型
export enum shopTypeEnum {
  'JOIN' = '加盟M',
  'DIRECT' = '加盟T',
}

// 自检任务类型
export enum selfCheckingEnum {
  单次 = 'ONCE',
  循环 = 'CIRCULATION',
}

// 循环自检任务计划的方式
// [ EVERY_DAY, WEEKLY, PER_MONTH, CYCLE_DAY, CYCLE_WEEK, CYCLE_MONTH ]
export enum cyclePlanType {
  日循环 = 'CYCLE_DAY',
  周循环 = 'CYCLE_WEEK',
  月循环 = 'CYCLE_MONTH',
}

export enum TaskOperatorEnum {
  修改任务时间,
  终止任务,
  更新任务,
  接收任务,
  验收任务,
  修改进度反馈,
}
export const patrolDetailCard = [
  { title: '计划巡检门店数', key: 'planPatrolShopCount' },
  { title: '已巡检门店数', key: 'patrolShopCount' },
  { title: '计划报告数', key: 'planReportCount' },
  { title: '提交报告数', key: 'submitReportCount' },
  { title: '报告平均分', key: 'averageScore' },
  { title: '通过报告数', key: 'passReportCount' },
  { title: '未通过报告数', key: 'failReportCount' },
  {
    title: '门店完成率',
    key: 'shopFinishRatio',
    isFormat: true,
    formatter: '0,0.0%',
  },
  {
    title: '报告完成率',
    key: 'reportFinishRatio',
    isFormat: true,
    formatter: '0,0.0%',
  },
  {
    title: '检查项合格率',
    key: 'itemPassRatio',
    isFormat: true,
    formatter: '0,0.0%',
  },
  {
    title: '巡检通过率',
    key: 'reportPassRatio',
    isFormat: true,
    formatter: '0,0.0%',
  },
];
export enum DisinfectionTypeEnum {
  待分配 = 'NEW',
  待开始 = 'WAIT',
  中止 = 'SUSPEND',
  进行中 = 'RUNNING',
  已完成 = 'COMPLETE',
  已关闭 = 'CLOSE',
}
export const DisinfectionStatusOptions = [
  { label: '全部任务', value: 'ALL' },
  { label: '待分配', value: DisinfectionTypeEnum.待分配 },
  { label: '待开始', value: DisinfectionTypeEnum.待开始 },
  { label: '中止', value: DisinfectionTypeEnum.中止 },
  { label: '进行中', value: DisinfectionTypeEnum.进行中 },
  { label: '已完成', value: DisinfectionTypeEnum.已完成 },
  { label: '已关闭', value: DisinfectionTypeEnum.已关闭 },
];
/**
 * 门店类型
 * @param DIRECT 加盟T
 * @param JOIN 加盟M
 */
export enum shopType {
  DIRECT = '加盟T',
  JOIN = '加盟M',
}
export enum yytShopTypeEnum {
  加盟T = 'DIRECT',
  加盟M = 'JOIN',
}
export enum yytShopTagEnum {
  必检门店 = 'necessaryFlag',
  本次未巡检过的门店 = 'hasPatrol',
}
export enum sortOptionSEnum {
  上次巡检日期由远到近 = 'lastPatrolTime',
  上次巡检分数由低到高 = 'lastPatrolScore',
}
export enum patrolTypeEnum {
  '巡检计划',
  '巡检任务',
}

// 门店状态
export enum ShopStatus {
  PREPARING = 'PREPARING',
  OPEN = 'OPEN',
  OFFLINE = 'OFFLINE',
  CLOSE = 'CLOSE',
  TO_BE_OPENED = 'TO_BE_OPENED',
}

export const ShopStatusCN: Record<ShopStatus, string> = {
  [ShopStatus.PREPARING]: '筹备中',
  [ShopStatus.OPEN]: '营业中',
  [ShopStatus.OFFLINE]: '停业中',
  [ShopStatus.CLOSE]: '闭店中',
  [ShopStatus.TO_BE_OPENED]: '待营业',
};
