import { FC, useContext, useEffect, useMemo, useRef, useState } from 'react';
import { Button, Checkbox, Dropdown, Empty, SearchBar } from 'antd-mobile';
import { FormContext } from '../FormContext';
import { IconFont } from '@src/components';
import dayjs from 'dayjs';

import './index.scss';
import { useLocation, useNavigate } from 'react-router-dom';
import PageContainer from '@src/components/PageContainer';

export const shopTypeEnum: any = {
  JOIN: '加盟M',
  DIRECT: '加盟T',
};

const calcDiffDaysStr = (now: number, early: number): string => {
  const diff = dayjs(now).diff(dayjs(early));
  const oneDayStamp = 60 * 60 * 24 * 1000;
  const dayNum = Math.floor(diff / oneDayStamp);
  return dayNum === 0 ? '今天' : dayNum + '天前';
};

const Mandatory: FC = () => {
  const [searchValue, setSearchValue] = useState<string | undefined>();
  const [taskInfo, dispatch]: any = useContext(FormContext);
  const [selectIds, setSelectIds] = useState<number[]>([]);
  const [filterData, setFilterData] = useState<{
    viewId?: string;
    selectId?: string;
  }>({});
  const navigate = useNavigate();
  const orgDropDownRef = useRef<any>();
  // const location = useLocation();
  // useEffect(() => {
  //   //ios safari浏览器返回title不会更新
  //   if (location.pathname === '/tasks/patrol/create/mandatory') {
  //     document.title = '必检门店';
  //   }
  // }, [location]);

  useEffect(() => {
    setSelectIds(taskInfo?.params?.taskShopIds || []);
  }, []);

  const { orgMap, highestOrgId } = useMemo(() => {
    let map: any = {};
    let highestOrgId: string = '';
    let looShops = (nodes: any[], parentId?: string) => {
      return nodes.map(({ id, name, thirdCode }) => {
        const value = 'SHOP-' + id;
        map[value] = { value: value, label: `${thirdCode} ${name}`, parentId };
        return value;
      });
    };
    let loopOrgs = (nodes: any[], parentId?: string) => {
      let allChild: any[] = [];
      const children = nodes?.map(({ groupId, groupName, childGroups, shopDTOS }) => {
        const value = 'ORG-' + groupId;
        map[value] = { value: value, label: groupName, parentId };
        let children: any[] = [];

        let allKeys: any = [];
        if (childGroups?.length > 0) {
          const { children: sub, allChild: subAll }: any = loopOrgs(childGroups, value);
          children = children.concat(sub);
          allKeys = subAll;
          allChild = Array.from(new Set(allChild.concat(subAll.concat(sub))));
        }
        if (shopDTOS?.length > 0) {
          let subChildren = looShops(shopDTOS, value);
          children = children.concat(subChildren);
          allChild = Array.from(new Set(allChild.concat(children)));
        }

        map[value].allChild = Array.from(new Set(allKeys.concat(children)));

        map[value].children = children;

        return value;
      });
      return { children, allChild };
    };
    if (taskInfo?.needfulOrgData) {
      loopOrgs(taskInfo?.needfulOrgData);
      const { groupId } = taskInfo?.needfulOrgData?.[0];
      highestOrgId = 'ORG-' + groupId;
    }

    return { orgMap: map, highestOrgId };
  }, [taskInfo?.needfulOrgData]);

  const filterShops = useMemo(() => {
    let shops: any[] = [];

    const filterByShopKeys = filterData.selectId
      ? [filterData.selectId]
          .concat(orgMap?.[filterData.selectId]?.allChild || [])
          .filter((key: string) => key.includes('SHOP'))
      : undefined;

    taskInfo?.needfulShops?.forEach((shop: any) => {
      const { shopName, shopId } = shop;
      const label: string = shopId + ' ' + shopName;
      const newShop: any = { ...shop, label };

      if (Array.isArray(filterByShopKeys)) {
        if (!filterByShopKeys.includes('SHOP-' + shopId)) {
          return;
        }
      }

      if (searchValue) {
        label.includes(searchValue) && shops.push(newShop);
      } else {
        shops.push(newShop);
      }
    });
    return shops;
  }, [taskInfo?.needfulShops, searchValue, filterData.selectId]);

  return (
    <PageContainer
      footer={
        <div className="py-2 px-3 bg-[#fff]">
          <Button
            color="primary"
            fill="solid"
            block
            onClick={() => {
              dispatch({ type: 'onChange', key: 'taskShopIds', value: selectIds });
              navigate(-1);
            }}
          >
            确认
          </Button>
        </div>
      }
    >
      <div className="flex flex-col h-full">
        <div className="pt-2 px-3 mb-[10px] bg-[#fff]">
          <SearchBar
            value={searchValue}
            placeholder="请输入门店名称/门店编号"
            onChange={(val: any) => {
              setSearchValue(val);
            }}
            style={{
              '--height': '40px',
              '--border-radius': '8px',
              '--background': '#FAFAFA',
            }}
          />
          <div className="patrol-task-mandatory-dropdown">
            {/* TODO: */}
            {/* <OrganizationDropDown
              title={
                orgMap[filterData?.selectId || filterData?.viewId || highestOrgId]?.label ||
                '全部门店'
              }
              className="my-2"
              ref={orgDropDownRef}
            >
              <ul>
                {orgMap[filterData?.viewId || highestOrgId] && (
                  <li className="flex justify-between w-full px-4 py-[10px] items-center">
                    <div>{filterData?.viewId === highestOrgId ? '全部门店' : '全部'}</div>
                    <div>
                      {orgMap[filterData?.viewId || highestOrgId]?.parentId && (
                        <Button
                          size="mini"
                          onClick={() => {
                            setFilterData({
                              ...filterData,
                              viewId: orgMap[filterData?.viewId || highestOrgId]?.parentId,
                            });
                          }}
                        >
                          返回上级
                        </Button>
                      )}
                      <Button
                        size="mini"
                        color="primary"
                        className="ml-2"
                        onClick={() => {
                          const target = orgMap[filterData?.viewId || highestOrgId];
                          setFilterData({
                            ...filterData,
                            selectId: target?.parentId ? target?.value : undefined,
                          });
                          orgDropDownRef?.current?.close();
                        }}
                      >
                        选择
                      </Button>
                    </div>
                  </li>
                )}
                {orgMap[filterData.viewId || highestOrgId]?.children?.map((key: string) => {
                  return (
                    <li
                      key={key}
                      className="flex justify-between w-full px-4 py-[10px] items-center"
                    >
                      <div>{orgMap[key].label}</div>
                      <div className="shrink-0">
                        {orgMap[key]?.children?.length > 0 && (
                          <Button
                            size="mini"
                            onClick={() => {
                              setFilterData({
                                ...filterData,
                                viewId: orgMap[key].value,
                              });
                            }}
                          >
                            查看下级
                          </Button>
                        )}
                        <Button
                          size="mini"
                          color="primary"
                          className="ml-2"
                          onClick={() => {
                            setFilterData({
                              ...filterData,
                              selectId: orgMap[key].value,
                            });
                            orgDropDownRef?.current?.close();
                          }}
                        >
                          选择
                        </Button>
                      </div>
                    </li>
                  );
                })}
              </ul>
            </OrganizationDropDown> */}
          </div>
        </div>
        {filterShops?.length > 0 ? (
          <div className="bg-[#fff] overflow-y-scroll flex-auto h-0 ">
            <ul>
              {filterShops.map(
                ({
                  label,
                  noReformCount,
                  passRate,
                  // FIXME:缺少该字段
                  city,
                  lastReportDate,
                  score,
                  shopType,
                  shopBusinessStatus,
                  shopId,
                }: any) => {
                  return (
                    <li className="patrol-task-mandatory-item">
                      <Checkbox
                        className="text-[#5E5E5E]"
                        checked={selectIds.includes(shopId)}
                        onChange={(val) => {
                          let ids = [...selectIds];
                          if (val) {
                            ids.push(shopId);
                          } else {
                            ids = ids.filter((id: any) => id !== shopId);
                          }
                          setSelectIds(ids);
                        }}
                      >
                        <div className="flex justify-between">
                          <span className="text-base text-[#141414] font-medium ">
                            {label}
                            {shopBusinessStatus === 'PREPARING' && <span className="text-[#858585]">【筹备中】</span>}
                          </span>
                          <span className="flex mt-[2px] text-xs leading-5 w-[44px] h-5 justify-center mb-1  bg-[rgba(78, 89, 105, 0.05)] rounded-[2px] border border-solid border-[#C9CDD4]">
                            {shopTypeEnum[shopType]}
                          </span>
                        </div>
                        {/* <div className="flex items-center leading-[22px] my-1 text-sm">
                          <IconFont
                            type="icon-location-1"
                            className="text-base leading-[22px] mr-1 mt-[-1px]  text-[#5E5E5E]"
                          />
                          {city || '-'}
                        </div> */}
                        <div className="flex items-center leading-[22px]  text-sm">
                          <IconFont
                            type="icon-calendar-check"
                            className="text-base leading-[22px] mr-1 text-[#5E5E5E] mt-[-1px]"
                          />
                          {`待整改问题 ${noReformCount !== null && noReformCount >= 0 ? noReformCount + '个' : '-'}，合格率 ${
                            passRate !== null && passRate >= 0 ? passRate + '%' : '-'
                          }`}
                        </div>
                        <div className="bg-[#FAFAFA] rounded-[8px] pl-2 flex py-3 text-sm leading-none mt-2">
                          <span className="flex w-[174px] mr-8">
                            上次巡检：
                            {lastReportDate
                              ? dayjs(lastReportDate).format('MM-DD') +
                                ` (${calcDiffDaysStr(dayjs().valueOf(), lastReportDate)})`
                              : '无'}
                          </span>
                          <span>得分：{score !== null && score >= 0 ? score : '-'}</span>
                        </div>
                      </Checkbox>
                    </li>
                  );
                },
              )}
            </ul>
          </div>
        ) : (
          <Empty description="暂无数据" className="bg-[#fff]" />
        )}
      </div>
    </PageContainer>
  );
};

export default Mandatory;
