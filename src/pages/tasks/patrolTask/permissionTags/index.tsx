import { useEffect, useMemo, useState } from 'react';
import { Loading } from '@src/components';
import { IPopup, IPopupProps } from '@src/components/IPopup';
import { useSelections } from 'ahooks';
import { Button, Checkbox, SearchBar } from 'antd-mobile';

interface PermissionTagsProps extends IPopupProps {
  onConfirm: (val: number[]) => void;
  onClose: () => void;
  initValue: number[];
  tagData: { id: number; name: string }[];
}

const PermissionTags: React.FC<PermissionTagsProps> = ({
  onClose,
  onConfirm,
  tagData,
  initValue,
  title,
  visible,
  ...props
}) => {
  const [searchValue, setSearchValue] = useState<string>('');

  const { selected, isSelected, setSelected } = useSelections(
    tagData?.map((item: { id: number; name: string }) => item?.id) || [],
    [],
  );

  const dataSource = useMemo(() => {
    let dataSource = tagData;

    if (searchValue) {
      dataSource = dataSource?.filter((item: any) => {
        const searchName = item?.name.includes(searchValue);
        return searchName;
      });
    }
    return dataSource;
  }, [tagData, searchValue]);
  useEffect(() => {
    if (visible) {
      // 更新选中态
      setSelected((initValue?.map((o) => o) || []) as any);
    }
  }, [visible]);
  return (
    <IPopup
      {...props}
      visible={visible}
      title={title}
      bodyStyle={{ height: '80vh' }}
      onClose={onClose}
      footer={
        <div className="flex">
          <div className="flex-grow pt-3 pl-4 pb-[.375rem] flex flex-col justify-between">
            <div className="flex items-center text-sm gap-[.375rem]">
              已选标签:
              <span className="text-primary text-base font-medium">{selected?.length}个</span>
            </div>
          </div>
          <Button
            onClick={() => {
              onConfirm?.(selected);
              onClose?.();
            }}
            color="primary"
            fill="solid"
            disabled={!selected?.length}
            style={{ padding: '1.1875rem 2.5625rem', '--border-radius': '0px' }}
          >
            确认
          </Button>
        </div>
      }
    >
      {visible && (
        <Loading spinning={false}>
          <div className="px-4 py-3 sticky top-0 left-0 bg-white">
            <SearchBar
              placeholder="请搜索权限标签"
              style={{
                '--height': '40px',
              }}
              onSearch={(val) => {
                setSearchValue(val);
              }}
              onClear={() => {
                setSearchValue('');
              }}
            />
          </div>
          {dataSource?.map((v: { id: number; name: string }) => {
            return (
              <div
                key={v?.id}
                className="flex items-center  justify-between p-4"
                onClick={() => {
                  if (isSelected(v?.id)) {
                    setSelected((pre) => pre.filter((item) => item !== v?.id));
                  } else {
                    setSelected((pre) => [...pre, v?.id]);
                  }
                }}
              >
                <div className="text-dark text-15 grow truncate">{v?.name}</div>
                <Checkbox
                  className="mr-4 mt-1"
                  style={{
                    '--icon-size': '18px',
                    '--font-size': '24px',
                  }}
                  checked={isSelected(v?.id)}
                />
              </div>
            );
          })}
        </Loading>
      )}
    </IPopup>
  );
};

export default PermissionTags;
