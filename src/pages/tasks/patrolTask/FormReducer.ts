import { Reducer, ReducerState } from 'react';
import { SignInOrOutEnum } from './constant';

type PatrolTaskParams = {
  planName?: string;
  signType: SignInOrOutEnum;
  planType?: number[];
  directSheets?: any[];
  joinSheets?: any[];
  groupList?: any[];
  hasPreparingShop?: boolean;
};

export const initialState: ReducerState<
  Reducer<
    {
      params: PatrolTaskParams;
      checklists?: any[];
      directSheets?: any[];
      joinSheets?: any[];
      sameSheet?: any;
      needfulShops?: any[];
      needfulOrgData?: any[];
      orgLoading?: boolean; // 加载组织数据请求的loading
      orgTreeData?: any; // 组织数据缓存，避免重复请求
      scrollTop?: number;
    },
    any
  >
> = {
  params: {
    signType: SignInOrOutEnum.SYSTEM,
    // planType: [0],
    directSheets: [],
    joinSheets: [],
    groupList: [],
    hasPreparingShop: false,
  },
  sameSheet: 0,
  orgLoading: true,
};

export const reducer: any = (state: any, action: any) => {
  console.log('🚀 ~ action:', action, action?.values?.sameSheet ? { sameSheet: action.values.sameSheet } : {});
  switch (action?.type) {
    case 'onChange':
      return Object.assign({ ...state }, { params: { ...state?.params, [action?.key]: action.value } });
    case 'onFormChange':
      return Object.assign(
        { ...state },
        { params: { ...state?.params, ...action?.values } },
        action?.values?.sameSheet !== undefined ? { sameSheet: action.values.sameSheet } : {},
      );
    case 'insertChecklists':
      return { ...state, checklists: action?.checklists };
    case 'onWrapChange':
      return { ...state, [action?.key]: action.value };
  }
};
