import { FC, useContext, useState } from 'react';
import ChecklistSelect from '../../components/ChecklistSelect';
import { FormContext } from '../../FormContext';
import useQuerySearchParams from '@src/hooks/useQuerySearchParams';
import { useNavigate } from 'react-router-dom';
import { Loading } from '@src/components';
import { getCalculationDefaultWeight } from '../../api';

const Select: FC = () => {
  const [params]: any = useQuerySearchParams();
  const navigate = useNavigate();

  const [taskInfo, dispatch]: any = useContext(FormContext);

  const [loading, setLoading] = useState(false);

  return (
    <Loading spinning={loading}>
      <ChecklistSelect
        checklists={taskInfo?.checklists}
        onSelect={async (value) => {
          let oldValue = taskInfo?.[params.name] || [];
          let newValue: any;
          if (params?.index) {
            //index代表编辑
            oldValue[params.index].workSheet = value;
            newValue = oldValue;
          } else {
            newValue = oldValue.concat({ workSheet: value, weight: '0' });
          }
          setLoading(true);

          const ids = newValue.map((item: any) => {
            return item.workSheet.value;
          });
          const data = await getCalculationDefaultWeight(ids);
          data.forEach((item: any) => {
            const index = newValue.findIndex(
              (item2: any) => item2.workSheet.value === item.worksheetId,
            );

            if (index > -1) {
              newValue[index].weight = item.weight.toString();
            }
          });
          setLoading(false);
          dispatch({
            type: 'onWrapChange',
            key: params.name,
            value: newValue,
          });
          setTimeout(() => {
            navigate(-1);
          }, 100);
        }}
      ></ChecklistSelect>
    </Loading>
  );
};

export default Select;
