import { Button, Form, Modal, Picker, Radio, Space, Toast } from 'antd-mobile';
import { FC, useCallback, useContext, useEffect, useRef, useState } from 'react';
import { TaskCheckListType, TaskCheckListTypeCN } from '../constant';
import './index.scss';
import { AddCircleOutline, ForbidFill } from 'antd-mobile-icons';
import SelectButton from '../components/SelectButton';
import { useNavigate } from 'react-router-dom';
import { FormContext } from '../FormContext';
import { renderPage } from '../renderPage';
import PageContainer from '@src/components/PageContainer';

const Checklist: FC = () => {
  const [form] = Form.useForm();
  const [taskInfo, dispatch]: any = useContext(FormContext);
  const navigate = useNavigate();

  const deleteModalRef = useRef<any>();

  useEffect(() => {
    form?.setFieldValue('sameSheet', taskInfo?.sameSheet);
    form?.setFieldValue('directSheets', taskInfo?.directSheets || []);
    form?.setFieldValue('joinSheets', taskInfo?.joinSheets || []);
  }, []);

  const [pickerProps, setPickerProps] = useState<any>({
    visible: false,
  });

  const sameSheet = Form.useWatch('sameSheet', form);
  const directSheets = Form.useWatch('directSheets', form);
  const joinSheets = Form.useWatch('joinSheets', form);

  const renderChecklistFormList = useCallback(
    ({ label, name }: any) => {
      const values = form?.getFieldValue(name);
      return (
        <div className="patrol-task-card">
          <h5 className="patrol-task-title">{label}</h5>
          <div className="patrol-task-list">
            <Form.Array name={name}>
              {(fields) => {
                return fields.map(({ index }: any) => (
                  <div className="pl-4 flex items-center ">
                    {fields.length > 1 && (
                      <ForbidFill
                        className="text-[20px] text-[#F53F3F] mr-2 shrink-0"
                        onClick={() => {
                          // remove(index)
                          deleteModalRef.current = Modal.show({
                            content: (
                              <div>
                                <p className="text-center text-base font-medium text-[#141414]">
                                  是否确定删除【{values?.[index]?.workSheet?.label}】检查表
                                </p>
                                <div className="flex mt-6">
                                  <Button
                                    className="w-1/2 text-[#5E5E5E] mr-3"
                                    onClick={() => {
                                      deleteModalRef.current.close();
                                    }}
                                  >
                                    取消
                                  </Button>
                                  <Button
                                    color="primary"
                                    className="w-1/2"
                                    onClick={() => {
                                      values.splice(index, 1);
                                      form.setFieldValue(name, values);
                                      deleteModalRef.current.close();
                                    }}
                                  >
                                    删除
                                  </Button>
                                </div>
                              </div>
                            ),
                            bodyClassName: 'tdm-modal-body',
                            // closeOnAction: true,
                            // actions: [
                            //   {
                            //     key: 'sure',
                            //     text: '删除',
                            //     primary: true,
                            //     onClick: () => {
                            //       values.splice(index, 1);
                            //       form.setFieldValue(name, values);
                            //     },
                            //   },
                            //   {
                            //     key: 'cancel',
                            //     text: '取消',
                            //   },
                            // ],
                          });
                        }}
                      />
                    )}
                    <div className="flex items-center w-full justify-between py-3 pr-4 patrol-task-list-item">
                      <span
                        className="text-[#5E5E5E] text-sm leading-[22px] w-[220px] whitespace-nowrap text-ellipsis overflow-hidden"
                        onClick={() => {
                          navigate(`select?name=${name}&index=${index}`);
                        }}
                      >
                        {values?.[index]?.workSheet?.label}
                      </span>
                      {
                        <Form.Item name={[index, 'weight']} noStyle>
                          <SelectButton
                            className="shrink-0 text-sm"
                            transform={(val: any) => {
                              return val !== '0' ? val + '%' : '0%';
                            }}
                            onClick={() => {
                              setPickerProps({
                                visible: true,
                                name,
                                index,
                                value: values?.[index]?.weight,
                              });
                            }}
                          />
                        </Form.Item>
                      }
                    </div>
                  </div>
                ));
              }}
            </Form.Array>
          </div>
          <div className="patrol-task-operation">
            <span
              className="flex items-center text-sm leading-6 cursor-pointer"
              onClick={() => {
                navigate(`select?name=${name}`);
                dispatch({ type: 'onWrapChange', key: 'sameSheet', value: sameSheet });
              }}
            >
              <AddCircleOutline className="text-[20px] mr-2" />
              添加检查表
            </span>
          </div>
        </div>
      );
    },
    [directSheets, joinSheets],
  );

  return (
    <>
      <PageContainer
        footer={
          <div className="py-2 px-3 bg-[#fff]">
            <Button
              color="primary"
              fill="solid"
              block
              onClick={() => {
                if (directSheets?.length === 0) {
                  return Toast.show(
                    sameSheet === TaskCheckListType.Different
                      ? '请选择加盟T店检查表'
                      : '请选择统一检查表',
                  );
                }
                if (sameSheet === TaskCheckListType.Different && joinSheets?.length === 0) {
                  return Toast.show('请选择加盟M店检查表');
                }
                if (directSheets?.length > 1) {
                  const weightSheets = directSheets.filter(
                    ({ weight }: any) => Number(weight) !== 0,
                  );
                  if (weightSheets?.length > 0 && weightSheets?.length < directSheets.length) {
                    return Toast.show('权重填写一个，其他的都要填写');
                  }

                  if (weightSheets?.length === directSheets.length) {
                    let sum: number = 0;
                    weightSheets?.forEach(({ weight }: any) => {
                      sum += Number(weight);
                    });
                    if (sum !== 100) {
                      return Toast.show('检查表权重必须为100%');
                    }
                  }
                  const onlyChecklistIds: number[] = [];
                  if (
                    directSheets.some(({ workSheet }: any) => {
                      const { value } = workSheet;
                      if (onlyChecklistIds.includes(value)) {
                        return true;
                      } else {
                        onlyChecklistIds.push(value);
                        return false;
                      }
                    })
                  ) {
                    return Toast.show('检查表重复');
                  }
                }
                console.log({ sameSheet, directSheets, joinSheets });
                dispatch({
                  type: 'onFormChange',
                  values: { sameSheet, directSheets, joinSheets },
                });
                navigate(-1);
              }}
            >
              确认
            </Button>
          </div>
        }
      >
        <div className="overflow-y-scroll h-full">
          <Form className="patrol-task-checklist" form={form}>
            <Space
              direction="vertical"
              className="w-full"
              style={{
                '--gap-vertical': '0.625rem',
              }}
            >
              <div className="patrol-task-card">
                <h5 className="patrol-task-title">
                  类型<span className="text-[#F53F3F] ml-1">*</span>
                </h5>
                <div className="px-4 pb-1">
                  <Form.Item noStyle name="sameSheet" initialValue={TaskCheckListType.Different}>
                    <Radio.Group>
                      <Radio value={TaskCheckListType.Same}>
                        {TaskCheckListTypeCN[TaskCheckListType.Same]}
                      </Radio>
                      <Radio value={TaskCheckListType.Different}>
                        {TaskCheckListTypeCN[TaskCheckListType.Different]}
                      </Radio>
                    </Radio.Group>
                  </Form.Item>
                </div>
              </div>
              <Form.Item dependencies={['sameSheet', 'directSheets', 'joinSheets']} noStyle>
                {({ getFieldsValue }) => {
                  const { sameSheet } = getFieldsValue(['sameSheet']);
                  return (
                    <Space
                      direction="vertical"
                      className="w-full"
                      style={{
                        '--gap-vertical': '0.625rem',
                      }}
                    >
                      {renderChecklistFormList({
                        label: sameSheet === 1 ? '统一检查表' : '加盟T店检查表',
                        name: 'directSheets',
                        values: directSheets,
                      })}
                      {sameSheet === 0 &&
                        renderChecklistFormList({
                          label: '加盟M店检查表',
                          name: 'joinSheets',
                          values: joinSheets,
                        })}
                    </Space>
                  );
                }}
              </Form.Item>
            </Space>
          </Form>
        </div>
      </PageContainer>
      <Picker
        columns={[
          [{ value: '0', label: '0%' }].concat(
            Array.from({ length: 99 }).map((_: any, index: number) => {
              return { value: (index + 1).toString(), label: index + 1 + '%' };
            }),
          ),
        ]}
        visible={pickerProps?.visible}
        onClose={() => {
          setPickerProps({ visible: false });
        }}
        value={[pickerProps?.value]}
        onConfirm={(val) => {
          const { index, name }: any = pickerProps;
          const values: any = form?.getFieldValue(name);
          Object.assign(values?.[index], { weight: val?.[0] });
          form?.setFieldValue(name, values);
        }}
      />
    </>
  );
};

export default renderPage(Checklist);
