import { IconFont } from '@src/components';
import { FC } from 'react';
import classNames from 'classnames';

interface SelectButtonProps {
  value?: any;
  placeholder?: string;
  onClick?: () => void;
  transform?: (value: any) => any;
  className?: string;
}

const SelectButton: FC<SelectButtonProps> = ({
  value,
  placeholder,
  onClick,
  transform = (value) => value,
  className,
}) => {
  return (
    <div
      className={classNames([
        'flex items-center text-base cursor-pointer',
        className,
        'leading-none',
      ])}
      onClick={onClick}
    >
      {transform(value) ? (
        <span className="text-[#5E5E5E] ">{transform(value)}</span>
      ) : (
        <span className="text-[#B8B8B8] ">{placeholder}</span>
      )}
      <IconFont type="icon-chevron-right" className="text-[#B8B8B8] text-[16px] "></IconFont>
    </div>
  );
};

export default SelectButton;
