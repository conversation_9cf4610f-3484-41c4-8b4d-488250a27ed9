import { Empty, SearchBar } from 'antd-mobile';
import { FC, useMemo, useState } from 'react';
import './index.scss';

interface ChecklistSelectProps {
  checklists?: any[];
  onSelect?: (value: any) => void;
}

const ChecklistSelect: FC<ChecklistSelectProps> = ({ checklists = [], onSelect }) => {
  const [subSearchValue, setSubSearchValue] = useState<string | undefined>();

  const checklistByFilter = useMemo(() => {
    return checklists.filter(({ sheetName }: any) => {
      if (!subSearchValue) {
        return true;
      } else {
        return sheetName?.includes(subSearchValue);
      }
    });
  }, [subSearchValue, checklists]);

  return (
    <div>
      <div className="p-3">
        <SearchBar
          placeholder="请输入检查表名称"
          onChange={(val: any) => {
            setSubSearchValue(val);
          }}
          value={subSearchValue}
          className="text-[#141414]"
          style={{
            '--background': '#FAFAFA',
            '--height': '40px',
          }}
        />
      </div>
      {checklistByFilter?.length > 0 ? (
        <ul>
          {checklistByFilter.map(({ id, sheetName }: any) => {
            return (
              <li
                key={id}
                className="flex justify-between px-4 py-3 border-b border-solid border-[#F0F0F0] first:border-t"
                onClick={() => {
                  onSelect?.({ value: id, label: sheetName });
                }}
              >
                <span className="text-[#141414] text-base w-[70%] whitespace-nowrap text-ellipsis overflow-hidden">
                  {sheetName}
                </span>
              </li>
            );
          })}
        </ul>
      ) : (
        <Empty description="暂无数据" />
      )}
    </div>
  );
};

export default ChecklistSelect;
