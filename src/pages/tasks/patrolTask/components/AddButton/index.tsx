import { AddCircleOutline } from 'antd-mobile-icons';
import { FC, ReactNode } from 'react';

interface AddButtonProps {
  onClick?: () => void;
  icon?: ReactNode;
  text: string;
  disabled?: boolean;
}

const AddButton: FC<AddButtonProps> = ({ onClick, icon, text, disabled }) => {
  return (
    <div
      className={`border-t border-solid border-[#f0f0f0] pt-3 pl-4 ${disabled ? 'text-primary/50' : 'text-primary'}`}
    >
      <span
        className="flex items-center text-sm leading-6 cursor-pointer"
        onClick={() => {
          if (!disabled && onClick) {
            onClick();
          }
        }}
      >
        {icon ? icon : <AddCircleOutline className="text-[20px] mr-2" />}
        {text}
      </span>
    </div>
  );
};

export default AddButton;
