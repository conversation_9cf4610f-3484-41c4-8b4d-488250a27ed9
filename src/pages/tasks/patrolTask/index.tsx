import { useContext, useEffect, useLayoutEffect, useMemo, useReducer, useRef, useState } from 'react';
import AuthorityEnum from '@src/common/authority';
import { IconFont, Loading } from '@src/components';
import PageContainer from '@src/components/PageContainer';
import useQuerySearchParams from '@src/hooks/useQuerySearchParams';
import { userStore } from '@src/store';
import inspectStore from '@src/store/inspect';
import { formatDateToUTC } from '@src/utils/date';
import { navigator } from '@tastien/rn-bridge/lib/h5';
import { useRequest } from 'ahooks';
import { Button, DatePicker, Dialog, Form, Input, Selector, Space, Switch, TextArea, Toast } from 'antd-mobile';
import { ForbidFill } from 'antd-mobile-icons';
import dayjs from 'dayjs';
import { debounce, groupBy, values } from 'lodash';
import { observer } from 'mobx-react';
import { useNavigate } from 'react-router-dom';
import {
  createFastPatrolPlan,
  getChecklistPermissionByUser,
  getCompanyEmployeesByUserId,
  getGroupShopTree,
  getMandatoryInspectionStore,
  getPermissionTagSelectList,
  getShopCount,
  getSimpleChecklistList,
} from './api';
import AddButton from './components/AddButton';
import SelectButton from './components/SelectButton';
import { SignInOrOutCN, type SignInOrOutEnum } from './constant';
import { FormContext } from './FormContext';
import { initialState, reducer } from './FormReducer';
import PermissionTags from './permissionTags';
import { renderPage } from './renderPage';
import './index.scss';
import { queryPlanDetails } from '../api';

const today = dayjs().toDate();

const PatrolTaskIndex = () => {
  const [form] = Form.useForm();
  const [taskInfo, dispatch]: any = useContext(FormContext);
  const [searchParams]: any = useQuerySearchParams();
  const isEdit = useMemo(() => {
    return !!searchParams?.planId;
  }, [searchParams?.planId]);
  const [tagsVisible, setTagsVisible] = useState(false);
  const [editTagOptions, setEditTagOptions] = useState([]);
  console.log(taskInfo, '=taskInfo');

  const { data: planDetails } = useRequest(() => queryPlanDetails(searchParams?.planId), {
    ready: isEdit && taskInfo?.orgLoading,
    onSuccess: (data) => {
      const _groupList = values(
        groupBy(data.shops, (o) => {
          return o.inspectors;
        }),
      ).map((i: any) => {
        return {
          taskUser: {
            value: i[0]?.inspectors,
            label: i[0]?.userName,
          },
          groups: i.map((o: any) => {
            return {
              key: `${o.type}-${o.entityId}`,
              label: o.groupName,
              type: o.type === 'ORGANIZATION' ? 1 : 2,
            };
          }),
        };
      });

      const _res = {
        directSheets: data?.directSheets.map((o: any) => {
          return {
            workSheet: { value: o?.workSheetId, label: o?.workSheetName },
            weight: o?.weight,
          };
        }),
        endDate: dayjs(data?.endDate).format('YYYY-MM-DD'),
        groupList: _groupList,
        hasPreparingShop: data?.hasPreparingShop,
        joinSheets: data?.joinSheets.map((o: any) => {
          return {
            workSheet: { value: o?.workSheetId, label: o?.workSheetName },
            weight: o?.weight,
          };
        }),
        planName: data?.planName,
        planReportCount: data?.planReportCount,
        planShopCount: data?.planShopCount,
        planType: data?.planType === 'NORMAL' ? [0] : data?.planType === 'VIDEO' ? [1] : [2],
        sameSheet: data?.sameSheet ? 1 : 0,
        signType: data?.signType,
        startDate: dayjs(data?.startDate).format('YYYY-MM-DD'),
        taskShopIds: data?.taskShopIds,
        permissionLabelIds: data?.labels?.map(({ id }: { id: number }) => id) || [],
      };
      setEditTagOptions(data?.labels || []);
      form.setFieldsValue(_res);
    },
  });

  const { data: tagList } = useRequest(
    async () => {
      const res = await getPermissionTagSelectList();
      return res || [];
    },
    { refreshDeps: [] },
  );

  const permissionTagOptions = useMemo(() => {
    const mergedArr = [...(tagList || []), ...(editTagOptions || [])];
    const uniqueArr = mergedArr.filter((obj, index, self) => self.findIndex((o) => o?.id === obj?.id) === index) || [];

    return uniqueArr || [];
  }, [editTagOptions, tagList]);

  const containerRef = useRef<any>();

  const navigate = useNavigate();
  const [dataPicker, setDataPicker] = useState<{
    visible: boolean;
    formName?: string;
    min?: Date;
  }>({
    visible: false,
  });
  const startDate = Form.useWatch('startDate', form);
  const groupList = Form.useWatch('groupList', form);
  const planType = Form.useWatch('planType', form);
  const planShopCount = Form.useWatch('planShopCount', form);

  const _planType = [
    {
      label: '到店巡检',
      value: 0,
      auth: AuthorityEnum.到店巡检,
    },
    {
      label: '视频云巡检',
      value: 1,
      auth: AuthorityEnum.视频云巡检,
    },
    {
      label: '食安线下稽核',
      value: 2,
      auth: AuthorityEnum.食安线下稽核,
    },
  ].filter((i) => userStore.permissionsMap.has(i.auth));
  useEffect(() => {
    if (planType) {
      const container = document.querySelector('.adm-space-horizontal.adm-space-wrap');
      if (planType[0] === 0) {
        if (container) {
          container.scrollLeft = 0;
        }
      }
      if (planType[0] === 2) {
        if (container) {
          container.scrollLeft = container.scrollWidth;
        }
      }
    }
  }, [planType]);

  const { run } = useRequest(
    async (data) => {
      await createFastPatrolPlan(data).then(() => {
        setLoad(false);
        isEdit ? Toast.show('编辑成功') : Toast.show('创建成功');
        // refresh('/mission');
        // const url = searchParams?.prev
        //   ? searchParams?.prev
        //   : `/mission?roletype=${roleTypeIsManage() ? 'supervision' : 'manager'}`;
        // navigate(url);
        // navigate(-1);
        if (searchParams?.form === 'RN') {
          navigator.goBack();
        } else {
          navigate(-1);
        }
      });
    },
    {
      manual: true,
    },
  );
  useEffect(() => {
    if (isEdit) {
      (window as any)?.ReactNativeWebView?.postMessage(
        JSON.stringify({
          title: '编辑巡检任务计划',
        }),
      );
    }
    getSimpleChecklistList({ planType: 'PATROL' }).then((res) => {
      dispatch({ type: 'insertChecklists', checklists: res });
    });
    // orgLoading为true，则代表需要第一次请求
    taskInfo?.orgLoading &&
      getCompanyEmployeesByUserId().then((res) => {
        dispatch({ type: 'onWrapChange', key: 'orgLoading', value: false });

        inspectStore.setInspector({
          inspectors: res.map(({ userId, nickname }: any) => ({
            label: nickname,
            value: userId,
          })),
        });
      });
  }, []);

  const [mandatoryShopCount, setMandatoryShopCount] = useState(0);
  useEffect(() => {
    const groupIds: string[] = [];
    const shopIds: string[] = [];
    groupList?.forEach(({ groups }: any) => {
      groups?.forEach(({ key }: any) => {
        const _str = key.split('-');
        if (_str?.[0] === 'ORGANIZATION') {
          groupIds.push(_str?.[1]);
        }
        if (_str?.[0] === 'SHOP') {
          shopIds.push(_str?.[1]);
        }
      });
    });
    (!!groupIds.length || !!shopIds.length) &&
      getShopCount({ shopIds, groupIds }).then((res) => setMandatoryShopCount(res));

    (!!groupIds.length || !!shopIds.length) &&
      getMandatoryInspectionStore({
        groupIdList: groupIds,
        shopIdList: shopIds,
      }).then((res: any) => {
        dispatch({ type: 'onWrapChange', key: 'needfulShops', value: res });
      });
  }, [groupList]);

  useLayoutEffect(() => {
    form?.setFieldsValue({
      ...taskInfo?.params,
      planType: taskInfo?.params?.planType
        ? taskInfo?.params?.planType
        : !!_planType?.length
          ? [_planType?.[0]?.value]
          : [],
    });
    if (taskInfo?.scrollTop) {
      setTimeout(() => {
        containerRef.current.scrollTo(0, taskInfo?.scrollTop);
      }, 10);
    }
    return () => {
      dispatch({
        type: 'onWrapChange',
        key: 'scrollTop',
        value: containerRef?.current.scrollTop,
      });
      dispatch({ type: 'onFormChange', values: form?.getFieldsValue() });
    };
  }, []);

  const [Load, setLoad] = useState(false);
  const handleSubmit = useMemo(
    () =>
      debounce(() => {
        setLoad(true);
        try {
          const values = form?.getFieldsValue();
          const {
            planName,
            planType,
            groupList,
            startDate,
            endDate,
            directSheets,
            joinSheets,
            signType,
            hasPreparingShop,
            planShopCount,
            planReportCount,
            sameSheet,
            taskShopIds,
            permissionLabelIds,
          } = values;
          const _shops: any[] = [];
          groupList.forEach(({ taskUser, groups }: any) => {
            groups.forEach(({ key, type }: { key: string; type: number }) => {
              _shops.push({
                entityId: key.split('-')?.[1],
                inspectors: taskUser.value,
                type: type === 1 ? 'ORGANIZATION' : 'SHOP',
              });
            });
          });

          const payload = {
            directSheets: directSheets.map((o: any) => ({
              workSheetId: o.workSheet.value,
              weight: +o.weight ? o.weight : undefined,
            })),
            endDate: formatDateToUTC(endDate),
            hasPreparingShop,
            joinSheets: (!!sameSheet ? directSheets : joinSheets).map((o: any) => ({
              workSheetId: o.workSheet.value,
              weight: +o.weight ? o.weight : undefined,
            })),
            planName,
            planReportCount: +planReportCount,
            planShopCount: +planShopCount,
            planType: planType?.[0] === 0 ? 'NORMAL' : planType?.[0] === 1 ? 'VIDEO' : 'FOOD_SAFETY_NORMAL',
            sameSheet: !!sameSheet,
            shops: _shops,
            signType: signType ?? 'SYSTEM',
            startDate: formatDateToUTC(startDate),
            taskShopIds: !!taskShopIds ? taskShopIds : undefined,
            id: searchParams?.planId,
            reportReviewPassScore: 100, // 兼容PC端 默认100
            permissionLabelIds,
          };

          if (!planName || !planName.trim()) {
            Toast.show('请输入任务名称');
            throw new Error('请输入任务名称');
          }
          if (!planType.length) {
            Toast.show('请选择任务类型');
            throw new Error('请选择任务类型');
          }
          if (!groupList || !groupList.length) {
            Toast.show('请完善巡检人或巡检组织');
            throw new Error('请完善巡检人或巡检组织');
          }
          if (!permissionLabelIds?.length) {
            Toast.show('请选择权限标签');
            throw new Error('请选择权限标签');
          }
          if (!startDate) {
            Toast.show('请选择开始时间');
            throw new Error('请选择开始时间');
          }
          if (!endDate) {
            Toast.show('请选择结束时间');
            throw new Error('请选择结束时间');
          }

          if (!directSheets || !directSheets.length) {
            Toast.show('请选择检查表');
            throw new Error('请选择检查表');
          }

          if (!planShopCount) {
            Toast.show('请填写计划巡检门店数');
            throw new Error('请填写计划巡检门店数');
          }
          if (!planReportCount) {
            Toast.show('请填写计划巡检报告数');
            throw new Error('请填写计划巡检报告数');
          }
          if (+planShopCount > +planReportCount) {
            Toast.show('计划巡检报告数必须≥计划巡检门店数');
            throw new Error('计划巡检报告数必须≥计划巡检门店数');
          }
          if (+planShopCount > mandatoryShopCount) {
            Toast.show('计划巡检门店数应该小于等于区域内门店数');
            throw new Error('计划巡检门店数应该小于等于区域内门店数');
          }
          const userIds = values.groupList.map((item: any) => item.taskUser.value);
          const allSheets = (payload.directSheets || []).concat(payload.joinSheets || []);
          const worksheetIds = [
            ...new Set(allSheets.map((item: { workSheetId: number; weight: any }) => item.workSheetId)),
          ];
          getChecklistPermissionByUser({
            worksheetIds,
            userIds,
          }).then((res) => {
            const resultMap = new Map();
            res.forEach((item: any) => {
              const { roleName } = item;
              const { worksheetName } = item;

              if (!resultMap.has(roleName)) {
                resultMap.set(roleName, new Set());
              }
              resultMap.get(roleName).add(worksheetName);
            });
            const formattedResult = [];
            for (const [roleName, worksheetNamesSet] of resultMap.entries()) {
              const worksheetNames = Array.from(worksheetNamesSet);
              const joinedWorksheetNames = worksheetNames.join('、');
              formattedResult.push(`${roleName}：${joinedWorksheetNames}`);
            }
            // let formattedResult = ["营运通督导角色：部分可见-巡检、部分可见-哈哈哈哈", "督导（总部角色）：部分可见-巡检"]
            const arr = (data: string[]) => {
              return data.map((item: string) => {
                return <div>{item}</div>;
              });
            };
            if (res.length > 0) {
              Dialog.alert({
                // cancelText: '取消',
                confirmText: '我知道了',
                title: '以下检查表相应角色暂无权限',
                content: <div className="text-[#141414] text-base">{arr(formattedResult)}</div>,
                onConfirm: () => {
                  // run(payload)
                  // createFastPatrolPlan(payload).then(() => {
                  //   Toast.show('创建成功');
                  //   refresh('/mission');
                  //   const url = searchParams?.prev
                  //     ? searchParams?.prev
                  //     : `/mission?roletype=${roleTypeIsManage() ? 'supervision' : 'manager'}`;
                  //   navigate(url);
                  // });
                },
              });
            } else {
              run(payload);
              // createFastPatrolPlan(payload).then(() => {
              //   Toast.show('创建成功');
              //   refresh('/mission');
              //   const url = searchParams?.prev
              //     ? searchParams?.prev
              //     : `/mission?roletype=${roleTypeIsManage() ? 'supervision' : 'manager'}`;
              //   navigate(url);
              // });
            }
          });
        } catch (error) {
          setLoad(false);
        }
      }, 500),
    [form, mandatoryShopCount, run, searchParams?.planId],
  );

  return (
    <Loading spinning={Load} maskClickable={false}>
      <PageContainer
        footer={
          <div className="py-2 px-3 bg-[#fff]">
            <Button
              color="primary"
              fill="solid"
              block
              // @ts-ignore
              onClick={handleSubmit}
            >
              提交
            </Button>
          </div>
        }
      >
        <div className="overflow-y-scroll h-full noScrollbar" ref={containerRef}>
          <Form className="tdm-form" layout="horizontal" form={form}>
            <Space
              direction="vertical"
              className="w-full"
              style={{
                '--gap-vertical': '0.625rem',
              }}
            >
              <>
                <Form.Item name="planName" label="任务名称">
                  <TextArea
                    placeholder="请输入任务名称，最多20字"
                    maxLength={20}
                    rows={1}
                    autoSize={{ minRows: 1, maxRows: 2 }}
                    style={{
                      '--font-size': '16px',
                      '--text-align': 'right',
                      '--placeholder-color': '#B8B8B8',
                      minWidth: '198px',
                      lineHeight: '24px',
                      padding: '12px 0',
                    }}
                  />
                </Form.Item>
                <Form.Item name="planType" label="任务类型" disabled={isEdit && planDetails?.status === 'PROCESS'}>
                  <Selector
                    showCheckMark={false}
                    onChange={(v) => {
                      console.log(v, '=vv');
                      dispatch({
                        type: 'onFormChange',
                        values: {
                          planType: v,
                        },
                      });
                      // 巡检类型变更时，清空巡检组织数据
                      form?.setFieldValue('groupList', []);
                    }}
                    options={_planType}
                  />
                </Form.Item>
              </>
              <div className="bg-[#fff] py-3 patrol-task-group-list">
                <div className="text-[#141414] text-base font-medium px-4">巡检组织</div>
                <div className="flex px-4 pt-1 pb-4 justify-between">
                  <p className="text-sm leading-[22px] text-[#858585]">针对筹备中和待营业的门店，亦同步创建任务计划</p>
                  <Form.Item name="hasPreparingShop" noStyle>
                    <Switch
                      style={{
                        '--height': '24px',
                        '--width': '40px',
                      }}
                      disabled={isEdit && planDetails?.status === 'PROCESS'}
                    />
                  </Form.Item>
                </div>
                <Form.Array name="groupList">
                  {(fields, { remove }) => {
                    const groupList = form?.getFieldValue('groupList');

                    return fields.map(({ index }: any) => (
                      <div className="flex items-center pl-4 ">
                        {!(isEdit && planDetails?.status === 'PROCESS') && fields.length > 1 && (
                          <ForbidFill className="text-[20px] text-[#F53F3F] mr-2" onClick={() => remove(index)} />
                        )}
                        <div className="flex-auto border-t border-solid border-[#F0F0F0] py-2">
                          <Form.Item
                            label="巡检人员："
                            name={[index, 'taskUser']}
                            disabled={isEdit && planDetails?.status === 'PROCESS'}
                          >
                            <SelectButton
                              placeholder="请选择"
                              className="text-sm"
                              transform={(val: any) => val?.label}
                              onClick={() => {
                                navigate('inspector');
                                const groupListProxy: any = [...groupList];

                                inspectStore.setSelectInspectorValue(groupListProxy[index]?.taskUser);
                                inspectStore?.setInspectorCallback((value: any) => {
                                  groupListProxy[index] = { taskUser: value };
                                  dispatch({
                                    type: 'onFormChange',
                                    values: {
                                      groupList: groupListProxy,
                                      planShopCount: undefined,
                                      taskShopIds: [],
                                      planReportCount: undefined,
                                    },
                                  });
                                });
                              }}
                            />
                          </Form.Item>
                          <Form.Item
                            label="巡检组织："
                            name={[index, 'groups']}
                            disabled={isEdit && planDetails?.status === 'PROCESS'}
                          >
                            <SelectButton
                              placeholder="请选择"
                              className="text-sm"
                              transform={(groups: any) => {
                                if (groups?.length > 0) {
                                  const firstLabel = groups?.[0]?.label;
                                  if (groups?.length > 1) {
                                    return `${firstLabel} ...`;
                                  }
                                  return firstLabel;
                                }
                                return undefined;
                              }}
                              onClick={async () => {
                                if (!groupList?.[index]?.taskUser) {
                                  return Toast.show('请先选择巡检人');
                                }
                                inspectStore.setSelectOrgIds(
                                  (groupList?.[index]?.groups || []).map(({ key }: any) => key),
                                );

                                setLoad(true);
                                const res = await getGroupShopTree({
                                  userId: groupList?.[index]?.taskUser.value,
                                  privilegeCode: planType?.[0] === 0 || planType?.[0] === 2 ? 1 : 2,
                                });

                                setLoad(false);

                                const filterData = (data: any) => {
                                  const map: any = {};
                                  const filter = (options: any, parents?: any[]) => {
                                    let descendant: any[] = [];

                                    const son = options?.map((option: any) => {
                                      const parentIds = parents || [];
                                      const { id, type, shopId, name, children, headNickname } = option;
                                      const value: string =
                                        type === 1 || !type ? `ORGANIZATION-${id}` : `SHOP-${shopId}`;
                                      const label: string =
                                        type === 1 || !type
                                          ? `${name}${headNickname ? `  (${headNickname})` : ''}`
                                          : `${shopId}  ${name}`;

                                      map[value] = {
                                        children: undefined,
                                        key: value,
                                        parentIds,
                                        label,
                                        type,
                                      };
                                      if (children?.length) {
                                        const { son: subSon, descendant: subDescendant } = filter(
                                          children,
                                          parentIds?.concat([value]),
                                        );
                                        descendant = descendant.concat(subSon, subDescendant);
                                        map[value]['childIds'] = subSon;
                                        map[value]['allChildIds'] = subSon.concat(subDescendant);
                                      }

                                      return value;
                                    });

                                    return { son, descendant };
                                  };
                                  filter(data);
                                  return map;
                                };

                                const _organizationMap = filterData(res);
                                inspectStore.setInspectOrg({
                                  organizationMap: _organizationMap,
                                });
                                inspectStore.setOrganizations({
                                  organizations: res,
                                });

                                if (planType?.[0] === 0) {
                                  navigate(`organization`);
                                } else {
                                  navigate(`organization`);
                                }

                                inspectStore?.setOrganizationCallback((value: any) => {
                                  const groupListProxy: any = [...groupList];
                                  groupListProxy[index].groups = value;
                                  dispatch({
                                    type: 'onFormChange',
                                    values: {
                                      groupList: groupListProxy,
                                      planShopCount: undefined,
                                      taskShopIds: [],
                                      planReportCount: undefined,
                                    },
                                  });
                                });
                              }}
                            />
                          </Form.Item>
                        </div>
                      </div>
                    ));
                  }}
                </Form.Array>
                <AddButton
                  text="添加巡检组织"
                  onClick={() => {
                    const values = (form?.getFieldValue('groupList') || []).concat([{}]);
                    form?.setFieldValue('groupList', values);
                  }}
                  disabled={isEdit && planDetails?.status === 'PROCESS'}
                />
              </div>
              <Form.Item
                name="permissionLabelIds"
                label="权限标签"
                disabled={isEdit && planDetails?.status === 'PROCESS'}
              >
                <SelectButton
                  placeholder="请选择"
                  transform={(val: any) => {
                    if (val?.length > 0) {
                      return '已选择';
                    }
                    return undefined;
                  }}
                  onClick={() => {
                    setTagsVisible(true);
                  }}
                />
              </Form.Item>
              <>
                <Form.Item name="startDate" label="开始时间" disabled={isEdit && planDetails?.status === 'PROCESS'}>
                  <SelectButton
                    placeholder="请选择"
                    onClick={() => {
                      setDataPicker({
                        visible: true,
                        min: today,
                        formName: 'startDate',
                      });
                    }}
                  />
                </Form.Item>
                <Form.Item name="endDate" label="结束时间">
                  <SelectButton
                    placeholder="请选择"
                    onClick={() => {
                      if (!startDate) {
                        return Toast.show({
                          content: '请先选择开始时间',
                        });
                      }
                      setDataPicker({
                        visible: true,
                        min: dayjs(startDate).toDate(),
                        formName: 'endDate',
                      });
                    }}
                  />
                </Form.Item>
              </>
              <>
                <Form.Item name="directSheets" label="检查表" disabled={isEdit && planDetails?.status === 'PROCESS'}>
                  <SelectButton
                    placeholder="请选择"
                    transform={(val: any) => {
                      if (val?.length > 0) {
                        return '已选择';
                      }
                      return undefined;
                    }}
                    onClick={() => {
                      navigate('checklist');
                    }}
                  />
                </Form.Item>
                <Form.Item name="joinSheets" noStyle disabled={isEdit && planDetails?.status === 'PROCESS'} />
                <Form.Item noStyle name="sameSheet" disabled={isEdit && planDetails?.status === 'PROCESS'} />
              </>
              <>
                <Form.Item
                  name="planShopCount"
                  label="计划巡检门店数"
                  className="tdm-form-long-label"
                  disabled={isEdit && planDetails?.status === 'PROCESS'}
                >
                  <Input placeholder="请输入" type="number" />
                </Form.Item>
                <p className="text-[#858585] text-xs leading-5 px-4 pt-2 pb-[6px] flex">
                  <IconFont type="icon-warning-circle" className="text-sm  leading-5 mr-1 mt-[-1px]" />
                  {`计划巡检门店数必须≤区域内门店数(${mandatoryShopCount || 0})`}
                </p>
              </>
              <>
                <Form.Item
                  name="planReportCount"
                  label="计划巡检报告数"
                  className="tdm-form-long-label"
                  disabled={isEdit && planDetails?.status === 'PROCESS'}
                >
                  <Input placeholder="请输入" type="number" />
                </Form.Item>
                <p className="text-[#858585] text-xs leading-5 px-4 pt-2 pb-[6px] flex">
                  <IconFont type="icon-warning-circle" className="text-sm  leading-5 mr-1 mt-[-1px]" />
                  {`计划巡检报告数必须≥计划巡检门店数(${planShopCount || 0})`}
                </p>
              </>
              <Form.Item
                name="taskShopIds"
                label="必检门店(选填)"
                className="tdm-form-long-label"
                disabled={isEdit && planDetails?.status === 'PROCESS'}
              >
                <SelectButton
                  placeholder="请选择"
                  transform={(val: any) => {
                    if (val?.length > 0) {
                      return `已选${val?.length}家`;
                    }
                    return undefined;
                  }}
                  onClick={() => {
                    if (groupList?.length > 0) {
                      const groupIdList: number[] = [];
                      const shops: number[] = [];
                      const taskUser: any[] = [];
                      groupList?.forEach(({ taskUser: user, groups }: any) => {
                        taskUser.push(user?.value);
                        groups?.forEach(({ key }: any) => {
                          const [type, id] = key.split('-');
                          if (type === 'SHOP') {
                            !shops.includes(id) && shops.push(id);
                          } else {
                            !groupIdList.includes(id) && groupIdList.push(id);
                          }
                        });
                      });

                      // TODO:获取顶部筛选
                      // getOrganizationsByInspect(groupIdList).then((res: any) => {
                      //   dispatch({ type: 'onWrapChange', key: 'needfulOrgData', value: [res] });
                      // });
                    }

                    navigate('mandatory');
                  }}
                />
              </Form.Item>
              <Form.Item noStyle dependencies={['planType']}>
                {() => {
                  const planType = form?.getFieldValue('planType');

                  return (
                    !planType?.includes(1) && (
                      <Form.Item
                        name="signType"
                        label="签到/签离"
                        className="mb-[10px]"
                        disabled={isEdit && planDetails?.status === 'PROCESS'}
                      >
                        <SelectButton
                          placeholder="请选择"
                          transform={(value: SignInOrOutEnum) => SignInOrOutCN[value]}
                          onClick={() => {
                            navigate('signInOrOut');
                          }}
                        />
                      </Form.Item>
                    )
                  );
                }}
              </Form.Item>
            </Space>
          </Form>
          <DatePicker
            title="时间选择"
            visible={dataPicker?.visible}
            onClose={() => {
              setDataPicker({ visible: false });
            }}
            min={dataPicker?.min}
            onConfirm={(val) => {
              if (dataPicker?.formName && val) {
                form.setFieldValue(dataPicker?.formName, dayjs(val).format('YYYY-MM-DD'));
              }
            }}
          />
          <PermissionTags
            title="权限标签"
            visible={tagsVisible}
            initValue={form.getFieldValue('permissionLabelIds') || []}
            onClose={() => setTagsVisible(false)}
            tagData={permissionTagOptions}
            onConfirm={(val) => {
              if (val) {
                form.setFieldValue('permissionLabelIds', val);
              }
            }}
          />
        </div>
      </PageContainer>
    </Loading>
  );
};

export default renderPage(observer(PatrolTaskIndex), {
  ContextComponent: ({ children }) => {
    const [taskInfo, dispatch]: any = useReducer(reducer, initialState);

    return <FormContext.Provider value={[taskInfo, dispatch]}>{children}</FormContext.Provider>;
  },
});
