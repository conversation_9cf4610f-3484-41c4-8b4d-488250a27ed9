import { Button, Checkbox, Empty, SearchBar } from 'antd-mobile';
import { observer } from 'mobx-react';
import { FC, useMemo, useState } from 'react';
import inspectStore from '@src/store/inspect';
import { useNavigate } from 'react-router-dom';
import PageContainer from '@src/components/PageContainer';

const Inspector: FC = () => {
  const { inspectors, selectInspectorValue, inspectorCallback } = inspectStore;
  const [searchValue, setSearchValue] = useState<string | undefined>();
  const navigate = useNavigate();

  const filterInspectors = useMemo(() => {
    return searchValue
      ? inspectors?.filter(({ label }: any) => label?.includes(searchValue))
      : inspectors;
  }, [inspectors, searchValue]);
  return (
    <PageContainer
      footer={
        <div className="py-2 px-3 bg-[#fff]">
          <Button
            color="primary"
            fill="solid"
            block
            onClick={() => {
              if (inspectorCallback && typeof inspectorCallback === 'function') {
                inspectorCallback(selectInspectorValue);
                inspectStore.setSelectInspectorValue(undefined);
                navigate(-1);
              }
            }}
          >
            确认
          </Button>
        </div>
      }
    >
      <div className="bg-[#fff] flex flex-col h-full">
        <div className="py-2 px-3">
          <SearchBar
            className="tdm-search-bar"
            value={searchValue}
            placeholder="请输入巡检人"
            onChange={(val: any) => {
              setSearchValue(val);
            }}
            style={{
              '--height': '40px',
              '--border-radius': '8px',
              '--background': '#FAFAFA',
            }}
          />
        </div>
        <div className="flex-auto h-0 overflow-y-scroll">
          {filterInspectors?.length > 0 ? (
            <ul>
              {filterInspectors?.map(({ value, label }: any) => {
                return (
                  <li className="px-4  py-3 border-b  border-solid border-[#F4F4F4]" key={value}>
                    <Checkbox
                      className="tdm-checkbox-reverse"
                      style={{
                        '--gap': '0',
                      }}
                      checked={selectInspectorValue?.value === value}
                      onClick={() => {
                        inspectStore.setSelectInspectorValue({ value, label });
                      }}
                    >
                      {label}
                    </Checkbox>
                  </li>
                );
              })}
            </ul>
          ) : (
            <Empty description="暂无巡检人" />
          )}
        </div>
      </div>
    </PageContainer>
  );
};

export default observer(Inspector);
