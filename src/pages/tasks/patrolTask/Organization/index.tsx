import { FC, useCallback, useEffect, useMemo, useState } from 'react';
import { Button, Checkbox, SearchBar } from 'antd-mobile';
import { IconFont } from '@src/components';
import inspectStore from '@src/store/inspect';
import useQuerySearchParams from '@src/hooks/useQuerySearchParams';
import { useNavigate } from 'react-router-dom';
import { observer } from 'mobx-react';
import PageContainer from '@src/components/PageContainer';

const InspectOrganization: FC = () => {
  const [searchValue, setSearchValue] = useState<string | undefined>();
  const [params, setParams]: any = useQuerySearchParams();
  const {
    organizationMap,
    organizations,
    selectOrganizationIds,
    organizationCallback,
    selectOrgIds,
  } = inspectStore;
  const navigate = useNavigate();

  const filterFn = useCallback(
    (map: any) => {
      if (searchValue) {
        let searchOrgs: any[] = [];
        Object.keys(map).forEach((key: string) => {
          if (map[key].label.includes(searchValue)) {
            searchOrgs.push(map[key]);
          }
        });
        return searchOrgs;
      }
      if (params?.parentId) {
        return map[params?.parentId].childIds.map((key: string) => {
          return map[key];
        });
      } else {
        return (organizations || []).map(({ id, shopId, type }) => {
          const key = type === 1 || !type ? `ORGANIZATION-${id}` : `SHOP-${shopId}`;
          return map[key];
        });
      }
    },
    [params?.parentId, searchValue],
  );

  useEffect(() => {
    selectNodes(selectOrgIds, true);
  }, []);

  const getAllSelectNodeKeys = useCallback(
    (keys: string[]) => {
      let allKeys: string[] = [];
      let allParentKeys: string[] = [];
      keys.forEach((key: string) => {
        allKeys.push(key);
        allKeys = allKeys.concat(organizationMap[key]?.allChildIds);
        allParentKeys = allParentKeys.concat(organizationMap[key]?.parentIds);
      });

      Array.from(new Set(allParentKeys.reverse())).forEach((parentKey: string) => {
        if (
          organizationMap[parentKey]?.childIds.every((key: string) =>
            allKeys.concat(selectOrganizationIds).includes(key),
          )
        ) {
          return allKeys.push(parentKey);
        }
      });
      return allKeys;
    },
    [organizationMap, selectOrganizationIds],
  );

  const selectNodes = (keys: string[], checked: boolean) => {
    let values = selectOrganizationIds || [];
    const allKeys: string[] = getAllSelectNodeKeys(keys);
    if (checked) {
      values = values.concat(allKeys);
    } else {
      values = values.filter((key) => !allKeys.includes(key));
    }

    inspectStore?.setSelectOrganizationIds(values);
  };
  const selectValues = (keys: string[], checked: boolean) => {
    let values = selectOrgIds || [];

    if (checked) {
      keys.forEach((key: string) => {
        values = values.filter((id) => {
          return !(organizationMap[key].allChildIds || []).includes(id);
        });
      });
      values = values.concat(keys);
    } else {
      const key = keys?.[0]; //取消全选情况下，keys在同一父级下，所以只要取第一个数据

      if (!selectOrgIds.includes(key)) {
        //如果取消的时候，选中值中不包含该值，则意味着是选中了其父祖辈
        let startParentId: any;
        let targetIds = organizationMap[key].parentIds;

        if (
          targetIds.some((parentId: any) => {
            if (selectOrgIds.includes(parentId)) {
              startParentId = parentId;
              return true;
            } else {
              return false;
            }
          })
        ) {
          const loopChild = (parentId: number) => {
            const targetChildIds = organizationMap[parentId].childIds;
            const otherIds = targetChildIds.filter((id: number) => {
              if (targetIds.includes(id)) {
                loopChild(id);
                return false;
              } else {
                return true;
              }
            });

            values = values.concat(otherIds);
          };
          loopChild(startParentId);
          values.splice(values.indexOf(startParentId), 1);
        }
      }
      values = values.filter((id: any) => !keys.includes(id));
    }

    inspectStore.setSelectOrgIds(values);
  };

  const filterOrganizations = useMemo(() => {
    return filterFn(organizationMap);
  }, [params?.parentId, searchValue]);

  const { num, total } = useMemo(() => {
    const filterAble = filterOrganizations;
    const total = filterAble?.length || 0;

    const num =
      filterAble?.filter(({ key }: any) => selectOrganizationIds?.includes(key))?.length || 0;
    return { total, num };
  }, [filterOrganizations, selectOrganizationIds]);

  return (
    <PageContainer
      footer={
        <div className="py-2 px-3 bg-[#fff] flex justify-between items-center">
          <Checkbox
            indeterminate={num > 0 && num < total}
            checked={num === total && total !== 0}
            className="tdm-checkbox"
            onChange={(val: any) => {
              const ids: any = filterOrganizations
                // .filter(({ key }: any) => !organizationMap[key].disabled)
                .map(({ key }: any) => key);
              selectNodes(ids, val);
              selectValues(ids, val);
            }}
          >
            全选
          </Checkbox>
          <Button
            color="primary"
            fill="solid"
            style={{
              width: '109px',
            }}
            onClick={() => {
              if (organizationCallback && typeof organizationCallback === 'function') {
                organizationCallback(
                  selectOrgIds.map((key: string) => {
                    const { label, type } = organizationMap[key];
                    return { key, label, type };
                  }),
                );
                // navigate('/tasks/patrol/create', { replace: true });
                navigate(-1);

                inspectStore.setSelectOrganizationIds([]);
                inspectStore.setSelectOrgIds([]);
              }
            }}
          >
            确定
            {/* {`确定(${num}/${total})`} */}
          </Button>
        </div>
      }
    >
      <div className="bg-[#fff] flex flex-col h-full">
        <div className="py-2 px-3">
          <SearchBar
            className="tdm-search-bar"
            value={searchValue}
            placeholder="请输入组织或门店名称或门店编号"
            onChange={(val: any) => {
              setSearchValue(val);
            }}
            style={{
              '--height': '40px',
              '--border-radius': '8px',
              '--background': '#FAFAFA',
            }}
          />
        </div>
        <div className="flex-auto h-0 overflow-y-scroll">
          <ul>
            {filterOrganizations?.map(({ key, childIds, label, allChildIds }: any) => {
              return (
                <li
                  key={key}
                  className="flex leading-6 px-4 py-3 justify-between items-center border-b border-solid border-[#F0F0F0]"
                >
                  <Checkbox
                    className="tdm-checkbox"
                    checked={selectOrganizationIds?.includes(key)}
                    indeterminate={
                      !selectOrganizationIds?.includes(key) &&
                      allChildIds?.some((key: string) => selectOrganizationIds.includes(key))
                    }
                    onChange={(val: any) => {
                      const ids: string[] = [key];
                      selectNodes(ids, val);
                      selectValues(ids, val);
                    }}
                  >
                    {label}
                  </Checkbox>
                  {childIds?.length > 0 && (
                    <IconFont
                      type="icon-chevron-right"
                      className="text-base text-[#B8B8B8]"
                      onClick={() => {
                        setParams({ ...params, parentId: key }, { replace: true });
                      }}
                    />
                  )}
                </li>
              );
            })}
          </ul>
        </div>
      </div>
    </PageContainer>
  );
};

export default observer(InspectOrganization);
