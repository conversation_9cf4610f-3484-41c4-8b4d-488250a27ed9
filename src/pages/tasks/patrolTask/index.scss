.patrol-task-group-list {
  .adm-list-card {
    margin: 0;
    .adm-list-body {
      border-radius: 0;
      border: 0;
    }
  }
  .adm-form-item {
    border: 0;
  }
  .adm-form-item:last-child {
    border: 0;
  }
  .adm-list-item {
    padding-left: 0;
  }
  .adm-form-item.adm-form-item-horizontal .adm-list-item-content-prefix {
    padding: 4px 0;
  }
  .adm-form-item-label {
    font-size: 14px;
    line-height: 22px;
  }
  .adm-list-body-inner {
    margin-top: 0;
  }
}

.adm-space-horizontal.adm-space-wrap {
  flex-wrap: nowrap;
  overflow: scroll;
}

.noScrollbar::-webkit-scrollbar {
  display: none;
}

.noScrollbar {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}
