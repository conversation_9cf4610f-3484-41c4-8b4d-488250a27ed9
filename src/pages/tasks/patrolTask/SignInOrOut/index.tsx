import { IconFont } from '@src/components';
import { Radio, Space } from 'antd-mobile';
import { FC, useContext } from 'react';
import { useNavigate } from 'react-router-dom';
import { SignInOrOutCN, SignInOrOutEnum } from '../constant';
import { FormContext } from '../FormContext';

const SignInOrOut: FC = () => {
  const [taskInfo, dispatch]: any = useContext(FormContext);

  const navigate = useNavigate();
  return (
    <div className="tdm">
      <Radio.Group
        onChange={(val: any) => {
          dispatch?.({ type: 'onChange', key: 'signType', value: val });
          navigate(-1);
        }}
        value={taskInfo?.params.signType}
      >
        <Space
          direction="vertical"
          className="w-full"
          style={{
            '--gap-vertical': '0.625rem',
          }}
        >
          <div>
            <Radio value={SignInOrOutEnum.SIGN_NEITHER}>
              {SignInOrOutCN[SignInOrOutEnum.SIGN_NEITHER]}
            </Radio>
            <Radio value={SignInOrOutEnum.SIGN_IN}>{SignInOrOutCN[SignInOrOutEnum.SIGN_IN]}</Radio>
            <Radio value={SignInOrOutEnum.SIGN_IN_OUT}>
              {SignInOrOutCN[SignInOrOutEnum.SIGN_IN_OUT]}
            </Radio>
          </div>
          <div>
            <Radio value={SignInOrOutEnum.SYSTEM}>{SignInOrOutCN[SignInOrOutEnum.SYSTEM]}</Radio>
            <div className="flex px-4 items-center text-xs leading-5 text-[#858585] mt-2 align-middle">
              <IconFont type="icon-warning-circle" className="text-[16px] mr-1"></IconFont>
              即与系统设置保持一致,不进行单独设置
            </div>
          </div>
        </Space>
      </Radio.Group>
    </div>
  );
};

export default SignInOrOut;
