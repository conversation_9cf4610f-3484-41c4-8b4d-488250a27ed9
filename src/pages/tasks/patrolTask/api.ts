import { get, post } from '@src/api';

// 获取检查表信息
export const getSimpleChecklistList = (data: { labelIds?: number[]; sheetName?: string; planType?: string }) =>
  post('/om-api/corp/worksheet/effective/list/simple', { data });

// 获取有塔塔营运通权限得用户列表
export const getCompanyEmployeesByUserId = () => post('/cc-api/gzt/getUnderUserInfoByUserId');

// 获取组织门店树数据
export const getGroupShopTree = (data: any) =>
  post('/cc-api/gzt/shop/getIntersectionGroupShopTree', {
    data: { ...data, groupType: 2, bizType: 14, clientType: 1 },
  });

// 获取计划巡检门店数
export const getShopCount = (data: any) => post(`/cc-api/gzt/getShopCount`, { data });

// 获取计划巡检门店数
export const getMandatoryInspectionStore = (data: any) => post(`/om-api/corp/patrol/plan/shop-report_info`, { data });

// 创建快捷巡检计划
export const createFastPatrolPlan = async (data: any) => await post(`/om-api//corp/patrol/plan/save-quick`, { data });

// 查询用户集是否缺少检查表使用权限
export const getChecklistPermissionByUser = async (data: any) =>
  await post(`/om-api/corp/worksheet/users-whether-lack-user-permission`, {
    data,
  });

// 查询用户集是否缺少检查表使用权限
export const getCalculationDefaultWeight = async (worksheetIds: number[]) =>
  await post(`/om-api/corp/worksheet/calculation-default-weight`, {
    data: { worksheetIds },
  });

// 获取权限标签下拉列表
export const getPermissionTagSelectList = async () => await get(`/om-api/corp/permission-label/list/by-permission`, {});
