export enum SignInOrOutEnum {
  SIGN_NEITHER = 'SIGN_NEITHER',
  SIGN_IN = 'SIGN_IN',
  SIGN_IN_OUT = 'SIGN_IN_OUT',
  SYSTEM = 'SYSTEM',
}

export const SignInOrOutCN: Record<SignInOrOutEnum, string> = {
  [SignInOrOutEnum.SIGN_NEITHER]: '不需要',
  [SignInOrOutEnum.SIGN_IN]: '需要签到',
  [SignInOrOutEnum.SIGN_IN_OUT]: '需要签到和签离',
  [SignInOrOutEnum.SYSTEM]: '跟随系统设置',
};

export enum TaskCheckListType {
  Different = 0,
  Same = 1,
}

export const TaskCheckListTypeCN: Record<TaskCheckListType, string> = {
  [TaskCheckListType.Different]: '加盟M店、加盟T店采用不同的检查表',
  [TaskCheckListType.Same]: '所有门店使用统一的检查表',
};
