import dayjs from 'dayjs';
import { TaskOperatorEnum, TaskStatusEnum } from './enum';
import { userStore } from '@src/store';

export function getDateStatusNode(
  status: TaskStatusEnum,
  taskDeadline: string,
  taskUpdateTime: string | null,
) {
  // 大于0都是延期
  const dayDiff =
    status === TaskStatusEnum.Completed
      ? dayjs(taskUpdateTime).diff(taskDeadline, 'd')
      : dayjs().diff(taskDeadline, 'd');
  const hoursDiff =
    status === TaskStatusEnum.Completed
      ? dayjs(taskUpdateTime).diff(taskDeadline, 'hour')
      : dayjs().diff(taskDeadline, 'hour');
  const minutesDiff =
    status === TaskStatusEnum.Completed
      ? dayjs(taskUpdateTime).diff(taskDeadline, 'minute')
      : dayjs().diff(taskDeadline, 'minute');
  if (Math.abs(minutesDiff) < 60) {
    // 分钟
    if (minutesDiff > 0) {
      return (
        <span className="text-[#FF7B29]">
          (延期{minutesDiff}分钟{status === TaskStatusEnum.Completed && '完成'})
        </span>
      );
    } else {
      if (status === TaskStatusEnum.Completed) {
        return <span>(如期完成)</span>;
      } else {
        return (
          <span className={status === TaskStatusEnum.Terminated ? '' : 'text-primary'}>
            (剩余{Math.abs(minutesDiff)}分钟)
          </span>
        );
      }
    }
  }
  if (Math.abs(hoursDiff) < 24) {
    // 小时
    if (hoursDiff > 0) {
      return (
        <span className="text-[#FF7B29]">
          (延期{hoursDiff}小时{status === TaskStatusEnum.Completed && '完成'})
        </span>
      );
    } else {
      if (status === TaskStatusEnum.Completed) {
        return <span>(如期完成)</span>;
      } else {
        return (
          <span className={status === TaskStatusEnum.Terminated ? '' : 'text-primary'}>
            (剩余{Math.abs(hoursDiff)}小时)
          </span>
        );
      }
    }
  }
  // 天
  if (dayDiff > 0) {
    return (
      <span className="text-[#FF7B29]">
        (延期{dayDiff}天{status === TaskStatusEnum.Completed && '完成'})
      </span>
    );
  } else {
    if (status === TaskStatusEnum.Completed) {
      return <span>(如期完成)</span>;
    } else if (Math.abs(dayDiff) <= 7) {
      return (
        <span className={status === TaskStatusEnum.Terminated ? '' : 'text-primary'}>
          (剩余{Math.abs(dayDiff)}天)
        </span>
      );
    }
  }
}

/**
 * 只判读的任务状态：已延期+进行中+待确认+待验收（待确认任务不能更新任务进度）
 * 创建人：修改任务时间+终止任务
 * 处理人：更新任务进度
 * 处理人（待确认的任务）：接收、拒绝任务
 * 验收任务
 */

export const taskOperatorHandler = (
  taskStatus?: TaskStatusEnum,
  taskCreator?: { userId: number },
  taskPrincipal?: { userId: number },
) => {
  const { userId } = userStore.getUserInfo();
  const taskOperatorArr: TaskOperatorEnum[] = [];

  if (taskStatus !== undefined && !!taskCreator && !!taskPrincipal) {
    // 验收任务
    if (taskStatus === TaskStatusEnum.waitForCheckig) {
      taskOperatorArr.push(TaskOperatorEnum['验收任务']);
    }

    if (
      taskStatus === TaskStatusEnum.Delayed ||
      taskStatus === TaskStatusEnum.InProgress ||
      taskStatus === TaskStatusEnum.Waiting
    ) {
      // 创建人
      taskCreator.userId === userId &&
        taskOperatorArr.push(TaskOperatorEnum['修改任务时间'], TaskOperatorEnum['终止任务']);

      // 处理人
      if (taskStatus === TaskStatusEnum.Waiting) {
        taskPrincipal.userId === userId && taskOperatorArr.push(TaskOperatorEnum['接收任务']);
      } else {
        taskPrincipal.userId === userId &&
          taskOperatorArr.push(TaskOperatorEnum['更新任务'], TaskOperatorEnum['修改进度反馈']);
      }
    }
  }

  return taskOperatorArr;
};
