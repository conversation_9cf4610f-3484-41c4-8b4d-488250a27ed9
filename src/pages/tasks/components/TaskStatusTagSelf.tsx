import { HTMLAttributes } from 'react';
import { selfPatrolStatusEnum, yytTaskStatusEnum } from '../enum';
import { log } from 'echarts/types/src/util/log.js';
interface SelfPatrolTagProps extends HTMLAttributes<HTMLSpanElement> {
  status: selfPatrolStatusEnum | yytTaskStatusEnum; // 任务状态
}

export const TaskStatusTagSelf = ({ status, style, className, ...props }: SelfPatrolTagProps) => {
  console.log('status',status);
  //暂时这样乱写吧 3月14日
  const taskStatusMap = {
    [selfPatrolStatusEnum.待点评]: {
      bg: 'rgba(0, 187, 180, 0.2)',
      color: 'white',
      text: '待点评',
    },
    [selfPatrolStatusEnum.已点评]: {
      bg: 'rgba(0, 187, 180, 0.2)',
      color: 'white',
      text: '已点评',
    },
    [selfPatrolStatusEnum.待点评1]: {
      bg: '#2DA55D',
      color: 'white',
      text: '已点评',
    },
    [selfPatrolStatusEnum.已点评1]: {
      bg: 'rgba(0, 187, 180, 0.2)',
      color: 'white',
      text: '已点评1',
    },
    // [selfPatrolStatusEnum.待开始]: {
    //   bg: 'none',
    //   color: '#00BBB4',
    //   text: '待开始',
    //   border: '1px solid #00BBB4',
    // },
    // [yytTaskStatusEnum.待开始]: {
    //   bg: 'none',
    //   color: '#00BBB4',
    //   text: '待开始',
    //   border: '1px solid #00BBB4',
    // },
    // [selfPatrolStatusEnum.已结束]: {
    //   bg: 'rgba(0, 187, 180, 0.2)',
    //   color: 'white',
    //   text: '已结束',
    // },
    // [yytTaskStatusEnum.已过期]: {
    //   bg: 'rgba(0, 187, 180, 0.2)',
    //   color: 'white',
    //   text: '已过期',
    // },
    // [selfPatrolStatusEnum.已取消]: {
    //   bg: 'rgba(0, 0, 0, 0.05)',
    //   color: '#858585',
    //   text: '已取消',
    // },
    // [yytTaskStatusEnum.已取消]: {
    //   bg: 'rgba(0, 0, 0, 0.05)',
    //   color: '#858585',
    //   text: '已取消',
    // },
  };
  console.log('bg颜色',(taskStatusMap as any)[1]);
  const { bg, color, text, ...other } = (taskStatusMap as any)[status];
  
  return (
    <span
      {...props}
      className={`inline-block px-1 py-[3px] rounded-sm text-white text-xs leading-[12px] ${
        className || ''
      }`}
      style={{ backgroundColor: bg, color: color, ...other, ...style }}
      // @ts-ignore
      attribute="tag"
    >
      {text}
    </span>
  );
};
