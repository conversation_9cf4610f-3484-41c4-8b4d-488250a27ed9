import { CheckOutline } from 'antd-mobile-icons';
import styles from './index.module.scss';
import { Drawer, DrawerProps } from 'antd';
import { SortEnum } from '@src/common/api.type';

const options = [
  { label: '截止时间最近', value: SortEnum.DESC },
  { label: '截止时间最远', value: SortEnum.ASC },
];

interface SortFilterProps extends DrawerProps {
  value: SortEnum;
  onChange: (value: SortEnum) => void;
}

const SortFilter: React.FC<SortFilterProps> = ({ value, onChange, ...props }) => {
  return (
    <Drawer
      {...props}
      placement="top"
      getContainer={() => document.getElementById('topBar')!}
      rootClassName={styles.drawer}
      closable={false}
      height="auto"
    >
      <div className="divide-y divide-[rgba(0,0,0,0.03)] px-4 bg-[#F7F7F7] text-sm leading-[14px] text-[#5E5E5E]">
        {options.map((i) => {
          const checked = value === i.value;
          return (
            <div
              key={i.value}
              className={`flex justify-between py-4 ${checked ? 'text-primary' : ''}`}
              onClick={() => onChange(i.value)}
            >
              {i.label}
              {checked && <CheckOutline className="text-base" />}
            </div>
          );
        })}
      </div>
    </Drawer>
  );
};

export default SortFilter;
