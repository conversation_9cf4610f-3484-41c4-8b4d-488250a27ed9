import { HTMLAttributes } from 'react';
import { DisinfectionTypeEnum } from '../enum';
interface DsinfectionTagProps extends HTMLAttributes<HTMLSpanElement> {
  status: DisinfectionTypeEnum; // 任务状态
}

export const DsinfectionStatusTag = ({
  status,
  style,
  className,
  ...props
}: DsinfectionTagProps) => {
  console.log('888status', status);
  const taskStatusMap = {
    [DisinfectionTypeEnum.进行中]: {
      bg: 'var(--adm-color-primary)',
      color: 'white',
      text: '进行中',
    },
    [DisinfectionTypeEnum.待开始]: {
      bg: 'none',
      color: '#378bff',
      text: '待开始',
      border: '1px solid #378bff',
    },

    [DisinfectionTypeEnum.已关闭]: {
      bg: 'rgba(0, 187, 180, 0.2)',
      color: 'white',
      text: '已关闭',
    },
    [DisinfectionTypeEnum.中止]: {
      bg: 'rgba(0, 187, 180, 0.2)',
      color: 'white',
      text: '中止',
    },
    [DisinfectionTypeEnum.待分配]: {
      bg: 'none',
      color: '#378bff',
      text: '待分配',
      border: '1px solid #378bff',
    },
    [DisinfectionTypeEnum.已完成]: {
      bg: 'rgba(0, 187, 180, 0.2)',
      color: 'white',
      text: '已完成',
    },
  };
  const { bg, color, text, ...other } = (taskStatusMap as any)?.[status] || {};
  return (
    <span
      {...props}
      className={`inline-block px-1 py-[3px] rounded-sm text-white text-xs leading-[12px] ${
        className || ''
      }`}
      style={{ backgroundColor: bg, color: color, ...other, ...style }}
      // @ts-ignore
      attribute="tag"
    >
      {text}
    </span>
  );
};
