import { DrawerProps } from 'antd';
import { FilterDrawer } from './FilterDrawer';
import { useState } from 'react';
import { WorkSheet } from './WorkSheet';
import { AllUserSelect } from './AllUserSelect';
import { CheckWayEnum, patrolTypeEnum, yytTaskPlanStatusEnum, yytTaskStatusEnum } from '../enum';

interface UnderPatrolFilterProps extends DrawerProps {
  value: Record<string, any>;
  onChange: (value: Record<string, any>) => void;
  onClose: () => void;
  patrolType: patrolTypeEnum;
}

export const init = {
  0: {
    status: undefined,
    planType: undefined,
    patrolUserIds: undefined,
  },
  1: {
    status: undefined,
    subType: undefined,
    patrolUserIds: undefined,
    workSheetId: undefined,
  },
};

export const UnderPatrolFilter = ({
  value,
  onChange,
  patrolType,
  onClose,
  ...props
}: UnderPatrolFilterProps) => {
  const [activity, setActivity] = useState<any>(value);
  const onOk = () => {
    onChange(activity);
  };
  const onClear = () => {
    setActivity(init[patrolType]);
  };
  return (
    <FilterDrawer
      {...props}
      onClose={() => {
        // 重置一下activity
        setActivity(value);
        onClose();
      }}
      onOk={onOk}
      onClear={onClear}
    >
      <div className="flex flex-col gap-6">
        <div className="flex flex-col gap-3">
          <h3 className="text-[#141414] text-sm leading-[14px]">巡检状态</h3>
          {patrolType === patrolTypeEnum.巡检计划 && (
            <div className="flex flex-wrap gap-2">
              {[
                { label: '待开始', value: yytTaskPlanStatusEnum.待开始 },
                { label: '进行中', value: yytTaskPlanStatusEnum.进行中 },
                { label: '已结束', value: yytTaskPlanStatusEnum.已结束 },
                { label: '已取消', value: yytTaskPlanStatusEnum.已取消 },
              ].map((item) => {
                return (
                  <button
                    key={item.value}
                    className={`w-20 h-[30px] rounded ${
                      activity?.status ===
                      yytTaskPlanStatusEnum[item.label as keyof typeof yytTaskPlanStatusEnum]
                        ? 'text-primary bg-primary/10'
                        : 'text-58 bg-black/[0.03]'
                    }  text-sm left-[14px]`}
                    onClick={() => {
                      setActivity((pre: any) => {
                        if (activity?.status === item.value) {
                          return {
                            ...pre,
                            status: undefined,
                          };
                        } else {
                          return {
                            ...pre,
                            status: item.value,
                          };
                        }
                      });
                    }}
                  >
                    {item.label}
                  </button>
                );
              })}
            </div>
          )}

          {patrolType === patrolTypeEnum.巡检任务 && (
            <div className="flex flex-wrap gap-2">
              {[
                { label: '待开始', value: yytTaskStatusEnum.待开始 },
                { label: '进行中', value: yytTaskStatusEnum.进行中 },
                { label: '已完成', value: yytTaskStatusEnum.已完成 },
              ].map((item) => {
                return (
                  <button
                    key={item.value}
                    className={`w-20 h-[30px] rounded ${
                      activity?.status ===
                      yytTaskStatusEnum[item.label as keyof typeof yytTaskStatusEnum]
                        ? 'text-primary bg-primary/10'
                        : 'text-58 bg-black/[0.03]'
                    }  text-sm left-[14px]`}
                    onClick={() => {
                      setActivity((pre: any) => {
                        if (activity?.status === item.value) {
                          return {
                            ...pre,
                            status: undefined,
                          };
                        } else {
                          return {
                            ...pre,
                            status: item.value,
                          };
                        }
                      });
                    }}
                  >
                    {item.label}
                  </button>
                );
              })}
            </div>
          )}
        </div>
        <div className="flex flex-col gap-3">
          <h3 className="text-[#141414] text-sm leading-[14px]">巡检类型</h3>
          <div className="flex flex-wrap gap-2">
            {[
              { label: '到店', value: CheckWayEnum.到店 },
              { label: '视频云', value: CheckWayEnum.视频云 },
            ].map((item) => {
              return (
                <button
                  key={item.value}
                  className={`w-20 h-[30px] rounded ${
                    activity?.[patrolType === patrolTypeEnum.巡检计划 ? 'planType' : 'subType'] ===
                    CheckWayEnum[item.label as keyof typeof CheckWayEnum]
                      ? 'text-primary bg-primary/10'
                      : 'text-58 bg-black/[0.03]'
                  }  text-sm left-[14px]`}
                  onClick={() => {
                    setActivity((pre: any) => {
                      if (
                        activity?.[
                          patrolType === patrolTypeEnum.巡检计划 ? 'planType' : 'subType'
                        ] === item.value
                      ) {
                        return {
                          ...pre,
                          [patrolType === patrolTypeEnum.巡检计划 ? 'planType' : 'subType']:
                            undefined,
                        };
                      } else {
                        return {
                          ...pre,
                          [patrolType === patrolTypeEnum.巡检计划 ? 'planType' : 'subType']:
                            item.value,
                        };
                      }
                    });
                  }}
                >
                  {item.label}巡检
                </button>
              );
            })}
          </div>
        </div>
        <AllUserSelect
          value={activity.patrolUserIds}
          onChange={(val: number) => {
            setActivity((pre: any) => ({
              ...pre,
              patrolUserIds: val,
            }));
          }}
        />
        {patrolType === patrolTypeEnum.巡检任务 && (
          <WorkSheet
            taskType={1}
            value={activity.workSheetId}
            onChange={(val: number) => {
              setActivity((pre: any) => ({
                ...pre,
                workSheetId: val,
              }));
            }}
          />
        )}
      </div>
    </FilterDrawer>
  );
};
