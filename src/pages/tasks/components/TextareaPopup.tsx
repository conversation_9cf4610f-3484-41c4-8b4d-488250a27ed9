import { useEffect, useState } from 'react';
import { IPopup, IPopupProps } from '@src/components/IPopup';
import { Button, TextArea, Toast } from 'antd-mobile';

interface TextareaPopupProps extends IPopupProps {
  okText: string;
  onOk: (value: string) => void;
  defaultValue?: string;
  maxLength?: number;
  placeholder?: string;
}

const TextareaPopup: React.FC<TextareaPopupProps> = ({
  title,
  visible,
  okText,
  defaultValue,
  onOk,
  onClose,
  maxLength = 100,
  placeholder = `请填写${title}`,
  ...props
}) => {
  const [value, setValue] = useState('');

  useEffect(() => {
    if (visible) {
      setValue(defaultValue || '');
    }
  }, [defaultValue, visible]);

  return (
    <IPopup
      {...props}
      visible={visible}
      title={title}
      onClose={onClose}
      footer={
        <div className="flex p-3 gap-3">
          <Button block color="primary" fill="outline" className="h-[45px] text-base" onClick={onClose}>
            取消
          </Button>
          <Button
            block
            color="primary"
            className="h-[45px] text-base"
            onClick={() => {
              if (value) {
                onOk(value);
              } else {
                Toast.show(`请填写${title}`);
              }
            }}
          >
            {okText}
          </Button>
        </div>
      }
    >
      <div className="px-4 py-6">
        <TextArea
          value={value}
          placeholder={placeholder}
          rows={6}
          style={{ '--font-size': '13px' }}
          className="bg-[#F6F6F6] p-3 rounded"
          onChange={setValue}
          maxLength={maxLength}
        />
      </div>
    </IPopup>
  );
};

export default TextareaPopup;
