import { Drawer, DrawerProps } from 'antd';
import { CheckOutline } from 'antd-mobile-icons';
import styles from './index.module.scss';
import { TaskStatusEnum, taskStatusOptions } from '../../enum';

interface TaskStatusFilterProps extends DrawerProps {
  value?: TaskStatusEnum;
  onChange: (value: TaskStatusEnum) => void;
  needWaitingOption?: boolean;
}

const TaskStatusFilter: React.FC<TaskStatusFilterProps> = ({
  value,
  onChange,
  needWaitingOption = false,
  ...props
}) => {
  return (
    <Drawer
      {...props}
      placement="top"
      getContainer={() => document.getElementById('topBar')!}
      rootClassName={styles.drawer}
      closable={false}
      height="auto"
    >
      <div className="divide-y divide-[rgba(0,0,0,0.03)] px-4 bg-[#F7F7F7] text-sm leading-[14px] text-[#5E5E5E]">
        {taskStatusOptions.map((i) => {
          const checked = value === i.value;
          return (
            <div
              key={i.label}
              className={`flex justify-between py-4 ${checked ? 'text-primary' : ''}`}
              onClick={() => onChange(i.value)}
            >
              {i.label}
              {checked && <CheckOutline className="text-base" />}
            </div>
          );
        })}
        {needWaitingOption && (
          <div
            key="待确认"
            className={`flex justify-between py-4 ${value === TaskStatusEnum.Waiting ? 'text-primary' : ''}`}
            onClick={() => onChange(TaskStatusEnum.Waiting)}
          >
            待确认
            {value === TaskStatusEnum.Waiting && <CheckOutline className="text-base" />}
          </div>
        )}
      </div>
    </Drawer>
  );
};

export default TaskStatusFilter;
