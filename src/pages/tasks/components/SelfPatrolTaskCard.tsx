import { IconFont } from '@src/components';
import dayjs from 'dayjs';
import { RightOutline } from 'antd-mobile-icons';
import { useNavigate } from 'react-router-dom';
import { SwipeAction, Toast } from 'antd-mobile';
import { useState } from 'react';
import { userStore } from '@src/store';
import { CardHead } from './PrincipalTasksCard/PrincipalPatrolSchemeCard';
import { shopTypeEnum, yytTaskStatusEnum } from '../enum';
import { TASK_STATUS } from '../../patrol/enum';
import { getNormalTaskList } from '../api.type';
// import { TaskStatusTag } from './TaskStatusTag';
import { TaskStatusTagSelf } from './TaskStatusTagSelf';
import UserAvatar from '@src/components/UserAvatar';
import { cancelTask } from '@src/pages/patrol/api';
// import { SelfPatrolTag } from './TaskStatusTagSelf';
interface PrincipalPatrolTaskCardProps {
  initial: getNormalTaskList;
}
const SelfPatrolTaskCard: React.FC<PrincipalPatrolTaskCardProps> = ({
  initial,
}: PrincipalPatrolTaskCardProps) => {
  const {
    yytUserInfo: { userId },
  } = userStore;
  // console.log('initial999', initial);
  const isSelf = initial.userId === userId;
  console.log(isSelf, 'initial.userId === userId');
  const { status, taskId } = initial;
  const [curStatus, setCurStatus] = useState<number>(status);
  const navigate = useNavigate();
  const handleClick = (item: { label: string; key: string }) => {
    if (item.label === '未整改问题' && initial?.reportDetail?.noReformCount > 0) {
      navigate(`/tasks/pollingAbarbeitung/${initial?.reportDetail?.reportId}/${initial?.cross}`);
    }
  };
  return (
    <SwipeAction
      // rightActions={
      //   [TASK_STATUS.canceled, TASK_STATUS.completed, TASK_STATUS.expired].includes(curStatus)
      //     ? []
      //     : isSelf
      //     ? [
      //         {
      //           key: 'cancel',
      //           text: (
      //             <div className="text-xs">
      //               <div>
      //                 <IconFont className="text-base" type="icon-trash-2" />
      //               </div>
      //               取消任务
      //             </div>
      //           ),
      //           color: 'danger',
      //         },
      //       ]
      //     : []
      // }
      onAction={async (e) => {
        if (e.key === 'cancel') {
          await cancelTask(taskId);
          Toast.show({
            icon: 'success',
            content: '取消成功',
          });
          setCurStatus(TASK_STATUS.canceled);
        }
      }}
    >
      <div className="divide-y-[1px] divide-black/[0.03] flex flex-col bg-white rounded-lg px-1 pt-3">
        <div>
          <div className="flex justify-start">
            <TaskStatusTagSelf status={curStatus} />
            <div className="ml-2">门店名称</div>
          </div>
          <div className="rounded py-2 flex flex-col gap-y-1  mb-2 ">
            <h3 className="text-[#141414] flex ">
              <span className="text-sm leading-[22px]">{'全国每日效期'}</span>
            </h3>
            <div className="flex items-center">
              <UserAvatar username={'苏满胜'} avatarUrl={''} />
              <span className="ml-2 text-[#5E5E5E] text-xs leading-[12px]">
                {/* {submitedUserName} */}
                苏满胜提交问题
              </span>
            </div>
          </div>
        </div>
        {/* bg-current */}
        <div className=" text-13 leading-[21px] text-85 flex justify-between items-center py-1">
          <div className="rounded-sm border border-[#DCDCDC] px-2"> 全国每日效期检查</div>
          <div className="bg-[#378BFF] text-[#FFFFFF] text-xs px-3.5 rounded py-1.5">去点评</div>
        </div>
      </div>
    </SwipeAction>
  );
};
export default SelfPatrolTaskCard;
