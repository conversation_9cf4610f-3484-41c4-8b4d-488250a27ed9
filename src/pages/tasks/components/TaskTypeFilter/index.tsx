import { CheckOutline } from 'antd-mobile-icons';
import styles from './index.module.scss';
import { Drawer, DrawerProps } from 'antd';
import { Button } from 'antd-mobile';
import { useEffect, useState } from 'react';

interface TaskTypeFilterProps extends DrawerProps {
  value: number[];
  options?: { label: string; value: number }[];
  onChange: (value: number[]) => void;
}

const TaskTypeFilter: React.FC<TaskTypeFilterProps> = ({
  open,
  value: propsValue,
  options,
  onChange,
  ...props
}) => {
  const [value, setValue] = useState<number[]>(propsValue);

  useEffect(() => {
    if (open) {
      setValue(propsValue);
    }
  }, [open]);

  return (
    <Drawer
      {...props}
      placement="top"
      getContainer={() => document.getElementById('topBar')!}
      rootClassName={styles.drawer}
      closable={false}
      height="auto"
      open={open}
    >
      <div className="overflow-y-auto divide-y divide-[rgba(0,0,0,0.03)] px-4 bg-[#F7F7F7] text-sm leading-[14px] text-[#5E5E5E] max-h-[50vh]">
        {options?.map((i) => {
          const checked = value.includes(i.value);
          return (
            <div
              key={i.value}
              className={`flex justify-between py-4 ${checked ? 'text-primary' : ''}`}
              onClick={() => {
                setValue(checked ? value.filter((val) => val !== i.value) : value.concat(i.value));
              }}
            >
              {i.label}
              {checked ? (
                <div className="w-4 h-4 bg-primary rounded-full flex justify-center items-center border border-primary">
                  <CheckOutline className="text-white text-xs" />
                </div>
              ) : (
                <div className="w-4 h-4 rounded-full border border-[rgba(0,0,0,0.3)]" />
              )}
            </div>
          );
        })}
      </div>
      <div className="flex p-4 gap-3">
        <Button
          block
          color="primary"
          fill="outline"
          className="h-[42px] text-sm"
          onClick={() => onChange([])}
        >
          重置
        </Button>
        <Button block color="primary" className="h-[42px] text-sm" onClick={() => onChange(value)}>
          确定
        </Button>
      </div>
    </Drawer>
  );
};

export default TaskTypeFilter;
