import { RightOutline } from 'antd-mobile-icons';
import {
  CheckWayEnum,
  TaskPermissionEnum,
  yytTaskPlanStatusEnum,
  yytTaskStatusEnum,
} from '../../enum';
import { OutlineTag } from '../OutlineTag';
import styles from './index.module.scss';
import { ProgressBar } from 'antd-mobile';
import dayjs from 'dayjs';
import { useRequest } from 'ahooks';
import cn from 'classnames';
import { getNormalTaskPlanListItem } from '../../api.type';
import { geTaskCompleteInfo } from '../../api';
import { TaskStatusTag } from '../TaskStatusTag';

interface PrincipalPatrolSchemeCardProps {
  initial: getNormalTaskPlanListItem;
  filterPermission?: boolean;
  isNeedUserId?: boolean;
  toNavigate?: () => void;
}

export function CardHead({
  status,
  planType,
  name,
  cross,
}: {
  status: yytTaskPlanStatusEnum | yytTaskStatusEnum;
  planType?: CheckWayEnum;
  name: string;
  cross?: TaskPermissionEnum;
}) {
  return (
    <div className={cn('text-sm leading-[22px] font-medium text-dark', styles.tag)}>
      <TaskStatusTag status={status} />
      {planType !== undefined && <OutlineTag text={CheckWayEnum[planType]} />}
      {cross !== undefined && <OutlineTag text={TaskPermissionEnum[cross]} />}
      <span className="ml-1">{name}</span>
    </div>
  );
}

export const PrincipalPatrolSchemeCard = ({
  initial,
  isNeedUserId = true,
  filterPermission = true,
  toNavigate,
}: PrincipalPatrolSchemeCardProps) => {
  const { data, run } = useRequest(
    (planId: number, filterPermission: boolean, userId: number) =>
      geTaskCompleteInfo({ planId, filterPermission, userId: isNeedUserId ? userId : undefined }),
    {
      manual: true,
    },
  );

  return (
    <div className="bg-white rounded-lg px-3 pt-3 pb-2 ">
      <div
        className="flex justify-between"
        onClick={() => {
          toNavigate && toNavigate();
        }}
      >
        <CardHead status={initial.status} planType={initial.planType} name={initial.name} />
        <RightOutline className="text-[#c5c5c5] mt-1 text-sm shrink-0" />
      </div>
      <div className="flex justify-around mt-3 mb-2">
        {[
          { label: '巡检通过报告', key: 'passReportCount' },
          { label: '巡检未通过报告', key: 'failReportCount' },
        ].map((o) => {
          return (
            <div key={o.key} className="flex flex-col items-center  text-85">
              {!!data ? (
                <span className="text-primary text-sm leading-[22px]">
                  {data[o.key as keyof typeof data]}个
                </span>
              ) : (
                <span className="text-[#5E5E5E] text-sm leading-[22px]">--</span>
              )}
              <span className="text-xs leading-[20px]">{o.label}</span>
            </div>
          );
        })}
      </div>
      {/* {!initial.createType === 'QUICK' } */}
      {initial.createType === 'BATCH' || data === undefined ? (
        <div className="rounded bg-[#FAFAFA] text-85 px-2 py-2 flex items-center mb-2">
          <span className="text-13 leading-[13px] shrink-0 ">巡检完成情况:</span>
          {initial.status === yytTaskPlanStatusEnum.待开始 ? (
            <span className="text-right w-full">巡检时间还没到</span>
          ) : !!data ? (
            <div className="grow flex flex-col gap-y-1 ml-3">
              <div className="flex justify-between text-xs leading-[20px]">
                <span>
                  {data.patrolShopCount}/{data.totalShopCount}家门店
                </span>
                {/* {initial.status === yytTaskPlanStatusEnum.进行中 && <span>疯狂巡检中</span>} */}
              </div>
              <ProgressBar
                percent={
                  Number.isNaN(Math.floor(data.patrolShopCount / data.totalShopCount))
                    ? 0
                    : Math.floor((data.patrolShopCount / data.totalShopCount) * 100)
                }
                style={{
                  '--fill-color':
                    initial.status === yytTaskPlanStatusEnum.进行中
                      ? 'var( --adm-color-primary)'
                      : '#858585',
                  '--track-width': '4px',
                  marginBottom: '8px',
                }}
              />
            </div>
          ) : (
            <span
              onClick={() => {
                run(
                  initial?.id || initial?.planId,
                  filterPermission,
                  initial?.userId || initial?.createUserId,
                );
              }}
              className="text-xs leading-[20px] text-primary ml-1"
            >
              点击查看进度详情
            </span>
          )}
        </div>
      ) : (
        <div className="mb-2"></div>
      )}
      <div className="flex justify-between  text-13 leading-[21px] text-85 ">
        <span>
          巡检时间:{dayjs(initial.startDate).format('YYYY/MM/DD')}-
          {dayjs(initial.endDate).format('YYYY/MM/DD')}
        </span>
        {!!initial?.patrolUserName && <span>巡检人:{initial?.patrolUserName}</span>}
      </div>
    </div>
  );
};
