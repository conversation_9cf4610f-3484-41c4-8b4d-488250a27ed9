import { IconFont } from '@src/components';
import { CardHead } from './PrincipalPatrolSchemeCard';
import { shopTypeEnum, yytTaskStatusEnum } from '../../enum';
import dayjs from 'dayjs';
import { RightOutline } from 'antd-mobile-icons';
import { createSearchParams, useNavigate } from 'react-router-dom';
import { SwipeAction, Toast } from 'antd-mobile';
import { TASK_STATUS } from '../../../patrol/enum';
import { useMemo, useState } from 'react';
import { useRequest } from 'ahooks';
import { GlobalStore, SignStore, userStore } from '@src/store';
import { getNormalTaskList } from '../../api.type';
import { cancelTask } from '@src/pages/patrol/api';
import { showBtnFn } from '@src/pages/patrol/planDetail/detailCard';
interface PrincipalPatrolTaskCardProps {
  initial: getNormalTaskList;
}
export const PrincipalPatrolTaskCard = ({ initial }: PrincipalPatrolTaskCardProps) => {
  const {
    yytUserInfo: { userId },
  } = userStore;
  const isSelf = initial.userId === userId;
  console.log(isSelf, 'initial.userId === userId');
  const { data: config } = useRequest(
    async () => {
      const res = await GlobalStore.getCorporationConfig();
      return res;
    },
    { refreshDeps: [] },
  );
  const newInitial = useMemo(() => {
    return {
      ...initial,
      shopId: initial.simpleShop.id,
      shopName: initial.simpleShop.name,
      shopAddress: initial.simpleShop.address,
      shopPhone: initial.phoneNumber,
      shopType: initial.simpleShop.type,
    };
  }, [initial]);
  const { signed, status, subType, signType, leaveStatus, cross, taskId } = initial;
  const [curStatus, setCurStatus] = useState<number>(status);
  const operateDisabled = [TASK_STATUS.canceled, TASK_STATUS.expired].includes(curStatus);
  const complete = curStatus === TASK_STATUS.completed;
  const showBtn = showBtnFn(complete, subType, signed, config, signType, cross);
  const isNoCompleteVideo = !!subType && !complete; //暂时将视频云巡检的签到、签离、巡店隐藏。 视频云巡检&&未来完成状态
  const isCompleteVideo = !!subType && complete;
  const navigate = useNavigate();
  const handleClick = (item: { label: string; key: string }) => {
    if (item.label === '未整改问题' && initial?.reportDetail?.noReformCount > 0) {
      navigate(`/tasks/pollingAbarbeitung/${initial?.reportDetail?.reportId}/${initial?.cross}`);
    }
  };
  return (
    <SwipeAction
      // closeOnAction={false}
      // closeOnTouchOutside={false}
      rightActions={
        [TASK_STATUS.canceled, TASK_STATUS.completed, TASK_STATUS.expired].includes(curStatus)
          ? []
          : isSelf
            ? [
                {
                  key: 'cancel',
                  text: (
                    <div className="text-xs">
                      <div>
                        <IconFont className="text-base" type="icon-trash-2" />
                      </div>
                      取消任务
                    </div>
                  ),
                  color: 'danger',
                },
              ]
            : []
      }
      onAction={async (e) => {
        if (e.key === 'cancel') {
          await cancelTask(taskId);
          Toast.show({
            icon: 'success',
            content: '取消成功',
          });
          setCurStatus(TASK_STATUS.canceled);
        }
      }}
    >
      <div className="bg-white rounded-lg px-3 pt-3 pb-2">
        <div
          className="flex justify-between"
          onClick={() => {
            yytTaskStatusEnum.已完成 === initial.status &&
              navigate(
                `/tasks/patrol/reportdetail/${initial?.reportDetail?.reportId}/${initial?.cross}`,
              );
          }}
        >
          <CardHead
            // status={initial.status}
            status={curStatus}
            planType={initial.subType}
            name={initial.taskName}
            cross={initial.cross ? 1 : 0}
          />
          {yytTaskStatusEnum.已完成 === initial.status && (
            <RightOutline className="text-[#c5c5c5] mt-1 text-sm shrink-0" />
          )}
        </div>
        {initial.status === yytTaskStatusEnum.已完成 && (
          <div className="flex justify-around mt-3 mb-2">
            {[
              { label: '巡检结果', key: 'reportScore' },
              { label: '问题项', key: 'issueCount' },
              { label: '未整改问题', key: 'noReformCount' },
            ].map((o) => {
              return (
                <div
                  key={o.key}
                  className="flex flex-col items-center  text-85"
                  onClick={() => handleClick(o)}
                >
                  <span className="text-primary text-sm leading-[22px]">
                    {initial.reportDetail[o.key as keyof typeof initial.reportDetail]}
                    {o.label === '巡检结果' ? '分' : '个'}
                  </span>
                  <span className="text-xs leading-[20px] left-3">{o.label}</span>
                </div>
              );
            })}
          </div>
        )}
        <div className="rounded bg-[#F6F6F6]  px-3 py-2 flex flex-col gap-y-1  mb-2 ">
          <h3 className="text-[#141414] flex ">
            <span className="text-sm leading-[22px]">{initial.simpleShop?.name}</span>
            <span className="text-13 leading-[21px] text-light">
              （{shopTypeEnum[initial.simpleShop.type as keyof typeof shopTypeEnum]}）
            </span>
          </h3>
          <span className="text-sm leading-[22px] text-[#5E5E5E] ">
            <IconFont type="icon-position" className="mr-1 text-13 text-[#5E5E5E]" />
            {initial.simpleShop?.address}
          </span>
          <div
            className="text-primary"
            onClick={() => {
              window.location.href = `tel:${initial.phoneNumber}`;
            }}
          >
            <IconFont type="icon-rongqi" className="text-13  " />
            <span className="ml-1 text-sm leading-[14px] ">电话联系</span>
          </div>
        </div>
        <div className="text-13 leading-[21px] text-85 flex justify-between items-center">
          <span>巡检时间：{dayjs(initial.taskDate).format('YYYY/MM/DD')}</span>
          <div className="flex justify-between">
            {/* {initial.status === yytTaskStatusEnum.已完成 && (
            <button
              onClick={() => {
                navigate(
                  `/tasks/patrol/reportdetail/${initial?.reportDetail?.reportId}/${initial?.cross}`,
                );
              }}
              className="focus:outline-none rounded border border-solid px-2 py-[6px] text-xs leading-[12px] border-[C4C4C4] "
            >
              查看报告
            </button>
          )} */}
            {/* {(!cross &&
              // 非视频云巡检，没有设置必须签到，或者设置了已签到，展示
              subType !== 1 &&
              signType === 'SYSTEM' &&
              (+config?.MUST_CHECK_IN === 0 || (+config?.MUST_CHECK_IN === 1 && signed))) ||
              signType === 'SIGN_NEITHER' ? (
              <button
                disabled={operateDisabled}
                className="focus:outline-none rounded border border-solid px-2 py-[6px] text-xs leading-[12px] border-[C4C4C4] "
                onClick={() => {
                  navigate(`/tasks/patrol/plan/sign`);
                  SignStore.setTaskShopInfo(newInitial);
                }}
              >
                {signed ? (leaveStatus ? '已签离' : '签离') : '签到'}
              </button>
            ) : null} */}
            {(() => {
              //cross是否权限外、subTyp任务类型：到店or视频云巡检
              //signed是否签到
              const shouldShowButton =
                (!cross &&
                  subType !== 1 &&
                  signType === 'SYSTEM' &&
                  (+config?.MUST_CHECK_IN === 0 || (+config?.MUST_CHECK_IN === 1 && signed))) ||
                (signType === 'SIGN_NEITHER' && isSelf);
              if (isNoCompleteVideo || isCompleteVideo) {
                return null;
              } //这个if也是暂时处理
              if (shouldShowButton) {
                return (
                  <button
                    disabled={operateDisabled}
                    // block
                    className="focus:outline-none rounded border border-solid px-2 py-[6px] text-xs leading-[12px] border-[C4C4C4] "
                    onClick={() => {
                      navigate(`/tasks/patrol/plan/sign`);
                      SignStore.setTaskShopInfo(newInitial);
                    }}
                  >
                    {signed ? (leaveStatus ? '已签离' : '签离') : '签到'}
                  </button>
                );
              }
              return null;
            })()}
            {(!isSelf && !complete) || isNoCompleteVideo ? null : ( //此处三元运算符也暂时处理，后面可移除
              <button
                onClick={() => {
                  SignStore.setTaskShopInfo(newInitial);
                  switch (showBtn.type) {
                    case 'report': //查看报告
                      navigate(
                        `/tasks/patrol/reportdetail/${initial?.reportDetail.reportId}/${initial.cross}`,
                      );
                      break;
                    case 'sign': //签到并巡店
                      navigate(`/tasks/patrol/plan/sign`);
                      break;
                    case 'patrol': // 巡店;
                      navigate({
                        pathname: `/patrol/shopcheck`,
                        search: `?${createSearchParams({
                          shopId: initial?.simpleShop.id,
                          taskId: initial?.taskId,
                        })}`,
                      });
                      break;
                  }
                }}
                disabled={operateDisabled}
                className="focus:outline-none rounded border border-solid px-2 py-[6px] text-xs leading-[12px] border-[C4C4C4] ml-3"
              >
                {showBtn.text}
              </button>
            )}
          </div>
        </div>
        <span className="text-13 leading-[21px] text-85">巡检人：{initial.userName}</span>
      </div>
    </SwipeAction>
  );
};
