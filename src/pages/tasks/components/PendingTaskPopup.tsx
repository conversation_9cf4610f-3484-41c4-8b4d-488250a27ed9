import { useState } from 'react';
import emptyImage from '@src/assets/images/task-empty.png';
import { Loading } from '@src/components';
import { IPopup } from '@src/components/IPopup';
import { useRequest } from 'ahooks';
import { Dialog, PopupProps, Toast } from 'antd-mobile';
import { RightOutline } from 'antd-mobile-icons';
import dayjs from 'dayjs';
import { useNavigate } from 'react-router-dom';
import InvestPop from './InvestPop';
import NorTaskStatusTag from './NorTaskStatusTag';
import TextareaPopup from './TextareaPopup';
import { getTaskListPage, modifyTaskStatus, queryShopInvestRate, updateInvestRate } from '../api';
import { TaskStatusEnum, TaskTypeEnum } from '../enum';
import { getDateStatusNode } from '../utils';

interface PendingTaskPopupProps extends PopupProps {
  // 更新待确认任务数量
  onUpdate: () => void;
}

const PendingTaskPopup: React.FC<PendingTaskPopupProps> = ({ visible, onUpdate, ...props }) => {
  const navigate = useNavigate();

  const [rejectPopupVisible, setRejectPopupVisible] = useState(false);
  const [currentRejectTaskUserId, setCurrentRejectTaskUserId] = useState<number>();
  // 投流任务
  const [investPopupVisible, setInvestPopupVisible] = useState(false);
  const { runAsync: investTaskRate } = useRequest(updateInvestRate, { manual: true });
  const [investDetails, setInvestDetails] = useState({});
  const [currentTaskItem, setCurrentTaskItem] = useState({});

  const { data, loading, refresh } = useRequest(
    () =>
      getTaskListPage({
        queryType: TaskTypeEnum.Waiting,
        taskStatus: TaskStatusEnum.Waiting,
        pageNum: 1,
        pageSize: 9999,
      }),
    {
      refreshDeps: [visible],
      ready: visible,
    },
  );
  const { runAsync, loading: modifyLoading } = useRequest(modifyTaskStatus, {
    manual: true,
  });

  const handleReject = async (remark: string, status?: number, taskUserId?: number) => {
    await runAsync({
      remark,
      taskStatus: status || TaskStatusEnum.Rejected,
      taskUserId: taskUserId || currentRejectTaskUserId!,
    });
    Toast.show('拒绝成功');
    setRejectPopupVisible(false);
    refresh();
    onUpdate();
  };

  const handleReceive = async (taskUserId: number) => {
    await runAsync({ taskStatus: TaskStatusEnum.InProgress, taskUserId });
    Toast.show('接受成功');
    setRejectPopupVisible(false);
    refresh();
    onUpdate();
  };

  // 拒绝投流
  const rejectInvest = (taskUserId: number) => {
    Dialog.confirm({
      content: <div className="text-center text-[#141414] text-base">确认不参与品牌公司统投计划</div>,
      onConfirm: async () => {
        await handleReject('确认不参与品牌公司统投计划', TaskStatusEnum.Rejected, taskUserId);
      },
    });
  };

  // 更新投流费用
  const handleInvest = async (actualCost: number) => {
    if (!actualCost || !/^\d+$/.test(actualCost?.toString())) {
      Toast.show('请填写整数实际推广费用');
      return false;
    }
    await investTaskRate({
      actualCost: Number(actualCost) ?? 0,
      id: +currentRejectTaskUserId!,
    });
    Toast.show('确认投流成功');
    setInvestPopupVisible(false);
  };

  return (
    <Loading maskClickable={!modifyLoading} spinning={loading || modifyLoading}>
      <IPopup title="待确认任务" visible={visible} bodyStyle={{ maxHeight: '80vh' }} {...props}>
        <div className="px-3 py-2 flex-1 overflow-y-auto bg-[#f9f9f9]">
          {data?.data && data?.data.length > 0 ? (
            data?.data?.map((item) => (
              <div
                key={item.taskUserId}
                className="bg-white rounded [&:not(:first-child)]:mt-[10px]"
                onClick={() => navigate(`/tasks/detail/${item.taskUserId}`)}
              >
                <div className="p-3 pb-0 flex">
                  <div className="flex-1">
                    <div className="ellipsis-2">
                      <NorTaskStatusTag status={TaskStatusEnum.Waiting} className="mr-1" />
                      <span className="font-medium leading-[22px]">
                        {item.taskType ? `【${item.taskType}】` : ''}
                        {item.taskTitle}
                      </span>
                    </div>
                    <span
                      className={`${item.tripartiteTaskType === 'FLOW' ? '' : 'ellipsis-1'} text-13 text-[#858585] mt-1`}
                    >
                      {item.taskInfo}
                    </span>
                  </div>
                  <RightOutline className="text-[#c5c5c5] mt-1 text-sm" />
                </div>
                <div className="p-3 pt-0 mt-2">
                  <div className="flex justify-between bg-[#f6f6f6] px-2 py-3 rounded text-light">
                    <span className="text-13 text-light leading-[13px]">
                      创建人：
                      <span className="text-grey">{item.taskCreatorName}</span>
                    </span>
                    <span className="text-xs leading-3">
                      {dayjs(item.taskDeadline).format('YYYY年MM月DD号')}截止
                      {getDateStatusNode(item.taskStatus, item.taskDeadline, item.taskUpdateTime)}
                    </span>
                  </div>
                  <div className="flex justify-end mt-3 gap-2">
                    <button
                      className="w-[72px] h-[30px] text-[#747578] text-xs leading-3 border border-[#eeeeee] rounded"
                      onClick={(e) => {
                        e.stopPropagation();
                        setCurrentRejectTaskUserId(item.taskUserId);
                        // 投流任务
                        if (item?.tripartiteTaskType === 'FLOW') {
                          rejectInvest(item.taskUserId);
                        } else {
                          setRejectPopupVisible(true);
                        }
                      }}
                    >
                      拒绝
                    </button>
                    <button
                      className="w-[72px] h-[30px] text-[#747578] text-xs leading-3 border border-[#eeeeee] rounded"
                      onClick={(e) => {
                        e.stopPropagation();
                        queryShopInvestRate(item?.taskUserId?.toString()).then((response) => {
                          setInvestDetails(response || {});
                          // 设置当前任务的用户id,重用拒绝的参数用于投流
                          setCurrentRejectTaskUserId(item.taskUserId);
                          // 投流任务
                          if (item?.tripartiteTaskType === 'FLOW') {
                            setCurrentTaskItem(item);
                            setInvestPopupVisible(true);
                          } else {
                            handleReceive(item.taskUserId);
                          }
                        });
                      }}
                    >
                      接受
                    </button>
                    <button className="w-[72px] h-[30px] text-primary text-xs leading-3 border border-primary rounded">
                      查看详情
                    </button>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="flex flex-col items-center my-5">
              <img src={emptyImage} className="w-[240px]" alt="" />
              <span className="text-13 text-[#9E9E9E] mt-5">暂无任务</span>
            </div>
          )}
        </div>

        <TextareaPopup
          visible={rejectPopupVisible}
          title="拒绝理由"
          okText="确认拒绝"
          onClose={() => setRejectPopupVisible(false)}
          onOk={handleReject}
        />
      </IPopup>
      {investPopupVisible && (
        <InvestPop
          visible={investPopupVisible}
          title="参与投流确认"
          okText="确认"
          investRate={investDetails}
          task={currentTaskItem}
          onClose={() => setInvestPopupVisible(false)}
          onOk={(actualCost) => handleInvest?.(actualCost)}
        />
      )}
    </Loading>
  );
};

export default PendingTaskPopup;
