import { HTMLAttributes } from 'react';
import { yytTaskPlanStatusEnum, yytTaskStatusEnum } from '../enum';
interface TaskStatusTagProps extends HTMLAttributes<HTMLSpanElement> {
  status: yytTaskPlanStatusEnum | yytTaskStatusEnum; // 任务状态
}

export const TaskStatusTag = ({ status, style, className, ...props }: TaskStatusTagProps) => {
  console.log('888status',status);
  const taskStatusMap = {
    [yytTaskPlanStatusEnum.进行中]: {
      bg: 'var(--adm-color-primary)',
      color: 'white',
      text: '进行中',
    },
    [yytTaskStatusEnum.进行中]: {
      bg: 'var(--adm-color-primary)',
      color: 'white',
      text: '进行中',
    },
    [yytTaskStatusEnum.已完成]: {
      bg: 'var(--adm-color-primary)',
      color: 'white',
      text: '已完成',
    },
    [yytTaskPlanStatusEnum.待开始]: {
      bg: 'none',
      color: '#00BBB4',
      text: '待开始',
      border: '1px solid #00BBB4',
    },
    [yytTaskStatusEnum.待开始]: {
      bg: 'none',
      color: '#00BBB4',
      text: '待开始',
      border: '1px solid #00BBB4',
    },
    [yytTaskPlanStatusEnum.已结束]: {
      bg: 'rgba(0, 187, 180, 0.2)',
      color: 'white',
      text: '已结束',
    },
    [yytTaskStatusEnum.已过期]: {
      bg: 'rgba(0, 187, 180, 0.2)',
      color: 'white',
      text: '已过期',
    },
    [yytTaskPlanStatusEnum.已取消]: {
      bg: 'rgba(0, 0, 0, 0.05)',
      color: '#858585',
      text: '已取消',
    },
    [yytTaskStatusEnum.已取消]: {
      bg: 'rgba(0, 0, 0, 0.05)',
      color: '#858585',
      text: '已取消',
    },
  };
  console.log('888taskStatusMap', (taskStatusMap as any)[status]);
  const { bg, color, text, ...other } = (taskStatusMap as any)[status];
  return (
    <span
      {...props}
      className={`inline-block px-1 py-[3px] rounded-sm text-white text-xs leading-[12px] ${
        className || ''
      }`}
      style={{ backgroundColor: bg, color: color, ...other, ...style }}
      // @ts-ignore
      attribute="tag"
    >
      {text}
    </span>
  );
};
