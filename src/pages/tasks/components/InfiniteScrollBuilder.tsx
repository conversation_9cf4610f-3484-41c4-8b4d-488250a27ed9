import { useContext, useEffect, useRef, useState } from 'react';
import { ErrCancel } from '@src/api/errors';
import emptyImage from '@src/assets/images/task-empty.png';
import { InfiniteScroll } from 'antd-mobile';
import { URLSearchParamsInit, useSearchParams } from 'react-router-dom';
import { WorkTypeEnum } from '../enum';
import { TasksContext } from '../layout';
export function Empty() {
  return (
    <div className="flex flex-col items-center mt-[70px]">
      <img src={emptyImage} className="w-[240px]" alt="" />
      <span className="text-13 text-[#9E9E9E] mt-5">暂无任务</span>
    </div>
  );
}

interface InfiniteScrollBuilderProps {
  renderChildren: (data: any) => React.ReactNode;
  searchParams: any;
  api: (data: any, options?: { [key: string]: any }) => Promise<any>;
  // 如果是自检任务必传WorkTypeEnum.Patrol
  workType?: WorkTypeEnum;
}

export const InfiniteScrollBuilder = ({
  renderChildren,
  searchParams,
  api,
  workType = WorkTypeEnum.Patrol,
}: InfiniteScrollBuilderProps) => {
  const [list, setList] = useState<any[]>([]);
  const [hasMore, setHasMore] = useState(false);
  const pageNumRef = useRef(0);
  const isFetchRef = useRef(false);
  const controllerRef = useRef<any>(null);

  const [, setSearchParams] = useSearchParams();
  const context = useContext(TasksContext);
  const loadMore = async () => {
    pageNumRef.current++;
    isFetchRef.current = true;
    controllerRef.current = new AbortController();
    !!context &&
      setSearchParams(
        {
          ...searchParams,
          queryType: context.queryType,
          workType,
        } as unknown as URLSearchParamsInit,
        { replace: true },
      );
    // 巡店计划
    try {
      const res = await api(
        {
          ...searchParams,
          pageNo: pageNumRef.current,
        },
        {
          signal: controllerRef.current.signal,
        },
      );

      setList((pre) => [...pre, ...(res?.data || res?.result || res || [])]); // res?.result兼容后端数据格式
      setHasMore((res?.data || res?.result || res || []).length > 0);
    } catch (error) {
      if (!(error instanceof ErrCancel)) {
        // setHasMore(true);
        // loadMore();
        setHasMore(false);
      }
    }
    isFetchRef.current = false;
  };

  useEffect(() => {
    // 重新获取列表
    setList([]);
    setHasMore(true);
    pageNumRef.current = 0;
    // 说明上一条请求还未完成，取消这条请求的发送
    if (isFetchRef.current) {
      controllerRef.current.abort();
    }
  }, [JSON.stringify(searchParams)]);

  return (
    <>
      {!hasMore && list.length === 0 ? <Empty /> : renderChildren(list)}
      {(!!list.length || hasMore) && <InfiniteScroll loadMore={loadMore} hasMore={hasMore} />}
    </>
  );
};
