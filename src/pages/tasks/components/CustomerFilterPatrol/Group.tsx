import useGroupTreeYyt, { ArchitectureDataNode } from '@src/hooks/useGroupTreeYyt';
import styles from './index.module.scss';
import { TreeSelect } from 'antd';
import { Button, Popup } from 'antd-mobile';
import { useEffect, useState } from 'react';
import { DownOutline } from 'antd-mobile-icons';

type IProps = {
  pid: string; //组织树第一个id
  value: string;
  treeData: ArchitectureDataNode[];
  onChange: (e: string) => void;
};
const GroupFilter: React.FC<IProps> = ({ treeData, onChange, pid, value: initValue = '' }) => {
  const { tagIdObj } = useGroupTreeYyt();

  const [visible, setVisible] = useState(false);
  const [value, setValue] = useState('');

  useEffect(() => {
    setValue(initValue);
  }, [initValue]);

  const handleConfirm = () => {
    onChange && onChange(value);
    setVisible(false);
  };

  return (
    <>
      {/* <Button key="group" color="default" size="small" onClick={() => setVisible(true)} style={{ '--border-radius': '8px' }}> */}
      <div
        key="group"
        className={`flex items-center px-2 rounded ${value ? 'text-primary' : 'text-grey'}`}
        onClick={() => setVisible(true)}
      >
        <div className="mr-1 text-left ellipsis" style={{ maxWidth: '4.375rem' }}>
          {value ? tagIdObj[value]?.name : '全部组织'}
        </div>
        {/* <Ellipsis
          className="mr-1 flex-1 text-left"
          direction="end"
          content={value ? tagIdObj[value]?.name : '全部组织'}
        /> */}
        <DownOutline className="text-xs" />
      </div>
      {/* </Button> */}
      <Popup
        position="top"
        visible={visible}
        bodyClassName="p-4 box-border"
        // bodyStyle={{ height: '450px' }}
        onMaskClick={() => {
          setValue(initValue);
          setVisible(false);
        }}
      >
        <div>
          <div style={{ height: '356px' }} className={styles.groupFilter}>
            <TreeSelect
              className={styles.iSelect}
              style={{ width: '100%' }}
              open
              showSearch
              // treeCheckable
              // treeDefaultExpandAll
              treeDefaultExpandedKeys={[pid]}
              autoClearSearchValue={false}
              getPopupContainer={(node) => node.parentElement}
              treeNodeFilterProp="title"
              placement="bottomLeft"
              treeData={treeData}
              value={value}
              onChange={(value) => setValue(value)}
              // maxTagCount={2}
              placeholder="请输入"
              listHeight={320}
              dropdownStyle={{ height: 320 }}
            />
            {/* <Tree
              checkStrictly
              defaultSelectedKeys={[value]}
              onCheck={(v) => {
                console.log(v);
              }}
              onSelect={(v) => {
                console.log(v);
                v && v.length && setValue(v[0]);
              }}
              height={320}
              treeData={treeData}
            /> */}
          </div>
          <div className="flex pt-2 gap-3">
            <Button
              block
              className="h-[42px] text-sm"
              onClick={() => {
                setValue(pid);
                onChange && onChange(pid);
                setVisible(false);
              }}
              color="primary"
              fill="outline"
            >
              清空筛选
            </Button>
            <Button block className="h-[42px] text-sm" color="primary" onClick={handleConfirm}>
              确定筛选
            </Button>
          </div>
        </div>
      </Popup>
    </>
  );
};

export default GroupFilter;
