/*
 * @Author: chen<PERSON><PERSON> <EMAIL>
 * @Date: 2023-10-20
 * @LastEditors: chenweibin <EMAIL>
 * @LastEditTime: 2023-12-18
 */
import { useGroupTreeYyt } from '@src/hooks';
import { useMemo } from 'react';
import GroupFilter from './Group';
import ShopFilter from './Shop';
import { useRequest, useControllableValue } from 'ahooks';
import classNames from 'classnames';
import { CssProps } from '@src/common/api.type';
import { ConfigProvider } from 'antd';
import { themeConfig } from '@src/common/theme';

type IProps = CssProps & {
  value?: { groupId?: string; shopId?: number[] };
  onChange?: ({ groupId, shopId }: { groupId?: string; shopId?: number[] }) => void;
  type?: 'group' | 'shop' | 'default';
  single?: boolean;
};
const CustomerFilter: React.FC<IProps> = ({ className, type = 'default', single, ...props }) => {
  const { treeData, getShopList, pid } = useGroupTreeYyt();

  const [params, setParams] = useControllableValue(props, {
    defaultValue: { groupId: '', shopId: [] as number[] },
  });
  console.log(params, '=params');

  //会导致页面发起两次请求
  // useEffect(() => {
  //   if (pid ?? '-' !== '-') {
  //     setParams((p) => ({ ...p, groupId: pid }));
  //   }
  // }, [pid]);

  const { data } = useRequest(
    () => {
      if (type === 'group') {
        throw null;
      }
      if (!params.groupId && (pid ?? '-') === '-') {
        console.log('缺少一级组织pid');
        throw null;
      }

      return getShopList(params.groupId || pid);
    },
    {
      refreshDeps: [params.groupId, pid],
    },
  );

  const shopData = useMemo(
    () => (data || []).map((v) => ({ ...v, title: v.name, value: `${v.id}` })),
    [data],
  );

  return (
    <ConfigProvider theme={themeConfig}>
      <div className={classNames(className)}>
        <div className="flex">
          {type !== 'shop' && (
            <GroupFilter
              pid={pid}
              treeData={treeData}
              value={params.groupId}
              onChange={(groupId) => setParams({ shopId: [], groupId })}
            />
          )}
          {type !== 'group' && (
            <ShopFilter
              value={params.shopId}
              treeData={shopData}
              onChange={(shopId) => setParams((p) => ({ ...p, shopId }))}
              single={single}
            />
          )}
        </div>
      </div>
    </ConfigProvider>
  );
};

export default CustomerFilter;
