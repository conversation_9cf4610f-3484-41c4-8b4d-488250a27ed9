/*
 * @Author: chen<PERSON><PERSON> <EMAIL>
 * @Date: 2023-10-20
 * @LastEditors: chenweibin <EMAIL>
 * @LastEditTime: 2024-01-22
 */
import { Button, Popup, SearchBar } from 'antd-mobile';
import { CheckOutline, DownOutline } from 'antd-mobile-icons';
import { useState, useMemo, useEffect } from 'react';
import { IconFont, PageList } from '@src/components';
import { cloneDeep } from 'lodash';
import { useDictData } from '@src/hooks';

enum ShopTypeEnum {
  '大加盟' = 'DIRECT',
  '小加盟' = 'JOIN',
}

type IProps = {
  value?: number[];
  treeData: { title: string; value: string; shopType: ShopTypeEnum; shopNo: string }[];
  onChange: (e: number[]) => void;
  single?: boolean;
};

const defaultValue = {
  [ShopTypeEnum.大加盟]: [],
  [ShopTypeEnum.小加盟]: [],
};

type ValueParams = {
  [ShopTypeEnum.大加盟]: string[];
  [ShopTypeEnum.小加盟]: string[];
};

const SearchResult = ({ showData, value, setValue }: any) => {
  return (
    <div className="overflow-y-auto flex-1 pt-[15px]" style={{ height: '25rem' }}>
      {showData.map((group: any) =>
        group.children.map((v: any) => {
          const idx = group.key;
          const checked = value[idx].includes(v.value);
          return (
            <div
              key={v.value}
              className={`flex items-center mb-6 text-sm px-5 ${checked ? 'text-primary' : ''}`}
              onClick={() => {
                if (checked) {
                  value[idx] = value[idx].filter((ele: any) => ele !== v.value);
                } else {
                  value[idx].includes('all') ? (value[idx] = [v.value]) : value[idx].push(v.value);
                }
                setValue((p: ValueParams) => ({ ...p }));
              }}
            >
              <div className="flex-1 mr-1">
                {v.shopNo || ''}
                {v.title}
              </div>
              {checked ? (
                <div className="w-4 h-4 bg-primary rounded-full flex justify-center items-center border border-primary">
                  <CheckOutline className="text-white text-xs" />
                </div>
              ) : (
                <div className="w-4 h-4 rounded-full border border-[rgba(0,0,0,0.3)]" />
              )}
            </div>
          );
        }),
      )}
    </div>
  );
};

const ShopFilter: React.FC<IProps> = ({ treeData, onChange, single, value: initValue = [] }) => {
  const [visible, setVisible] = useState(false);
  const [value, setValue] = useState<ValueParams>({ ...cloneDeep(defaultValue) });

  const [curKey, setCurKey] = useState<ShopTypeEnum>(ShopTypeEnum.大加盟);

  const [keyWords, setKeyWords] = useState('');

  const { data: dictData } = useDictData(
    (s) => s?.find((v: any) => v.dictCode === 'shopType')?.relList || [],
  );

  const ShopTypeNameMap = useMemo(() => {
    return {
      [ShopTypeEnum.大加盟]: dictData.find((v: any) => v?.enumCode === '1')?.enumValue || '-',
      [ShopTypeEnum.小加盟]: dictData.find((v: any) => v?.enumCode === '2')?.enumValue || '-',
    };
  }, [dictData]);

  const showData = useMemo(() => {
    if (!treeData.length) {
      return [];
    }
    const list = [
      {
        title: ShopTypeNameMap[ShopTypeEnum.大加盟],
        key: ShopTypeEnum.大加盟,
        default: { title: `全部${ShopTypeNameMap[ShopTypeEnum.大加盟]}门店`, value: 'all' },
        children: treeData.filter((v) => v.shopType === ShopTypeEnum.大加盟),
      },
      {
        title: ShopTypeNameMap[ShopTypeEnum.小加盟],
        key: ShopTypeEnum.小加盟,
        default: { title: `全部${ShopTypeNameMap[ShopTypeEnum.小加盟]}门店`, value: 'all' },
        children: treeData.filter((v) => v.shopType === ShopTypeEnum.小加盟),
      },
    ].filter((v) => v.children && v.children.length);
    list.length && setCurKey(list[0].key);
    // if (keyWords) {
    //   list = list.map((v) => ({
    //     ...v,
    //     children: v.children.filter((ele) => ele.title.includes(keyWords)),
    //   }));
    // }
    return list;
  }, [treeData]);

  const showSearchData = useMemo(() => {
    let list = showData;
    if (keyWords) {
      list = list.map((v) => ({
        ...v,
        children: v.children.filter(
          (ele) => ele?.title?.includes(keyWords) || ele?.shopNo?.includes(keyWords),
        ),
      }));
    }
    return list;
  }, [showData, keyWords]);

  const handleSetValue = () => {
    const res = treeData.filter((v) => initValue.includes(+v.value));
    const val = {
      [ShopTypeEnum.大加盟]: res
        .filter((v) => v.shopType === ShopTypeEnum.大加盟)
        .map((e) => e.value),
      [ShopTypeEnum.小加盟]: res
        .filter((v) => v.shopType === ShopTypeEnum.小加盟)
        .map((e) => e.value),
    };

    setValue(() => val);
  };

  useEffect(() => {
    if (initValue.length) {
      handleSetValue();
    } else {
      setValue({ ...cloneDeep(defaultValue) });
    }
  }, [initValue]);

  const formatValue = useMemo(() => {
    if (!showData.length) return [];
    return Object.keys(value).reduce((arr: number[], key) => {
      const item = value[key as unknown as ShopTypeEnum];
      const dataItem = showData.find((v) => v.key === (key as unknown as ShopTypeEnum));
      if (item.includes('all')) {
        return [...arr, ...(dataItem?.children || []).map((v) => +v.value)];
      } else {
        return [...arr, ...item.map((v) => +v)];
      }
    }, []);
  }, [value]);
  const handleConfirm = () => {
    onChange && onChange(formatValue);
    setKeyWords('');
    setVisible(false);
  };

  return (
    <>
      {/* <Button key="group" color="default" size="small" onClick={() => setVisible(true)} style={{ '--border-radius': '8px' }}> */}
      <div
        key="shop"
        className={`flex items-center px-2 rounded ${
          formatValue.length ? 'text-primary' : 'text-grey'
        }`}
        style={{ maxWidth: '7rem' }}
        onClick={() => setVisible(true)}
      >
        <div className="mr-1 flex-1 text-left ellipsis">
          {formatValue.length ? `门店已选${formatValue.length}` : '门店'}
        </div>
        <DownOutline className="text-xs" />
      </div>
      {/* </Button> */}
      <Popup
        position="top"
        visible={visible}
        bodyClassName="box-border"
        onMaskClick={() => {
          handleSetValue(); //数据还原
          setVisible(false);
        }}
      >
        <div>
          <div className="py-3 px-4">
            <SearchBar
              className={`flex-1 mr-2`}
              style={{ '--border-radius': '4px', '--height': '2.5rem', fontSize: '0.875rem' }}
              onClear={() => setKeyWords('')}
              onSearch={setKeyWords}
              onChange={setKeyWords}
              value={keyWords}
              placeholder="请搜索门店名称"
            />
          </div>
          {keyWords ? (
            <SearchResult showData={showSearchData} value={value} setValue={setValue} />
          ) : (
            <div className="flex items-center">
              <div className="overflow-y-auto bg-line" style={{ height: '25rem', width: '6.5rem' }}>
                {showData.map((p) => (
                  <div
                    key={p.title}
                    className={`h-11 pl-4 text-13 leading-[43px] ${
                      curKey === p.key ? 'bg-white text-primary' : ''
                    }`}
                    onClick={() => {
                      const listElement = document.getElementById('shopContent');

                      setCurKey(p.key);
                      listElement && (listElement.scrollTop = 0);
                    }}
                  >
                    {!!value[p.key].length && <IconFont type="icon-point" />}
                    {p.title}
                  </div>
                ))}
              </div>
              <div
                className="overflow-y-auto flex-1 pt-[15px]"
                id="shopContent"
                style={{ height: '25rem' }}
              >
                {!!showData.length && (
                  <>
                    {single || (
                      <div
                        className={`flex items-center mb-6 text-sm px-5 ${
                          value[curKey].includes(
                            showData.find((v) => v.key === curKey)?.default.value || '',
                          )
                            ? 'text-primary'
                            : ''
                        }`}
                        onClick={() => {
                          if (value[curKey].includes('all')) {
                            value[curKey] = [];
                          } else {
                            value[curKey] = ['all'];
                          }
                          setValue((p) => ({ ...p }));
                        }}
                      >
                        <div className="flex-1 mr-1">
                          {showData.find((v) => v.key === curKey)?.default.title}
                        </div>
                        {value[curKey].includes(
                          showData.find((v) => v.key === curKey)?.default.value || '',
                        ) ? (
                          <div className="w-4 h-4 bg-primary rounded-full flex justify-center items-center border border-primary">
                            <CheckOutline className="text-white text-xs" />
                          </div>
                        ) : (
                          <div className="w-4 h-4 rounded-full border border-[rgba(0,0,0,0.3)]" />
                        )}
                      </div>
                    )}
                    <PageList
                      dataSource={showData.find((v) => v.key === curKey)?.children || []}
                      itemRender={(v: any) => {
                        const checked = value[curKey].includes(v.value);
                        return (
                          <div
                            key={v.value}
                            className={`flex items-center mb-6 text-sm px-5 ${
                              checked ? 'text-primary' : ''
                            }`}
                            onClick={() => {
                              if (single) {
                                // value[curKey] = [v.value];
                                for (const key in value) {
                                  if (Object.prototype.hasOwnProperty.call(value, key)) {
                                    curKey === key ? (value[key] = [v.value]) : (value[key] = []);
                                  }
                                }
                              } else {
                                if (checked) {
                                  value[curKey] = value[curKey].filter((ele) => ele !== v.value);
                                } else {
                                  value[curKey].includes('all')
                                    ? (value[curKey] = [v.value])
                                    : value[curKey].push(v.value);
                                }
                              }
                              setValue((p) => ({ ...p }));
                            }}
                          >
                            <div className="flex-1 mr-1">
                              {v.title}({v?.shopNo})
                            </div>
                            {checked ? (
                              <div className="w-4 h-4 bg-primary rounded-full flex justify-center items-center border border-primary">
                                <CheckOutline className="text-white text-xs" />
                              </div>
                            ) : (
                              <div className="w-4 h-4 rounded-full border border-[rgba(0,0,0,0.3)]" />
                            )}
                          </div>
                        );
                      }}
                    />
                  </>
                )}
              </div>
            </div>
          )}
          <div className="flex p-4 gap-3">
            <Button
              block
              className="h-[42px] text-sm"
              onClick={() => {
                setValue({ ...cloneDeep(defaultValue) });
                setKeyWords('');
                onChange && onChange([]);
                setVisible(false);
              }}
              color="primary"
              fill="outline"
            >
              清空筛选
            </Button>
            <Button block className="h-[42px] text-sm" color="primary" onClick={handleConfirm}>
              确定筛选
            </Button>
          </div>
        </div>
      </Popup>
    </>
  );
};

export default ShopFilter;
