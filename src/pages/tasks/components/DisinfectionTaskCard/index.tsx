import React, { useMemo } from 'react';
import successPNG from '@src/assets/images/success.png';
import PositiveReport from '@src/assets/positiveReport.svg';
import { DisinfectionItem } from '@src/pages/mission/api';
import { formatScore } from '@src/pages/mission/components/core';
import OperationBarDisinfection from '@src/pages/reportDetail/patrol/OperationBarDisinfection';
import { roleTypeIsManage } from '@src/utils/tokenUtils';
import { H5Bridge } from '@tastien/rn-bridge';
import dayjs from 'dayjs';
import { disinfectionTypeStatus } from '../../api';
import { DailyDisinfectionInfo, disinfectionTypeValue } from '../../sructKill/StructKillCard';
type CardProps = {
  initial: DisinfectionItem;
};
const DisinfectionType = {
  FIRST_STRUCTURE: '一道结构消杀',
  SECOND_STRUCTURE: '二道结构消杀',
  DAILY: '日常消杀',
};
const OperateActionType: { [key: string]: string } = {
  SUBMIT_TASK: '提交',
  NEW_TASK: '新建',
};
export function DisinfectionResBuilder(initial: DisinfectionItem) {
  const handleCardClick = () => {
    H5Bridge.navigator.push({
      pathname: 'ReportDetail',
      title: ' ',
      message: { id: initial?.id },
    });
  };

  const showItem = useMemo(() => {
    const list: any = [
      { label: '检查项', key: 'reportCheckCount' as const },
      { label: '问题项', key: 'reportIssueCount' as const },
    ];

    if (initial?.reportIssueCount) {
      list.push({ label: '未整改项', key: 'reportNoReformCount' as const });
    }

    return list;
  }, [initial]);
  return (
    <div
      className="text-13 leading-[21px] text-85 mt-[5px] px-3 pb-3 border-b border-[#F6F6F6]"
      onClick={() => handleCardClick()}
    >
      <div className="flex justify-between">
        <div>{DisinfectionType[initial?.disinfectionType]}</div>
      </div>
      <div className=" flex justify-between items-center">
        <div className=" text-[#858585]">检查得分</div>
        <div className=" text-[#141414] font-medium text-2xl">{formatScore(initial?.reportScore)}</div>
      </div>
      {!!initial?.processList?.length && (
        <div className=" flex justify-between items-center bg-[#fafafa] px-2 py-2 relative">
          <div>
            {initial?.processList?.map((item) => {
              return (
                <div className="flex items-center mb-2">
                  <span className=" text-[#422a2a] text-xs">
                    {item?.operatorName}&nbsp;&nbsp;
                    {dayjs(item?.createTime).format('MM-DD HH:mm')}&nbsp;&nbsp;
                    {OperateActionType[item.operateAction]}
                  </span>
                </div>
              );
            })}
            {initial.disinfectionType === disinfectionTypeStatus.EXIGENCY && (
              <div className="pt-1 mt-1">
                <DailyDisinfectionInfo data={initial} isShowPlanningTime={false} />
              </div>
            )}
          </div>

          {!!initial?.reportIssueCount && initial?.reportNoReformCount === 0 && (
            <div>
              <img src={successPNG} className="size-[64px]" alt="" />
            </div>
          )}

          {(initial?.disinfectionType === 'DAILY' || initial?.disinfectionType === 'EXIGENCY') &&
            initial?.reportPositive && (
              <img
                src={PositiveReport}
                className="absolute"
                style={{
                  right: !!initial?.reportIssueCount && initial?.reportNoReformCount === 0 ? 40 : 0,
                  top: !!initial?.reportIssueCount && initial?.reportNoReformCount === 0 ? -20 : 0,
                }}
                alt=""
              />
            )}
        </div>
      )}

      <div className="my-3  flex justify-around">
        {showItem?.map((o) => {
          return (
            <div
              key={o.key}
              className="flex flex-col items-center"
              onClick={(e) => {
                e.stopPropagation();
                handleClick(o);
              }}
            >
              <span className={`${'text-[#F53F3F]'} text-15 leading-[23px]`}>{initial[o.key]}个</span>
              <span className="text-85 text-xs  leading-[20px]">{o.label}</span>
            </div>
          );
        })}
      </div>
      <div className=" w-full border border-t border-[#f7f7f7]" />
      <div className=" text-13 leading-[21px] text-85 flex justify-between items-center py-1">
        <div className=" flex mt-1">
          {initial?.worksheetList?.map((item) => (
            <div className="rounded-sm border border-[#DCDCDC] px-2 mr-2" key={item.worksheetId}>
              {item.worksheetName}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

const completeCard = (initial: DisinfectionItem) => {
  const isDudao = roleTypeIsManage();

  return (
    <div className="bg-white rounded-lg pt-3  relative mb-3">
      <div className="px-3">
        <div className="bg-primary rounded w-fit text-white px-1 text-sm">
          {disinfectionTypeValue?.[initial?.disinfectionType]}
        </div>
      </div>
      <div className="flex justify-between  px-3 items-center">
        {initial.shopId} {initial.shopName}
        {/* <div className="text-primary text-sm">
          <DsinfectionStatusTag status={initial?.taskStatus} />
        </div> */}
      </div>
      {DisinfectionResBuilder(initial)}
      <OperationBarDisinfection
        isDudao={isDudao}
        taskId={initial.id}
        count={initial.reportNoReformCount}
        pollingType="DisinfectionPolling"
        disinfectionType={initial?.disinfectionType}
        isDAILY={
          initial?.disinfectionType === disinfectionTypeStatus.DAILY ||
          initial?.disinfectionType === disinfectionTypeStatus.EXIGENCY
        }
        entity={initial}
      />
    </div>
  );
};
// const unCompleteCard = (initial: DisinfectionItem) => {
//   return (
//     <div className="bg-white rounded-lg  relative pb-2 mb-3">
//       <div className="flex justify-between mb-2  pr-2 ">
//         <div className="bg-[#378bff] text-xs  text-white py-1 px-2 rounded-l-lg ">
//           {DisinfectionType[initial?.disinfectionType]}
//         </div>
//         <IconFont type="icon-chevron-right" className="text-base text-[#B8B8B8]" />
//       </div>

//       <div className="flex justify-start   p-2">
//         <DsinfectionStatusTag status={initial?.taskStatus} />
//         <div className="ml-2">
//           {initial.shopId} {initial.shopName}
//         </div>
//       </div>
//       <div className=" flex justify-between items-center bg-[#fafafa]   p-2 py-2 mx-2">
//         <div>
//           <div className="flex items-center mt-2">
//             <span className=" text-[#858585] text-xs leading-[12px]">创建人:{initial?.createUserName || '-'}</span>
//           </div>
//           <div className="flex items-center mt-2">
//             <span className=" text-[#858585] text-xs leading-[12px]">
//               消杀公司:{initial?.disinfectionCompanyName || '-'}
//             </span>
//           </div>
//           <div className="flex items-center mt-2">
//             <span className=" text-[#858585] text-xs leading-[12px]">
//               到店时间: {dayjs(initial?.arriveTime).format('YYYY/MM/DD HH:mm')}
//             </span>
//           </div>
//         </div>
//       </div>
//       <div className="text-85 text-13  px-3 leading-[21px] mt-2 flex flex-col gap-y-1">
//         <div className="flex gap-x-1">
//           <span>创建时间:</span>
//           <span>
//             {dayjs(initial?.taskStartTime).format('YYYY/MM/DD HH:mm')}—
//             {dayjs(initial?.taskClosedTime).format('YYYY/MM/DD HH:mm')}
//           </span>
//         </div>
//       </div>
//     </div>
//   );
// };
export const DisinfectionTaskCard = ({ initial }: CardProps) => {
  return <React.Fragment>{completeCard(initial)}</React.Fragment>;
};
