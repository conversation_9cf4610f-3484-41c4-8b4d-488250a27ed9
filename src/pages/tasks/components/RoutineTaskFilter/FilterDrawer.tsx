import { Drawer, DrawerProps } from 'antd';
import styles from './index.module.scss';
import { Button } from 'antd-mobile';
import { useMemo } from 'react';

interface FilterDrawerProps extends DrawerProps {
  children: React.ReactNode;
  onOk: () => void;
  onClear: () => void;
}

export const FilterDrawer = ({ children, onOk, onClear, ...props }: FilterDrawerProps) => {
  const drawerHeight = useMemo(
    () =>
      document.body.clientHeight -
      (document.getElementById('filterDrawerContainer')?.getBoundingClientRect().bottom || 0) +
      'px',
    [props.open],
  );
  return (
    <Drawer
      {...props}
      placement="top"
      // getContainer={() => document.getElementById('filterDrawerContainer')!}
      rootStyle={{
        position: 'absolute',
        top: document.getElementById('filterDrawerContainer')?.getBoundingClientRect().bottom,
        left: 0,
        height: drawerHeight,
      }}
      rootClassName={styles.drawer}
      closable={false}
      height="auto"
    >
      <div className="pt-7 pb-5 px-3">{children}</div>
      <div className="p-4 flex gap-3">
        <Button
          block
          // style={{
          //   '--background-color': '#D8D8D8',
          //   '--text-color': '#58595B',
          // }}
          className="h-[42px] text-sm"
          color="primary"
          fill="outline"
          onClick={onClear}
        >
          清空筛选
        </Button>
        <Button onClick={onOk} block className="h-[42px] text-sm" color="primary">
          确定筛选
        </Button>
      </div>
    </Drawer>
  );
};
