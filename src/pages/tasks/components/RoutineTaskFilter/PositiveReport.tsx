import { memo } from 'react';
import { Select } from 'antd';

type TProps = {
  value: string;
  onChange: (value: string) => void;
};

export default memo<TProps>(function PositiveReport({ value, onChange }) {
  console.log('🚀 ~ PositiveReport ~ value:', value);
  return (
    <div className="flex items-center justify-between">
      <h3 className="text-[#141414] text-sm leading-[14px]">阳性报告</h3>
      <Select
        defaultValue="ALL"
        style={{ width: 200 }}
        options={[
          { label: '全部', value: 'ALL' },
          {
            label: '是',
            value: true,
          },
          {
            label: '否',
            value: false,
          },
        ]}
        value={value ?? 'ALL'}
        onChange={onChange}
        allowClear
        filterOption={false}
        placeholder="请选择"
      />
    </div>
  );
});
