import { useRequest } from 'ahooks';
import { Select } from 'antd';
import { querySimpleTaskList } from '../../api';
import { debounce, uniqBy, concat } from 'lodash';
import { useState } from 'react';

interface WorkNameProps {
  value?: any;
  onChange?: any;
}

export const WorkName = ({ value, onChange }: WorkNameProps) => {
  const [initData, setInitData] = useState([]);
  const { data, run } = useRequest((val) => querySimpleTaskList({ taskTitle: val }), {
    manual: true,
  });
  useRequest(
    async () => {
      const res = await querySimpleTaskList({ taskIds: [value] });
      setInitData(res);
    },
    {
      ready: value,
    },
  );
  const handleSearch = debounce((val) => {
    run(val);
  }, 500);
  return (
    <div className="flex items-center justify-between">
      <h3 className="text-[#141414] text-sm leading-[14px]">任务名称</h3>
      <Select
        showSearch
        style={{ width: 200 }}
        options={uniqBy(concat(data || [], initData || []), 'taskId')}
        placeholder="请选择任务名称"
        fieldNames={{
          value: 'taskId',
          label: 'taskTitle',
        }}
        filterOption={false}
        value={value}
        onChange={onChange}
        allowClear
        onSearch={(val) => {
          setInitData([]);
          !!val && val.length >= 1 && handleSearch(val);
        }}
      />
    </div>
  );
};
