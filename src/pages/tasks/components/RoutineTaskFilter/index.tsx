import { DrawerProps, Select } from 'antd';
import { FilterDrawer } from './FilterDrawer';
import { useState } from 'react';
import { WorkName } from './WorkName';
import { CreatePeople } from './CreatePeople';
import { DealPeople } from './DealPeople';
import { TaskTypeEnum } from '../../enum';
import { SortEnum } from '@src/common/api.type';

interface RoutineTaskFilterProps extends DrawerProps {
  value: Record<string, any>;
  queryType: any;
  onChange: (value: Record<string, any>) => void;
  onClose: () => void;
}

const SHOWITEM = {
  principal: {
    create: true,
  },
  created: {
    deal: true,
  },
  recipients: {
    deal: true,
  },
  under: {
    create: true,
    deal: true,
  },
};
export const init = {
  taskCreatorIds: [],
  taskInfoId: undefined,
  taskPrincipalIds: [],
  sortBy: SortEnum.DESC,
};

export const RoutineTaskFilter = ({
  value,
  onChange,
  onClose,
  queryType,
  ...props
}: RoutineTaskFilterProps) => {
  const [activity, setActivity] = useState<any>(value);
  const onOk = () => {
    onChange(activity);
  };
  const onClear = () => {
    setActivity(init);
  };
  TaskTypeEnum;
  return (
    <FilterDrawer
      {...props}
      onClose={() => {
        // 重置一下activity
        setActivity(value);
        onClose();
      }}
      onOk={onOk}
      onClear={onClear}
    >
      <div className="flex flex-col gap-6">
        {(SHOWITEM as any)[queryType]?.create && (
          <CreatePeople
            value={activity?.taskCreatorIds}
            onChange={(val: number) => {
              setActivity((pre: any) => ({
                ...pre,
                taskCreatorIds: val,
              }));
            }}
          />
        )}
        {(SHOWITEM as any)[queryType]?.deal && (
          <DealPeople
            value={activity?.taskPrincipalIds}
            onChange={(val: number) => {
              setActivity((pre: any) => ({
                ...pre,
                taskPrincipalIds: val,
              }));
            }}
          />
        )}
        <WorkName
          value={activity?.taskInfoId && Number(activity?.taskInfoId)}
          onChange={(val: number) => {
            setActivity((pre: any) => ({
              ...pre,
              taskInfoId: val,
            }));
          }}
        />
        <div className="flex items-center justify-between">
          <h3 className="text-[#141414] text-sm leading-[14px]">排序</h3>
          <Select
            defaultValue={activity?.sortBy}
            style={{ width: 200 }}
            onChange={(val) => {
              setActivity((pre: any) => ({
                ...pre,
                sortBy: val,
              }));
            }}
            placeholder="请选择"
            options={[
              { label: '截止时间最近', value: SortEnum.DESC },
              { label: '截止时间最远', value: SortEnum.ASC },
            ]}
          />
        </div>
      </div>
    </FilterDrawer>
  );
};
