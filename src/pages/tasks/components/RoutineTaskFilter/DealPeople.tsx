import { useRequest } from 'ahooks';
import { Select } from 'antd';
import { detailPageByIds, getUserList } from '../../api';
import { debounce, uniqBy, concat } from 'lodash';
import { useMemo } from 'react';

interface DealPeopleProps {
  value?: any;
  onChange?: any;
}

export const DealPeople = ({ value, onChange }: DealPeopleProps) => {
  const { data, run } = useRequest((val) => getUserList(val), {
    manual: true,
  });
  const { data: initData } = useRequest(() => detailPageByIds(value), {
    ready: (value || []).length !== 0,
  });
  const initOptions = useMemo(() => {
    const options = initData?.result?.filter((item: { userId: number }) => {
      return value?.includes(item?.userId);
    });
    return options;
  }, [initData, value]);

  const handleSearch = debounce((val) => {
    run(val);
  }, 500);
  return (
    <div className="flex items-center justify-between">
      <h3 className="text-[#141414] text-sm leading-[14px]">处理人</h3>
      <Select
        showSearch
        style={{ width: 200 }}
        options={uniqBy(concat(data?.result || [], initOptions || []), 'userId')}
        mode="multiple"
        placeholder="请输入选择处理人"
        fieldNames={{
          value: 'userId',
          label: 'nickName',
        }}
        filterOption={false}
        value={value}
        onChange={onChange}
        allowClear
        onSearch={(val) => {
          !!val && val.length >= 1 && handleSearch(val);
        }}
      />
    </div>
  );
};
