import { useEffect, useState } from 'react';
import { getDisinfectionCompany } from '@src/pages/mission/api';
import { useRequest } from 'ahooks';
import { Select } from 'antd';
interface CompanyProps {
  value?: number;
  onChange?: any;
}

export const DisinfectionCompany = ({ value, onChange }: CompanyProps) => {
  const [searchText, setSearchText] = useState<string>('');
  const { data } = useRequest(() => getDisinfectionCompany(), {
    refreshDeps: [],
  });

  return (
    <div className="flex items-center justify-between">
      <h3 className="text-[#141414] text-sm leading-[14px]">消杀公司</h3>
      <Select
        showSearch
        style={{ width: 200 }}
        options={data}
        fieldNames={{
          value: 'id',
          label: 'name',
        }}
        value={value}
        onChange={onChange}
        allowClear
        filterOption={false}
        placeholder="请输入消杀公司"
        mode="multiple"
        onSearch={(val) => {
          setSearchText(val);
        }}
        onClear={() => {
          setSearchText('');
        }}
      />
    </div>
  );
};
