import { Drawer, DrawerProps } from 'antd';
import { CheckOutline } from 'antd-mobile-icons';
import styles from './index.module.scss';
import { KillTaskStatusEnum, KillTaskTypeEnum } from '../../enum';

interface TaskStatusFilterProps extends DrawerProps {
  value?: KillTaskStatusEnum | KillTaskTypeEnum;
  options: { label: string; value: KillTaskStatusEnum | KillTaskTypeEnum }[];
  onChange: (value: KillTaskStatusEnum | KillTaskTypeEnum) => void;
  needWaitingOption?: boolean;
}

const KillTaskStatusFilter: React.FC<TaskStatusFilterProps> = ({
  value,
  onChange,
  options,
  needWaitingOption = false,
  ...props
}) => {
  return (
    <Drawer
      {...props}
      placement="top"
      getContainer={() => document.getElementById('topBar')!}
      rootClassName={styles.drawer}
      closable={false}
      height="auto"
    >
      <div className="divide-y divide-[rgba(0,0,0,0.03)] px-4 bg-[#F7F7F7] text-sm leading-[14px] text-[#5E5E5E]">
        {options.map((i) => {
          const checked = value === i.value;
          return (
            <div
              key={i.label}
              className={`flex justify-between py-4 ${checked ? 'text-primary' : ''}`}
              onClick={() => onChange(i.value)}
            >
              {i.label}
              {checked && <CheckOutline className="text-base" />}
            </div>
          );
        })}
        {/* {needWaitingOption && (
          <div
            key="待确认"
            className={`flex justify-between py-4 ${
              value === KillTaskStatusEnum.Waiting ? 'text-primary' : ''
            }`}
            onClick={() => onChange(KillTaskStatusEnum.Waiting)}
          >
            待确认
            {value === KillTaskStatusEnum.Waiting && (
              <CheckOutline className="text-base" />
            )}
          </div>
        )} */}
      </div>
    </Drawer>
  );
};

export default KillTaskStatusFilter;
