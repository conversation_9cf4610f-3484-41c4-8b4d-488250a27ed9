import { useEffect, useState } from 'react';
import { IPopup, IPopupProps } from '@src/components/IPopup';
import { Button, Input, Popover, Toast } from 'antd-mobile';
import { QuestionCircleOutline } from 'antd-mobile-icons';

interface InvestPopupProps extends IPopupProps {
  okText: string;
  onOk: (value: number) => void;
  investRate?: any;
  task?: any;
  roi?: number;
  maxLength?: number;
  placeholder?: string;
}

const InvestPop: React.FC<InvestPopupProps> = ({
  title,
  visible,
  okText,
  investRate,
  task,
  roi,
  onOk,
  onClose,
  maxLength = 100,
  ...props
}) => {
  const [actualCost, setActualCost] = useState<number>(20);
  const [expectCost, setExpectCost] = useState<number>(300);

  /**
   * @description 获取某年某月的费用
   * @param year
   * @param month
   * @returns
   */
  const getMonthCost = (taskDeadline: any) => {
    const time = new Date(taskDeadline);
    const nextMonthFirstDay = new Date(time.getFullYear(), time.getMonth() + 1);
    const lastDayOfMonth = new Date(nextMonthFirstDay.getTime() - 1);

    return lastDayOfMonth.getDate() * 30;
  };
  useEffect(() => {
    setActualCost(Math.floor(investRate?.actualCost || 0));
    if (task?.taskDeadline) {
      setTimeout(() => {
        const _expectCost = getMonthCost(task?.taskDeadline);
        console.log('expectCost', _expectCost);
        if (_expectCost) {
          setExpectCost(_expectCost);
        }
      }, 1000);
    }
  }, [investRate, task, visible]);
  return (
    <IPopup
      {...props}
      visible={visible}
      title={title}
      onClose={onClose}
      footer={
        <div className="flex p-3 gap-3">
          <Button block color="primary" fill="outline" className="h-[45px] text-base" onClick={onClose}>
            取消
          </Button>
          <Button
            block
            color="primary"
            className="h-[45px] text-base"
            onClick={() => {
              if (!actualCost || !/^\d+$/.test(actualCost?.toString())) {
                Toast.show('请填写整数实际推广费用');
              } else if (!actualCost || Number(actualCost) < Number(expectCost)) {
                Toast.show(`实际推广费用不小于当月预估推广费用${expectCost}元`);
              } else {
                onOk(actualCost);
              }
            }}
          >
            {okText}
          </Button>
        </div>
      }
    >
      <div className="px-4 py-6">
        <div className="flex items-center gap-3 my-3">
          <div
            style={{
              width: '30%',
              minWidth: '110px',
            }}
          >
            预估推广费用
          </div>
          <div className="flex-grow">
            <Input
              type="text"
              name="estimatedCost"
              disabled
              value={investRate?.estimatedCost?.toString() ?? '-'}
              max={maxLength}
              style={{
                borderColor: '#ccc',
                borderWidth: '1px',
                padding: '4px 10px',
                borderRadius: '4px',
              }}
            />
          </div>
        </div>

        <div className="flex items-center gap-3 my-3">
          <div
            style={{
              width: '30%',
              minWidth: '110px',
            }}
          >
            实际推广费用
          </div>
          <div className="flex flex-row flex-grow">
            <Input
              placeholder="请填写"
              inputMode="numeric"
              value={actualCost?.toString() ?? ''}
              type="number"
              onChange={(val) => {
                setActualCost(Math.floor(Number(val)));
              }}
              style={{
                borderColor: actualCost < expectCost ? '#f00' : '#999',
                borderWidth: '1px',
                padding: '4px 10px',
                borderRadius: '4px',
                width: '88%',
              }}
            />
            <div
              style={{
                cursor: 'pointer',
                padding: '6px',
              }}
            >
              <Popover
                content={
                  <>
                    实际推广费用需大于投流月份天数*30元,
                    <br />
                    即日投流预算不得低于30元
                  </>
                }
                mode="dark"
                trigger="click"
                placement="left"
                defaultVisible={false}
              >
                <QuestionCircleOutline fontSize={18} color="#999" />
              </Popover>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-3 my-3">
          <div
            style={{
              width: '30%',
              minWidth: '110px',
            }}
          />
          {actualCost < expectCost && (
            <div
              className="flex flex-row flex-grow"
              style={{
                color: 'red',
                fontSize: '12px',
              }}
            >
              月推广费用不得低于{expectCost}元
            </div>
          )}
        </div>

        <div className="flex items-center gap-3 my-3">
          <div
            style={{
              width: '30%',
              minWidth: '110px',
            }}
          >
            预估投产比
          </div>
          <div className="flex flex-row flex-grow">
            <Input
              type="text"
              name="estimatedCost"
              disabled
              value={investRate?.roi?.toString() ?? '-'}
              max={maxLength}
              style={{
                borderColor: '#ccc',
                borderWidth: '1px',
                padding: '4px 10px',
                borderRadius: '4px',
                width: '90%',
              }}
            />
            <div
              style={{
                cursor: 'pointer',
                padding: '6px',
              }}
            >
              <Popover
                content={
                  <>
                    预估投产比：预估投产比为上月日均投
                    <br />
                    产比，上月无数据时取同城平均值。
                  </>
                }
                mode="dark"
                trigger="click"
                placement="left"
                defaultVisible={false}
              >
                <QuestionCircleOutline fontSize={18} color="#999" />
              </Popover>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-3 my-3">
          <div
            style={{
              width: '30%',
              minWidth: '110px',
            }}
          >
            预估推广收入
          </div>
          <div className="flex flex-row flex-grow">
            <Input
              type="text"
              name="estimatedRevenue"
              disabled
              value={String((investRate?.roi * parseInt(actualCost || investRate?.actualCost, 10)).toFixed(2))}
              max={maxLength}
              style={{
                borderColor: '#ccc',
                borderWidth: '1px',
                padding: '4px 10px',
                borderRadius: '4px',
                width: '90%',
              }}
            />
            <div
              style={{
                cursor: 'pointer',
                padding: '6px',
              }}
            >
              <Popover
                content={
                  <>
                    预估推广收入：预估推广收入=
                    <br />
                    实际推广费用*预估投产比
                  </>
                }
                mode="dark"
                trigger="click"
                placement="left"
                defaultVisible={false}
              >
                <QuestionCircleOutline fontSize={18} color="#999" />
              </Popover>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-3 my-3">
          <div
            style={{
              width: '30%',
              minWidth: '110px',
            }}
          >
            账户余额
          </div>
          <div className="flex-grow">
            <Input
              type="text"
              name="accountBalance"
              disabled
              value={investRate?.accountBalance?.toString() ?? '-'}
              max={maxLength}
            />
          </div>
        </div>
        {(Number(actualCost) ?? 0) > Number(investRate?.accountBalance ?? 0) && (
          <div className="flex items-center gap-3 my-3 text-[#ff0000]">
            请在截止时间之前完成充值，截止时间余额不足则无法进行本月投流业务
          </div>
        )}
      </div>
    </IPopup>
  );
};

export default InvestPop;
