import { Select } from 'antd';
import { useRequest } from 'ahooks';
import { getAllUserBase } from '../api';

interface AllUserSelectProps {
  value?: number;
  onChange?: any;
}

export const AllUserSelect = ({ value, onChange }: AllUserSelectProps) => {
  const { data } = useRequest(getAllUserBase);
  return (
    <div className="flex items-center justify-between">
      <h3 className="text-[#141414] text-sm leading-[14px]">巡检人</h3>
      <Select
        mode="multiple"
        showSearch
        style={{ width: 200 }}
        options={data}
        fieldNames={{
          value: 'userId',
          label: 'userName',
        }}
        value={value}
        onChange={onChange}
        filterOption={(input: string, option?: { userName: string; userId: number }) => {
          return (option?.userName ?? '').toLowerCase().includes(input.toLowerCase());
        }}
        allowClear
      />
    </div>
  );
};
