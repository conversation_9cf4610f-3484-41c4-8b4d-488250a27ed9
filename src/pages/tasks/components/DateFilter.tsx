import { Button, Calendar, CalendarProps, Popup } from 'antd-mobile';
import { DownOutline } from 'antd-mobile-icons';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import cn from 'classnames';

interface DateFilterProps
  extends Omit<
    CalendarProps,
    'value' | 'onChange' | 'defaultValue' | 'type' | 'shouldDisableDate'
  > {
  value: any;
  defaultValue?: any;
  onChange: (value: Record<string, any>) => void;
  shouldDisableDate?: (date: Date | null, selectedDate: [Date, Date] | null) => boolean;
  type?: 'range' | 'single';
}

export const DateFilter = ({
  value,
  onChange,
  shouldDisableDate,
  defaultValue,
  type = 'range',
  ...restProps
}: DateFilterProps) => {
  const [visible, setVisible] = useState(false);

  const [dateValue, setDateValue] = useState<any>(value);
  const onOk = () => {
    if (type === 'range') {
      if (dateValue) {
        onChange({
          start: dayjs(dateValue?.[0]).startOf('day').toISOString(),
          end: dayjs(dateValue?.[1]).endOf('day').toISOString(),
        });
      } else {
        onChange({
          start: undefined,
          end: undefined,
        });
      }
    } else {
      if (dateValue) {
        onChange({
          endDate: dayjs(dateValue).startOf('day').toISOString(),
        });
      } else {
        onChange({
          endDate: undefined,
        });
      }
    }
    setVisible(false);
  };

  const onClear = () => {
    if (defaultValue) {
      setDateValue(defaultValue);
    } else {
      setDateValue(null);
    }
  };

  useEffect(() => {
    visible && setDateValue(value);
  }, [visible]);

  return (
    <>
      <span
        className="flex items-center gap-x-1"
        onClick={() => {
          setVisible(true);
        }}
      >
        <span className={cn(`${!!value ? 'text-primary' : 'text-[#5E5E5E]'}`)}>
          {type === 'single' && (value ? `${dayjs(value).format('MM/DD')}` : '时间')}
          {type === 'range' &&
            (value
              ? `${dayjs(value?.[0]).format('MM/DD')}-${dayjs(value?.[1]).format('MM/DD')}`
              : '时间')}
        </span>
        <DownOutline className="text-xs text-[#B8B8B8]" />
      </span>
      <Popup
        position="top"
        visible={visible}
        bodyClassName="p-4 box-border"
        onMaskClick={() => {
          setVisible(false);
        }}
        destroyOnClose
      >
        <Calendar
          selectionMode={type as any}
          onChange={(val: any) => {
            setDateValue(val);
          }}
          value={dateValue}
          defaultValue={value}
          shouldDisableDate={(date) => !!shouldDisableDate && shouldDisableDate(date, dateValue)}
          {...restProps}
        />
        <div className="pt-4 flex gap-2 border-t border-[rgba(0, 0, 0, 0.03)]">
          <Button
            block
            // style={{
            //   '--background-color': '#D8D8D8',
            //   '--text-color': '#58595B',
            // }}
            onClick={onClear}
            color="primary"
            fill="outline"
          >
            清空筛选
          </Button>
          <Button onClick={onOk} block color="primary">
            确认筛选
          </Button>
        </div>
      </Popup>
    </>
  );
};
