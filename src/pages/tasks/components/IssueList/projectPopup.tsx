import { Loading } from '@src/components';
import { IPopup, IPopupProps } from '@src/components/IPopup';
import { useRequest } from 'ahooks';
import { ErrorBlock } from 'antd-mobile';
import { getProjectList, toStudy } from '../../pollingAbarbeitung/api';

interface ProjectPopupProps extends IPopupProps {
  issueId: number | undefined;
}
const ProjectPopup: React.FC<ProjectPopupProps> = ({ issueId, onClose, title, visible, ...props }) => {
  const { data: projectList, loading } = useRequest(
    async () => {
      if (!issueId) {
        // eslint-disable-next-line no-throw-literal
        throw null;
      }
      const res = visible && (await getProjectList(issueId));
      return res || [];
    },
    { refreshDeps: [issueId, visible] },
  );
  return (
    <IPopup
      {...props}
      visible={visible}
      title={title}
      onClose={onClose}
      bodyStyle={{ maxHeight: '80vh', minHeight: '80vh' }}
    >
      {visible && (
        <Loading spinning={loading}>
          <div className="p-4 flex-col overflow-y-scroll">
            {!!projectList?.length ? (
              projectList?.map((v: any, index: number) => {
                return (
                  <div className="flex justify-between items-center mb-4" key={index}>
                    <span>{v?.studyProjectName}</span>
                    <a
                      onClick={async () => {
                        const res = await toStudy({ userStudyDetailId: v?.id });
                        console.log(res, '=res');

                        window.location.href = res;
                      }}
                      className="text-primary"
                    >
                      去学习 》
                    </a>
                  </div>
                );
              })
            ) : (
              <ErrorBlock status="empty" description="暂无数据" />
            )}
          </div>
        </Loading>
      )}
    </IPopup>
  );
};

export default ProjectPopup;
