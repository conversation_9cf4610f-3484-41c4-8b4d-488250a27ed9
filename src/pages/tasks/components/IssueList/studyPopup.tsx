import { Loading } from '@src/components';
import { IPopup, IPopupProps } from '@src/components/IPopup';
import { useRequest } from 'ahooks';
import { ErrorBlock } from 'antd-mobile';
import { StudyStatusMap, StudyStatusType } from './IssueCardShop';
import { getStudyList } from '../../pollingAbarbeitung/api';

interface StudyPopupProps extends IPopupProps {
  id: number | undefined;
}
const StudyPopup: React.FC<StudyPopupProps> = ({ id, onClose, title, visible, ...props }) => {
  const { data: studyList, loading } = useRequest(
    async () => {
      if (!id) {
        // eslint-disable-next-line no-throw-literal
        throw null;
      }
      const res = visible && (await getStudyList(id));
      return res || [];
    },
    { refreshDeps: [id, visible] },
  );
  return (
    <IPopup
      {...props}
      visible={visible}
      title={title}
      onClose={onClose}
      bodyStyle={{ maxHeight: '80vh', minHeight: '80vh' }}
    >
      {visible && (
        <Loading spinning={loading}>
          <div className="p-4 flex-col overflow-y-scroll">
            {!!studyList?.length ? (
              studyList?.map((v: any, index: number) => {
                return (
                  <div className="flex justify-between items-center mb-4" key={index}>
                    <span>{v?.nickname}</span>
                    <span>{v?.studyProjectName}</span>
                    <span>{StudyStatusMap[v?.status as StudyStatusType]}</span>
                  </div>
                );
              })
            ) : (
              <ErrorBlock status="empty" description="暂无数据" />
            )}
          </div>
        </Loading>
      )}
    </IPopup>
  );
};

export default StudyPopup;
