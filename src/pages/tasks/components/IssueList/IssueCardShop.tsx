import React, { useEffect, useState } from 'react';
import UserAvatar from '@src/components/UserAvatar';
import VideoViewer from '@src/components/VideoViewer';
import { OperationTypeEdit } from '@src/pages/tasks/pollingAbarbeitung/Edit/index';
import { roleTypeIsManage } from '@src/utils/tokenUtils';
import { useRequest } from 'ahooks';
import { Image, ImageViewer, Toast } from 'antd-mobile';
import { DownOutline, PlayOutline, UpOutline } from 'antd-mobile-icons';
import dayjs from 'dayjs';
import { useNavigate } from 'react-router-dom';
import ProjectPopup from './projectPopup';
import StudyPopup from './studyPopup';
import { addPollingNormal, expendSelf, passAI, passSelf } from '../../pollingAbarbeitung/api';
import ItemTag from '../OptionsDetail/itemTag';
// import VideoViewer from '@src/components/VideoViewer';
// eslint-disable-next-line react-refresh/only-export-components
export enum StatusNum {
  all = -1,
  process = 1,
  unReviewed = 2,
  overdue = 3,
  completed = 4,
}
// enum OperationType {
//   提交整改反馈,
//   驳回,
//   审核通过,
// }
enum OperationType {
  CREATED = '提交待整改内容',
  SUPERVISOR_REJECTED = '驳回申请',
  SHOP_REJECTED = '提交AI识别错误',
  REFORM_SUBMITTED = '提交整改反馈',
  COMPLETED = '通过整改反馈',
  PAST_DUE = '逾期',
}
// eslint-disable-next-line react-refresh/only-export-components
export enum Status {
  UNRECTIFIED = 'UNRECTIFIED',
  UNAUDITED = 'UNAUDITED',
  RECTIFIED = 'RECTIFIED',
  OVERDUE = 'OVERDUE',
}
// 待整改包含CREATED  SUPERVISOR_REJECTED两种
// 待审核包含SHOP_REJECTED REFORM_SUBMITTED两种
enum AIlStatus {
  CREATED = 'UNRECTIFIED',
  SUPERVISOR_REJECTED = 'UNRECTIFIED',
  UNRECTIFIED = 'UNRECTIFIED',
  SHOP_REJECTED = 'UNAUDITED',
  REFORM_SUBMITTED = 'UNAUDITED',
  RECTIFIED = 'RECTIFIED',
  COMPLETED = 'RECTIFIED',
  PAST_DUE = 'OVERDUE',
  UNAUDITED = 'UNAUDITED',
  OVERDUE = 'OVERDUE',
  INVALID = 'INVALID',
  AUDIT_OVERDUE = 'AUDIT_OVERDUE',
}

// eslint-disable-next-line react-refresh/only-export-components
export enum AIlStatusMap {
  UNRECTIFIED = '待整改',
  UNAUDITED = '待审核',
  RECTIFIED = '已整改',
  OVERDUE = '已过期',
  INVALID = '已作废',
  AUDIT_OVERDUE = '已逾期',
}
export enum StudyStatusMap {
  WAITING_COMPLETE = '待完成',
  COMPLETED = '已完成',
  EXPIRED_WAITING_COMPLETE = '逾期未完成',
  EXPIRED_COMPLETE = '逾期已完成',
  NO_COMPLETED = '未完成',
  WAITING_SEND = '待下发',
}
export type StudyStatusType =
  | 'WAITING_COMPLETE'
  | 'COMPLETED'
  | 'EXPIRED_WAITING_COMPLETE'
  | 'EXPIRED_COMPLETE'
  | 'NO_COMPLETED'
  | 'WAITING_SEND';

export enum StudyStatusCNMap {
  学习进行中 = 'WAITING_COMPLETE',
  学习已完成 = 'COMPLETED',
  学习未完成 = 'NO_COMPLETED',
  逾期未完成 = 'EXPIRED_WAITING_COMPLETE',
  逾期已完成 = 'EXPIRED_COMPLETE',
  待下发 = 'WAITING_SEND',
}

enum AIStatusColor {
  UNRECTIFIED = 'text-[#E05F42]',
  UNAUDITED = 'text-[#378BFF]',
  RECTIFIED = 'text-[#3DB86D]',
  OVERDUE = 'text-[#DDDDDD]',
}
type IssueCardProps = {
  cross?: string;
  className?: string;
  data: any;
  onRefesh?: () => void;
  pollingType?: string | undefined;
  shopId?: string;
  /** 是否不折叠 */
  isNotFold?: boolean;
  /** 是否隐藏操作按钮 */
  hiddenOperation?: boolean;
};

const IssueCard: React.FC<IssueCardProps> = ({
  cross = 'true',
  data,
  pollingType,
  onRefesh,
  shopId,
  isNotFold = false,
  hiddenOperation = false,
}) => {
  const navigate = useNavigate();
  const {
    status,
    studyStatus, // 学习考试状态
    issueId,
    itemImages,
    submitedUserName,
    itemRemark, // 默认的巡检意见
    selectReasons, // 不合格原因
    otherReason, // 其他原因
    copyUsers = [], // 抄送人
    id,
    processes, // 整改的操作流程
    labels,
    workSheetItemName, // 检查项内容
    workSheetName, // 检查表名称
    worksheetName, // 兼容自检检查表名称
    workSheetCategoryName, // 检查项分类
    shopNo,
    shopName,
    taskPlanName,
    elapsedTime, // 整改耗时
    deadline, // 截止日期
    rectifyMustUpload, // 判断图片是否必传
    shopId: dataShopId,
  } = data;

  const [videoPreviewVisible, setVideoPreviewVisible] = useState(false);
  const [videoPreviewUrl, setVideoPreviewUrl] = useState('');
  const [studyPopupVisible, setStudyPopupVisible] = useState(false);
  const [projectPopupVisible, setProjectPopupVisible] = useState(false);

  const { run: passRun } = useRequest(
    async () => {
      if (pollingType === 'AIPolling') {
        return await passAI({ reportId: id });
      }
      if (pollingType === 'NormalPolling') {
        return await addPollingNormal({ id, status: 2 });
      }
      if (pollingType === 'SelfPolling') {
        return await passSelf({ id });
      }
    },
    {
      manual: true,
      onSuccess: () => {
        Toast.show('审核通过');
        onRefesh?.();
      },
    },
  );

  // const { run: toStudyRun } = useRequest(
  //   async () => {
  //     return await toStudy({ userStudyDetailId: issueId });
  //   },
  //   {
  //     manual: true,
  //     onSuccess: (res) => {
  //       console.log('studyData', res);
  //       window.location.href = res;
  //     },
  //   },
  // );
  const { run: expendRun } = useRequest(
    async () => {
      const res = await expendSelf(id);
      data.processes = (res?.selfReportIssueProcesses || []).map((item: any) => {
        return {
          ...item,
          submitedUserName: item.createByName,
          submitedUserAvatar: item.createByUserAvatar,
          type: item.processType,
          submitTime: item.createdAt,
          issueImages: item.feedbackResources,
          text: item.feedbackText,
        };
      });
    },
    {
      manual: true,
    },
  );

  const [roleTypeIsShop, setRoleTypeIsShop] = useState(!roleTypeIsManage());
  const [expend, setExpend] = useState(!!isNotFold);
  const [usersExpend, setUsersExpend] = useState(false);

  type tagString = 'RED_LINE' | 'NECESSARY' | 'YELLOW' | 'KEY' | 'PENALTY';
  type tagType = tagString[];

  const renderTags = (labels: tagType) => {
    const tagsMap = {
      RED_LINE: (
        <ItemTag className="mr-1" color="#F53F3F" bg="rgba(245, 63, 63, 0.05)" borderColor="#FBACA3" text="S项">
          S项
        </ItemTag>
      ),
      NECESSARY: (
        <ItemTag className="mr-1" color="#4E5969" bg="rgba(78, 89, 105, 0.05)" borderColor="#C9CDD4" text="必检项">
          必检项
        </ItemTag>
      ),
      YELLOW: (
        <ItemTag className="mr-1" color="#4E5969" bg="rgba(78, 89, 105, 0.05)" borderColor="#C9CDD4" text="M项">
          M项
        </ItemTag>
      ),
      KEY: (
        <ItemTag className="mr-1" color="#4E5969" bg="rgba(78, 89, 105, 0.05)" borderColor="#C9CDD4" text="关键项">
          关键项
        </ItemTag>
      ),
      PENALTY: (
        <ItemTag className="mr-1" color="#4E5969" bg="rgba(78, 89, 105, 0.05)" borderColor="#C9CDD4" text="罚款项">
          罚款项
        </ItemTag>
      ),
      POSITIVE: (
        <ItemTag className="mr-1" color="#4E5969" bg="rgba(78, 89, 105, 0.05)" borderColor="#C9CDD4" text="阳性指标项">
          阳性指标项
        </ItemTag>
      ),
    };
    return (labels || []).map((label: tagString) => {
      return tagsMap[label];
    });
  };

  const renderButtons = () => {
    let buttons: any[] = [];
    // AI巡检
    if (pollingType === 'AIPolling') {
      if (roleTypeIsShop) {
        // 门店端
        const toRectify = () => {
          navigate(
            `/tasks/pollingAbarbeitung/edit?reportId=${id}&watchName=${submitedUserName}&&operationType=${OperationTypeEdit.FeedbackAI}&rectifyMustUpload=${rectifyMustUpload}&shopId=${shopId || dataShopId}`,
          );
          setRoleTypeIsShop(false);
        };
        switch (status) {
          case 'CREATED':
            buttons = [
              {
                color: 'error',
                btnName: 'AI识别错误',
                onClick: () => {
                  navigate(
                    `/tasks/pollingAbarbeitung/edit?reportId=${id}&watchName=${submitedUserName}&operationType=${OperationTypeEdit.ErrorAI}&rectifyMustUpload=${rectifyMustUpload}&shopId=${shopId || dataShopId}`,
                  );
                  setRoleTypeIsShop(false);
                },
                size: 18,
              },
              {
                color: 'process',
                btnName: '整改反馈',
                onClick: () => toRectify(),
              },
            ];
            break;
          case 'SUPERVISOR_REJECTED':
            buttons = [
              {
                color: 'process',
                btnName: '整改反馈',
                onClick: () => toRectify(),
              },
            ];
            break;
          default:
            break;
        }
      } else {
        // 公司端
        switch (status) {
          case 'SHOP_REJECTED':
            buttons = [
              {
                color: 'error',
                btnName: '驳回',
                onClick: () => {
                  setRoleTypeIsShop(true);
                  navigate(
                    // `/tasks/pollingAbarbeitung/edit?reportId=${id}&watchName=${submitedUserName}&operationType=${OperationTypeEdit.OverruleAI}&rectifyMustUpload=${rectifyMustUpload}&shopId=${shopId}`,
                    `/tasks/pollingAbarbeitung/edit?reportId=${id}&watchName=${submitedUserName}&operationType=${OperationTypeEdit.AnomalyAI}&rectifyMustUpload=${rectifyMustUpload}&shopId=${shopId || dataShopId}`,
                  );
                },
              },
              {
                color: 'process',
                btnName: '审核通过',
                onClick: () => passRun(),
              },
            ];
            break;
          case 'REFORM_SUBMITTED':
            buttons = [
              {
                color: 'error',
                btnName: '驳回',
                onClick: () => {
                  setRoleTypeIsShop(true);
                  navigate(
                    `/tasks/pollingAbarbeitung/edit?reportId=${id}&watchName=${submitedUserName}&operationType=${OperationTypeEdit.OverruleAI}&rectifyMustUpload=${rectifyMustUpload}&shopId=${shopId || dataShopId}`,
                  );
                },
              },
              {
                color: 'process',
                btnName: '审核通过',
                onClick: () => passRun(),
              },
            ];
            break;
          default:
            break;
        }
      }
    }
    if (pollingType === 'SelfPolling') {
      if (roleTypeIsShop) {
        // 门店端
        const toRectify = () => {
          navigate(
            `/tasks/pollingAbarbeitung/edit?reportId=${id}&watchName=${submitedUserName}&&operationType=${OperationTypeEdit.AddPollingSelf}&rectifyMustUpload=${rectifyMustUpload}&shopId=${shopId || dataShopId}`,
          );
          setRoleTypeIsShop(false);
        };
        switch (status) {
          case 'SUPERVISOR_REJECTED': // 整改反馈被驳回
            buttons = [
              {
                color: 'process',
                btnName: '整改反馈',
                onClick: () => toRectify(),
              },
            ];
            break;
          case 'CREATED':
            buttons = [
              {
                color: 'process',
                btnName: '整改反馈',
                onClick: () => toRectify(),
              },
            ];

            break;
          case 'UNRECTIFIED':
            buttons = [
              {
                color: 'process',
                btnName: '整改反馈',
                onClick: () => toRectify(),
              },
            ];
            break;
          default:
            break;
        }
      } else {
        switch (status) {
          case 'SHOP_REJECTED':
            buttons = [
              {
                color: 'error',
                btnName: '驳回',
                onClick: () => {
                  // navigate(`/tasks/pollingAbarbeitung/edit?reportId=${id}&watchName=${submitedUserName}&&operationType=${OperationTypeEdit.Reject}`,)
                },
              },
              {
                color: 'process',
                btnName: '审核通过',
                onClick: () => passRun(),
              },
            ];
            break;
          case 'REFORM_SUBMITTED':
            buttons = [
              {
                color: 'error',
                btnName: '驳回',
                onClick: () => {
                  navigate(
                    `/tasks/pollingAbarbeitung/edit?reportId=${id}&watchName=${submitedUserName}&operationType=${OperationTypeEdit.OverruleSelf}&rectifyMustUpload=${rectifyMustUpload}&shopId=${shopId || dataShopId}`,
                  );
                },
              },
              {
                color: 'process',
                btnName: '审核通过',
                onClick: () => passRun(),
              },
            ];
            break;
          default:
            break;
        }
      }
    }
    if (pollingType === 'NormalPolling') {
      if (roleTypeIsShop) {
        // 门店端
        const toRectify = () => {
          navigate(
            `/tasks/pollingAbarbeitung/edit?reportId=${id}&watchName=${submitedUserName}&&operationType=${OperationTypeEdit.AddOverruleNormal}&status=0&rectifyMustUpload=${rectifyMustUpload}&shopId=${shopId || dataShopId}`,
          );
        };
        switch (status) {
          case 'UNRECTIFIED':
            buttons =
              // studyStatus === 'WAITING_COMPLETE'
              //   ? [
              //       {
              //         btnName: '整改',
              //         onClick: () => toRectify(),
              //       },
              //       {
              //         btnName: '学习考试',
              //         onClick: () => toStudyRun(),
              //       },
              //     ]
              //   :
              [
                {
                  btnName: '整改',
                  onClick: () => toRectify(),
                },
              ];
            break;
          default:
            break;
        }
      } else {
        switch (status) {
          case 'UNAUDITED':
            if (dayjs(data?.auditDeadlineTime).isAfter(dayjs())) {
              buttons = [
                {
                  btnName: '驳回',
                  onClick: () => {
                    navigate(
                      `/tasks/pollingAbarbeitung/edit?reportId=${id}&watchName=${submitedUserName}&&operationType=${OperationTypeEdit.AddOverruleNormal}&status=1&rectifyMustUpload=${rectifyMustUpload}&shopId=${shopId || dataShopId}&auditDeadlineTime=${data?.auditDeadlineTime}`,
                    );
                  },
                },
                {
                  btnName: '审核通过',
                  onClick: () => passRun(),
                },
              ];
            }
            break;
          default:
            break;
        }
      }
      [StudyStatusCNMap.学习进行中].includes(studyStatus) &&
        roleTypeIsShop &&
        buttons.push({
          btnName: '学习考试',
          onClick: () => setProjectPopupVisible(true),
        });
    }

    if (hiddenOperation) {
      return null;
    }

    return (
      <>
        {buttons?.length > 0 && (
          <div
            className={`${buttons.length === 1 ? 'flex justify-center border-[0.5px] border-[#378BFF] py-[9px] rounded mt-3 text-[#378BFF]' : 'justify-between flex'} `}
          >
            {buttons.map(({ btnName, onClick }: any, index: number) => {
              return (
                <div
                  key={index}
                  className={`${buttons.length === 1 ? '' : 'px-10  border-[0.5px] border-[#378BFF]   py-[9px] rounded mt-3 text-[#378BFF]'} `}
                  onClick={onClick}
                >
                  {btnName}
                </div>
              );
            })}
          </div>
        )}
      </>
    );
  };

  const reasonListText = (reasonLis: string[], otherReason: string, isText?: boolean) => {
    let text: (string | JSX.Element)[] | string = !!reasonLis
      ? reasonLis?.map((v: string) => {
          return isText ? `#${v} ` : <div>{v}</div>;
        })
      : [];
    if (!!otherReason) {
      Array.isArray(text) ? text.push(`#${otherReason}`) : (text = `#${otherReason}`);
    }
    return text || '';
  };

  const imagesOrVideoRender = (
    images: {
      url: string;
      type: 'IMG' | 'VIDEO';
      id: string;
      snapshotUrl: string;
    }[],
  ) => {
    return (
      !!images?.length && (
        <div className={`flex  flex-wrap gap-1 items-center`}>
          {images.map((item) => {
            return (
              <div key={item.url}>
                {item.type === 'VIDEO' ? (
                  <div
                    onClick={() => {
                      setVideoPreviewVisible(true);
                      setVideoPreviewUrl(item.url);
                    }}
                    className="relative "
                  >
                    <video
                      style={{ objectFit: 'cover' }}
                      poster={item?.snapshotUrl}
                      className="w-[48px] h-[48px]"
                      src={item.url}
                    />
                    <div className="absolute left-[50%] top-[50%] -translate-x-[50%] -translate-y-[50%]">
                      <PlayOutline fontSize={30} />
                    </div>
                  </div>
                ) : (
                  <Image
                    onClick={() => {
                      ImageViewer.show({ image: item.url });
                    }}
                    src={item?.snapshotUrl || item.url}
                    width={48}
                    height={48}
                  />
                )}
              </div>
            );
          })}
        </div>
      )
    );
  };

  const hasContent =
    (itemRemark && itemRemark?.length !== 0) ||
    (selectReasons && selectReasons?.length !== 0) ||
    (itemImages && itemImages?.length > 0) ||
    otherReason;

  useEffect(() => {
    isNotFold && pollingType === 'SelfPolling' && expendRun();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isNotFold, pollingType]);

  const renderTimeNode = (data: any) => {
    if (data?.processes?.length > 0 && data.processes[data.processes.length - 1]?.type === 'SUPERVISOR_REJECTED') {
      return `任务截止时间 ${dayjs(data?.auditDeadlineTime).format('MM月DD日 HH时mm分')}`;
    }

    if (data?.status === AIlStatus.UNAUDITED) {
      return `审核截止时间 ${dayjs(data?.auditDeadlineTime).format('MM月DD日 HH时mm分')}`;
    }

    return `截止时间 ${dayjs(data?.deadline).format('MM月DD日')}`;
  };
  return (
    <div className={` bg-white rounded-lg `}>
      <div className="cardbody ">
        <div className="text-sm leading-[14px] flex justify-between border-b  border-b-[0.5px] border-[#F0F0F0] py-3 px-4 text-[#141414]">
          <span>
            {status === 'COMPLETED'
              ? // ? `整改耗时 ${getElapsed(processes)}`
                `整改耗时 ${elapsedTime}`
              : renderTimeNode(data)}
          </span>
          <span className={`${(AIStatusColor as any)[(AIlStatus as any)[status]]}`}>
            {' '}
            {(AIlStatusMap as any)[(AIlStatus as any)[status]]}
          </span>
        </div>
        {taskPlanName && (
          <div className="flex items-center mt-2 pl-2">
            <span className="ml-2 text-text-minor text-sm leading-[14px]  text-[#141414] font-medium">
              {taskPlanName}
            </span>
          </div>
        )}
        <div className="p-3">
          <div className="border-b-[0.5px] pb-2 border-[#F0F0F0] font-medium">
            <div className="text-base pt-1">
              {shopNo}：{shopName}
            </div>
            {/* <div className="text-sm pt-1">自检测试信息</div> */}
            <span>
              {/* <AppstoreOutline className="w-[12px] h-[12px] inline-block" /> */}
              <span className="ml-1 text-text-minor  text-xs leading-5 text-[#141414] ">
                {/* {checkListTypeName} / {checkListName} */}
                {workSheetName || worksheetName} / {workSheetCategoryName}
              </span>
            </span>
          </div>
          <div className="mt-3 justify-between flex">
            <div className=" flex justify-start">
              {renderTags(labels as any)}
              {/* hhhh */}
            </div>
            {!!studyStatus && (
              <div
                onClick={() => {
                  setStudyPopupVisible(true);
                }}
                className={`${studyStatus !== StudyStatusCNMap.学习已完成 ? 'text-[#378BFF]' : 'text-[#6CCD5F]'}`}
              >
                {studyStatus === 'COMPLETED' ? '已学习' : '未学习'}
              </div>
            )}
          </div>
          <div className="text-sm leading-[22px] my-1">{workSheetItemName}</div>
          {hasContent && (
            <div className="mt-1 bg-[#f6f6f6] rounded p-2">
              {((itemRemark && itemRemark?.length !== 0) ||
                (selectReasons && selectReasons?.length !== 0) ||
                otherReason) && (
                <div className="text-[#5B6A91] pl-2 pb-2 text-sm leading-[14px]">
                  {((selectReasons && selectReasons?.length !== 0) || otherReason) &&
                    reasonListText(selectReasons, otherReason, true)}
                  {/* {otherReason && otherReason} */}
                  {itemRemark && itemRemark?.length !== 0 && (
                    <div className="text-[#5E5E5E] pt-1 text-sm leading-[22px]">{itemRemark}</div>
                  )}
                </div>
              )}
              {itemImages && imagesOrVideoRender(itemImages)}
            </div>
          )}
          <div className="relative before:w-[1px] before:h-full before:bg-[#F0F0F0] before:block before:absolute before:top-0 before:bottom-0 before:left-3">
            <div className="mt-[18px] flex justify-between text-text-minor text-sm items-center">
              <div className="flex items-center">
                <UserAvatar username={submitedUserName} avatarUrl={data.submitedUserAvatar} />
                <span className="ml-2 text-[#5E5E5E] text-xs leading-[12px]">
                  {pollingType === 'AIPolling' ? 'AI巡检' : submitedUserName} 提交问题
                </span>
              </div>
              <div className="ml-2 text-[#858585] text-xs leading-[12px]">
                {dayjs(data.sumbimitedTime).format('MM-DD HH:mm')}
              </div>
            </div>
            {copyUsers && (
              <div
                className={`copyUsers flex items-start justify-between ${usersExpend ? 'expend' : ''}`}
                onClick={() => setUsersExpend((v) => !v)}
              >
                <div className="ml-8 mt-3 text-[#5E5E5E] text-xs leading-[12px]">
                  <span>抄送人：</span>
                  <span>
                    {!usersExpend
                      ? copyUsers
                          .slice(0, 3)
                          .map((v: any) => v.nickname)
                          .join('，')
                      : copyUsers.map((v: any) => v.nickname).join('，')}
                    {!usersExpend && copyUsers?.length > 3 ? `等${copyUsers?.length}人` : ''}
                  </span>
                </div>
                {copyUsers?.length > 3 && <div className="sh-icon sh-icon-right" />}
              </div>
            )}
            {expend &&
              (processes || []).map((v: any, idx: any) => (
                <div key={idx}>
                  <div className="flex justify-between items-center mt-[18px] text-text-minor text-sm">
                    <div className="flex items-center">
                      <UserAvatar username={v.submitedUserName} avatarUrl={v.submitedUserAvatar} />
                      <div className="ml-2 text-[#5E5E5E] text-xs leading-[12px] ">
                        {v.submitedUserName} {OperationType[v.type as keyof typeof OperationType]}
                      </div>
                      {!!v.isOverdue && (
                        <span className="text-white bg-red-500 px-1 rounded-sm text-xs ml-2 h-fit">逾期</span>
                      )}
                    </div>
                    <div className="text-[#858585] text-xs leading-[12px]">
                      {dayjs(v.submitTime).format('MM-DD HH:mm')}
                    </div>
                  </div>
                  {((v.issueImages && v.issueImages?.length > 0) || v.text) && (
                    <div className="rounded-lg ml-7 bg-[#f6f6f6] p-2  text-light mt-4">
                      {v.text && (
                        <div className="pb-2">
                          <span className="text-[#858585] text-sm leading-[14px] pl-2">{v.text}</span>
                        </div>
                      )}
                      {v.issueImages && imagesOrVideoRender(v.issueImages)}
                    </div>
                  )}
                </div>
              ))}
          </div>
          {(processes?.length > 0 || pollingType === 'SelfPolling') && !isNotFold && (
            <div
              className="flex justify-center items-center text-text-minor text-sm pt-[19px]"
              onClick={() => {
                !expend && pollingType === 'SelfPolling' && expendRun();
                setExpend(!expend);
              }}
            >
              <div className={`mr-2 sh-icon sh-icon-${expend ? 'up' : 'down'}-square`} style={{ fontSize: '13px' }} />

              <span className="text-[#858585] text-sm leading-[14px]">{expend ? '收起' : '展开全部'}</span>
              {expend ? (
                <UpOutline className="text-xs ml-1 text-[#858585]" />
              ) : (
                <DownOutline className="text-xs ml-1 text-[#858585]" />
              )}
            </div>
          )}
          {cross === 'true' && renderButtons()}
        </div>
      </div>
      <VideoViewer visible={videoPreviewVisible} url={videoPreviewUrl} onClose={() => setVideoPreviewVisible(false)} />
      <StudyPopup
        id={id}
        title="门店员工学习情况"
        visible={studyPopupVisible}
        onClose={() => setStudyPopupVisible(false)}
      />
      <ProjectPopup
        issueId={issueId}
        title="学习项目"
        visible={projectPopupVisible}
        onClose={() => setProjectPopupVisible(false)}
      />
    </div>
  );
};

export default IssueCard;
