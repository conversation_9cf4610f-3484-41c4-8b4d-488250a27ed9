/*
 * @Author: ZengWu <EMAIL>
 * @Date: 2022-11-24
 * @LastEditors: ZengWu <EMAIL>
 * @LastEditTime: 2023-04-27
 * @Description:
 */
import React, { useState } from 'react';
import UserAvatar from '@src/components/UserAvatar';
import VideoViewer from '@src/components/VideoViewer';
import { Button, Image, ImageViewer } from 'antd-mobile';
import { AppstoreOutline, DownOutline, UpOutline } from 'antd-mobile-icons';
import dayjs from 'dayjs';
import ItemTag from '../OptionsDetail/itemTag';
// import { getSelfReportIssueDetail } from '../../api';
// import ItemTag from '../OptionsDetail/itemTag';
export enum Status {
  all = -1,
  process = 1,
  unReviewed = 2,
  overdue = 3,
  completed = 4,
}

enum OperationType {
  提交整改反馈,
  驳回,
  审核通过,
}
const StatusTitle = {
  [Status.process]: '待整改',
  [Status.unReviewed]: '待审核',
  [Status.completed]: '已整改',
  [Status.overdue]: '已过期',
};
type IssueCardProps = {
  className?: string;
  // data: Issue;
  data: any;

  onChange(): void;
  expend?: boolean;
  type?: 'patrol' | 'self';
  showButton?: boolean;
  taskId?: string;
  cross?: string;
  onExamine?: (data: any) => void; // 审核
  onReject?: (data: any) => void; // 驳回
  examineLoading?: boolean;
};
type WeekSheetItemProperty = {
  /**
   * S项
   * deductType: CATEGORY-检查表分类得分 WORKSHEET-检查表得分 REPORT-最终报告得分
   * deductRatio: 扣分比例
   */
  redLine: 0 | 1;
  deductType: 'CATEGORY' | 'WORKSHEET' | 'REPORT';
  deductRatio: number;
  /**
   * 罚款项
   */
  fineFlag: 0 | 1;
  /**
   * 必检项
   */
  necessaryFlag: 0 | 1;
  /**
   * 关键项
   */
  keyItem: 0 | 1;
  /**
   * M项
   */
  yellowItem: 0 | 1;
};
// const renderTags = (data: WeekSheetItemProperty, onClick: (data: { title: string; desc: string }) => void) => {
const renderTags = (data: WeekSheetItemProperty) => {
  const { keyItem, yellowItem, redLine, fineFlag, necessaryFlag } = data;
  return (
    <>
      {!!redLine && (
        <ItemTag className="mr-1" color="#F53F3F" bg="rgba(245, 63, 63, 0.05)" borderColor="#FBACA3" text="S项">
          S项
        </ItemTag>
      )}
      {fineFlag === 1 && (
        <ItemTag className="mr-1" color="#4E5969" bg="rgba(78, 89, 105, 0.05)" borderColor="#C9CDD4" text="罚款项">
          罚款项
        </ItemTag>
      )}
      {necessaryFlag === 1 && (
        <ItemTag className="mr-1" color="#4E5969" bg="rgba(78, 89, 105, 0.05)" borderColor="#C9CDD4" text="必检项">
          必检项
        </ItemTag>
      )}
      {keyItem === 1 && (
        <ItemTag className="mr-1" color="#4E5969" bg="rgba(78, 89, 105, 0.05)" borderColor="#C9CDD4" text="关键项">
          关键项
        </ItemTag>
      )}
      {yellowItem === 1 && (
        <ItemTag className="mr-1" color="#4E5969" bg="rgba(78, 89, 105, 0.05)" borderColor="#C9CDD4" text="M项">
          M项
        </ItemTag>
      )}
    </>
  );
};
const IssueCard: React.FC<IssueCardProps> = ({
  data,
  expend: expendProp,
  type = 'patrol',
  showButton = true,
  cross,
  examineLoading,
  onExamine,
  onReject,
}) => {
  const [expend, setExpend] = useState(expendProp);
  const [usersExpend, setUsersExpend] = useState(false);
  const [videoPreviewUrl, setVideoPreviewUrl] = useState('');
  const [videoPreviewVisible, setVideoPreviewVisible] = useState(false);
  // const { data: processes, run: runProcesses } = useRequest(
  //   async () => {
  //     const res = await getSelfReportIssueDetail({ id: data.id });
  //     return res || null;
  //   },
  //   {
  //     manual: true,
  //   },
  // );

  const {
    itemName,
    // issueId,
    // reportId,
    status,
    itemImages,
    submitedUserName,
    // watchName,
    itemRemark,
    selectReasons,
    copyUsers = [],
    children,
    // reformMustUpload,
    // ALLOW_ALBUM,
  } = data;
  console.log('hasContentdata', data);

  const renderButton = () => {
    const { buttonEnums } = data;

    if (!buttonEnums || buttonEnums.length === 0) return null;

    // 整改反馈的时候，watchName是提出问题的人即watchName
    // 驳回的时候取提交问题的名字，即children里面最后一个的submitedName
    // const name = children && children.length ? children[children.length - 1].submitedName : '';
    return (
      true && (
        <div
          className={`bg-white flex gap-x-3 p-3 ${buttonEnums.length === 1 ? 'justify-center' : 'justify-between'} `}
        >
          {/* {buttonEnums.includes('FOLLOW_PROBLEM') && (
            <Button
              color="primary"
              fill="outline"
              block
              className="h-[32px] text-sm leading-none"
              onClick={() => {}}
            >
              整改反馈
            </Button>
          )} */}
          {buttonEnums.includes('REJECT_PROBLEM') && (
            <Button
              style={{
                '--border-color': '#DCDCDC',
              }}
              fill="outline"
              block
              className="h-[32px] text-sm leading-none text-[#5E5E5E] "
              onClick={() => onReject?.(data)}
            >
              驳回
            </Button>
          )}
          {buttonEnums.includes('AUDIT_PASS') && (
            <Button
              color="primary"
              fill="outline"
              block
              className="h-[32px] text-sm leading-none "
              loading={examineLoading}
              onClick={() => {
                onExamine?.(data);
              }}
            >
              审核通过
            </Button>
          )}
        </div>
      )
    );
  };
  const reasonListText = (reasonLis: string[], isText?: boolean) => {
    const text = reasonLis?.map((v: string) => {
      return isText ? `#${v} ` : <div>{v}</div>;
    });
    return text || '';
  };
  const imagesOrVideoRender = (images: { url: string; type: 'IMG' | 'VIDEO'; id: string }[]) => {
    return (
      !!images?.length && (
        <div className={`flex  flex-wrap gap-1 items-center`}>
          {images.map((item) => {
            return (
              <div key={item.url}>
                {item.type === 'VIDEO' ? (
                  <video
                    // controls
                    className="w-[48px] h-[48px]"
                    width={48}
                    onClick={() => {
                      setVideoPreviewVisible(true);
                      setVideoPreviewUrl(item.url);
                    }}
                    height={48}
                    src={item.url}
                  />
                ) : (
                  <Image
                    onClick={() => {
                      ImageViewer.show({ image: item.url });
                    }}
                    src={item?.snapshotUrl || item.url}
                    width={48}
                    height={48}
                  />
                )}
              </div>
            );
          })}
        </div>
      )
    );
  };
  const hasContent =
    (itemRemark && itemRemark?.length !== 0) ||
    (selectReasons && selectReasons?.length !== 0) ||
    (itemImages && itemImages?.length > 0);
  console.log('hasContent', hasContent);
  console.log('hasContentselectReasons111', console.log(itemImages));

  return (
    <div className={` bg-white rounded-lg `}>
      <div className="cardbody ">
        <div className="text-sm leading-[14px] flex justify-between border-b  border-b-[0.5px] border-[#F0F0F0] py-3 px-4 text-[#141414]">
          {/* <span>截止时间：{dayjs(data.deadline).format('MM-DD')}</span> */}
          <span>
            {status === Status.completed
              ? `整改耗时 ${data.elapsedTime}`
              : `截止时间 ${dayjs(data.deadline).format('MM-DD')}`}
          </span>
          <span className="text-[#00BBB4]"> {(StatusTitle as any)[status]}</span>
        </div>
        {data.taskPlanName && (
          <div className="flex items-center mt-2 pl-2">
            <span className="ml-2 text-text-minor text-sm leading-[14px]  text-[#141414] font-medium">
              {data.taskPlanName}
            </span>
          </div>
        )}
        <div className="p-3">
          <div className="border-b-[0.5px] pb-2 border-[#F0F0F0]">
            <span>
              <AppstoreOutline className="w-[12px] h-[12px] inline-block" />
              <span className="ml-1 text-text-minor  text-xs leading-5 text-[#141414] font-medium">
                {data.checkListName} / {data.checkListTypeName}
              </span>
            </span>
          </div>
          <div className="mt-3 flex justify-start">{renderTags(data as any)}</div>
          <div className="text-sm leading-[22px] my-1">{itemName}</div>
          {hasContent && (
            <div className="mt-1 bg-[#f6f6f6] rounded p-2">
              {((itemRemark && itemRemark?.length !== 0) || (selectReasons && selectReasons?.length !== 0)) && (
                <div className="text-[#5B6A91] pl-2 pb-2 text-sm leading-[14px]">
                  {selectReasons && selectReasons?.length !== 0 && reasonListText(selectReasons, true)}
                  {itemRemark && itemRemark?.length !== 0 && (
                    <div className="text-[#5E5E5E] pt-1 text-sm leading-[22px]">{itemRemark}</div>
                  )}
                </div>
              )}
              {itemImages && imagesOrVideoRender(itemImages)}
            </div>
          )}
          <div className="relative before:w-[1px] before:h-full before:bg-[#F0F0F0] before:block before:absolute before:top-0 before:bottom-0 before:left-3">
            <div className="mt-[18px] flex justify-between text-text-minor text-sm items-center">
              <div className="flex items-center">
                <UserAvatar username={submitedUserName} avatarUrl={data.submitedUserAvatar} />
                {/* {
                <Image
                  width={24}
                  height={24}
                  style={{ borderRadius: '100%' }}
                  src={data.submitedUserAvatar}
                />
              } */}

                <span className="ml-2 text-[#5E5E5E] text-xs leading-[12px]">{submitedUserName} 提交问题</span>
              </div>
              <div className="ml-2 text-[#858585] text-xs leading-[12px]">
                {dayjs(data.sumbimitedTime).format('MM-DD HH:mm')}
              </div>
            </div>
            {copyUsers && (
              <div
                className={`copyUsers flex items-start justify-between ${usersExpend ? 'expend' : ''}`}
                onClick={() => setUsersExpend((v) => !v)}
              >
                <div className="ml-8 mt-3 text-[#5E5E5E] text-xs leading-[12px]">
                  <span>抄送人：</span>
                  <span>
                    {!usersExpend
                      ? copyUsers
                          .slice(0, 3)
                          .map((v) => v.name)
                          .join('，')
                      : copyUsers.map((v) => v.name).join('，')}
                    {!usersExpend && copyUsers?.length > 3 ? `等${copyUsers?.length}人` : ''}
                  </span>
                </div>
                {copyUsers?.length > 3 && <div className="sh-icon sh-icon-right" />}
              </div>
            )}
            {expend &&
              ((type === 'patrol' ? children : processes) || []).map((v, idx) => (
                <div key={idx}>
                  <div className="flex justify-between mt-[18px] text-text-minor text-sm">
                    <div className="flex items-center">
                      <UserAvatar username={v.submitedName} avatarUrl={v.submitedAvatar} />
                      <div className="ml-2 text-[#5E5E5E] text-xs leading-[12px] ">
                        {v.submitedName} {OperationType[v.type]}
                      </div>
                    </div>
                    <div className="text-[#858585] text-xs leading-[12px]">
                      {dayjs(v.submitTime).format('MM-DD HH:mm')}
                    </div>
                  </div>
                  {((v.issueImages && v.issueImages?.length > 0) || v.issueRemark) && (
                    <div className="rounded-lg ml-7 bg-[#f6f6f6] p-2  text-light mt-4">
                      {v.issueRemark && (
                        <div className="pb-2">
                          <span className="text-[#858585] text-sm leading-[14px] pl-2">{v.issueRemark}</span>
                        </div>
                      )}
                      {v.issueImages && imagesOrVideoRender(v.issueImages)}
                    </div>
                  )}
                </div>
              ))}
          </div>

          {(type === 'patrol' ? data.children && data.children?.length > 0 : data.existProcess) && (
            // {true && (
            <div
              className="flex justify-center items-center text-text-minor text-sm pt-[19px]"
              onClick={() => {
                // if (type === 'self') {
                //   !processes && runProcesses();
                // }
                setExpend(!expend);
              }}
            >
              <div className={`mr-2 sh-icon sh-icon-${expend ? 'up' : 'down'}-square`} style={{ fontSize: '13px' }} />

              <span className="text-[#858585] text-sm leading-[14px]">{expend ? '收起' : '展开全部'}</span>
              {/* <UpOutline className="text-xs ml-1" /> */}
              {expend ? (
                <UpOutline className="text-xs ml-1 text-[#858585]" />
              ) : (
                <DownOutline className="text-xs ml-1 text-[#858585]" />
              )}
            </div>
          )}
        </div>
      </div>
      {cross === 'false' && showButton && renderButton()}
      {/* {popupConfirmNode} */}
      <VideoViewer visible={videoPreviewVisible} url={videoPreviewUrl} onClose={() => setVideoPreviewVisible(false)} />
    </div>
  );
};

export default IssueCard;
