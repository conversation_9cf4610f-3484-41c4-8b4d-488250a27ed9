import React from 'react';
import IssueCardShop from './IssueCardShop';
// import emptyImage from '@src/assets/images/task-empty.png';
type Issue = {
  worksheetNames?: string | string[];
  worksheetName?: string;
  taskPlanName?: string;
  existProcess: boolean;
  statusEnum: 'CREATED' | 'REFORM_SUBMITTED' | 'SUPERVISOR_REJECTED' | 'COMPLETED' | 'PAST_DUE';
  id: number;
  issueId: number;
  reportId: number;
  shopId: number;
  shopName: string;
  shopNo: string;
  status: number;
  statusDesc: string;
  deadline: string;
  elapsedTime: string;
  checkListId: number;
  checkListName: string;
  checkListTypeId: number;
  checkListTypeName: string;
  itemId: number;
  itemName: string;
  itemRemark: string;
  selectReasons?: string[];
  itemImages: {
    url: string;
    type: 'IMG' | 'VIDEO';
    id: string;
  }[];
  submitedUserId: number;
  submitedUserName: string;
  submitedUserAvatar: string;
  sumbimitedTime: string;
  redLine: 0 | 1;
  deductType: 'CATEGORY' | 'WORKSHEET' | 'REPORT';
  deductRatio: number;
  necessaryFlag: 0 | 1;
  fineFlag: 0 | 1;
  watchId: number;
  watchName: string;
  belongsMe: boolean;
  children?: Comment[];
  buttonEnums: Array<
    | 'AUDIT_PASS'
    | 'FOLLOW_PROBLEM'
    | 'REJECT_PROBLEM'
    | 'SELF_AUDIT_PASS'
    | 'SELF_FOLLOW_PROBLEM'
    | 'SELF_REJECT_PROBLEM'
  >;
  copyUsers?: Nullable<
    {
      id: number;
      name: string;
    }[]
  >;
  reformMustUpload: number;
  ALLOW_ALBUM: number;
};
type IssueListProps = {
  className?: string;
  loading?: boolean;
  dataSource?: Issue[];
  empty?: React.ReactNode;
  isOver?: boolean;
  onChange(): void;
  expend?: boolean;
  type?: 'patrol' | 'self';
  taskId?: string;
  cross?: string;
  onExamine?: (data: any) => void; // 审核
  onReject?: (data: any) => void; // 驳回
  onRefesh?: () => void;
  examineLoading?: boolean;
  isShop?: boolean; // 是否是门店侧
  pollingType?: string | undefined;
};

const IssueList: React.FC<IssueListProps> = ({
  onRefesh,
  className,
  dataSource,
  isOver,
  onChange,
  expend,
  type,
  taskId,
  cross,
  onExamine,
  onReject,
  examineLoading,
  isShop,
  pollingType,
}) => {
  return (
    <div className={`${className} issueScrollList flex-grow gap-y-2.5 flex flex-col`}>
      {dataSource && dataSource.length > 0 ? (
        isShop ? (
          dataSource?.map((v, idx) => (
            <>
              <IssueCardShop
                cross={cross}
                onRefesh={onRefesh}
                pollingType={pollingType}
                key={`${v.issueId || v.reportId}_${idx}`}
                className="mb-4"
                data={v}
              />
            </>
          ))
        ) : (
          <div className="flex flex-col items-center mt-[70px]">
            {/* <img src={emptyImage} className="w-[240px]" /> */}
            <span className="text-13 text-[#9E9E9E] mt-5">暂无任务</span>
          </div>
        )
      ) : (
        <div className="flex flex-col items-center mt-[70px]">
          {/* <img src={emptyImage} className="w-[240px]" /> */}
          <span className="text-13 text-[#9E9E9E] mt-5">暂无任务</span>
        </div>
      )}
      {isOver && <div className="h-20">&nbsp;</div>}
    </div>
  );
};

IssueList.defaultProps = {
  dataSource: [],
};

export default IssueList;
