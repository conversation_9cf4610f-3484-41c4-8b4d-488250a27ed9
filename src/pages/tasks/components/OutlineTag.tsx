import { HTMLAttributes } from 'react';

interface OutlineTagProps extends HTMLAttributes<HTMLSpanElement> {
  text: string;
}

export const OutlineTag = ({ text, className }: OutlineTagProps) => {
  return (
    <span
      className={`inline-flex justify-center items-center px-1 h-[18px]  rounded-sm text-primary border border-primary text-xs leading-none ${
        className || ''
      }`}
      // @ts-ignore
      attribute="tag"
    >
      {text}
    </span>
  );
};
