import { useEffect, useMemo, useState } from 'react';
import { Loading } from '@src/components';
import { IPopup } from '@src/components/IPopup';
import PdfViewer from '@src/components/PdfViewer';
import { uploadFile } from '@src/utils/utils';
import { useRequest } from 'ahooks';
import { Upload, UploadFile, UploadProps } from 'antd';
import { Button, DotLoading, Form, Image, ImageViewer, PopupProps, Slider, Space, TextArea, Toast } from 'antd-mobile';
import { CloseOutline } from 'antd-mobile-icons';
import styles from './index.module.scss';
import { editProcessTask, getTaskSchedule, processTask } from '../../api';

type IUploadFile<T> = UploadFile<T> & {
  id: number;
  fileName: string;
  fileUrl: string;
};

type IUploadProps = Omit<UploadProps, 'fileList'> & {
  fileList?: Array<IUploadFile<any>>;
};

// 上传 pdf 和图片
export const Uploader: React.FC<IUploadProps> = ({ fileList, onChange, ...props }) => {
  const [pdfPreviewUrl, setPdfPreviewUrl] = useState('');
  const [pdfPreviewVisible, setPdfPreviewVisible] = useState(false);

  // pdf 排前，图片排后
  const files = useMemo(() => {
    const pdfFiles: IUploadFile<any>[] = [];
    const imageFiles: IUploadFile<any>[] = [];

    fileList?.forEach((i) => {
      if (i?.id) {
        // 说明该附件已上传过
        if (i?.fileName?.endsWith('.pdf')) {
          pdfFiles.push(i);
        } else {
          imageFiles.push(i);
        }
      } else {
        if (i.type?.startsWith('image')) {
          imageFiles.push(i);
        } else if (i.type === 'application/pdf') {
          pdfFiles.push(i);
        }
      }
    });

    return pdfFiles.concat(imageFiles);
  }, [fileList]);

  return (
    <>
      <Space direction="vertical" align="end" className="float-right">
        {files.map((i, index) => {
          let isImg: any = '';

          if (i?.id) {
            isImg = !i?.fileName?.endsWith('.pdf');
          } else {
            isImg = i.type?.startsWith('image');
          }

          return (
            <div
              key={i.uid || i.id}
              className="flex items-center text-[#858585] text-sm rounded-sm leading-[14px] px-2 py-[6px] bg-[#F5F5F5]"
              onClick={() => {
                if (isImg) {
                  ImageViewer.show({ image: i?.response?.url || i?.fileUrl });
                } else {
                  setPdfPreviewVisible(true);
                  setPdfPreviewUrl(i?.response?.url || i?.fileUrl);
                }
              }}
            >
              {i.status === 'uploading' ? (
                <DotLoading color="primary" className="text-xs" />
              ) : isImg ? (
                <Image src={i?.response?.url || i?.fileUrl} className="w-[28px] h-[28px]" fit="contain" />
              ) : (
                <span className="max-w-[200px] overflow-hidden whitespace-nowrap text-ellipsis">
                  {index + 1}.{i?.name || i?.fileName}
                </span>
              )}
              <CloseOutline
                className="text-[#B8B8B8] ml-1"
                onClick={(e) => {
                  e.stopPropagation();
                  onChange?.({
                    file: i,
                    fileList: (fileList || []).filter((file) => {
                      if (file?.id) {
                        return file.id !== i.id;
                      } else {
                        return file.uid !== i.uid;
                      }
                    }),
                  });
                }}
              />
            </div>
          );
        })}
        <Upload
          fileList={fileList}
          onChange={onChange}
          multiple
          showUploadList={false}
          beforeUpload={(file) => {
            const isImgOrPdf = file.type.startsWith('image') || file.type === 'application/pdf';

            if (!isImgOrPdf) {
              Toast.show('只能上传pdf和图片文件');
            }

            const isLt20M = file.size / 1024 / 1024 <= 20;

            if (!isLt20M) {
              Toast.show('上传文件不可大于 20M');
            }

            return (isImgOrPdf && isLt20M) || Upload.LIST_IGNORE;
          }}
          customRequest={async ({ file, onSuccess, onError }) => {
            try {
              const res = await uploadFile(file as File, true, false, 'ROUTINE_TASK');

              onSuccess?.(res);
            } catch (error) {
              Toast.show('上传失败，请重新上传');
              onError?.(error as Error);
            }
          }}
          {...props}
        >
          <span className="text-primary text-15">添加附件</span>
        </Upload>
      </Space>

      <PdfViewer visible={pdfPreviewVisible} url={pdfPreviewUrl} onClose={() => setPdfPreviewVisible(false)} />
    </>
  );
};

interface ProgressPopupProps extends PopupProps {
  progressMin?: number;
  taskUserId: number;
  isFinish?: boolean;
  isEdit?: boolean;
  scheduleId?: number;
  finishAttachRequired?: number | null;
  updateAttachRequired?: number | null;
  onSuccess: () => void;
  scheduleMark?: string;
  scheduleFileList?: any[];
}

const ProgressPopup: React.FC<ProgressPopupProps> = ({
  progressMin,
  visible,
  taskUserId,
  scheduleId,
  isFinish,
  isEdit,
  finishAttachRequired,
  updateAttachRequired,
  scheduleMark,
  scheduleFileList,
  onSuccess,
  onClose,
  ...props
}) => {
  console.log('🚀 ~ progressMin:', progressMin);

  const [form] = Form.useForm();

  const isUploadfile: boolean = isFinish ? !!finishAttachRequired : !!updateAttachRequired;
  const { runAsync, loading } = useRequest(processTask, { manual: true });
  const { runAsync: runEdit, loading: EditLoading } = useRequest(editProcessTask, {
    manual: true,
  });

  const { data: taskProgress = 0, loading: progressLoading } = useRequest(
    () => {
      return getTaskSchedule(taskUserId);
    },
    {
      // 没有传progressMin，则向后端请求进度
      ready: visible && !!!progressMin,
      onSuccess: (data) => {
        form.setFieldsValue({
          scheduleValue: data,
        });
      },
    },
  );

  const marks = [0, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100].reduce<Record<string, number>>((total, cur) => {
    if (cur >= (progressMin || taskProgress)) {
      total[cur] = cur;
    }

    return total;
  }, {});

  useEffect(() => {
    if (visible) {
      form.resetFields();

      if (isEdit) {
        form.setFieldsValue({
          remark: scheduleMark,
          fileEntityList: scheduleFileList || [],
        });
      }
    }
  }, [visible]);

  const handleSave = async (values: { scheduleValue: number; remark: string; fileEntityList: IUploadFile<any>[] }) => {
    const { fileEntityList, scheduleValue } = values || {};

    if (scheduleValue === 100 || scheduleValue > (progressMin || taskProgress) || isEdit) {
      if (
        (values.scheduleValue === 100 && !!finishAttachRequired && !(fileEntityList?.length > 0)) ||
        (isFinish && !!updateAttachRequired && !(fileEntityList?.length > 0))
      ) {
        // 进度100%相等于完成任务||提交完成任务相等于更新
        Toast.show('请上传任务附件');
      } else {
        const Parms = {
          ...values,
          scheduleValue: values.scheduleValue,
          taskUserId,
          remark: values.remark,
          fileEntityList: (values.fileEntityList || []).map((i) => {
            if (i?.id) {
              return { id: i?.id };
            } else {
              return {
                fileName: i.name,
                objectName: i.response.key,
              };
            }
          }),
        };

        isEdit
          ? await runEdit({
              ...Parms,
              scheduleId: scheduleId!,
            } as any)
          : await runAsync(Parms as any);
        Toast.show('更新成功');
        onClose?.();
        onSuccess();
      }
    } else {
      Toast.show('请更新任务进度后再提交');
    }
  };

  return (
    <Loading spinning={loading || EditLoading || progressLoading}>
      <IPopup
        visible={visible}
        title="进度反馈"
        bodyStyle={{ maxHeight: '80vh' }}
        footer={
          <div className="p-4">
            <Button block color="primary" type="submit" onClick={form.submit}>
              提交
            </Button>
          </div>
        }
        onClose={onClose}
        {...props}
      >
        <Form
          form={form}
          className={styles.iAdmForm}
          style={{ '--border-top': '0', '--border-bottom': '0' }}
          hasFeedback={false}
          requiredMarkStyle="none"
          onFinishFailed={({ errorFields }) => Toast.show(errorFields[0].errors[0])}
          onFinish={handleSave}
        >
          <Form.Item
            name="scheduleValue"
            initialValue={progressMin}
            label={
              <div className="flex justify-between">
                <span className="text-dark leading-[15px]">任务进度</span>
                <span className="text-base text-grey leading-4">
                  <Form.Item noStyle shouldUpdate>
                    {({ getFieldValue }) => `${getFieldValue('scheduleValue') || 0}%`}
                  </Form.Item>
                </span>
              </div>
            }
            rules={[
              {
                required: true,
                min: progressMin || taskProgress,
                type: 'number',
                message: '请更新任务进度后再提交',
              },
            ]}
          >
            <Slider marks={marks} ticks={false} icon={<span />} className={styles.iAdmSlider} />
          </Form.Item>
          <Form.Item name="remark" label={<span className="text-dark">任务说明</span>}>
            <TextArea
              style={{
                '--font-size': '13px',
              }}
              className="rounded bg-[#F6F6F6] mt-2 text-13 p-3"
              placeholder="请填写任务说明"
              maxLength={100}
            />
          </Form.Item>
          <Form.Item
            layout="horizontal"
            name="fileEntityList"
            valuePropName="fileList"
            rules={[
              {
                required: isUploadfile,
                message: '请上传任务附件',
              },
            ]}
            getValueFromEvent={(e) => (e.fileList as IUploadFile<any>[]).filter((i) => i.status !== 'error')}
            label={
              <span className="text-dark text-15 flex items-center">
                {isUploadfile && <span className="text-[red]">*</span>}
                <span>任务附件</span>
              </span>
            }
          >
            <Uploader />
          </Form.Item>
        </Form>
      </IPopup>
    </Loading>
  );
};

export default ProgressPopup;
