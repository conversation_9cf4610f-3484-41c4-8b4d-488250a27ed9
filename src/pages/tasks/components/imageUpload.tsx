import React, { useEffect, useMemo, useState } from 'react';
import <PERSON>Viewer from '@src/components/VideoViewer';
import { uploadFile } from '@src/utils/utils';
import { UploadProps as AntdUploadProps, Upload, UploadFile } from 'antd';
import { DotLoading, Image, ImageViewer, Space, Toast } from 'antd-mobile';
import { AddOutline, CloseOutline, PlayOutline } from 'antd-mobile-icons';
import { isString } from 'lodash';
import { nanoid } from 'nanoid';
import { uploadImage, uploadVideo } from '../api';
import { TWatermark } from '../api.type';
interface CustomUploadProps extends AntdUploadProps<any> {
  updateIsFormValid: (isValid: boolean) => void; // 自定义属性
  shopId?: string;
}
export const ImgUploader: React.FC<CustomUploadProps> = ({
  updateIsFormValid,
  fileList,
  onChange,
  shopId,
  ...props
}) => {
  const [videoPreviewUrl, setVideoPreviewUrl] = useState('');
  const [videoPreviewVisible, setVideoPreviewVisible] = useState(false);

  const [location, setLocation] = useState<{ latitude?: number; longitude?: number } | undefined>();

  useEffect(() => {
    // 通知APP获取定位
    if (window?.['ReactNativeWebView']?.['postMessage']) {
      // 安卓
      window['ReactNativeWebView']['postMessage'](JSON.stringify({ getLocation: true }));
    } else {
      // ios
      window.parent.postMessage(JSON.stringify({ getLocation: true }), '*');
    }
    function handleEvent(e: any) {
      if (e.data && isString(e.data)) {
        try {
          const result = JSON.parse(e.data);

          if (result?.location) {
            setLocation(result?.location);
          }
        } catch (error) {
          console.log('error :>> ', error);
        }
      }
    }
    document.addEventListener('message', handleEvent); // 安卓端监听
    window.addEventListener('message', handleEvent); // ios端监听
    return () => {
      document.removeEventListener('message', handleEvent);
      window.addEventListener('message', handleEvent);
    };
  }, []);
  const files = useMemo(() => {
    const imageFiles: UploadFile<any>[] = [];
    fileList?.forEach((i) => {
      imageFiles.push(i);
    });
    return imageFiles;
  }, [fileList]);

  useEffect(() => {
    files.forEach((i) => {
      if (i?.status === 'uploading') {
        updateIsFormValid(false);
      } else {
        updateIsFormValid(true);
      }
    });
  }, [files, updateIsFormValid]);
  return (
    <React.Fragment>
      <Space direction="horizontal" wrap style={{ '--gap': '4px' }}>
        {files.map((i: any, index) => {
          const isImg = i.type?.startsWith('image');
          const url = i?.url || i.response?.url;
          const snapshotUrl = i?.snapshotUrl || i.response?.snapshotUrl;
          return (
            <div
              key={index}
              className="flex relative items-center text-[#858585] text-sm rounded-sm leading-[14px] bg-[#F5F5F5]"
            >
              {i?.status === 'uploading' ? (
                <DotLoading color="primary" className="text-xs" />
              ) : isImg ? (
                <Image
                  onClick={() => {
                    ImageViewer.show({ image: url });
                  }}
                  src={url}
                  style={{ width: 80, height: 80 }}
                  fit="cover"
                />
              ) : (
                <div
                  className="relative"
                  onClick={() => {
                    setVideoPreviewVisible(true);
                    setVideoPreviewUrl(url);
                  }}
                >
                  <video
                    style={{ objectFit: 'cover' }}
                    preload="auto"
                    poster={snapshotUrl}
                    className="w-[80px] h-[80px]"
                    src={url}
                  />
                  <div className="absolute left-[50%] top-[50%] -translate-x-[50%] -translate-y-[50%]">
                    <PlayOutline color="#000" fontSize={30} />
                  </div>
                </div>
              )}
              <CloseOutline
                className="absolute right-0 top-0"
                onClick={(e) => {
                  e.stopPropagation();
                  onChange?.({
                    file: i,
                    fileList: (fileList || []).filter((file) => file.uid !== i.uid),
                  });
                }}
              />
            </div>
          );
        })}
        <Upload
          fileList={fileList}
          onChange={(e) => {
            console.log(e, '=e');

            onChange && onChange(e);
          }}
          multiple
          id="myUpload"
          showUploadList={false}
          beforeUpload={(file) => {
            const isImgOrVideo = file.type.startsWith('image') || file.type.startsWith('video');
            console.log(file, '=file');

            if (!isImgOrVideo) {
              Toast.show('只能上传图片文件');
            }
            const isLt20M = file.size / 1024 / 1024 <= 20;
            if (!isLt20M) {
              Toast.show('上传文件不可大于 20M');
            }
            return (isImgOrVideo && isLt20M) || Upload.LIST_IGNORE;
          }}
          customRequest={async ({ file, onSuccess, onError }) => {
            try {
              const res = await uploadFile(file as File, false, true);
              console.log(res, '=res');

              const params = {
                bucket: res?.bucket!,
                key: res?.key,
                originName: nanoid(),
                shopId,
                ...location,
              };

              const result = file?.type?.startsWith('image')
                ? await uploadImage(params, shopId ? TWatermark.extra : TWatermark.default)
                : await uploadVideo(params);
              console.log(result, '=result');
              onSuccess?.(result);
            } catch (error) {
              Toast.show('上传失败，请重新上传');
              onError?.(error as Error);
            }
          }}
          {...props}
        >
          <div
            style={{
              alignItems: 'center',
            }}
            className="w-[80px]  h-[80px] flex justify-center  align-middle rounded-[4px] bg-[#F5F5F5] border-solid border border-[#DCDCDC]"
          >
            <AddOutline fontSize={24} color="#B8B8B8" />
          </div>
        </Upload>
      </Space>
      <VideoViewer
        visible={videoPreviewVisible}
        url={videoPreviewUrl}
        onClose={() => {
          setVideoPreviewVisible(false);
        }}
      />
    </React.Fragment>
  );
};
