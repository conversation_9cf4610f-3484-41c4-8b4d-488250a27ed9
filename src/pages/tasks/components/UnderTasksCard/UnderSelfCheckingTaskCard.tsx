import { useState } from 'react';
import noCompleteImg from '@src/assets/images/self_task_no_pass.png';
import completeImg from '@src/assets/images/self_task_pass.png';
import { SelfStatusTag, StatusTag } from '@src/pages/patrol/planDetail/detailCard';
import { Image } from 'antd-mobile';
import { RightOutline } from 'antd-mobile-icons';
import dayjs from 'dayjs';
import { useNavigate } from 'react-router-dom';
import { yytTaskStatusEnum } from '../../enum';
import { selftaskStatusEnum } from '../../yyt/UnderSelfFilter';
import { CardHead } from '../PrincipalTasksCard/PrincipalPatrolSchemeCard';
interface UnderSelfCheckingTaskCardProps {
  initial: any;
}

export function SelfCheckingResBuilder({ initial }: UnderSelfCheckingTaskCardProps) {
  console.log('initial99', initial);

  const navigate = useNavigate();
  const handleClick = (item: { label: string; key: string }) => {
    if (item.label === '未整改项' && initial?.noReformCount > 0) {
      // navigate(`/tasks/pollingAbarbeitung?taskId=${info.taskId}&pollingType=SelfPolling`);

      navigate(
        // `/tasks/pollingAbarbeitung?taskId=${initial?.reportDetail?.reportId}&pollingType=SelfPolling`,
        `/tasks/pollingAbarbeitung?taskId=${initial?.taskId}&pollingType=SelfPolling`,
      );
    }
  };
  const handleCardClick = () => {
    // 已完成时可跳转
    // reportStatus为1：待提交 2：已提交 3：门店已确认 4：待点评 5：已点评
    // if (initial?.reportDetail?.reportStatus === 5) {
    if (initial?.taskStatus === 'COMPLETED' && initial?.reportStatus === 'REVIEWED') {
      navigate(`/self/reportdetail?taskId=${initial.taskId}`);
    }
  };
  return (
    <div
      className="text-13 leading-[21px] text-85 mt-[5px] px-3 pb-3 border-b border-[#F6F6F6]"
      onClick={() => handleCardClick()}
    >
      <div className="flex justify-between">
        <div>{initial.planName}</div>
      </div>

      {initial?.taskStatus === 'COMPLETED' ? (
        <div className="mt-4 flex justify-around">
          {[
            { label: '自检结果', key: 'reportScore' },
            { label: '问题项', key: 'issuesCount' },
            { label: '未整改项', key: 'noReformCount' },
          ].map((o) => {
            return (
              <div
                key={o.key}
                className="flex flex-col items-center"
                onClick={(e) => {
                  e.stopPropagation();
                  handleClick(o);
                }}
              >
                <span className={`${initial.passed ? 'text-primary' : 'text-[#F53F3F]'} text-15 leading-[23px]`}>
                  {initial[o.key]}
                  {o.label === '自检结果' ? '分' : '个'}
                </span>
                <span className="text-85 text-xs  leading-[20px]">{o.label}</span>
              </div>
            );
          })}
        </div>
      ) : (
        initial?.reportDetail?.reportStatus === 4 && (
          <div className="text-13 leading-none mt-2">
            自检结果:<span className="text-primary ml-1">待点评</span>
          </div>
        )
      )}
    </div>
  );
}

export const UnderSelfCheckingTaskCard = ({ initial }: UnderSelfCheckingTaskCardProps) => {
  // const [curStatus, setCurStatus] = useState<string>(initial.taskStatus);

  return (
    <div className="bg-white rounded-lg pt-3 pb-2 relative">
      <div className="flex justify-between  px-3 items-center">
        {/* <CardHead status={initial.taskStatus} name={initial.shopName} /> */}
        {initial.shopName}
        {/* <div className="text-primary text-sm"> {StatusTag[initial.taskStatus]}</div> */}
        <div className="text-primary text-sm"> {SelfStatusTag[initial.taskStatus]}</div>
        {/* {initial.taskStatus === selftaskStatusEnum.已完成 && (
          <RightOutline className="text-[#c5c5c5] mt-1 text-sm shrink-0" />
        )} */}
      </div>
      <SelfCheckingResBuilder initial={initial} />
      <div className="text-85 text-13  px-3 leading-[21px] mt-2 flex flex-col gap-y-1">
        <div className="flex gap-x-1">
          <span>自检时间:</span>
          <span>
            {/* 有任务逾期时间就取任务逾期时间expiredTime2，没有就取任务过期时间 */}
            {dayjs(initial.startDate).format('YYYY/MM/DD HH:mm')}—
            {dayjs(initial.expiredTime2 ? initial.expiredTime2 : initial.expiredTime).format('YYYY/MM/DD HH:mm')}
          </span>
        </div>
      </div>
      {initial?.taskStatus === 'COMPLETED' && (
        <Image
          className="absolute right-[22px] -top-1 w-[80px] h-[80px] object-cover pointer-events-none"
          src={initial.passed ? completeImg : noCompleteImg}
        />
      )}
    </div>
  );
};
