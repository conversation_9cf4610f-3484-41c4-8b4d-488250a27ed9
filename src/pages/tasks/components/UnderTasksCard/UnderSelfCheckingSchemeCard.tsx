import { useNavigate } from 'react-router-dom';
import { CardHead } from '../PrincipalTasksCard/PrincipalPatrolSchemeCard';
// import { renderPeriod } from '../../selfDetail';
import { RightOutline } from 'antd-mobile-icons';
import dayjs from 'dayjs';

interface UnderSelfCheckingSchemeCardProps {
  initial: any;
}

function SelfCheckingSchemeResBuilder({ initial }: any) {
  return (
    <div className="text-13 leading-[21px] text-85 mt-2 pb-4 border-b px-3 border-[#F6F6F6]">
      {initial.planName}
      {initial?.selfTaskPlanResult ? (
        <div className="mt-4 flex justify-around">
          {[
            { label: '通过', key: 'reportPassCount' },
            { label: '未通过', key: 'reportUnPassCount' },
            { label: '未完成', key: 'taskUnfinishedCount' },
          ].map((o) => {
            return (
              <div key={o.key} className="flex flex-col items-center">
                <span className={`text-primary text-15  leading-[23px]`}>
                  {initial?.selfTaskPlanResult[o.key]}
                  {o.label === '通过' ? '次' : '个'}
                </span>
                <span className="text-85 text-xs  leading-[20px]">{o.label}</span>
              </div>
            );
          })}
        </div>
      ) : (
        <div className="text-13 leading-none mt-2">
          自检结果:<span className="text-primary ml-1">暂未自检</span>
        </div>
      )}
    </div>
  );
}

export const UnderSelfCheckingSchemeCard = ({ initial }: UnderSelfCheckingSchemeCardProps) => {
  const navigate = useNavigate();
  return (
    <div
      className="bg-white rounded-lg  pt-3 pb-2 "
      onClick={() =>
        navigate(`/tasks/selfDetail?planId=${initial.planId}&shopId=${initial.shopId}`)
      }
      // onClick={() => navigate(`/tasks/pollingAbarbeitung`)}
    >
      <div className="flex justify-between px-3">
        <CardHead status={initial.planStatus} name={initial.shopName} />
        <RightOutline className="text-[#c5c5c5] mt-1 text-sm shrink-0" />
      </div>
      <SelfCheckingSchemeResBuilder initial={initial} />
      <div className="text-85 text-13 leading-[21px] px-3 mt-2 flex flex-col gap-y-1">
        <div className="flex gap-x-1 items-center">
          <span>循环周期:</span>
          <span>
            {dayjs(initial.startDate).format('YYYY/MM/DD')}-
            {dayjs(initial.endDate).format('YYYY/MM/DD')}
          </span>
          <span className="text-xs leading-none px-[2px] py-[1px] rounded-[2px] border border-[#DCDCDC] ">
            {initial.repeatTypeDesc}
          </span>
        </div>
        {true && (
          <span className=" flex gap-x-1">
            <span>任务发起日期:</span>
            {/* <span>{renderPeriod(initial)}</span> */}
          </span>
        )}
      </div>
    </div>
  );
};
