import { RightOutline } from 'antd-mobile-icons';
// import { useNavigate, createSearchParams } from 'react-router-dom';
// import { TasksContext } from '../../layout';
// import { useContext } from 'react';
import { getUnderTaskSelfPlanListItem } from '../../api.type';

interface UnderPatrolSchemeCardProps {
  initial: getUnderTaskSelfPlanListItem;
  searchParams: {
    fromStartTime: string;
    toEndTime: string;
  };
}

export const UnderPatrolSchemeCard = ({ initial, searchParams }: UnderPatrolSchemeCardProps) => {
  // const navigate = useNavigate();

  // const { queryType } = useContext(TasksContext);

  return (
    <div
      className="bg-white rounded-lg p-3 "
      onClick={() => {
        const params = {
          userName: initial.name,
          fromStartTime: searchParams.fromStartTime,
          toEndTime: searchParams.toEndTime,
          waitStartCount: `${initial.waitStartCount}`,
          processCount: `${initial.processCount}`,
          completeCount: `${initial.completeCount}`,
          cancelCount: `${initial.cancelCount}`,
        };
        // navigate({
        //   pathname: `/tasks/patrol/planList/${initial.userId}/${queryType}`,
        //   search: `?${createSearchParams(params)}`,
        // });
      }}
    >
      <div className="flex justify-between font-medium text-sm leading-[22px]">
        <div>
          <span className="text-[#5E5E5E] ">巡检人:</span>
          <span className="text-dark ">{initial.patrolUserName}</span>
        </div>
        <RightOutline className="text-[#c5c5c5] mt-1 text-sm shrink-0" />
      </div>
      <div className="rounded p-2 bg-[#FAFAFA] flex flex-col gap-2 mt-2 ">
        <span className="text-13 leading-[21px] text-[#5E5E5E] ">巡检计划完成情况：</span>
        <div className="flex justify-around">
          {[
            { label: '待开始', value: 'waitStartCount' },
            { label: '进行中', value: 'processCount' },
            { label: '已结束', value: 'completeCount' },
            { label: '已取消', value: 'cancelCount' },
          ].map((o) => {
            return (
              <div key={o.value} className="flex flex-col items-center">
                <span className="text-primary text-15 leading-[23px] ">
                  {initial[o.value as keyof typeof initial]}个
                </span>
                <span className="text-xs leading-[20px] text-85">{o.label}</span>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};
