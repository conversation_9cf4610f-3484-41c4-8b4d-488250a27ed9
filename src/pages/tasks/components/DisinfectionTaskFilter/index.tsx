import { useState } from 'react';
import { ESheetType } from '@src/pages/mission/api';
import { WorkSheet } from '@src/pages/mission/components/WorkSheet';
import { DrawerProps } from 'antd';
import { FilterDrawer } from '../FilterDrawer';
import { DisinfectionCompany } from '../RoutineTaskFilter/DisinfectionCompany';
import { DisinfectionPeople } from '../RoutineTaskFilter/DisinfectionPeople';
import PositiveReport from '../RoutineTaskFilter/PositiveReport';

interface DisinfectionTaskFilterProps extends DrawerProps {
  value: Record<string, any>;
  queryType?: any;
  onChange: (value: Record<string, any>) => void;
  onClose: () => void;
}

// eslint-disable-next-line react-refresh/only-export-components
export const init = {
  disinfectionCompanyIds: [],
  workSheetIds: undefined,
  disinfectionUserIds: [],
  reportPositive: 'ALL',
};

export const DisinfectionTaskFilter = ({
  value,
  onChange,
  onClose,
  queryType,
  ...props
}: DisinfectionTaskFilterProps) => {
  const [activity, setActivity] = useState<any>({
    disinfectionCompanyIds: value?.disinfectionCompanyIds || [],
    workSheetIds: value?.workSheetIds || undefined,
    disinfectionUserIds: value?.disinfectionUserIds || [],
    reportPositive: 'ALL',
  });
  const onOk = () => {
    onChange(activity);
  };
  const onClear = () => {
    setActivity(init);
  };
  return (
    <FilterDrawer
      {...props}
      onClose={() => {
        // 重置一下activity
        setActivity(value);
        onClose();
      }}
      onOk={onOk}
      onClear={onClear}
    >
      <div className="flex flex-col gap-6">
        <PositiveReport
          value={activity.reportPositive}
          onChange={(val) => {
            setActivity((pre: any) => ({ ...pre, reportPositive: val }));
          }}
        />
        <DisinfectionPeople
          value={activity?.disinfectionUserIds}
          onChange={(val: number) => {
            setActivity((pre: any) => ({
              ...pre,
              disinfectionUserIds: val,
            }));
          }}
        />
        <DisinfectionCompany
          value={activity?.disinfectionCompanyIds}
          onChange={(val: number) => {
            setActivity((pre: any) => ({
              ...pre,
              disinfectionCompanyIds: val,
            }));
          }}
        />
        <WorkSheet
          taskType={1}
          sheetType={ESheetType.消杀}
          shouldUseMultipleMode={true}
          value={activity.workSheetIds}
          onChange={(val: number) => {
            setActivity((pre: any) => ({
              ...pre,
              workSheetIds: val,
            }));
          }}
        />
      </div>
    </FilterDrawer>
  );
};
