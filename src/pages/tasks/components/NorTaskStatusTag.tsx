import { HTMLAttributes } from 'react';
import { TaskStatusEnum } from '../enum';

interface TaskStatusTagProps extends HTMLAttributes<HTMLSpanElement> {
  status: TaskStatusEnum; // 任务状态
}

const NorTaskStatusTag: React.FC<TaskStatusTagProps> = ({ status, style, className, ...props }) => {
  const taskStatusMap = {
    [TaskStatusEnum.Waiting]: {
      bg: 'var(--adm-color-primary)',
      color: 'white',
      text: '待确认',
    },
    [TaskStatusEnum.InProgress]: {
      bg: 'var(--adm-color-primary)',
      color: 'white',
      text: '进行中',
    },
    [TaskStatusEnum.Rejected]: {
      bg: '#EBEBEB',
      color: '#747578',
      text: '已拒绝',
    },
    [TaskStatusEnum.Completed]: {
      bg: 'rgba(0, 187, 180, 0.2)',
      color: 'white',
      text: '已完成',
    },
    [TaskStatusEnum.Terminated]: {
      bg: '#f4abaa',
      color: 'white',
      text: '已终止',
    },
    [TaskStatusEnum.Delayed]: {
      bg: '#FF7B29',
      color: 'white',
      text: '已逾期',
    },
    [TaskStatusEnum.WaitToStart]: {
      bg: 'none',
      color: '#00BBB4',
      text: '待开始',
      border: '1px solid #00BBB4',
    },
    [TaskStatusEnum.End]: {
      bg: 'rgba(0, 187, 180, 0.2)',
      color: 'white',
      text: '已结束',
    },
    [TaskStatusEnum.Cancel]: {
      bg: 'rgba(0, 0, 0, 0.05)',
      color: '#858585',
      text: '已取消',
    },
    [TaskStatusEnum.EXAMINE]: {
      bg: 'var(--adm-color-primary)',
      color: 'white',
      text: '去审核',
    },
    [TaskStatusEnum.waitForCheckig]: {
      bg: 'rgba(52, 199, 89, 0.3)',
      color: 'white',
      text: '待验收',
    },
  };
  const { bg, color, text, ...other } = (taskStatusMap as any)[status] || {};

  return (
    <span
      {...props}
      className={`inline-block px-1 py-[2px] rounded-sm text-white text-xs leading-[14px] ${
        className || ''
      }`}
      style={{ backgroundColor: bg, color: color, ...other, ...style }}
      // @ts-ignore
      attribute="tag"
    >
      {text}
    </span>
  );
};

export default NorTaskStatusTag;
