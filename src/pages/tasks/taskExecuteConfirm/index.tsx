import React, { useState } from 'react';
import { CustomerFilter, DateFilter, IconFont } from '@src/components';
import { formatDateToUTC } from '@src/utils/utils';
import { Card } from 'antd-mobile';
import classNames from 'classnames';
import dayjs from 'dayjs';
import { isUndefined, omitBy } from 'lodash';
import { useSearchParams } from 'react-router-dom';
import ExecuteItem from './executeItem';
import { TaskConfirmFilter } from './taskConfirmFilter';
import { getTaskExecuteConfirmList } from '../api';
import { ExecuteConfirmItem } from '../api.type';
import { InfiniteScrollBuilder } from '../components/InfiniteScrollBuilder';

export default function TaskExecuteConfirm() {
  const [searchParams] = useSearchParams();
  const [refreshTime, setRefreshTime] = useState(new Date());

  const [timeParams, setTimeParams] = useState<{
    startTime?: string;
    endTime?: string;
  }>(() => {
    return {
      startTime: searchParams.get('startTime') ?? dayjs().subtract(1, 'months').format('YYYY-MM-DD'),
      endTime: searchParams.get('endTime') ?? dayjs().format('YYYY-MM-DD'),
    };
  });
  const [shopTree, setShopTree] = useState<{
    groupId?: string;
    shopIdList?: string[];
  }>(() => {
    return {
      groupId: searchParams.get('groupId') ?? undefined,
      shopIdList: searchParams.getAll('shopIdList').map((i) => i) ?? undefined,
    };
  });

  const [filterParam, setFilterParam] = useState<{
    status?: string;
  }>({
    status: searchParams.get('taskStatus') ?? undefined,
  });
  const [filterVisible, setFilterVisible] = useState(false);
  console.log(shopTree, '=shopTree');

  return (
    <React.Fragment>
      <div className="flex flex-col grow h-0 ">
        <div className="shrink-0">
          <Card className="rounded-none border-b border-solid border-line border-px mb-2 " bodyClassName="py-2">
            <div className="flex justify-between items-start">
              <CustomerFilter
                needAll
                noBg={true}
                groupMaxWidth="3.625rem"
                value={{
                  groupId: shopTree?.groupId?.toString(),
                  shopCodes: shopTree?.shopIdList || [],
                }}
                onChange={(e: any) => {
                  setShopTree({
                    groupId: !e.groupId ? undefined : e.groupId,
                    shopIdList: e.shopCodes,
                  });
                }}
              />

              <DateFilter
                noBg={true}
                shouldDisableDate={() => {
                  return false;
                }}
                value={
                  timeParams.endTime || timeParams.startTime
                    ? [dayjs(timeParams.startTime).toDate(), dayjs(timeParams.endTime).toDate()]
                    : undefined
                }
                onChange={(e) => {
                  setTimeParams(() => {
                    if (e) {
                      return {
                        startTime: dayjs(e[0]).startOf('day').format('YYYY-MM-DD HH:mm:ss'),
                        endTime: dayjs(e[1]).endOf('day').format('YYYY-MM-DD HH:mm:ss'),
                      };
                    }

                    return {
                      startTime: undefined,
                      endTime: undefined,
                    };
                  });
                }}
                key="month"
                type="date"
              />
              <button
                className={classNames(
                  `text-sm flex items-center leading-[14px] flex-shrink-0 ${
                    filterVisible ? 'text-[#141414]' : 'text-[#5E5E5E]'
                  } focus:outline-none`,
                )}
                onClick={() => {
                  setFilterVisible(true);
                }}
              >
                <div className="w-[2px] h-5 bg-black/[0.03] mr-[18px]" />
                筛选
                <IconFont type="icon-a-1111-copy" className="ml-1 text-xs text-[#B8B8B8]" />
              </button>
            </div>
          </Card>
        </div>
        <div className={`grow ${filterVisible ? 'overflow-y-hidden' : 'overflow-y-scroll'} px-[10px] relative`}>
          <div className="flex flex-col gap-[10px]">
            <InfiniteScrollBuilder
              key="TaskExecuteConfirmCard"
              searchParams={omitBy(
                {
                  ...searchParams,
                  refreshTime,
                  createTimeBegin: formatDateToUTC(dayjs(timeParams.startTime).startOf('day')),
                  createTimeEnd: formatDateToUTC(dayjs(timeParams.endTime).endOf('day')),
                  pageSize: 50,
                  shopIds: Array.isArray(shopTree.shopIdList) ? shopTree.shopIdList : undefined,
                  groupId: shopTree.groupId,
                  exePeriodConfirmStatus: filterParam.status,
                },
                isUndefined,
              )}
              api={getTaskExecuteConfirmList}
              renderChildren={(data) => {
                return data.map((o: ExecuteConfirmItem) => {
                  return <ExecuteItem initial={o} setRefreshTime={() => setRefreshTime(new Date())} />;
                });
              }}
            />
          </div>
        </div>
        {/* 筛选框 */}
        <TaskConfirmFilter
          value={filterParam}
          open={filterVisible}
          onClose={() => setFilterVisible(false)}
          onChange={(value: Record<string, any>) => {
            setFilterParam((pre: any) => ({
              ...pre,
              status: value.status,
            }));
            setFilterVisible(false);
          }}
        />
      </div>
    </React.Fragment>
  );
}
