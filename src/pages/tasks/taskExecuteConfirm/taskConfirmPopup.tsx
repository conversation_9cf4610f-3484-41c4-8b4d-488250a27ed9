import React, { useState } from 'react';
import { IPopupPage } from '@src/components/IPopup';
import { useRequest } from 'ahooks';
import { Button, Radio, Space, Toast } from 'antd-mobile';
import dayjs from 'dayjs';
import { observer } from 'mobx-react';
import { submitExecuteConfirm } from '../api';
import { ExecuteConfirmItem } from '../api.type';

interface TaskInfoProps {
  taskInfo: ExecuteConfirmItem;
  visiblePopup: boolean;
  onClose: () => void;
  setRefreshTime: () => void;
}
enum shopLevelEum {
  不合格门店 = 'UNQUALIFIED_SHOP',
  复检门店 = 'RETEST_SHOP',
}

const TaskConfirmPopup: React.FC<TaskInfoProps> = observer((props: TaskInfoProps) => {
  const { taskInfo, visiblePopup, onClose, setRefreshTime } = props;
  const [confirmType, setConfirmType] = useState(undefined);
  const { run, loading: createLoading } = useRequest((data) => submitExecuteConfirm(data), {
    manual: true,
    onSuccess: () => {
      Toast.show({
        icon: 'success',
        content: '提交成功',
        duration: 1000,
        afterClose: () => {
          setTimeout(() => {
            setRefreshTime();
            onClose();
          }, 500);
        },
      });
    },
    onError: (error) => {
      console.log(error);
      setRefreshTime();
      onClose();
    },
  });
  return (
    <React.Fragment>
      <IPopupPage
        DocumentTitle="任务执行时段确认"
        visible={visiblePopup}
        onClose={() => {
          onClose();
        }}
        footer={
          <div className="p-3 bg-white">
            <Button
              onClick={async () => {
                run({
                  taskId: taskInfo?.taskId,
                  exePeriodConfirmType: confirmType,
                });
              }}
              color="primary"
              loading={createLoading}
              disabled={!confirmType}
              loadingText="提交中"
              fill="solid"
              block
              className="h-[45px] text-base"
            >
              提交
            </Button>
          </div>
        }
      >
        <div className="bg-white p-3 ">
          <div className="text-base text-[#141414] font-semibold">{`${taskInfo?.shopId}  ${taskInfo?.shopName}`}</div>
          <div className="flex items-start mt-2 mb-2">
            <span className="text-sm text-[#5E5E5E]">
              任务创建时间 ：{dayjs(taskInfo?.createTime)?.format('MM-DD HH:mm')}
            </span>
          </div>
          <div className="text-base text-[#141414]  mb-2">
            任务名称：<span className="text-primary"> {taskInfo?.taskName}</span>
          </div>
          <div className="text-base text-[#141414] mb-4 font-medium">请确认以下哪个执行时段内能完成任务</div>
          <Radio.Group
            onChange={(val: any) => {
              console.log(val, '=val');

              setConfirmType(val);
            }}
            value={confirmType}
          >
            {taskInfo?.level === shopLevelEum.复检门店 ? (
              <Space direction="vertical" className="w-full">
                <Radio value="_24_HOURS_ARRIVE_SHOP">24小时内能到店完成</Radio>
                <Radio value="_48_HOURS_ARRIVE_SHOP">48小时内能到店完成</Radio>
                <Radio value="OVER_48_HOURS_ARRIVE_SHOP">超出48小时能到店完成</Radio>
              </Space>
            ) : (
              // 不合格门店
              <Space direction="vertical" className="w-full">
                <Radio value="_72_HOURS_ARRIVE_SHOP">72小时内能到店完成</Radio>
                <Radio value="OVER_72_HOURS_ARRIVE_SHOP">超出72小时内能到店完成</Radio>
              </Space>
            )}
          </Radio.Group>
        </div>
      </IPopupPage>
    </React.Fragment>
  );
});
export default TaskConfirmPopup;
