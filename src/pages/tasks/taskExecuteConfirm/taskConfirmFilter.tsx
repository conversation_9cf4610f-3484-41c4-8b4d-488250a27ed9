import { useState } from 'react';
import { DrawerProps } from 'antd';
import { FilterDrawer } from '../components/FilterDrawer';
import { yytTaskPlanStatusEnum, yytTaskStatusCNToEnEnum } from '../enum';

interface TaskConfirmFilterProps extends DrawerProps {
  value: Record<string, any>;
  onChange: (value: Record<string, any>) => void;
  onClose: () => void;
}

export const init = {
  status: undefined,
};

export const TaskConfirmFilter = ({ value, onChange, onClose, ...props }: TaskConfirmFilterProps) => {
  const [activity, setActivity] = useState<any>(value);
  console.log(activity, '=activity');

  const onOk = () => {
    onChange(activity);
  };
  const onClear = () => {
    setActivity(init);
  };
  return (
    <FilterDrawer
      {...props}
      onClose={() => {
        // 重置一下activity
        setActivity(value);
        onClose();
      }}
      onOk={onOk}
      onClear={onClear}
    >
      <div className="flex flex-col gap-6">
        <div className="flex flex-col gap-3">
          <h3 className="text-[#141414] text-sm leading-[14px]">状态</h3>
          {
            <div className="flex flex-wrap gap-2">
              {[
                { label: '待确认', value: yytTaskStatusCNToEnEnum.待确认 },
                { label: '执行修改审核', value: yytTaskStatusCNToEnEnum.执行修改审核 },
                { label: '转派中', value: yytTaskStatusCNToEnEnum.转派中 },
                { label: '已确认', value: yytTaskStatusCNToEnEnum.已确认 },
                { label: '已取消', value: yytTaskStatusCNToEnEnum.已取消 },
              ].map((item) => {
                return (
                  <button
                    key={item.value}
                    className={`w-20 h-[30px] rounded ${
                      activity?.status === yytTaskPlanStatusEnum[item.label as keyof typeof yytTaskPlanStatusEnum]
                        ? 'text-primary bg-primary/10'
                        : 'text-58 bg-black/[0.03]'
                    }  text-sm left-[14px]`}
                    onClick={() => {
                      setActivity((pre: any) => {
                        if (activity?.status === item.value) {
                          return {
                            ...pre,
                            status: undefined,
                          };
                        } else {
                          return {
                            ...pre,
                            status: item.value,
                          };
                        }
                      });
                    }}
                  >
                    {item.label}
                  </button>
                );
              })}
            </div>
          }
        </div>
      </div>
    </FilterDrawer>
  );
};
