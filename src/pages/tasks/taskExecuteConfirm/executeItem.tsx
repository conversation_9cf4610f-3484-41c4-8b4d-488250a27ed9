import React, { useState } from 'react';
import { IconFont } from '@src/components';
import { SubTypeTag } from '@src/pages/patrol/planDetail/detailCard';
import { encrypt } from '@src/utils/utils';
import { H5Bridge } from '@tastien/rn-bridge';
import { Button, Toast } from 'antd-mobile';
import dayjs from 'dayjs';
import { useNavigate } from 'react-router-dom';
import TaskConfirmPopup from './taskConfirmPopup';
import { ExecuteConfirmItem } from '../api.type';

interface DetailCardProps {
  initial: ExecuteConfirmItem;
  setRefreshTime: () => void;
}

enum confirmStatusEum {
  转派中 = 'TRANSFERRING',
  已确认 = 'CONFIRMED',
  执行修改审核 = 'EXE_MODIFY_AUDIT',
  待确认 = 'NOT_CONFIRMED',
}
enum taskStatusEum {
  待开始 = 'NOT_READY',
  进行中 = 'PROCESS',
  已完成 = 'COMPLETED',
  已过期 = 'EXPIRED',
  已取消 = 'CANCELED',
  待确认 = 'WAIT_CONFIRM',
}
// eslint-disable-next-line react-refresh/only-export-components
export const statusTagMap: { [key: string]: React.ReactNode } = {
  TRANSFERRING: <div className="text-sm text-[#378BFF]">转派中</div>,
  CONFIRMED: <div className="text-sm text-[#6CCD5F]">已确认</div>,
  EXE_MODIFY_AUDIT: <div className="text-sm text-[#3DB86D]">执行修改审核</div>,
  NOT_CONFIRMED: <div className="text-sm text-[#EA0000]">待确认</div>,
  CANCELED: <div className="text-sm text-[#EA0000]">已取消</div>,
};
const ExecuteItem: React.FC<DetailCardProps> = ({ initial, setRefreshTime }) => {
  const [visiblePopup, setVisiblePopup] = useState<boolean>(false);
  const navigate = useNavigate();
  return (
    <React.Fragment>
      <div className="bg-white p-3  rounded [&:not(:first-child)]:mt-[12px]">
        <div className="flex justify-between  items-center">
          <div className="flex  items-center">
            <div className="ml-1 text-xs bg-primary/10 border border-primary text-primary rounded px-[2px] py-[1px]">
              {SubTypeTag[initial?.taskType]}
            </div>

            <div className="ml-1 text-xs">
              {`由 ${initial.inspectorName}  ${dayjs(initial?.createTime)?.format('MM-DD HH:mm')}  创建`}
            </div>
          </div>
        </div>
        <div className="flex  flex-col  items-start mt-[6px] p-3 bg-[#FAFAFA] rounded-[4px]">
          <div className="flex w-full justify-between items-center">
            <div className="text-sm text-[#141414] font-semibold">{`${initial?.shopId}  ${initial?.shopName}`}</div>
            <div>{statusTagMap[initial?.exePeriodConfirmStatus]}</div>
          </div>
          <div className="flex items-start mt-2 mb-2">
            <IconFont type="icon-location-1" className="text-sm text-[#5E5E5E] mt-[3px]" />
            <span className="ml-1 text-sm text-[#5E5E5E]">{initial?.shopAddress}</span>
          </div>
          <div
            className="text-primary text-sm flex  items-center"
            onClick={() => {
              if (!initial?.shopContact) {
                Toast.show({
                  content: '暂无联系方式',
                });
                return;
              }
              window.location.href = `tel:${initial?.shopContact}`;
            }}
          >
            <IconFont type="icon-rongqi" />
            <span className="ml-1">电话联系</span>
          </div>
          <div className="text-[#9C9C9C] text-sm my-2">巡检人：{initial?.inspectorName}</div>
          {initial?.exePeriodConfirmStatus === confirmStatusEum.已确认 && (
            <div className="text-[#9C9C9C] text-sm">
              任务执行时间：{dayjs(initial?.executionPeriodBeginTime)?.format('MM-DD HH:mm')} ~{' '}
              {dayjs(initial?.executionPeriodEndTime)?.format('MM-DD HH:mm')}
            </div>
          )}
        </div>
        {initial?.exePeriodConfirmStatus === confirmStatusEum.待确认 && (
          <div className="flex justify-between">
            <Button
              onClick={async () => {
                setVisiblePopup(true);
              }}
              block
              className="mt-3 mb-1 border-[0.5px] text-sm leading-4 border-[#DCDCDC] ml-2"
            >
              确认执行时段
            </Button>
          </div>
        )}
        {initial?.exePeriodConfirmStatus === confirmStatusEum.已确认 &&
          [taskStatusEum.待开始, taskStatusEum.进行中].includes(initial?.taskStatus as taskStatusEum) && (
            <div className="flex justify-between">
              <Button
                onClick={async () => {
                  if (initial?.taskStatus === taskStatusEum.进行中) {
                    navigate(`/patrol/shopcheck?taskId=${initial?.taskId}`);
                    return;
                  }
                  const info = {
                    path: 'SupervisorSignIn',
                    shopId: initial.shopId,
                    taskId: initial.taskId,
                    callBackUrl: `/patrol/shopcheck?taskId=${initial.taskId}`,
                  };
                  await H5Bridge.customPostMessage.customPostMessage({
                    module: 'face-module',
                    method: 'navigateTo',
                    params: {
                      param: encrypt(info),
                    },
                  });
                }}
                block
                className="mt-3 mb-1 border-[0.5px] text-sm leading-4 border-[#DCDCDC] ml-2"
              >
                去处理
              </Button>
            </div>
          )}
        {[taskStatusEum.已完成].includes(initial?.taskStatus as taskStatusEum) && (
          <div className="flex justify-between">
            <Button
              onClick={async () => {
                navigate(`/patrol/reportdetail?taskId=${initial?.taskId}`);
              }}
              block
              className="mt-3 mb-1 border-[0.5px] text-sm leading-4 border-[#DCDCDC] ml-2"
            >
              查看报告
            </Button>
          </div>
        )}
      </div>
      <TaskConfirmPopup
        taskInfo={initial}
        visiblePopup={visiblePopup}
        onClose={() => setVisiblePopup(false)}
        setRefreshTime={() => setRefreshTime()}
      />
    </React.Fragment>
  );
};

export default ExecuteItem;
