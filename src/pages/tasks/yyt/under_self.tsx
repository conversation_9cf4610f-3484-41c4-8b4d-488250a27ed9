import { useContext, useState } from 'react';
import { PageContext } from '@src/common/page-context';
import { CustomerFilter, IconFont } from '@src/components';
import SelfDateFilter from '@src/components/SelfDateFilter';
import { useGroupTree } from '@src/hooks';
import { formatDateToUTC } from '@src/utils/utils';
import { Card } from 'antd-mobile';
import classNames from 'classnames';
import dayjs from 'dayjs';
import qs from 'qs';
import { useLocation, useNavigate } from 'react-router-dom';
import { WorkTypeEnum } from '../enum';
import { TasksContext } from '../layout';
import { getUnderTaskSelfPlanList } from '../api';
import { InfiniteScrollBuilder } from '../components/InfiniteScrollBuilder';
import { UnderSelfCheckingTaskCard } from '../components/UnderTasksCard';
import { UnderSelfFilter } from './UnderSelfFilter';

export enum patrolTypeEnum {
  '巡检计划',
  '巡检任务',
}

const patrolType = [
  {
    label: '巡检计划',
    value: patrolTypeEnum.巡检计划,
    initial: {
      pageNo: 1,
      pageSize: 50,
      filterPermission: true,
    },
  },
  {
    label: '巡检任务',
    value: patrolTypeEnum.巡检任务,
    initial: {
      pageNo: 1,
      pageSize: 50,
      cross: 0,
      groupId: undefined,
      shopIds: [],
      filterPermission: true,
    },
  },
];
type TRequestType = {
  endTime?: string;
  startTime?: string;
  groupId?: number;
  pageNo: number;
  pageSize: number;
  sortBy?: string;
  shopIdList?: string[];
  subTypeList?: string[];
};
const defaultParams: TRequestType = {
  // startTime: dayjs().subtract(1, 'months').format('YYYY-MM-DD'),
  // endTime: dayjs().format('YYYY-MM-DD'),
  // startTime: dayjs().subtract(6, 'day').format('YYYY-MM-DD'),
  // startTime: dayjs().format('YYYY-MM-DD'),
  // endTime: dayjs().format('YYYY-MM-DD'),
  pageNo: 1,
  pageSize: 10,
  shopIdList: [],
  // groupId: 0,
  // shopIdList: undefined,
};
export default function YytUnderSelf() {
  const { pid } = useGroupTree();
  const location = useLocation();
  const [activity, setActivity] = useState(() => {
    const _activity = qs.parse(location.search, { ignoreQueryPrefix: true })?.activity;
    if (_activity !== undefined) {
      return Number(_activity);
    } else {
      return patrolType[0].value;
    }
  });
  // const [searchParams, setSearchParams] = useState<any>(() => {
  //   const _activity = qs.parse(location.search, { ignoreQueryPrefix: true })?.activity;
  //   if (_activity !== undefined) {
  //     return patrolType[Number(_activity)].initial;
  //   } else {
  //     return patrolType[0].initial;
  //   }
  // });
  const [searchParams, setSearchParams] = useState<TRequestType>({ ...defaultParams });

  const [filterVisible, setFilterVisible] = useState(false);

  const navigate = useNavigate();

  const { queryType } = useContext(TasksContext);
  const searchParamsBuilder = (params: any) => {
    const { groupId, shopIdList, ...rest } = params;
    if (shopIdList?.length === 0) {
      if (groupId === undefined) {
        return {
          ...rest,
          resource: undefined,
        };
      } else {
        return {
          ...rest,
          resource: {
            resourceType: 'ORGANIZATION',
            resourceId: groupId,
            hasMonitor: true,
          },
        };
      }
    } else {
      console.log('shopIdList', shopIdList);
      return {
        ...rest,
        resource: {
          resourceType: 'SHOP',
          resourceId: shopIdList[0],
          hasMonitor: true,
        },
      };
    }
  };
  return (
    <PageContext.Provider value={{ pid }}>
      <div className="flex flex-col grow h-0 ">
        <div className="shrink-0">
          <Card className="rounded-none border-b border-solid border-line border-px mb-2 " bodyClassName="py-2">
            <div className="flex justify-between items-start">
              <CustomerFilter
                needAll
                noBg={true}
                groupMaxWidth="3.625rem"
                value={{ groupId: searchParams?.groupId?.toString(), shopCodes: searchParams.shopIdList || [] }}
                onChange={(e) => {
                  setSearchParams((pre: any) => ({
                    ...pre,
                    // groupId: e?.shopCodes?.length > 0 ? undefined : +e.groupId,
                    groupId: !e.groupId ? undefined : +e.groupId,
                    shopIdList: e.shopCodes,
                  }));
                }}
              />

              <SelfDateFilter
                noBg={true}
                onlyDate
                // noDefault
                // value={paramDate[pollingType]}
                value={[dayjs(searchParams?.startTime).toDate(), dayjs(searchParams?.endTime).toDate()]}
                // value={[dayjs(searchParams.startTime).toDate(), dayjs(searchParams.endTime).toDate()]}

                onChange={(e) => {
                  setSearchParams((p) => ({
                    ...p,
                    startTime: dayjs(e[0]).format('YYYY-MM-DD'),
                    endTime: dayjs(e[1]).format('YYYY-MM-DD'),
                  }));
                }}
                key="month"
                type="date"
              />
              <button
                className={classNames(
                  `text-sm flex items-center leading-[14px] flex-shrink-0 ${
                    filterVisible ? 'text-[#141414]' : 'text-[#5E5E5E]'
                  } focus:outline-none`,
                )}
                onClick={() => {
                  setFilterVisible(true);
                }}
              >
                <div className="w-[2px] h-5 bg-black/[0.03] mr-[18px]" />
                筛选
                <IconFont type="icon-a-1111-copy" className="ml-1 text-xs text-[#B8B8B8]" />
              </button>
            </div>
          </Card>
        </div>
        <div className={`grow ${filterVisible ? 'overflow-y-hidden' : 'overflow-y-scroll'} px-[10px] relative`}>
          {/* <div className="flex justify-between py-3 bg-[#f9f9f9]">
          {activity === 1 && (
            <div className="flex text-xs leading-3">
              {[
                { label: '权限内', value: 0 },
                { label: '权限外', value: 1 },
              ].map((o) => {
                return (
                  <button
                    onClick={() => {
                      setSearchParams((pre: any) => ({ ...pre, cross: o.value }));
                    }}
                    key={o.value}
                    className={`focus:outline-none px-[9px] py-[6px] ${searchParams?.cross !== o.value
                      ? 'border border-[#F0F0F0] text-85'
                      : 'bg-primary/10 border border-primary text-primary'
                      } ${o.value === TaskPermissionEnum['权限内']
                        ? 'rounded-l-[4px]'
                        : 'rounded-r-[4px]'
                      }`}
                  >
                    {o.label}
                  </button>
                );
              })}
            </div>
          )}
        </div> */}
          <div className="flex flex-col gap-[10px]">
            <InfiniteScrollBuilder
              key="PrincipalPatrolTaskCard"
              // searchParams={
              //   {
              //     ...searchParams,
              //   }
              searchParams={{
                ...searchParams,
                startTime: formatDateToUTC(dayjs(searchParams.startTime).startOf('day')),
                endTime: formatDateToUTC(dayjs(searchParams.endTime).endOf('day')),
              }}
              api={getUnderTaskSelfPlanList}
              renderChildren={(data) => {
                return data.map((o: any) => {
                  return (
                    <UnderSelfCheckingTaskCard key={o.taskId} initial={{ ...o, taskStatus: o.taskBusinessStatus }} />
                  );
                });
              }}
              workType={WorkTypeEnum.Self}
            />
            {/* )} */}
          </div>
        </div>
        {/* 筛选框 */}
        {/* 筛选框 */}
        <UnderSelfFilter
          value={searchParamsBuilder(searchParams)}
          open={filterVisible}
          onClose={() => setFilterVisible(false)}
          onChange={(value: Record<string, any>) => {
            setSearchParams((pre: any) => {
              return { ...pre, ...value };
            });
            setFilterVisible(false);
          }}
          selfType={1}
        />
      </div>
    </PageContext.Provider>
  );
}
