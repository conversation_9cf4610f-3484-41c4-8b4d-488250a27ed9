import { useContext, useEffect, useState } from 'react';
import emptyImage from '@src/assets/images/task-empty.png';
import { CustomerFilter, DateFilter, IconFont, Loading } from '@src/components';
import { useScrollFetch } from '@src/hooks';
import useQuerySearchParams from '@src/hooks/useQuerySearchParams';
import { ERoleType } from '@src/pages/mission';
import { ESheetType } from '@src/pages/mission/api';
import { KillTaskFilter } from '@src/pages/mission/components/KillTaskFilter';
import { formatDateToUTC } from '@src/utils/utils';
import { InfiniteScroll } from 'antd-mobile';
import cn from 'classnames';
import dayjs from 'dayjs';
import { flatten, isUndefined, omitBy } from 'lodash';
import { observer } from 'mobx-react';
// import { useNavigate, useSearchParams } from 'react-router-dom';
import { useSearchParams } from 'react-router-dom';
import { getKillListPage, TKillItem } from '../api';
import KillTaskStatusFilter from '../components/KillTaskStatusFilter';
import PendingTaskPopup from '../components/PendingTaskPopup';
import ProgressPopup from '../components/ProgressPopup';
import {
  EmergencyStructKillTaskStatusOptions,
  KillTaskStatusEnum,
  killTaskStatusOptions,
  KillTaskTypeEnum,
  killTaskTypeOptions,
  TaskTypeEnum,
  WorkTypeEnum,
} from '../enum';
import { TasksContext } from '../layout';
import { StructKillCard } from '../sructKill/StructKillCard';

function Under_structKill_isEmergency({ isEmergency = false }: { isEmergency?: boolean }) {
  const [{ queryType }] = useQuerySearchParams();
  const { refreshCount } = useContext(TasksContext);

  const [searchParams, setSearchParams] = useSearchParams();
  const [searchFilter, setSearchFilter] = useState(() => {
    return {
      worksheetIds: searchParams.getAll('worksheetIds')?.map((i) => Number(i)) || undefined,
      disinfectionCompanyIds: searchParams.getAll('disinfectionCompanyIds')?.map((i) => i) || undefined,
      taskStatus: searchParams.get('taskStatus') || KillTaskStatusEnum.ALL,
    };
  });
  const [taskStatusFilterOpen, setTaskStatusFilterOpen] = useState(false);
  const [taskTypeFilterOpen, setTaskTypeFilterOpen] = useState(false);
  const [, setSortFilterOpen] = useState(false);

  const [pendingTaskPopupVisible, setPendingTaskPopupVisible] = useState(false);
  const [filterVisible, setFilterVisible] = useState(false);

  const [timeParams, setTimeParams] = useState<{
    arriveTimeStart?: string;
    arriveTimeEnd?: string;
  }>(() => {
    return {
      arriveTimeStart: searchParams.get('arriveTimeStart') ?? dayjs().subtract(7, 'day').format('YYYY-MM-DD'),
      arriveTimeEnd: searchParams.get('arriveTimeEnd') ?? dayjs().add(7, 'day').format('YYYY-MM-DD'),
    };
  });

  const [taskStatus, setTaskStatus] = useState<KillTaskStatusEnum | KillTaskTypeEnum>(() => {
    const query = searchParams.get('taskStatus');
    return (query ? query : KillTaskStatusEnum.ALL) as KillTaskStatusEnum;
  });
  const [disinfectionType, setDisinfectionType] = useState<KillTaskTypeEnum | KillTaskStatusEnum>(() => {
    const query = searchParams.get('disinfectionType');
    return (query ? query : KillTaskTypeEnum.ALL) as KillTaskTypeEnum;
  });
  const [progressPopupStatus, setProgressPopupStatus] = useState<{
    visible: boolean;
    progressMin: number;
    taskUserId: number;
    scheduleId?: number;
    isFinish: boolean;
    isEdit?: boolean;
    finishAttachRequired: number;
    updateAttachRequired: number;
  }>({
    visible: false,
    progressMin: 0,
    taskUserId: 0,
    isFinish: false,
    isEdit: false,
    finishAttachRequired: 0,
    updateAttachRequired: 0,
  });
  const [shopTree, setShopTree] = useState<{
    groupId?: string;
    shopIds?: string[];
  }>(() => {
    return {
      groupId: searchParams.get('groupId') ?? undefined,
      shopIds: searchParams.getAll('shopIds').map((i) => i) ?? undefined,
    };
  });

  const {
    list,
    fetchNext,
    over,
    reload: refreshList,
    loading,
  } = useScrollFetch(
    ({ pageNo, pageSize }) => {
      const params = {
        pageNo,
        hasCount: true, // 需要total
        pageSize,
        queryType: !isEmergency ? TaskTypeEnum.Under : TaskTypeEnum.Created,
        disinfectionType: isEmergency ? 'EXIGENCY' : disinfectionType === 'ALL' ? undefined : disinfectionType,
        // taskTypeIds,
        ...searchFilter,
        taskStatus: !isEmergency
          ? taskStatus === 'ALL'
            ? undefined
            : taskStatus
          : searchFilter?.taskStatus === KillTaskStatusEnum.ALL
            ? undefined
            : searchFilter?.taskStatus,
        worksheetIds: searchFilter.worksheetIds.length > 0 ? searchFilter.worksheetIds : undefined,
        disinfectionCompanyIds:
          searchFilter.disinfectionCompanyIds.length > 0 ? searchFilter.disinfectionCompanyIds : undefined,
        arriveTimeStart: !(queryType === 'created' && isEmergency)
          ? formatDateToUTC(dayjs(timeParams.arriveTimeStart).startOf('day'))
          : undefined,
        arriveTimeEnd: !(queryType === 'created' && isEmergency)
          ? formatDateToUTC(dayjs(timeParams.arriveTimeEnd).endOf('day'))
          : undefined,

        createTimeStart:
          queryType === 'created' && isEmergency
            ? formatDateToUTC(dayjs(timeParams.arriveTimeStart).startOf('day'))
            : undefined,
        createTimeEnd:
          queryType === 'created' && isEmergency
            ? formatDateToUTC(dayjs(timeParams.arriveTimeEnd).endOf('day'))
            : undefined,
        orderByFiled: queryType === 'created' && isEmergency ? 'create_time' : undefined,

        shopIds: isEmergency ? (Array.isArray(shopTree.shopIds) ? shopTree.shopIds : undefined) : undefined,
        groupId: isEmergency ? shopTree?.groupId : undefined,
      };
      setSearchParams(
        omitBy(
          {
            ...params,
            ...searchFilter,
            workType: !isEmergency ? WorkTypeEnum.StructKill : WorkTypeEnum.EmergencyStructKill,
            taskInfoId: searchFilter?.taskInfoId || [],
          },
          isUndefined,
        ),
        { replace: true },
      );
      return getKillListPage(params);
    },
    [disinfectionType, taskStatus, searchFilter, timeParams, shopTree],
    {
      pageSize: 20,
      postResult: (res) => {
        return res?.data;
      },
      computeOver: (res) => {
        return res?.data.length === 0;
      },
    },
  );

  useEffect(() => {
    const messageHandler = (event: any) => {
      if (event?.data) {
        const messageData = JSON.parse(event?.data)?.data;
        if (messageData?.type === 'refresh') {
          refreshList();
        }
      }
    };
    document.addEventListener('message', messageHandler);
    window.addEventListener('message', messageHandler);
    return () => {
      document.removeEventListener('message', messageHandler);
      window.removeEventListener('message', messageHandler);
    };
  }, []);

  const typeOptions = isEmergency ? EmergencyStructKillTaskStatusOptions : killTaskStatusOptions;
  return (
    <Loading spinning={loading}>
      <div className="grow flex flex-col overflow-hidden">
        <div className="bg-white px-4  flex justify-between items-center" id="filterDrawerContainer">
          <CustomerFilter
            needAll
            noBg={true}
            groupMaxWidth="3.625rem"
            value={{
              groupId: shopTree?.groupId?.toString(),
              shopCodes: shopTree?.shopIds || [],
            }}
            onChange={(e: any) => {
              setShopTree({
                groupId: !e.groupId ? undefined : e.groupId,
                shopIds: e.shopCodes,
              });
            }}
          />

          {/* <div className="flex gap-2">
            <button
              className={`flex gap-1 text-sm leading-[14px] p-2 rounded ${
                taskStatusFilterOpen ? 'items-start rounded-b-none' : 'mb-2'
              } ${taskStatus === KillTaskStatusEnum.ALL ? 'bg-[#F7F7F7] text-grey' : 'bg-primary/[0.08] text-primary'}`}
              onClick={() => {
                setTaskStatusFilterOpen(true);
                setTaskTypeFilterOpen(false);
                setSortFilterOpen(false);
                setFilterVisible(false);
              }}
            >
              {typeOptions.find((i) => i.value === taskStatus)?.label}
              <DownOutline className="text-xs" />
            </button>
            {!isEmergency && (
              <button
                className={`flex gap-1 text-sm leading-[14px] p-2 rounded ${
                  taskTypeFilterOpen ? 'items-start rounded-b-none' : 'mb-2'
                } ${
                  disinfectionType === KillTaskTypeEnum.ALL
                    ? 'bg-[#F7F7F7] text-grey'
                    : 'bg-primary/[0.08] text-primary'
                }`}
                onClick={() => {
                  setTaskTypeFilterOpen(true);
                  setTaskStatusFilterOpen(false);
                  setSortFilterOpen(false);
                  setFilterVisible(false);
                }}
              >
                {
                  killTaskTypeOptions
                    .concat({ label: '日常消杀', value: KillTaskTypeEnum.DAILY })
                    .find((i) => i.value === disinfectionType)?.label
                }
                <DownOutline className="text-xs" />
              </button>
            )}
          </div> */}
          <DateFilter
            value={
              timeParams.arriveTimeEnd || timeParams.arriveTimeStart
                ? [dayjs(timeParams.arriveTimeStart).toDate(), dayjs(timeParams.arriveTimeEnd).toDate()]
                : undefined
            }
            onChange={(e) => {
              setTimeParams(() => {
                if (e) {
                  return {
                    arriveTimeStart: dayjs(e[0]).startOf('day').format('YYYY-MM-DD HH:mm:ss'),
                    arriveTimeEnd: dayjs(e[1]).endOf('day').format('YYYY-MM-DD HH:mm:ss'),
                  };
                }

                return {
                  arriveTimeStart: undefined,
                  arriveTimeEnd: undefined,
                };
              });
            }}
            noDefault
            className="my-2"
            noMaxFlag={true}
          />

          <button
            className={cn(
              `flex text-sm leading-[14px] my-2 ${
                filterVisible ? 'text-primary' : 'text-[#858585]'
              } focus:outline-none`,
            )}
            onClick={() => {
              setFilterVisible(true);
              setSortFilterOpen(false);
              setTaskStatusFilterOpen(false);
              setTaskTypeFilterOpen(false);
            }}
          >
            筛选
            <IconFont type="icon-a-1111-copy" className="ml-1 text-xs text-[#B8B8B8]" />
          </button>
        </div>
        <div className="flex flex-col flex-1 overflow-y-auto relative" id="topBar">
          <div className="px-3 py-2 flex-1 overflow-y-auto relative">
            {list && flatten(list).length > 0 ? (
              <div>
                {flatten(list)?.map((item: TKillItem) => (
                  <StructKillCard info={item} key={item?.taskId} delReportFn={refreshList} />
                ))}
                <InfiniteScroll loadMore={fetchNext} hasMore={!over} />
              </div>
            ) : (
              <div className="flex flex-col items-center mt-[70px]">
                <img src={emptyImage} className="w-[240px]" alt="" />
                <span className="text-13 text-[#9E9E9E] mt-5">暂无任务</span>
              </div>
            )}
          </div>
        </div>

        <KillTaskStatusFilter
          open={taskStatusFilterOpen}
          value={taskStatus}
          options={typeOptions}
          onClose={() => setTaskStatusFilterOpen(false)}
          onChange={(value) => {
            setTaskStatusFilterOpen(false);
            setTaskStatus(value);
          }}
        />
        <KillTaskStatusFilter
          open={taskTypeFilterOpen}
          value={disinfectionType}
          options={killTaskTypeOptions.concat({ label: '日常消杀', value: KillTaskTypeEnum.DAILY })}
          onClose={() => setTaskTypeFilterOpen(false)}
          onChange={(value) => {
            setTaskTypeFilterOpen(false);
            setDisinfectionType(value);
          }}
        />
        <ProgressPopup
          visible={progressPopupStatus.visible}
          isFinish={progressPopupStatus.isFinish}
          progressMin={progressPopupStatus.progressMin}
          finishAttachRequired={progressPopupStatus.finishAttachRequired}
          updateAttachRequired={progressPopupStatus.updateAttachRequired}
          taskUserId={progressPopupStatus.taskUserId}
          scheduleId={progressPopupStatus.scheduleId}
          isEdit={progressPopupStatus.isEdit}
          onClose={() =>
            setProgressPopupStatus((prev) => ({
              ...prev,
              visible: false,
            }))
          }
          onSuccess={() => {
            refreshCount();
            refreshList();
          }}
        />
        <PendingTaskPopup
          visible={pendingTaskPopupVisible}
          onClose={() => setPendingTaskPopupVisible(false)}
          onUpdate={() => {
            refreshCount();
            refreshList();
          }}
        />
        <KillTaskFilter
          value={searchFilter}
          sheetType={ESheetType.消杀}
          open={filterVisible}
          onClose={() => setFilterVisible(false)}
          onChange={(value: Record<string, any>) => {
            setSearchFilter((pre: any) => ({
              ...pre,
              worksheetIds: value.worksheetIds || [],
              disinfectionCompanyIds: value.disinfectionCompanyIds || [],
              patrolUserIds: value.patrolUserIds || [],
              taskStatus: value.taskStatus,
            }));
            setFilterVisible(false);
          }}
          roleType={ERoleType.督导}
          showWorkSheet={!isEmergency}
          showKillCompany
          showKillPollingUser={!isEmergency}
          isEmergency={isEmergency}
        />
      </div>
    </Loading>
  );
}

export default observer(Under_structKill_isEmergency);
