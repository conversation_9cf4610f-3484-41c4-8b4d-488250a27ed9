import { useState } from 'react';
import { AllUserSelect } from '@src/pages/mission/components/AllUserSelect';
import { DrawerProps } from 'antd';
// import { FilterDrawer } from './FilterDrawer';
// import { CheckWayEnum, yytTaskPlanStatusEnum, yytTaskStatusEnum } from '../../enum';
// import { patrolTypeEnum } from '../principal_patrol';
// import { WorkSheet } from './WorkSheet';
// import { AllUserSelect } from './AllUserSelect';
import { FilterDrawer } from '../components/FilterDrawer';
import {
  AlertColorEnum,
  CheckWayCNToEnEnum,
  CheckWayEnum,
  ColorCNToEnEnum,
  patrolTypeEnum,
  yytTaskPlanStatusEnum,
  yytTaskStatusCNToEnEnum,
  yytTaskStatusENToCNEnum,
  yytTaskStatusEnum,
} from '../enum';
// import { AllUserSelect } from '../components/AllUserSelect';

interface UnderPatrolFilterProps extends DrawerProps {
  value: Record<string, any>;
  onChange: (value: Record<string, any>) => void;
  onClose: () => void;
  patrolType: patrolTypeEnum;
}

export const init = {
  0: {
    status: undefined,
    planType: undefined,
    patrolUserIds: undefined,
  },
  1: {
    status: undefined,
    subType: undefined,
    // patrolUserIds: undefined,
    patrolUserId: undefined,
    worksheetId: undefined,
  },
};

export const UnderPatrolFilter = ({ value, onChange, patrolType, onClose, ...props }: UnderPatrolFilterProps) => {
  console.log('筛选组件收到的', value);

  const [activity, setActivity] = useState<any>(value);
  const onOk = () => {
    onChange(activity);
  };
  const onClear = () => {
    setActivity(init[patrolType]);
  };
  return (
    <FilterDrawer
      {...props}
      onClose={() => {
        // 重置一下activity
        setActivity(value);
        onClose();
      }}
      onOk={onOk}
      onClear={onClear}
    >
      <div className="mt-20 flex flex-col gap-6">
        <div className="flex flex-col gap-3">
          <h3 className="text-[#141414] text-sm leading-[14px]">巡检状态</h3>
          {patrolType === patrolTypeEnum.巡检计划 && (
            <div className="flex flex-wrap gap-2">
              {[
                { label: '待开始', value: yytTaskStatusCNToEnEnum['待开始'] },
                { label: '进行中', value: yytTaskStatusCNToEnEnum['进行中'] },
                { label: '已完成', value: yytTaskStatusCNToEnEnum['已完成'] },
                { label: '已取消', value: yytTaskStatusCNToEnEnum['已取消'] },
              ].map((item) => {
                return (
                  <button
                    key={item.value}
                    className={`w-20 h-[30px] rounded ${
                      activity?.status === yytTaskPlanStatusEnum[item.label as keyof typeof yytTaskPlanStatusEnum]
                        ? 'text-primary bg-primary/10'
                        : 'text-58 bg-black/[0.03]'
                    }  text-sm left-[14px]`}
                    onClick={() => {
                      setActivity((pre: any) => {
                        if (activity?.status === item.value) {
                          return {
                            ...pre,
                            status: undefined,
                          };
                        } else {
                          return {
                            ...pre,
                            status: item.value,
                          };
                        }
                      });
                    }}
                  >
                    {item.label}
                  </button>
                );
              })}
            </div>
          )}

          {patrolType === patrolTypeEnum.巡检任务 && (
            <div className="flex flex-wrap gap-2">
              {[
                { label: '待开始', value: yytTaskStatusCNToEnEnum['待开始'] },
                { label: '进行中', value: yytTaskStatusCNToEnEnum['进行中'] },
                { label: '已完成', value: yytTaskStatusCNToEnEnum['已完成'] },
                {
                  label: yytTaskStatusENToCNEnum.EXPIRED,
                  value: yytTaskStatusCNToEnEnum[yytTaskStatusENToCNEnum.EXPIRED],
                },
                {
                  label: yytTaskStatusENToCNEnum.CANCELED,
                  value: yytTaskStatusCNToEnEnum[yytTaskStatusENToCNEnum.CANCELED],
                },
              ].map((item) => {
                return (
                  <button
                    key={item.value}
                    className={`w-20 h-[30px] rounded ${
                      activity?.status === yytTaskStatusCNToEnEnum[item.label as keyof typeof yytTaskStatusEnum]
                        ? 'text-primary bg-primary/10'
                        : 'text-58 bg-black/[0.03]'
                    }  text-sm left-[14px]`}
                    onClick={() => {
                      setActivity((pre: any) => {
                        if (activity?.status === item.value) {
                          return {
                            ...pre,
                            status: undefined,
                          };
                        } else {
                          return {
                            ...pre,
                            status: item.value,
                          };
                        }
                      });
                    }}
                  >
                    {item.label}
                  </button>
                );
              })}
            </div>
          )}
        </div>
        <div className="flex flex-col gap-3">
          <h3 className="text-[#141414] text-sm leading-[14px]">巡检类型</h3>
          <div className="flex flex-wrap gap-2">
            {[
              {
                label: '到店巡检',
                value: CheckWayCNToEnEnum['到店巡检'],
              },
              { label: '视频云巡检', value: CheckWayCNToEnEnum['视频云巡检'] },
              {
                label: '食安线下稽核',
                value: CheckWayCNToEnEnum['食安线下稽核'],
              },
              {
                label: '食安线上稽核',
                value: CheckWayCNToEnEnum['食安线上稽核'],
              },
              {
                label: '诊断巡检',
                value: CheckWayCNToEnEnum['诊断巡检'],
              },
              // 任务中心/我管辖的/巡检
              {
                label: '食安稽核到店辅导',
                value: CheckWayCNToEnEnum['食安稽核到店辅导'],
              },
            ].map((item) => {
              return (
                <button
                  key={item.value}
                  className={`px-4 h-[30px] rounded ${
                    activity?.[patrolType === patrolTypeEnum.巡检计划 ? 'planType' : 'subType'] ===
                    CheckWayCNToEnEnum[item.label as keyof typeof CheckWayEnum]
                      ? 'text-primary bg-primary/10'
                      : 'text-58 bg-black/[0.03]'
                  }  text-sm left-[14px]`}
                  onClick={() => {
                    setActivity((pre: any) => {
                      if (activity?.[patrolType === patrolTypeEnum.巡检计划 ? 'planType' : 'subType'] === item.value) {
                        return {
                          ...pre,
                          [patrolType === patrolTypeEnum.巡检计划 ? 'planType' : 'subType']: undefined,
                        };
                      } else {
                        return {
                          ...pre,
                          [patrolType === patrolTypeEnum.巡检计划 ? 'planType' : 'subType']: item.value,
                        };
                      }
                    });
                  }}
                >
                  {item.label}
                </button>
              );
            })}
          </div>
        </div>
        {activity?.subType === CheckWayCNToEnEnum.诊断巡检 && (
          <div className="flex flex-col gap-3">
            <h3 className="text-[#141414] text-sm leading-[14px]">诊断类型</h3>
            <div className="flex flex-wrap gap-2">
              {[
                {
                  label: '倔强青铜',
                  value: ColorCNToEnEnum.倔强青铜,
                },
                {
                  label: '秩序白银',
                  value: ColorCNToEnEnum.秩序白银,
                },
                {
                  label: '尊贵铂金',
                  value: ColorCNToEnEnum.尊贵铂金,
                },
              ].map((item) => {
                return (
                  <button
                    key={item.value}
                    className={`px-4 h-[30px] rounded ${
                      activity?.alarmLevel === ColorCNToEnEnum[item.label as keyof typeof AlertColorEnum]
                        ? 'text-primary bg-primary/10'
                        : 'text-58 bg-black/[0.03]'
                    }  text-sm left-[14px]`}
                    onClick={() => {
                      setActivity((pre: any) => {
                        return {
                          ...pre,
                          alarmLevel: item.value,
                        };
                      });
                    }}
                  >
                    {item.label}
                  </button>
                );
              })}
            </div>
          </div>
        )}
        <AllUserSelect
          value={activity.patrolUserId}
          onChange={(val: number) => {
            setActivity((pre: any) => ({
              ...pre,
              // patrolUserIds: val,
              patrolUserId: val,
            }));
          }}
        />
      </div>
    </FilterDrawer>
  );
};
