import { useState } from 'react';
import { PageContext } from '@src/common/page-context';
import { CustomerFilter, DateFilter, IconFont } from '@src/components';
import { useGroupTree } from '@src/hooks';
import DetailCard from '@src/pages/patrol/planDetail/detailCard';
import { getUserId } from '@src/utils/tokenUtils';
import { formatDateToUTC } from '@src/utils/utils';
import { Card } from 'antd-mobile';
import classNames from 'classnames';
import dayjs from 'dayjs';
import { isUndefined, omitBy } from 'lodash';
import { useSearchParams } from 'react-router-dom';
import { UnderPatrolFilter } from './UnderPatrolFilter';
import { getUnderTaskPlanList } from '../api';
import { InfiniteScrollBuilder } from '../components/InfiniteScrollBuilder';
import { CheckWayCNToEnEnum, TaskPermissionEnum } from '../enum';
export enum patrolTypeEnum {
  '巡检计划',
  '巡检任务',
}
// type TRequestType = {
//   endTime: string;
//   startTime: string;
//   groupId?: number;
//   pageNo: number;
//   pageSize: number;
//   sortBy?: string;
//   shopIdList?: string[];
//   subType?: string;
//   hasCross?: boolean;
//   patrolUserId?: number;
// };
// const defaultParams: TRequestType = {
//   startTime: dayjs().subtract(1, 'months').format('YYYY-MM-DD'),
//   endTime: dayjs().format('YYYY-MM-DD'),
//   pageNo: 1,
//   pageSize: 50,
//   hasCross: false,
//   groupId: undefined,
// };
export default function YytUnderPatrol() {
  const userId = getUserId();
  console.log('123456userId', userId);
  const { pid } = useGroupTree();
  // const [searchParams, setSearchParams] = useState<TRequestType>({
  //   ...defaultParams,
  // });
  const [searchParams] = useSearchParams();
  const [timeParams, setTimeParams] = useState<{
    startTime?: string;
    endTime?: string;
  }>(() => {
    return {
      startTime: searchParams.get('startTime') ?? dayjs().subtract(1, 'months').format('YYYY-MM-DD'),
      endTime: searchParams.get('endTime') ?? dayjs().format('YYYY-MM-DD'),
    };
  });
  const [shopTree, setShopTree] = useState<{
    groupId?: string;
    shopIdList?: string[];
  }>(() => {
    return {
      groupId: searchParams.get('groupId') ?? undefined,
      shopIdList: searchParams.getAll('shopIdList').map((i) => i) ?? undefined,
    };
  });

  const [hasCross, setHasCross] = useState<boolean>(false);
  const [filterParam, setFilterParam] = useState<{
    status?: string;
    subType?: string;
    patrolUserId?: string;
    alarmLevel?: string;
  }>({
    status: searchParams.get('taskStatus') ?? undefined,
    subType: searchParams.get('subType') ?? undefined,
    patrolUserId: searchParams.get('patrolUserId') ?? undefined,
    alarmLevel: searchParams.get('alarmLevel') ?? undefined,
  });
  const [filterVisible, setFilterVisible] = useState(false);
  return (
    <PageContext.Provider value={{ pid }}>
      <div className="flex flex-col grow h-0 ">
        <div className="shrink-0">
          <Card className="rounded-none border-b border-solid border-line border-px mb-2 " bodyClassName="py-2">
            <div className="flex justify-between items-start">
              <CustomerFilter
                needAll
                noBg={true}
                groupMaxWidth="3.625rem"
                value={{
                  groupId: shopTree?.groupId?.toString(),
                  shopCodes: shopTree?.shopIdList || [],
                }}
                onChange={(e) => {
                  // setSearchParams((pre: any) => ({
                  //   ...pre,
                  //   groupId: !e.groupId ? undefined : +e.groupId,
                  //   shopIdList: e.shopCodes,
                  // }));
                  setShopTree({
                    groupId: !e.groupId ? undefined : e.groupId,
                    shopIdList: e.shopCodes,
                  });
                }}
              />

              <DateFilter
                noBg={true}
                shouldDisableDate={() => {
                  return false;
                }}
                // value={[
                //   dayjs(searchParams.startTime).toDate(),
                //   dayjs(searchParams.endTime).toDate(),
                // ]}
                value={
                  timeParams.endTime || timeParams.startTime
                    ? [dayjs(timeParams.startTime).toDate(), dayjs(timeParams.endTime).toDate()]
                    : undefined
                }
                // onChange={(e) => {
                //   setSearchParams((p) => ({
                //     ...p,
                //     startTime: dayjs(e[0]).format('YYYY-MM-DD'),
                //     endTime: dayjs(e[1]).format('YYYY-MM-DD'),
                //   }));
                // }}
                onChange={(e) => {
                  setTimeParams(() => {
                    if (e) {
                      return {
                        startTime: dayjs(e[0]).startOf('day').format('YYYY-MM-DD HH:mm:ss'),
                        endTime: dayjs(e[1]).endOf('day').format('YYYY-MM-DD HH:mm:ss'),
                      };
                    }

                    return {
                      startTime: undefined,
                      endTime: undefined,
                    };
                  });
                }}
                key="month"
                type="date"
              />
              <button
                className={classNames(
                  `text-sm flex items-center leading-[14px] flex-shrink-0 ${
                    filterVisible ? 'text-[#141414]' : 'text-[#5E5E5E]'
                  } focus:outline-none`,
                )}
                onClick={() => {
                  setFilterVisible(true);
                }}
              >
                <div className="w-[2px] h-5 bg-black/[0.03] mr-[18px]" />
                筛选
                <IconFont type="icon-a-1111-copy" className="ml-1 text-xs text-[#B8B8B8]" />
              </button>
            </div>
          </Card>
        </div>
        <div className={`grow ${filterVisible ? 'overflow-y-hidden' : 'overflow-y-scroll'} px-[10px] relative`}>
          <div className="flex justify-end py-3">
            <div className="flex text-xs leading-3">
              {[
                { label: '权限内', value: 0 },
                { label: '权限外', value: 1 },
              ].map((o) => {
                return (
                  <button
                    onClick={() => {
                      setHasCross(!!o.value);
                    }}
                    key={o.value}
                    className={`focus:outline-none px-[9px] py-[6px] ${
                      hasCross !== !!o.value
                        ? 'border border-[#F0F0F0] text-85'
                        : 'bg-primary/10 border border-primary text-primary'
                    } ${o.value === TaskPermissionEnum['权限内'] ? 'rounded-l-[4px]' : 'rounded-r-[4px]'}`}
                  >
                    {o.label}
                  </button>
                );
              })}
            </div>
          </div>
          <div className="flex flex-col gap-[10px]">
            <InfiniteScrollBuilder
              key="PrincipalPatrolTaskCard"
              // searchParams={{
              //   ...searchParams,
              //   startTime: formatDateToUTC(
              //     dayjs(searchParams.startTime).startOf('day'),
              //   ),
              //   endTime: formatDateToUTC(
              //     dayjs(searchParams.endTime).endOf('day'),
              //   ),
              // }}
              searchParams={omitBy(
                {
                  ...searchParams,
                  startTime: formatDateToUTC(dayjs(timeParams.startTime).startOf('day')),
                  endTime: formatDateToUTC(dayjs(timeParams.endTime).endOf('day')),
                  pageSize: 10,
                  shopIdList: Array.isArray(shopTree.shopIdList) ? shopTree.shopIdList : undefined,
                  groupId: shopTree.groupId,
                  hasCross,
                  taskStatus: filterParam.status,
                  subType: filterParam.subType,
                  patrolUserId: filterParam.patrolUserId ? filterParam.patrolUserId : undefined,
                  alarmLevel: filterParam.subType === CheckWayCNToEnEnum.诊断巡检 ? filterParam?.alarmLevel : undefined,
                },
                isUndefined,
              )}
              api={getUnderTaskPlanList}
              renderChildren={(data) => {
                return data.map((o: any) => {
                  const realo = {
                    ...o,
                    businessStatus: o.status,
                    createByName: o.userName,
                    createTime: o.taskCreateTime,
                    shopPhone: o.phoneNumber,
                  };
                  return (
                    <DetailCard key={realo?.taskId} initial={realo} isSelf={realo.userId === userId} isFormTaskCenter />
                  );
                });
              }}
            />
            {/* )} */}
          </div>
        </div>
        {/* 筛选框 */}
        <UnderPatrolFilter
          value={filterParam}
          open={filterVisible}
          onClose={() => setFilterVisible(false)}
          onChange={(value: Record<string, any>) => {
            console.log('vvvvv', value);
            setFilterParam((pre: any) => ({
              ...pre,
              status: value.status,
              subType: value.subType,
              patrolUserId: value.patrolUserId,
              alarmLevel: value?.alarmLevel,
            }));
            setFilterVisible(false);
          }}
          patrolType={1}
        />
      </div>
    </PageContext.Provider>
  );
}
