import { DrawerProps } from 'antd';
// import { FilterDrawer } from './FilterDrawer';
import { useState } from 'react';
import { FilterDrawer } from '../components/FilterDrawer';
import { cyclePlanType, yytTaskStatusEnum } from '../enum';
// import { cyclePlanType, yytTaskStatusEnum } from '../../enum';
// import { selfTypeEnum } from '../under_self';
// import { SelfTaskNameSelect } from './SelfTaskNameSelect';
export enum selfTypeEnum {
  '自检计划',
  '自检任务',
}
interface UnderSelfFilterProps extends DrawerProps {

  value: Record<string, any>;
  onChange: (value: Record<string, any>) => void;
  onClose: () => void;
  selfType: selfTypeEnum;
}
// export enum yytTaskStatusEnum {
//   待开始 = 1,
//   进行中,
//   已完成,
//   已过期,
//   已取消,
// }
export enum selftaskStatusEnum {
  // 待开始 = 'CREATE',
  进行中 = 'IN_PROGRESS',
  已完成 = 'COMPLETED',
  已结束 = 'FINISHED',
  // 'CREATE' = 1,//;带开始
  // 'IN_PROGRESS',//进行中
  // 'COMPLETED',//已完成
  // 'OVERDUE',//已过期
  // 'EXPIRED',//逾期进行中
}
export const init = {
  0: {
    status: undefined,
    selfPlanType: undefined,
    repeatType: undefined,
    planIds: [],
  },
  1: {
    status: undefined,
    selfPlanType: undefined,
    repeatType: undefined,
    planIds: [],
  },
};

export const UnderSelfFilter = ({
  value,
  onChange,
  selfType,
  onClose,
  ...props
}: UnderSelfFilterProps) => {
  const [activity, setActivity] = useState<any>(value);
  const onOk = () => {
    onChange(activity);
  };
  const onClear = () => {
    setActivity(init[selfType]);
  };
  return (
    <FilterDrawer
      {...props}
      onClose={() => {
        // 重置一下activity
        setActivity(value);
        onClose();
      }}
      onOk={onOk}
      onClear={onClear}
    >
      <div className="flex flex-col gap-6">
        <div className="flex flex-col gap-3 mt-20">
          <h3 className="text-[#141414] text-sm leading-[14px]">自检状态</h3>
          {/* {selfType === selfTypeEnum.自检计划 && (
            <div className="flex flex-wrap gap-2">
              {[
                { label: '待开始', value: yytTaskStatusEnum.待开始 },
                { label: '进行中', value: yytTaskStatusEnum.进行中 },
                { label: '已过期', value: yytTaskStatusEnum.已过期 },
                { label: '已取消', value: yytTaskStatusEnum.已取消 },
              ].map((item) => {
                return (
                  <button
                    key={item.value}
                    className={`w-20 h-[30px] rounded ${activity?.status ===
                      yytTaskStatusEnum[item.label as keyof typeof yytTaskStatusEnum]
                      ? 'text-primary bg-primary/10'
                      : 'text-58 bg-black/[0.03]'
                      }  text-sm left-[14px]`}
                    onClick={() => {
                      setActivity((pre: any) => {
                        if (activity?.status === item.value) {
                          return {
                            ...pre,
                            status: undefined,
                          };
                        } else {
                          return {
                            ...pre,
                            status: item.value,
                          };
                        }
                      });
                    }}
                  >
                    {item.label === '已过期' ? '已结束' : item.label}
                  </button>
                );
              })}
            </div>
          )} */}
          {selfType === selfTypeEnum.自检任务 && (
            <div className="flex flex-wrap gap-2">
              {[
                // { label: '待开始', value: selftaskStatusEnum.待开始 },
                { label: '进行中', value: selftaskStatusEnum.进行中 },
                // { label: '逾期进行中', value: selftaskStatusEnum.逾期进行中 },
                { label: '已结束', value: selftaskStatusEnum.已结束 },
                { label: '已完成', value: selftaskStatusEnum.已完成 },

              ].map((item) => {
                return (
                  <button
                    key={item.value}
                    className={`w-20 h-[30px] rounded ${activity?.status ===
                      selftaskStatusEnum[item.label as keyof typeof selftaskStatusEnum]
                      ? 'text-primary bg-primary/10'
                      : 'text-58 bg-black/[0.03]'
                      }  text-sm left-[14px]`}
                    onClick={() => {
                      setActivity((pre: any) => {
                        if (activity?.status === item.value) {
                          return {
                            ...pre,
                            status: undefined,
                          };
                        } else {
                          return {
                            ...pre,
                            status: item.value,
                          };
                        }
                      });
                    }}
                  >
                    {/* {item.label === '已过期' ? '已结束' : item.label} */}
                    {item.label}


                  </button>
                );
              })}
            </div>
          )}
        </div>
        {/* <div className="flex flex-col gap-3">
          <h3 className="text-[#141414] text-sm leading-[14px]">自检类型</h3>
          <div className="flex flex-wrap gap-2">
            {[
              { label: '单次', value: selfCheckingEnum.单次 },
              { label: '循环', value: selfCheckingEnum.循环 },
            ].map((item) => {
              return (
                <button
                  key={item.value}
                  className={`w-20 h-[30px] rounded ${
                    activity?.selfPlanType ===
                    selfCheckingEnum[item.label as keyof typeof selfCheckingEnum]
                      ? 'text-primary bg-primary/10'
                      : 'text-58 bg-black/[0.03]'
                  }  text-sm left-[14px]`}
                  onClick={() => {
                    setActivity((pre: any) => {
                      if (activity?.selfPlanType === item.value) {
                        return {
                          ...pre,
                          selfPlanType: undefined,
                        };
                      } else {
                        return {
                          ...pre,
                          selfPlanType: item.value,
                        };
                      }
                    });
                  }}
                >
                  {item.label}
                </button>
              );
            })}
          </div>
        </div> */}
        {selfType === selfTypeEnum.自检计划 && (
          <div className="flex flex-col gap-3">
            <h3 className="text-[#141414] text-sm leading-[14px]">循环方式</h3>
            <div className="flex flex-wrap gap-2">
              {[
                { label: '日循环', value: cyclePlanType.日循环 },
                { label: '周循环', value: cyclePlanType.周循环 },
                { label: '月循环', value: cyclePlanType.月循环 },
              ].map((item) => {
                return (
                  <button
                    key={item.value}
                    className={`w-20 h-[30px] rounded ${activity?.repeatType ===
                      cyclePlanType[item.label as keyof typeof cyclePlanType]
                      ? 'text-primary bg-primary/10'
                      : 'text-58 bg-black/[0.03]'
                      }  text-sm left-[14px]`}
                    onClick={() => {
                      setActivity((pre: any) => {
                        if (activity?.repeatType === item.value) {
                          return {
                            ...pre,
                            repeatType: undefined,
                          };
                        } else {
                          return {
                            ...pre,
                            repeatType: item.value,
                          };
                        }
                      });
                    }}
                  >
                    {item.label}
                  </button>
                );
              })}
            </div>
          </div>
        )}
        {/* <SelfTaskNameSelect
          value={activity.planIds?.[0]}
          onChange={(val: number) => {
            setActivity((pre: any) => ({
              ...pre,
              planIds: [val],
              selfPlanType: selfType === selfTypeEnum.自检计划 ? 'CIRCULATION' : 'ONCE',
            }));
          }}
          selfPlanType={selfType === selfTypeEnum.自检计划 ? 'CIRCULATION' : 'ONCE'}
        /> */}
      </div>
    </FilterDrawer>
  );
};
