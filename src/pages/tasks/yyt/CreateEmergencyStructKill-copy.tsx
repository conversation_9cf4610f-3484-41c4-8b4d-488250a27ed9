import { useContext, useState } from 'react';
import emptyImage from '@src/assets/images/task-empty.png';
import { SortEnum } from '@src/common/api.type';
import { DateFilter, IconFont, Loading } from '@src/components';
import { useScrollFetch } from '@src/hooks';
import { roleTypeIsManage } from '@src/utils/tokenUtils';
import { navigator } from '@tastien/rn-bridge/lib/h5';
import { useRequest } from 'ahooks';
import { InfiniteScroll } from 'antd-mobile';
import { DownOutline, RightOutline } from 'antd-mobile-icons';
import cn from 'classnames';
import dayjs from 'dayjs';
import { flatten, isUndefined, omitBy } from 'lodash';
import { observer } from 'mobx-react';
import qs from 'qs';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { getTaskListPage, getTaskTypeList } from '../api';
import NorTaskStatusTag from '../components/NorTaskStatusTag';
import PendingTaskPopup from '../components/PendingTaskPopup';
import ProgressPopup from '../components/ProgressPopup';
import { RoutineTaskFilter } from '../components/RoutineTaskFilter';
import TaskStatusFilter from '../components/TaskStatusFilter';
import TaskTypeFilter from '../components/TaskTypeFilter';
import { TaskStatusEnum, taskStatusOptions, TaskTypeEnum, WorkTypeEnum } from '../enum';
import { TasksContext } from '../layout';
import { getDateStatusNode } from '../utils';

function YytCreateEmergencyStructKill() {
  const navigate = useNavigate();

  const { queryType, refreshCount, waiting } = useContext(TasksContext);
  const [searchParams, setSearchParams] = useSearchParams();

  const [taskStatusFilterOpen, setTaskStatusFilterOpen] = useState(false);
  const [taskTypeFilterOpen, setTaskTypeFilterOpen] = useState(false);
  const [, setSortFilterOpen] = useState(false);

  const [pendingTaskPopupVisible, setPendingTaskPopupVisible] = useState(false);
  const [filterVisible, setFilterVisible] = useState(false);
  const [searchFilter, setSearchFilter] = useState(() => {
    return {
      taskCreatorIds: searchParams.getAll('taskCreatorIds')?.map((i) => Number(i)),
      taskPrincipalIds: searchParams.getAll('taskPrincipalIds')?.map((i) => Number(i)),
      taskInfoId: searchParams.get('taskInfoId'),
      sortBy: searchParams.get('sortBy') || SortEnum.DESC,
    };
  });

  const [timeParams, setTimeParams] = useState<{
    createTimeStart?: string;
    createTimeEnd?: string;
  }>(() => {
    return {
      createTimeStart: searchParams.get('createTimeStart') ?? undefined,
      createTimeEnd: searchParams.get('createTimeEnd') ?? undefined,
    };
  });

  const [taskStatus, setTaskStatus] = useState<TaskStatusEnum>(() => {
    const query = searchParams.get('taskStatus');

    return (query ? Number(query) : TaskStatusEnum.All) as TaskStatusEnum;
  });
  const [taskTypeIds, setTaskTypeIds] = useState<number[]>(() =>
    searchParams.getAll('taskTypeIds').map((i) => Number(i)),
  );
  // const [sortBy, setSortBy] = useState<SortEnum>(
  //   () => (searchParams.get('sortBy') || SortEnum.DESC) as SortEnum,
  // );

  const [progressPopupStatus, setProgressPopupStatus] = useState<{
    visible: boolean;
    progressMin: number;
    taskUserId: number;
    scheduleId?: number;
    isFinish: boolean;
    isEdit?: boolean;
    finishAttachRequired: number;
    updateAttachRequired: number;
  }>({
    visible: false,
    progressMin: 0,
    taskUserId: 0,
    isFinish: false,
    isEdit: false,
    finishAttachRequired: 0,
    updateAttachRequired: 0,
  });
  const { data: taskTypeList, loading: getTaskTypeListLoading } = useRequest(() => getTaskTypeList());

  const {
    list,
    fetchNext,
    over,
    reload: refreshList,
    loading,
  } = useScrollFetch(
    ({ pageNo, pageSize }) => {
      const params = {
        pageNum: pageNo,
        pageSize,
        queryType,
        taskStatus,
        taskTypeIds,
        ...searchFilter,
        taskCreatorIds: [TaskTypeEnum.Principal, TaskTypeEnum.Under].includes(queryType)
          ? searchFilter?.taskCreatorIds
          : undefined,
        taskPrincipalIds: [TaskTypeEnum.Created, TaskTypeEnum.Recipients, TaskTypeEnum.Under].includes(queryType)
          ? searchFilter?.taskPrincipalIds
          : undefined,
        ...timeParams,
      };

      setSearchParams(
        omitBy(
          {
            ...params,
            ...searchFilter,
            workType: WorkTypeEnum.EmergencyStructKill,
            taskInfoId: searchFilter.taskInfoId || [],
          },
          isUndefined,
        ),
        { replace: true },
      );

      return getTaskListPage(params);
    },
    [queryType, taskStatus, taskTypeIds, searchFilter, timeParams],
    {
      pageSize: 20,
      postResult: (res) => {
        return res?.data;
      },
      computeOver: (res) => {
        return res?.data.length === 0;
      },
    },
  );

  return (
    <Loading spinning={loading || getTaskTypeListLoading}>
      <div className="grow flex flex-col overflow-hidden">
        <div className="bg-white px-4 pt-2 flex justify-between items-center" id="filterDrawerContainer">
          <div className="flex gap-2">
            <button
              className={`flex gap-1 text-sm leading-[14px] p-2 rounded ${
                taskStatusFilterOpen ? 'items-start rounded-b-none' : 'mb-2'
              } ${taskStatus === TaskStatusEnum.All ? 'bg-[#F7F7F7] text-grey' : 'bg-primary/[0.08] text-primary'}`}
              onClick={() => {
                setTaskStatusFilterOpen(true);
                setTaskTypeFilterOpen(false);
                setSortFilterOpen(false);
                setFilterVisible(false);
              }}
            >
              {
                taskStatusOptions
                  .concat({ label: '待确认', value: TaskStatusEnum.Waiting })
                  .find((i) => i.value === taskStatus)?.label
              }
              <DownOutline className="text-xs" />
            </button>
            <button
              className={`flex gap-1 text-sm leading-[14px] p-2 rounded ${
                taskTypeFilterOpen ? 'items-start rounded-b-none' : 'mb-2'
              } ${taskTypeIds.length > 0 ? 'bg-primary/[0.08] text-primary' : 'bg-[#F7F7F7] text-grey'}`}
              onClick={() => {
                setTaskTypeFilterOpen(true);
                setTaskStatusFilterOpen(false);
                setSortFilterOpen(false);
                setFilterVisible(false);
              }}
            >
              <span className="truncate max-w-[110px]">
                {taskTypeIds.length > 0
                  ? taskTypeIds.map((type) => taskTypeList?.find((i) => i.id === type)?.taskType).join('、')
                  : '任务类型'}
              </span>
              <DownOutline className="text-xs" />
            </button>
          </div>
          <DateFilter
            value={
              timeParams.createTimeEnd || timeParams.createTimeStart
                ? [dayjs(timeParams.createTimeStart).toDate(), dayjs(timeParams.createTimeEnd).toDate()]
                : undefined
            }
            onChange={(e) => {
              setTimeParams(() => {
                if (e) {
                  return {
                    createTimeStart: dayjs(e[0]).startOf('day').format('YYYY-MM-DD HH:mm:ss'),
                    createTimeEnd: dayjs(e[1]).endOf('day').format('YYYY-MM-DD HH:mm:ss'),
                  };
                }

                return {
                  createTimeStart: undefined,
                  createTimeEnd: undefined,
                };
              });
            }}
            noDefault
            className="mb-2"
          />
          {/* <button
            className={`flex text-sm leading-[14px] mb-2 ${
              sortFilterOpen ? 'text-primary' : 'text-[#858585]'
            }`}
            onClick={() => {
              setSortFilterOpen(true);
              setTaskStatusFilterOpen(false);
              setTaskTypeFilterOpen(false);
              setFilterVisible(false);
            }}
          >
            排序
            <IconFont type="icon-sort" className="ml-[2px] text-[#B8B8B8]" />
          </button> */}
          <button
            className={cn(
              `flex text-sm leading-[14px] mb-2 ${
                filterVisible ? 'text-primary' : 'text-[#858585]'
              } focus:outline-none`,
              {
                'text-primary':
                  searchFilter.taskCreatorIds?.length > 0 ||
                  searchFilter.taskPrincipalIds?.length > 0 ||
                  !!searchFilter.taskInfoId,
              },
            )}
            onClick={() => {
              setFilterVisible(true);
              setSortFilterOpen(false);
              setTaskStatusFilterOpen(false);
              setTaskTypeFilterOpen(false);
            }}
          >
            筛选
            <IconFont type="icon-a-1111-copy" className="ml-1 text-xs text-[#B8B8B8]" />
          </button>
        </div>
        <div className="flex flex-col flex-1 overflow-y-auto relative" id="topBar">
          <div className="px-3 py-2 flex-1 overflow-y-auto relative">
            {!!queryType && list && flatten(list).length > 0 ? (
              <div>
                {flatten(list).map((item) => {
                  const dateNode = (
                    <span className="text-xs leading-3">
                      {dayjs(item.taskDeadline).format('YYYY年MM月DD号')}截止
                      {![TaskStatusEnum.Rejected, TaskStatusEnum.Terminated].includes(item.taskStatus) &&
                        getDateStatusNode(item.taskStatus, item.taskDeadline, item.taskUpdateTime)}
                    </span>
                  );
                  return (
                    <div
                      key={item.taskUserId}
                      className="bg-white rounded [&:not(:first-child)]:mt-[10px]"
                      onClick={() => {
                        if (roleTypeIsManage()) {
                          navigate(
                            `/tasks/detail/${item.taskUserId}${qs.stringify(
                              // 我管辖的任务需要带参进入(需要判断任务是否需要验收，因为产品说只有我管辖下的待验收任务才需要验收和驳回)
                              queryType === TaskTypeEnum.Under
                                ? {
                                    needCheck: 1,
                                  }
                                : {},
                              { addQueryPrefix: true },
                            )}`,
                          );
                        } else {
                          navigator.push({
                            pathname: 'WebView',
                            message: {
                              url: `/tasks/detail/${item.taskUserId}${qs.stringify(
                                // 我管辖的任务需要带参进入(需要判断任务是否需要验收，因为产品说只有我管辖下的待验收任务才需要验收和驳回)
                                queryType === TaskTypeEnum.Under
                                  ? {
                                      needCheck: 1,
                                    }
                                  : {},
                                { addQueryPrefix: true },
                              )}`,
                            },
                          });
                        }
                      }}
                    >
                      <div className="p-3 pb-0 flex">
                        <div className="flex-1">
                          <div className="ellipsis-2">
                            <NorTaskStatusTag status={item.taskStatus} className="mr-1" />
                            <span className="font-medium leading-[22px]">
                              【{item.taskType}】{item.taskTitle}
                            </span>
                          </div>
                          {item?.taskInfo && (
                            <span className="ellipsis-1 text-13 leading-none break-all text-[#858585] mt-1">
                              {item.taskInfo}
                            </span>
                          )}
                        </div>
                        <RightOutline className="text-[#c5c5c5] mt-1 text-sm" />
                      </div>
                      {queryType === TaskTypeEnum.Principal &&
                      [TaskStatusEnum.Delayed, TaskStatusEnum.InProgress].includes(item.taskStatus) ? (
                        <div className="p-3 pt-0 mt-2">
                          <div className="flex justify-between bg-[#f6f6f6] px-2 py-3 rounded text-light">
                            <span className="text-13 text-light leading-[13px]">
                              创建人：
                              <span className="text-grey">{item.taskCreatorName}</span>
                            </span>
                            {dateNode}
                          </div>
                          <div className="flex justify-end mt-3 gap-2">
                            <button
                              className="px-2 py-[7px] text-grey text-xs leading-3 border border-[#eeeeee] rounded"
                              onClick={(e) => {
                                e.stopPropagation();
                                setProgressPopupStatus({
                                  visible: true,
                                  progressMin: item?.scheduleValue,
                                  taskUserId: item.taskUserId,
                                  isEdit: false,
                                  isFinish: false,
                                  finishAttachRequired: item?.finishAttachRequired,
                                  updateAttachRequired: item?.updateAttachRequired,
                                });
                              }}
                            >
                              更新进度
                            </button>
                            <button
                              className="px-2 py-[7px] text-grey text-xs leading-3 border border-[#eeeeee] rounded"
                              onClick={(e) => {
                                e.stopPropagation();
                                setProgressPopupStatus({
                                  visible: true,
                                  progressMin: 100,
                                  taskUserId: item.taskUserId,
                                  isFinish: true,
                                  isEdit: false,
                                  finishAttachRequired: item?.finishAttachRequired,
                                  updateAttachRequired: item?.updateAttachRequired,
                                });
                              }}
                            >
                              完成任务
                            </button>
                          </div>
                        </div>
                      ) : (
                        <div className="flex justify-between border-t mt-3 border-[#f6f6f6] p-3 text-light">
                          <span className="text-13 leading-[13px]">
                            {[TaskTypeEnum.Principal, TaskTypeEnum.Under].includes(queryType) ? '创建人' : '处理人'}：
                            <span className="text-grey">
                              {[TaskTypeEnum.Principal, TaskTypeEnum.Under].includes(queryType)
                                ? item.taskCreatorName
                                : item.taskPrincipalName}
                            </span>
                          </span>
                          {dateNode}
                        </div>
                      )}
                      {TaskTypeEnum.Under === queryType && ( // 我管辖页
                        <div className="border-t px-3 py-2 text-13  border-[#f6f6f6] text-light">
                          <span className="leading-[13px]">处理人：</span>
                          <span className="text-grey">{item.taskPrincipalName}</span>
                        </div>
                      )}
                    </div>
                  );
                })}
                <InfiniteScroll loadMore={fetchNext} hasMore={!over} />
              </div>
            ) : (
              <div className="flex flex-col items-center mt-[70px]">
                <img src={emptyImage} className="w-[240px]" alt="" />
                <span className="text-13 text-[#9E9E9E] mt-5">暂无任务</span>
              </div>
            )}
          </div>
        </div>

        <TaskStatusFilter
          open={taskStatusFilterOpen}
          value={taskStatus}
          onClose={() => setTaskStatusFilterOpen(false)}
          onChange={(value) => {
            setTaskStatusFilterOpen(false);
            setTaskStatus(value);
          }}
          needWaitingOption={queryType !== TaskTypeEnum.Principal}
        />
        <TaskTypeFilter
          value={taskTypeIds}
          open={taskTypeFilterOpen}
          options={taskTypeList?.map((i) => ({
            label: i.taskType,
            value: i.id,
          }))}
          onClose={() => setTaskTypeFilterOpen(false)}
          onChange={(value) => {
            setTaskTypeFilterOpen(false);
            setTaskTypeIds(value);
          }}
        />
        {/* <SortFilter
          value={sortBy}
          open={sortFilterOpen}
          onClose={() => setSortFilterOpen(false)}
          onChange={(value) => {
            setSortBy(value);
            setSortFilterOpen(false);
          }}
        /> */}
        <RoutineTaskFilter
          value={searchFilter}
          queryType={queryType}
          open={filterVisible}
          onClose={() => setFilterVisible(false)}
          onChange={(value: Record<string, any>) => {
            setSearchFilter((pre: any) => ({ ...pre, ...value }));
            setFilterVisible(false);
          }}
        />
        <ProgressPopup
          visible={progressPopupStatus.visible}
          isFinish={progressPopupStatus.isFinish}
          progressMin={progressPopupStatus.progressMin}
          finishAttachRequired={progressPopupStatus.finishAttachRequired}
          updateAttachRequired={progressPopupStatus.updateAttachRequired}
          taskUserId={progressPopupStatus.taskUserId}
          scheduleId={progressPopupStatus.scheduleId}
          isEdit={progressPopupStatus.isEdit}
          onClose={() =>
            setProgressPopupStatus((prev) => ({
              ...prev,
              visible: false,
            }))
          }
          onSuccess={() => {
            refreshCount();
            refreshList();
          }}
        />
        <PendingTaskPopup
          visible={pendingTaskPopupVisible}
          onClose={() => setPendingTaskPopupVisible(false)}
          onUpdate={() => {
            refreshCount();
            refreshList();
          }}
        />
      </div>
    </Loading>
  );
}

export default observer(YytCreateEmergencyStructKill);
