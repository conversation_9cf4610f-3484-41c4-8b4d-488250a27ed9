import { FC, useState } from 'react';
import useQuerySearchParams from '@src/hooks/useQuerySearchParams';
import { useLocation, useNavigate } from 'react-router-dom';
import ChecklistSelect from './components/ChecklistSelect';
import { observer } from 'mobx-react';
import checklistSelectStore from '@src/store/checklistSelectStore';
import { stringify } from 'qs';
import { Loading } from '@src/components';
import { getCalculationDefaultWeight } from '../patrolTask/api';

const Select: FC = () => {
  const [params]: any = useQuerySearchParams();
  const navigate = useNavigate();
  const location = useLocation();
  const { checklists, fromUrl } = checklistSelectStore;

  const [loading, setLoading] = useState(false);

  return (
    <Loading spinning={loading}>
      <ChecklistSelect
        checklists={checklists}
        onSelect={async (value) => {
          const { name, index } = params;
          const { state } = location;
          const newSearchParams = { ...state };
          setLoading(true);

          const ids = (newSearchParams[name] || []).map((item: any) => {
            return +item.workSheet.value;
          });
          const data = await getCalculationDefaultWeight([...ids, value.value]);
          data.forEach((item: any) => {
            const index = (newSearchParams[name] || []).findIndex(
              (item2: any) => item2.workSheet.value == item.worksheetId,
            );

            if (index > -1) {
              newSearchParams[name][index].weight = item.weight.toString();
            }
          });
          if (name && state) {
            if (newSearchParams[name]) {
              if (index >= 0) {
                newSearchParams[name][index] = {
                  workSheet: value,
                  weight: newSearchParams[name][index].weight,
                };
              } else {
                newSearchParams[name].push({
                  workSheet: value,
                  weight:
                    newSearchParams[name].findIndex((i: any) => i.workSheet.value == value.value) >
                    -1
                      ? 0
                      : data.find((o: any) => o.worksheetId == value.value).weight.toString(),
                });
              }
            } else {
              newSearchParams[name] = [
                {
                  workSheet: value,
                  weight: data.find((o: any) => o.worksheetId == value.value).weight.toString(),
                },
              ];
            }
          }
          setLoading(false);
          navigate(`${fromUrl}?${stringify(newSearchParams)}`, { replace: true });
        }}
      ></ChecklistSelect>
    </Loading>
  );
};

export default observer(Select);
