import { Empty, Radio, SearchBar } from 'antd-mobile';
import { FC, useCallback, useMemo, useState } from 'react';
import './index.scss';
import { IconFont } from '@src/components';
import classNames from 'classnames';
import { IPopupPage } from '@src/components/IPopup';
import { IChecklistItem } from '@src/pages/tasks/unplannedTask/api';

enum ChecklistFilterType {
  Type = 'type',
  Name = 'name',
}

/* const ChecklistFilterTypeCN: Record<ChecklistFilterType, string> = {
  [ChecklistFilterType.Type]: '检查表类型',
  [ChecklistFilterType.Name]: '检查表名称',
}; */

interface ChecklistSelectProps {
  // onSubClose?: () => void;
  checklists?: IChecklistItem[];
  onSelect?: (value: any) => void;
}

let subChecklistsMap: any = {};
const ChecklistSelect: FC<ChecklistSelectProps> = ({ checklists = [], onSelect }) => {
  // 界面显示状态  0:初始 1:切换检查表查询条件
  // const [showStatus, setShowStatus] = useState<number>(0);
  const [subVisible, setSubVisible] = useState<boolean>(false);

  const [filterType, setFilterType] = useState<ChecklistFilterType>(ChecklistFilterType.Name);
  const [searchValue, setSearchValue] = useState<string | undefined>();
  const [subSearchValue, setSubSearchValue] = useState<string | undefined>();
  const [selectValue, setSelectValue] = useState<any>();

  const checklistsByName = useMemo(() => {
    return (
      checklists.map(({ id, sheetName }) => ({
        value: id,
        label: sheetName,
      })) || []
    );
  }, [checklists]);

  // 这个版本没有检查表类型
  /* const checklistsByType = useMemo(() => {
    const map: any = {};
    checklists.forEach((checklist) => {
      const { id, sheetName, typeName, typeCode } = checklist;
      if (!map[typeCode]) {
        map[typeCode] = { value: typeCode, label: typeName, children: [] };
      }
      map[typeCode]?.children.push({ value: id, label: sheetName });
    });
    subChecklistsMap = map;
    return Object.keys(map).map((key: string) => map[key]) || [];
  }, [checklists]); */

  const checklistByFilter = useMemo<typeof checklistsByName>(() => {
    return checklistsByName.filter(({ label }) => {
      if (!searchValue) {
        return true;
      } else {
        return label?.includes(searchValue);
      }
    });
  }, [searchValue, checklists]);

  const subChecklistsByFilter = useMemo<typeof checklistsByName>(() => {
    if (!!selectValue?.value?.toString()) {
      return subChecklistsMap[selectValue?.value]?.children?.filter(
        ({ label }: { label: string }) => {
          if (!subSearchValue) {
            return true;
          } else {
            return label?.includes(subSearchValue);
          }
        },
      );
    }
    return [];
  }, [subSearchValue, checklists, selectValue]);

  const onMainSelect = useCallback(
    ({ value, label }: { value: number; label: string }) => {
      if (filterType === ChecklistFilterType.Type) {
        setSubVisible(true);
        return setSelectValue({ value, label });
      } else {
        return onSelect?.({ value, label });
      }
    },
    [filterType],
  );

  return (
    <>
      <div className="checklist-select h-full bg-[#fff]">
        <div className="px-3 py-2 checklist-select-fixed">
          {/* <div className="rounded-lg bg-[#FAFAFA] p-2 flex items-center h-[40px]"> */}
          <div className="rounded-lg bg-[#FAFAFA] p-2 w-full h-[40px]">
            {/* <div
              className="flex items-center text-[#5E5E5E] border-r border-solid border-[#DCDCDC] shrink-0 text-sm leading-[22px]"
              onClick={() => {
                setShowStatus(Number(!showStatus));
              }}
            >
              {ChecklistFilterTypeCN[filterType]}
              <IconFont
                type="icon-chevron-down"
                className="text-[#5E5E5E] text-base ml-[2px] mt-[-1px] mr-3"
              />
            </div> */}
            <SearchBar
              placeholder={
                filterType === ChecklistFilterType.Name ? '请输入检查表名称' : '请输入检查表类型'
              }
              value={searchValue}
              onChange={(val) => {
                setSearchValue(val);
              }}
              style={{
                '--background': 'transparent',
                lineHeight: '22px',
                fontSize: '14px',
                '--height': '23px',
              }}
            />
          </div>
        </div>
        {/* <div
          className={classNames('checklist-select-popup-container', {
            active: showStatus === 1,
          })}
          onClick={() => {
            setShowStatus(0);
          }}
        >
          <div className="checklist-select-filter-type">
            <Radio.Group
              onChange={(val: any) => {
                setFilterType(val);
                setShowStatus(0);
              }}
              value={filterType}
            >
              <Radio value={ChecklistFilterType.Type}>
                {ChecklistFilterTypeCN[ChecklistFilterType.Type]}
              </Radio>
              <Radio value={ChecklistFilterType.Name}>
                {ChecklistFilterTypeCN[ChecklistFilterType.Name]}
              </Radio>
            </Radio.Group>
          </div>
        </div> */}
        <div className="pt-14">
          {checklistByFilter?.length > 0 ? (
            <ul className="bg-white">
              {checklistByFilter.map(({ value, label }, idx) => {
                return (
                  <li
                    key={idx}
                    className="flex justify-between px-4 py-3 border-b border-solid border-[#F0F0F0]"
                    onClick={() => onMainSelect({ value, label })}
                  >
                    <span className="text-[#141414] text-base w-[70%] whitespace-nowrap text-ellipsis overflow-hidden">
                      {label}
                    </span>
                    {filterType === ChecklistFilterType.Type && (
                      <IconFont type="icon-chevron-right" className="text-base text-[#B8B8B8]" />
                    )}
                  </li>
                );
              })}
            </ul>
          ) : (
            <Empty description="暂无数据" />
          )}
        </div>
      </div>
      <IPopupPage
        visible={subVisible}
        onClose={() => {
          setSubVisible(false);
        }}
      >
        <div>
          <div className="flex items-center text-[#858585] leading-[22px] px-4 pt-2">
            <span>选择检查表</span>
            <IconFont type="icon-chevron-right" className="text-base leading-[22px] mx-[2px]" />
            <span>{selectValue?.label}</span>
          </div>
          <div className="p-3">
            <SearchBar
              placeholder="请输入检查表名称"
              onChange={(val) => {
                setSubSearchValue(val);
              }}
              value={subSearchValue}
              className="text-[#141414]"
              style={{
                '--background': '#FAFAFA',
                '--height': '40px',
              }}
            />
          </div>
          {subChecklistsByFilter?.length > 0 ? (
            <ul>
              {subChecklistsByFilter.map(({ value, label }) => {
                return (
                  <li
                    key={value}
                    className="flex justify-between px-4 py-3 border-b border-solid border-[#F0F0F0] first:border-t"
                    onClick={() => {
                      onSelect?.({ value, label });
                    }}
                  >
                    <span className="text-[#141414] text-base w-[70%] whitespace-nowrap text-ellipsis overflow-hidden">
                      {label}
                    </span>
                  </li>
                );
              })}
            </ul>
          ) : (
            <Empty description="暂无数据" />
          )}
        </div>
      </IPopupPage>
    </>
  );
};

export default ChecklistSelect;
