.checklist-select {
  .adm-tabs-header {
    display: none;
  }

  .adm-tabs-content {
    padding: 0;
  }

  &-fixed {
    position: fixed;
    left: 0;
    z-index: 2;
    width: 100%;
    background-color: #fff;
  }
  &-popup-container {
    position: fixed;
    top: 56px;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    transform: translateY(-100%);

    z-index: 1;
    // transition: transform 0.3s;
    &.active {
      transform: translateY(0);
    }
  }

  &-filter-type {
    display: flex;
    flex-direction: column;
    background-color: #fff;

    .adm-radio {
      padding-left: 16px;
    }
    .adm-radio + .adm-radio {
      .adm-radio-content {
        border-top: 0.5px solid #f0f0f0;
      }
    }
    .adm-radio-content {
      flex-grow: 1;
      padding: 12px 0;
      margin-left: 8px;
      font-size: 14px;
      line-height: 24px;
      color: #5e5e5e;
    }
  }
}
