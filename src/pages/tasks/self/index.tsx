import { useState } from 'react';
import { IconFont } from '@src/components';
import { Tabs } from 'antd-mobile';
import cn from 'classnames';
import dayjs from 'dayjs';
import { has, keys } from 'lodash';
import { getUnderTaskPlanList, getUnderTaskSelfList } from '../api';
import CustomerFilter from '../components/CustomerFilterPatrol';
import { DateFilter } from '../components/DateFilter';
import { InfiniteScrollBuilder } from '../components/InfiniteScrollBuilder';
import SelfPatrolTaskCard from '../components/SelfPatrolTaskCard';
import { init, UnderPatrolFilter } from '../components/UnderPatrolFilter';
import { TaskPermissionEnum } from '../enum';
import styles from '../index.module.scss';
export enum patrolTypeEnum {
  '巡检计划',
  '巡检任务',
}

const patrolType = [
  {
    label: '巡检计划',
    value: patrolTypeEnum.巡检计划,
    initial: {
      pageNo: 1,
      pageSize: 5,
      fromStartTime: dayjs().startOf('day').subtract(1, 'month').toISOString(),
      toEndTime: dayjs().endOf('day').toISOString(),
      filterPermission: true,
    },
  },
  {
    label: '巡检任务',
    value: patrolTypeEnum.巡检任务,
    initial: {
      pageNo: 1,
      pageSize: 50,
      cross: 0,
      groupId: undefined,
      shopIds: [],
      filterPermission: true,
    },
  },
];

export default function YytUnderPatrol() {
  // const [activity, setActivity] = useState(patrolType[0].value);
  const [activity, setActivity] = useState(1);

  const [searchParams, setSearchParams] = useState<any>(patrolType[0].initial);
  const [filterVisible, setFilterVisible] = useState(false);
  const tabs = [
    {
      label: '日任务',
      // value: WorkTypeEnum.Self,
      value: 1,
    },
    {
      label: '周任务',
      value: 2,
    },
    {
      label: '月任务',
      value: 3,
    },
    {
      label: '整改任务',
      value: 4,
    },
  ];
  return (
    <div className="flex flex-col grow h-0 ">
      <div>
        <Tabs
          style={{
            '--title-font-size': '1rem',
          }}
          className={'bg-white'}
          // activeKey={queryType}
          onChange={(value) => {}}
        >
          {tabs.map((item, idx) => (
            <Tabs.Tab
              title={
                <div
                  className={cn('text-[#5E5E5E] flex items-center leading-4', {
                    'mr-[22px]': tabs.length === 4 && idx === tabs.length - 1,
                  })}
                >
                  {item.label}
                </div>
              }
              key={item.value}
            />
          ))}
        </Tabs>
      </div>
      <div className="shrink-0">
        <div
          className="bg-white py-2 flex justify-between px-4 border-b border-black/[0.03] relative z-[1000]"
          id="filterDrawerContainer"
        >
          <>
            <CustomerFilter
              value={{ groupId: searchParams.groupId, shopId: searchParams.shopIds }}
              onChange={(e) => {
                setSearchParams((pre: any) => ({
                  ...pre,
                  groupId: e.groupId,
                  shopIds: e.shopId,
                }));
              }}
              single
            />
            <DateFilter
              value={searchParams?.endDate ? dayjs(searchParams?.endDate).toDate() : null}
              onChange={(value: Record<string, any>) => {
                setSearchParams((pre: any) => ({ ...pre, ...value }));
              }}
              type="single"
            />
            <button
              className={cn(
                `text-sm flex items-center leading-[14px] ${
                  filterVisible ? 'text-[#141414]' : 'text-[#5E5E5E]'
                } focus:outline-none`,
                {
                  'text-primary': keys(init[1]).some((o) => !!searchParams?.[o] && has(searchParams, o)),
                },
              )}
              onClick={() => {
                setFilterVisible(true);
              }}
            >
              <div className="w-[1px] h-5 bg-black/[0.03] mr-[18px]" />
              筛选
              <IconFont type="icon-a-1111-copy" className="ml-1 text-xs text-[#B8B8B8]" />
            </button>
          </>
        </div>
      </div>
      <div className={`grow ${filterVisible ? 'overflow-y-hidden' : 'overflow-y-scroll'} px-[10px] relative`}>
        <div className="flex flex-col gap-[10px]">
          <InfiniteScrollBuilder
            key="PrincipalPatrolTaskCard"
            searchParams={searchParams}
            api={getUnderTaskSelfList}
            renderChildren={(data) => {
              const ttt = [
                {
                  taskId: 4566078,
                  taskName: '2/28 到店巡检(权限外)',
                  status: 3,
                  subType: 0,
                  cross: false,
                  taskDate: 1709049600000,
                  userId: 10336,
                  userName: '导入人员003',
                  phoneNumber: '17745788888',
                  simpleShop: {
                    id: 19012,
                    name: '福州米圈圈二店222',
                    type: 'DIRECT',
                    address: '河北省石家庄市桥西区108号华润万象城2层L214号',
                  },
                  shopNo: 'T1021013',
                  shopLog: 114.47896,
                  shopLat: 38.042071,
                  shopManagerId: 10336,
                  shopManagerName: '导入人员003',
                  reportDetail: {
                    reportId: 15952,
                    reportStatus: 3,
                    reportScore: 0,
                    passed: 0,
                    issueCount: 4,
                    noReformCount: 4,
                  },
                  signed: 0,
                },
                {
                  taskId: 4563961,
                  taskName: '2/21 到店巡检(权限内)',
                  status: 3,
                  subType: 0,
                  cross: false,
                  taskDate: 1708444800000,
                  userId: 10545,
                  userName: '陈雨晴员工9238',
                  phoneNumber: '13178389238',
                  simpleShop: {
                    id: 20757,
                    name: '孔晨1',
                    type: 'JOIN',
                    address: '福建省福州市鼓楼区鼓楼区',
                  },
                  shopNo: 'M353544661',
                  shopLog: 119.2152481079,
                  shopLat: 26.040512085,
                  shopManagerId: 10652,
                  shopManagerName: '测试123456',
                  reportDetail: {
                    reportId: 15652,
                    reportStatus: 3,
                    reportScore: 0,
                    passed: 0,
                    issueCount: 1,
                    noReformCount: 1,
                  },
                  signed: 0,
                },
                {
                  taskId: 4558761,
                  taskName: '1/31 到店巡检(权限外)',
                  status: 3,
                  subType: 0,
                  cross: false,
                  taskDate: 1706630400000,
                  userId: 10138,
                  userName: '测试作战小队',
                  phoneNumber: '17759082330',
                  simpleShop: {
                    id: 2544,
                    name: '庆元县松源街店',
                    type: 'DIRECT',
                    address: '浙江省庆元县汇金城152号1',
                  },
                  shopNo: 'T62',
                  shopLog: 119.064051,
                  shopLat: 27.617781,
                  shopManagerId: 10636,
                  shopManagerName: '朱有波',
                  reportDetail: {
                    reportId: 14776,
                    reportStatus: 3,
                    reportScore: 40.54,
                    passed: 0,
                    issueCount: 2,
                    noReformCount: 2,
                  },
                  signed: 0,
                },
                {
                  taskId: 4558393,
                  taskName: '1/30 到店巡检(权限内)',
                  status: 3,
                  subType: 0,
                  cross: false,
                  taskDate: 1706544000000,
                  userId: 10487,
                  userName: '陈雨晴改',
                  phoneNumber: '15750931781',
                  simpleShop: {
                    id: 19012,
                    name: '福州米圈圈二店222',
                    type: 'DIRECT',
                    address: '河北省石家庄市桥西区108号华润万象城2层L214号',
                  },
                  shopNo: 'T1021013',
                  shopLog: 114.47896,
                  shopLat: 38.042071,
                  shopManagerId: 10336,
                  shopManagerName: '导入人员003',
                  reportDetail: {
                    reportId: 14737,
                    reportStatus: 3,
                    reportScore: 100,
                    passed: 0,
                    issueCount: 0,
                    noReformCount: 0,
                  },
                  signed: 0,
                },
                {
                  taskId: 4558392,
                  taskName: '1/30 到店巡检(权限内)',
                  status: 3,
                  subType: 0,
                  cross: false,
                  taskDate: 1706544000000,
                  userId: 10487,
                  userName: '陈雨晴改',
                  phoneNumber: '15750931781',
                  simpleShop: {
                    id: 19012,
                    name: '福州米圈圈二店222',
                    type: 'DIRECT',
                    address: '河北省石家庄市桥西区108号华润万象城2层L214号',
                  },
                  shopNo: 'T1021013',
                  shopLog: 114.47896,
                  shopLat: 38.042071,
                  shopManagerId: 10336,
                  shopManagerName: '导入人员003',
                  reportDetail: {
                    reportId: 14736,
                    reportStatus: 3,
                    reportScore: 100,
                    passed: 0,
                    issueCount: 0,
                    noReformCount: 0,
                  },
                  signed: 0,
                },
              ];
              return ttt.map((o: any) => {
                o.cross = false;

                return <SelfPatrolTaskCard key={o.taskId} initial={o} />;
              });
            }}
          />
        </div>
      </div>
      {/* 筛选框 */}
      <UnderPatrolFilter
        value={searchParams}
        open={filterVisible}
        onClose={() => setFilterVisible(false)}
        onChange={(value: Record<string, any>) => {
          setSearchParams((pre: any) => ({ ...pre, ...value }));
          setFilterVisible(false);
        }}
        patrolType={activity}
      />
    </div>
  );
}
