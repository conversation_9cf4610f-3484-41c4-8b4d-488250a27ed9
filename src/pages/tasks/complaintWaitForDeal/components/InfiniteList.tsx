import emptyImage from '@src/assets/images/task-empty.png';
import { InfiniteScroll } from 'antd-mobile';

const EmptyNode = ({ placeholder = '暂无任务' }: { placeholder: string }) => {
  return (
    <div className="flex flex-col items-center mt-[70px]">
      <img src={emptyImage} className="w-[240px]" alt="" />
      <span className="text-13 text-[#9E9E9E] mt-5">{placeholder}</span>
    </div>
  );
};

interface InfiniteListProps<T> {
  dataSource: T[];
  hasNextPage: boolean;
  loadMore: (isRetry?: boolean) => Promise<void>;
  itemRender: (item: T, idx?: number) => React.ReactNode;
}

export const InfiniteList = <T,>({ dataSource = [], hasNextPage, loadMore, itemRender }: InfiniteListProps<T>) => {
  return (
    <div>
      {dataSource.length === 0 ? (
        <EmptyNode placeholder="暂无任务" />
      ) : (
        <div className="flex flex-col gap-2">
          {dataSource?.map(itemRender)}
          <InfiniteScroll loadMore={loadMore} hasMore={hasNextPage} />
        </div>
      )}
    </div>
  );
};
