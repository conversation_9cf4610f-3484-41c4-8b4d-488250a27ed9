import { useState } from 'react';
import VideoViewer from '@src/components/VideoViewer';
import type { FileDTO } from '@src/pages/reportDetail/self/api.type';
import { Image, ImageViewer } from 'antd-mobile';
import { PlayOutline } from 'antd-mobile-icons';

export const FilesPreview = ({ files }: { files: FileDTO[] }) => {
  const [videoPreviewUrl, setVideoPreviewUrl] = useState('');
  const [videoPreviewVisible, setVideoPreviewVisible] = useState(false);

  return (
    <>
      {files?.length ? (
        <div className="p-2">
          <div className="flex flex-wrap gap-2 empty:hidden">
            {files.map((item) => {
              const isVideo = item.contentType?.startsWith('video') || item.fileType === 'VIDEO';

              if (isVideo) {
                return (
                  <div
                    onClick={(e) => {
                      e.stopPropagation();
                      setVideoPreviewVisible(true);
                      setVideoPreviewUrl(item.url);
                    }}
                    className="relative"
                  >
                    <video
                      style={{ objectFit: 'cover' }}
                      poster={item?.snapshotUrl}
                      className="w-[70px] h-[70px] border"
                      src={item.url}
                    />
                    <div className="absolute left-[50%] top-[50%] -translate-x-[50%] -translate-y-[50%]">
                      <PlayOutline fontSize={40} />
                    </div>
                  </div>
                );
              }

              return (
                <Image
                  onClick={(e) => {
                    e.stopPropagation();
                    ImageViewer.show({ image: item?.url });
                  }}
                  width="70px"
                  height="70px"
                  className="rounded-lg"
                  src={item?.snapshotUrl || item?.url}
                />
              );
            })}
          </div>
          <VideoViewer
            visible={videoPreviewVisible}
            url={videoPreviewUrl}
            onClose={() => {
              setVideoPreviewVisible(false);
            }}
          />
        </div>
      ) : null}
    </>
  );
};

export interface DescAndFilesCardProps {
  desc?: React.ReactNode;
  files?: FileDTO[];
}

export const DescAndFilesCard = ({ desc, files = [] }: DescAndFilesCardProps) => {
  return desc || files?.length ? (
    <div className="bg-[#eee] rounded-lg flex flex-col">
      <div className="px-2 leading-8">{desc}</div>
      <FilesPreview files={files} />
    </div>
  ) : null;
};
