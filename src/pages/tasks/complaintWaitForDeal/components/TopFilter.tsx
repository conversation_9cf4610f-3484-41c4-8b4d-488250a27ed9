import { DateFilter } from '@src/components';
import { ShopTree } from '@src/components/ShopTree';
import { TaskTypeEnum } from '@src/pages/reportDetail/self/enum';
import type { AppealStatus } from '@src/pages/selfCheckComment/api.type';
import dayjs from 'dayjs';
import { TypeFilter } from './TypeFilter';

export type TopFilterParams = {
  startTime: Date;
  endTime: Date;
  groupId: number | undefined;
  shopIds: string[] | undefined;
  statuses: AppealStatus[];
  // taskType: 'SELF' | 'PATROL' | undefined;
  taskType?: TaskTypeEnum.自检第二点评 | TaskTypeEnum.巡检;
};

interface TopFilterProps {
  value: TopFilterParams;
  onChange: (value: TopFilterParams) => void;
}

export const TopFilter = ({ value, onChange }: TopFilterProps) => {
  return (
    <div className="flex justify-items-center items-center px-3 bg-white">
      <DateFilter
        noBg={true}
        value={[value.startTime, value.endTime]}
        onChange={(e: any) => {
          onChange({
            ...value,
            startTime: dayjs(e[0]).toDate(),
            endTime: dayjs(e[1]).toDate(),
          });
        }}
        key="month"
        type="date"
      />
      <ShopTree
        isInPermission
        onSelectChange={(e) => {
          onChange({ ...value, groupId: e.groupId, shopIds: e.shopId });
        }}
        hasEllipsis
      />
      {/* <TaskTypeFilter value={value.taskType} onChange={(e) => onChange({ ...value, taskType: e })} /> */}
      <TypeFilter value={value.statuses} onChange={(e) => onChange({ ...value, statuses: e })} />
    </div>
  );
};
