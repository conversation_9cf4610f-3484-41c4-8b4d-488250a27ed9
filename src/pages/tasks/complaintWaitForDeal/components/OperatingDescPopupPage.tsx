import { useEffect, useState } from 'react';
import { IPopupPage } from '@src/components/IPopup';
import {
  maintainAppeal,
  maintainAppealForPatrol,
  maintainAppealTactics,
  passAppeal,
  passAppealForPatrol,
  passAppealTactics,
} from '@src/pages/reportDetail/self/api';
import type { CorpAppTaskReportAppealInfoDTO } from '@src/pages/reportDetail/self/api.type';
import { TaskTypeEnum } from '@src/pages/reportDetail/self/enum';
import { header } from '@tastien/rn-bridge/lib/h5';
import { useRequest } from 'ahooks';
import { message } from 'antd';
import { Button, TextArea } from 'antd-mobile';

interface OperatingDescPopupPageProps {
  visible: boolean;
  onClose: () => void;
  record?: CorpAppTaskReportAppealInfoDTO;
  isMaintain: boolean;
  /** 是否为策略 */
  isTactics?: boolean;
  onSuccessCallback?: () => void;
}

export const OperatingDescPopupPage = ({
  visible,
  onClose,
  record,
  isMaintain,
  isTactics,
  onSuccessCallback,
}: OperatingDescPopupPageProps) => {
  const [value, setValue] = useState('');
  const { run, loading } = useRequest(
    async () => {
      if ([TaskTypeEnum.自检, TaskTypeEnum.自检第二点评].includes(record?.taskType!)) {
        if (isMaintain) {
          if (isTactics) {
            return await maintainAppealTactics({
              taskId: record?.taskId!,
              baseTaskId: record?.baseTaskId,
              taskItemId: record?.taskItemId,
              remark: value,
            });
          }
          return await maintainAppeal({
            taskId: record?.taskId!,
            taskWorksheetItemId: record?.taskWorksheetItemId!,
            remark: value,
            appealType: record?.taskType ?? '',
          });
        } else {
          if (isTactics) {
            return await passAppealTactics({
              taskId: record?.taskId!,
              baseTaskId: record?.baseTaskId,
              taskItemId: record?.taskItemId,
              remark: value,
            });
          }
          return await passAppeal({
            taskId: record?.taskId!,
            taskWorksheetItemId: record?.taskWorksheetItemId!,
            remark: value,
            appealType: record?.taskType ?? '',
          });
        }
      } else {
        if (isMaintain) {
          return await maintainAppealForPatrol({
            taskId: record?.taskId!,
            taskWorksheetItemId: record?.taskWorksheetItemId!,
            remark: value,
          });
        } else {
          return await passAppealForPatrol({
            taskId: record?.taskId!,
            taskWorksheetItemId: record?.taskWorksheetItemId!,
            remark: value,
          });
        }
      }
    },
    {
      manual: true,
      onSuccess: () => {
        message.success('操作成功');
        setValue('');
        onClose();
        onSuccessCallback?.();
      },
    },
  );

  useEffect(() => {
    if (visible) {
      setTimeout(() => {
        header.setNavbar({ title: isMaintain ? '维持原点评结果' : '判断为合格' });
      }, 300);
    }
  }, [isMaintain, visible]);
  return (
    <IPopupPage
      DocumentTitle={isMaintain ? '维持原点评结果' : '判断为合格'}
      visible={visible}
      onClose={() => {
        setValue('');
        onClose();
      }}
      footer={
        <div className="p-3 bg-white">
          <Button
            onClick={async () => {
              if (value.trim()) {
                run();
              } else {
                message.warning('请输入具体理由');
              }
            }}
            color="primary"
            fill="solid"
            block
            className="h-[45px] text-base"
            loading={loading}
          >
            确定
          </Button>
        </div>
      }
      destroyOnClose
    >
      <div className="w-full h-full bg-[#F5F5F5]">
        <div className="p-3 bg-white">
          <h3 className="text-xl text-[#141414] font-medium">详细说明</h3>
          <TextArea
            placeholder={`请说明${isMaintain ? '维持原点评人结果' : '判定为合格'}的具体理由，最多100字`}
            maxLength={100}
            rows={3}
            className="resize-none mt-3"
            value={value}
            onChange={(value) => setValue(value)}
          />
        </div>
      </div>
    </IPopupPage>
  );
};
