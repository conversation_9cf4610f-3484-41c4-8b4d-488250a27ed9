import { useState } from 'react';
import { IconFont } from '@src/components';
import { AppealStatus } from '@src/pages/selfCheckComment/api.type';
import { Drawer } from 'antd';
import { CheckOutline } from 'antd-mobile-icons';
import cn from 'classnames';
import styles from './index.module.scss';

const TaskStatusOptions = [
  { label: '全部任务', value: [] },
  { label: '待审核', value: [AppealStatus.发起, AppealStatus.审核超时转派] },
  { label: '已审核', value: [AppealStatus.通过] },
  {
    label: '已驳回',
    value: [AppealStatus.驳回],
  },
  { label: '已撤回', value: [AppealStatus.撤回] },
  { label: '已过期', value: [AppealStatus.已过期] },
  { label: '已作废', value: [AppealStatus.已作废] },
];

interface TypeFilterProps {
  value?: AppealStatus[];
  onChange?: (value: AppealStatus[]) => void;
}

export const TypeFilter = ({ value, onChange }: TypeFilterProps) => {
  const [open, setOpen] = useState(false);
  return (
    <>
      <button
        className={cn(
          'flex gap-y-1 text-sm leading-[14px] p-2 rounded',
          !value?.length ? 'bg-[#F7F7F7] text-grey' : 'bg-primary/[0.08] text-primary',
        )}
        onClick={() => setOpen((pre) => !pre)}
      >
        {TaskStatusOptions.find((i) => JSON.stringify(i.value) === JSON.stringify(value))?.label || '全部任务'}
        <IconFont
          type="icon-chevron-down"
          className={cn('organization-dropdown-select-arrow', {
            'organization-dropdown-select-arrow-unfold': open,
          })}
        />
      </button>
      <Drawer
        rootClassName={styles.drawer}
        placement="top"
        getContainer={() => document.getElementById('topBar')!}
        closable={false}
        height="auto"
        open={open}
        onClose={() => setOpen(false)}
      >
        <div className="divide-y divide-[rgba(0,0,0,0.03)] px-4 bg-[#F7F7F7] text-sm leading-[14px] text-[#5E5E5E]">
          {TaskStatusOptions.map((i) => {
            const checked = JSON.stringify(value) === JSON.stringify(i.value);
            return (
              <div
                key={i.label}
                className={`flex justify-between py-4 ${checked ? 'text-primary' : ''}`}
                onClick={() => {
                  onChange?.(i.value);
                  setOpen(false);
                }}
              >
                {i.label}
                {checked && <CheckOutline className="text-base" />}
              </div>
            );
          })}
        </div>
      </Drawer>
    </>
  );
};
