import { useState } from 'react';
import { TaskTypeEnum } from '@src/pages/reportDetail/self/enum';
import { Drawer } from 'antd';
import { CheckOutline } from 'antd-mobile-icons';
import cn from 'classnames';
import styles from './index.module.scss';

const TaskStatusOptions = [
  { label: '全部任务', value: undefined },
  // { label: '自检', value: 'SELF' as const },
  { label: '自检', value: TaskTypeEnum.自检第二点评 },
  { label: '巡检', value: TaskTypeEnum.巡检 },
];

interface TaskTypeFilterProps {
  value?: 'SELF_SECOND_REVIEW' | 'PATROL' | undefined;
  // onChange?: (value: 'SELF' | 'PATROL' | undefined) => void;
  onChange?: (value?: TaskTypeEnum.自检第二点评 | TaskTypeEnum.巡检) => void;
}

export const TaskTypeFilter = ({ value, onChange }: TaskTypeFilterProps) => {
  const [open, setOpen] = useState(false);
  return (
    <>
      <button
        className={cn(
          'flex gap-1 text-sm leading-[14px] p-2 rounded',
          !value?.length ? 'bg-[#F7F7F7] text-grey' : 'bg-primary/[0.08] text-primary',
        )}
        onClick={() => setOpen(true)}
      >
        {TaskStatusOptions.find((i) => JSON.stringify(i.value) === JSON.stringify(value))?.label || '全部任务'}
      </button>
      <Drawer
        rootClassName={styles.drawer}
        placement="top"
        getContainer={() => document.getElementById('topBar')!}
        closable={false}
        height="auto"
        open={open}
        onClose={() => setOpen(false)}
      >
        <div className="divide-y divide-[rgba(0,0,0,0.03)] px-4 bg-[#F7F7F7] text-sm leading-[14px] text-[#5E5E5E]">
          {TaskStatusOptions.map((i) => {
            const checked = value === i.value;
            return (
              <div
                key={i.label}
                className={`flex justify-between py-4 ${checked ? 'text-primary' : ''}`}
                onClick={() => {
                  onChange?.(i.value as TaskTypeEnum.自检第二点评 | TaskTypeEnum.巡检);
                  setOpen(false);
                }}
              >
                {i.label}
                {checked && <CheckOutline className="text-base" />}
              </div>
            );
          })}
        </div>
      </Drawer>
    </>
  );
};
