import { useMemo, useState } from 'react';
import SopPopup from '@src/components/OptionsDetail/SopPopup';
import {
  type CorpAppTaskReportAppealInfoDTO,
  OperationType,
  OperationTypeText,
} from '@src/pages/reportDetail/self/api.type';
import { NodeTypeEnum, TaskTypeEnum } from '@src/pages/reportDetail/self/enum';
import { AppealStatus } from '@src/pages/selfCheckComment/api.type';
import { Button } from 'antd';
import dayjs from 'dayjs';
import { AttributeTag } from './AttributeTag';
import { DescAndFilesCard } from './DescAndFilesCard';
import { OperatingDescPopupPage } from './OperatingDescPopupPage';
import { OperationRecord } from './OperationRecord';

interface ComplaintCardProps {
  data: CorpAppTaskReportAppealInfoDTO;
  refetch?: () => void;
}

const complaintCardStatusMap = {
  [AppealStatus.发起]: <span className="text-blue-500">待审核</span>,
  [AppealStatus.审核超时转派]: <span className="text-blue-500">待审核</span>,
  [AppealStatus.驳回]: <span className="text-red-500">已驳回</span>,
  [AppealStatus.通过]: <span className="text-green-500">已处理</span>,
  [AppealStatus.撤回]: <span className="text-gray-500">已撤回</span>,
  [AppealStatus.未申诉]: <span className="text-gray-500">未申诉</span>,
  [AppealStatus.已过期]: <span className="text-gray-500">已过期</span>,
};

export const ComplaintCard = ({ data, refetch }: ComplaintCardProps) => {
  const [operatingDescPopupVisible, setOperatingDescPopupVisible] = useState(false);
  const [operatingDesc, setOperatingDesc] = useState<CorpAppTaskReportAppealInfoDTO>();
  const [operatingType, setOperatingType] = useState<'maintain' | 'pass'>();
  const [sopPopup, setSopPopup] = useState<{
    visible: boolean;
    sopId?: number;
  }>({
    visible: false,
  });

  // 描述数据来源
  const descDataSource = data?.taskType === 'PATROL' ? data?.patrolTaskWorksheetItem : data?.selfTaskWorksheetItem;

  const renderCommentDescBuilder = (noReason?: boolean) => {
    return (
      <>
        {data?.taskType === 'PATROL' && !noReason && <span className="empty:hidden">{descDataSource?.itemRemark}</span>}

        {!noReason && (
          <div className="flex gap-x-2 flex-wrap empty:hidden">
            {(descDataSource?.otherReason
              ? [
                  ...(descDataSource?.reportReviewAbnormalReasons ||
                    descDataSource?.selectedNonconformityReasons ||
                    []),
                  descDataSource?.otherReason,
                ]
              : descDataSource?.reportReviewAbnormalReasons || descDataSource?.selectedNonconformityReasons
            )?.map((r: any) => {
              return (
                <span key={r} className="text-blue-400">
                  #{r}
                </span>
              );
            })}
          </div>
        )}
      </>
    );
  };

  const operationRecord = useMemo(() => {
    if (!Array.isArray(data?.process) || !data?.process?.length) {
      return [];
    }

    return (
      data.process
        ?.filter(
          (v, idx) =>
            !(data?.taskType === TaskTypeEnum.巡检 && v?.operationType === OperationType.提交点评 && idx === 0) &&
            // 二审不能看到一审的结果
            !(
              data?.nodeType === NodeTypeEnum.二审 &&
              [AppealStatus.发起, AppealStatus.审核超时转派].includes(data?.status!) &&
              [OperationType.通过, OperationType.超时未审核].includes(v?.operationType!)
            ),
        )
        ?.map((v) => ({
          operationRecordUser: v?.operatorUserName!,
          operationRecordText: `${
            [
              OperationType.超时未审核,
              OperationType.第二审核超时未审核,
              OperationType.审核超时,
              OperationType.第二审核超时,
            ].includes(v?.operationType!)
              ? ''
              : v?.operatorUserName
          } ${OperationTypeText[v?.operationType!] ?? ''}`,
          operationRecordTime: dayjs(v?.createTime).format('MM-DD HH:mm'),
          descAndFiles: {
            desc: [OperationType.超时未审核, OperationType.第二审核超时未审核].includes(v?.operationType!) ? null : (
              <>
                <span className="text-sm empty:hidden break-all">{v?.remark}</span>
                {renderCommentDescBuilder(v?.operationType !== 'REVIEW')}
              </>
            ),
            files: v?.attachmentFiles?.map((f) => ({
              fileType: f?.fileType,
              id: f?.id,
              snapshotUrl: f?.snapshotUrl,
              url: f?.url,
            })),
          },
        })) || []
    );
  }, [data?.nodeType, data.process, data?.status, data?.taskType]);

  return (
    <>
      <div className="flex flex-col gap-3 rounded-lg bg-white px-4 py-3">
        <div className="flex items-center justify-between">
          <span className="text-lg">截止时间 {dayjs(data.deadlineTime).format('MM-DD HH:mm')}</span>
          <span>{complaintCardStatusMap[data.status as AppealStatus]}</span>
        </div>

        <div className="text-md font-medium">{data?.taskName}</div>

        <div className="flex flex-col gap-1">
          <span className="text-lg">
            {data?.shopId}: {data?.shopName}
          </span>
          <div className="flex items-center justify-between">
            <span className="text-xs">
              {data?.worksheetName} / {data?.worksheetCategoryName}
            </span>
            {!!data?.sopId && (
              <span
                className="text-sm text-primary"
                onClick={() => {
                  setSopPopup({
                    visible: true,
                    sopId: data.sopId,
                  });
                }}
              >
                参考标准
              </span>
            )}
          </div>
        </div>

        <div className="flex flex-col gap-2">
          <AttributeTag tags={data?.accentedTermTags || []} />
          <span className="text-base">{data?.worksheetItemContent}</span>
          <DescAndFilesCard
            desc={
              data?.taskType === 'PATROL' ? (
                // 巡检
                <>{renderCommentDescBuilder()}</>
              ) : (
                // 自检
                <>
                  <span className="empty:hidden">{descDataSource?.shopCheckContent?.text}</span>
                </>
              )
            }
            files={(data?.taskType === 'PATROL'
              ? descDataSource?.imageList
              : descDataSource?.shopCheckContent?.images
            )?.map((i: any) => {
              return {
                fileType: 'IMG' as any,
                id: i?.id,
                snapshotUrl: i?.snapshotUrl,
                url: i?.url,
              };
            })}
          />
          <OperationRecord record={operationRecord} />
        </div>

        {[AppealStatus.发起, AppealStatus.审核超时转派].includes(data?.status!) && data?.hasPermissionAudit && (
          <div className="flex gap-8">
            <Button
              block
              className="h-[40px] text-base"
              onClick={() => {
                setOperatingDesc(data);
                setOperatingDescPopupVisible(true);
                setOperatingType('maintain');
              }}
            >
              维持原结果
            </Button>
            <Button
              block
              className="h-[40px] text-base"
              onClick={() => {
                setOperatingDesc(data);
                setOperatingDescPopupVisible(true);
                setOperatingType('pass');
              }}
            >
              判断为合格
            </Button>
          </div>
        )}
      </div>
      <OperatingDescPopupPage
        visible={operatingDescPopupVisible}
        onClose={() => setOperatingDescPopupVisible(false)}
        record={operatingDesc}
        isMaintain={operatingType === 'maintain'}
        onSuccessCallback={() => {
          refetch?.();
        }}
      />
      <SopPopup
        sopId={sopPopup.sopId}
        title="参考标准"
        visible={sopPopup.visible}
        onClose={() => {
          setSopPopup({
            visible: false,
            sopId: undefined,
          });
        }}
      />
    </>
  );
};
