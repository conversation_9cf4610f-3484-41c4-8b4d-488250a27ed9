import { useState } from 'react';
import UserAvatar from '@src/components/UserAvatar';
import { DownOutline, UpOutline } from 'antd-mobile-icons';
import { DescAndFilesCard, type DescAndFilesCardProps } from './DescAndFilesCard';

interface OperationRecordProps {
  record: {
    operationRecordUser: string;
    operationRecordText?: string;
    operationRecordTime?: React.ReactNode;
    descAndFiles?: DescAndFilesCardProps;
  }[];
  isAlwaysShowMore?: boolean;
}

export const OperationRecord = ({ record = [], isAlwaysShowMore = false }: OperationRecordProps) => {
  const [showMore, setShowMore] = useState(isAlwaysShowMore);

  return (
    <div className="flex flex-col gap-4">
      {record.slice(0, showMore ? record.length : 2).map((v, idx) => {
        return (
          <div key={idx} className="flex flex-col gap-2">
            <div className="flex justify-between items-start gap-x-4">
              <div className="flex items-center gap-x-2">
                <UserAvatar username={v?.operationRecordUser} />
                <div className="ml-2 text-[#5E5E5E] text-xs">{v?.operationRecordText}</div>
              </div>
              <span className="text-[#858585] text-xs shrink-0">{v?.operationRecordTime}</span>
            </div>
            <div className="pl-8">
              <DescAndFilesCard
                desc={v?.descAndFiles?.desc}
                files={v?.descAndFiles?.files?.map((f) => ({
                  fileType: f?.fileType,
                  id: f?.id,
                  snapshotUrl: f?.snapshotUrl,
                  url: f?.url,
                }))}
              />
            </div>
          </div>
        );
      })}
      {!isAlwaysShowMore && record.length > 2 && (
        <div className="flex flex-row items-center justify-center">
          {!showMore ? (
            <div className="flex items-center gap-x-1" onClick={() => setShowMore(true)}>
              展开
              <DownOutline className="text-xs ml-1 text-[#858585]" />
            </div>
          ) : (
            <div className="flex items-center gap-x-1" onClick={() => setShowMore(false)}>
              收起
              <UpOutline className="text-xs ml-1 text-[#858585]" />
            </div>
          )}
        </div>
      )}
    </div>
  );
};
