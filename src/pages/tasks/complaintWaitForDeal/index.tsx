import { useMemo, useState } from 'react';
import type { ClientError } from '@src/api/react-query';
import { Loading } from '@src/components';
import useQuerySearchParams from '@src/hooks/useQuerySearchParams';
import { getAppealList } from '@src/pages/reportDetail/self/api';
import type { CorpAppTaskReportAppealInfoDTO } from '@src/pages/reportDetail/self/api.type';
import { TaskTypeEnum } from '@src/pages/reportDetail/self/enum';
import { AppealStatus } from '@src/pages/selfCheckComment/api.type';
import { formatDateToUTC } from '@src/utils/utils';
import { type InfiniteData, type QueryKey, useInfiniteQuery } from '@tanstack/react-query';
import dayjs from 'dayjs';
import { ComplaintCard } from './components/ComplaintCard';
import ComplaintTacticsCard from './components/ComplaintTacticsCard';
import { InfiniteList } from './components/InfiniteList';
import { TopFilter, type TopFilterParams } from './components/TopFilter';

export default function ComplaintWaitForDealIndex() {
  const [params] = useQuerySearchParams();

  const isTactics = params?.isTactics === 'true';

  const [filterParams, setFilterParams] = useState<TopFilterParams>({
    startTime: dayjs().subtract(1, 'months').toDate(),
    endTime: dayjs().toDate(),
    groupId: undefined,
    shopIds: undefined,
    statuses: params?.statuses || [AppealStatus.发起, AppealStatus.审核超时转派],
    taskType: TaskTypeEnum.自检第二点评,
  });

  const { data, isLoading, fetchNextPage, hasNextPage, refetch } = useInfiniteQuery<
    PageRes<CorpAppTaskReportAppealInfoDTO>,
    ClientError,
    InfiniteData<PageRes<CorpAppTaskReportAppealInfoDTO>, number>,
    QueryKey,
    number
  >({
    queryKey: ['complaintWaitForDeal', filterParams],
    queryFn: async ({ pageParam = 1 }) =>
      await getAppealList(
        {
          ...filterParams,
          pageNo: pageParam,
          pageSize: 5,
          beginTime: formatDateToUTC(dayjs(filterParams?.startTime).startOf('day').format('YYYY-MM-DD HH:mm:ss')),
          endTime: formatDateToUTC(dayjs(filterParams?.endTime).endOf('day').format('YYYY-MM-DD HH:mm:ss')),
          // 待审核状态只看待我处理
          onlyMePending: [AppealStatus.发起, AppealStatus.审核超时转派].every((e) =>
            filterParams?.statuses.includes(e),
          ),
        },
        isTactics,
      ),
    initialPageParam: 1,
    getNextPageParam: (lastPage, allPages) => {
      const lastLength = lastPage?.data?.length || 0;
      const hasNext = lastLength !== 0;

      if (hasNext) {
        return allPages.length + 1;
      }

      return;
    },
    enabled: filterParams?.groupId !== undefined,
  });

  const dataSource = useMemo(() => {
    if (!data?.pages?.length) {
      return [];
    }

    return data.pages.flatMap((item) => item?.data || []) || [];
  }, [data]);

  const ItemComponet = isTactics ? ComplaintTacticsCard : ComplaintCard;

  return (
    <Loading spinning={isLoading}>
      <div className="flex flex-col grow h-0 bg-[#F7F7F7]">
        <TopFilter value={filterParams} onChange={setFilterParams} />
        <div className="flex flex-col flex-1 overflow-y-auto relative px-3 py-2 " id="topBar">
          <div className="flex-1 overflow-y-auto relative">
            <InfiniteList
              dataSource={dataSource}
              hasNextPage={hasNextPage}
              loadMore={fetchNextPage as any}
              itemRender={(item) => <ItemComponet key={item.id} data={item} refetch={refetch} />}
            />
          </div>
        </div>
      </div>
    </Loading>
  );
}
