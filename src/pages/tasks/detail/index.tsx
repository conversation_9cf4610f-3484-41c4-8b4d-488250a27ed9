import { useState } from 'react';
import { IconFont, Loading } from '@src/components';
import { CustomerFormFieldWrapper } from '@src/components/CustomerFormFieldWrapper';
import { IPopup } from '@src/components/IPopup';
import PdfViewer from '@src/components/PdfViewer';
import <PERSON>Viewer from '@src/components/VideoViewer';
import { roleTypeIsManage } from '@src/utils/tokenUtils';
import { navigator } from '@tastien/rn-bridge/lib/h5';
import { useRequest } from 'ahooks';
import { Button, Calendar, Dialog, Image, ImageViewer, Picker, Steps, Tabs, Toast } from 'antd-mobile';
import classNames from 'classnames';
import dayjs from 'dayjs';
import { useParams, useSearchParams } from 'react-router-dom';
import {
  acceptanceTask,
  getTaskDetail,
  modifyTaskStatus,
  queryShopInvestRate,
  setTaskDeadline,
  updateInvestRate,
} from '../api';
import { taskFileList } from '../api.type';
import InvestPop from '../components/InvestPop';
import NorTaskStatusTag from '../components/NorTaskStatusTag';
import ProgressPopup from '../components/ProgressPopup';
import TextareaPopup from '../components/TextareaPopup';
import { TaskOperatorEnum, TaskStatusEnum } from '../enum';
import styles from '../index.module.scss';
import { getDateStatusNode, taskOperatorHandler } from '../utils';

const { Step } = Steps;

enum taskStepsTypeEnum {
  任务进度 = 'taskScheduleList',
  操作记录 = 'taskOperationLogList',
}

export default function DetailIndex() {
  const { taskUserId } = useParams();
  const [search] = useSearchParams();
  const needCheck = search.get('needCheck');

  const [endDatePopupStatus, setEndDatePopupStatus] = useState({
    visible: false,
    date: dayjs().toDate(),
  });
  const [timePickerVisible, setTimePickerVisible] = useState(false);
  const [progressPopupStatus, setProgressPopupStatus] = useState<{
    visible: boolean;
    progressMin: number;
    taskUserId: number;
    scheduleId?: number;
    isFinish: boolean;
    isEdit?: boolean;
    finishAttachRequired: number;
    updateAttachRequired: number;
    scheduleMark?: string;
    scheduleFileList?: any[];
  }>({
    visible: false,
    progressMin: 0,
    taskUserId: 0,
    isFinish: false,
    isEdit: false,
    finishAttachRequired: 0,
    updateAttachRequired: 0,
  });
  const [pdfPreviewUrl, setPdfPreviewUrl] = useState('');
  const [pdfPreviewVisible, setPdfPreviewVisible] = useState(false);
  const [videoPreviewUrl, setVideoPreviewUrl] = useState('');
  const [videoPreviewVisible, setVideoPreviewVisible] = useState(false);

  const [cancelTaskPopupVisible, setCancelTaskPopupVisible] = useState(false);
  const [rejectPopupVisible, setRejectPopupVisible] = useState(false);
  const [rebutPopupVisible, setRebutPopupVisible] = useState(false);
  const [investPopupVisible, setInvestPopupVisible] = useState(false);

  const [taskStepsType, setTaskStepsType] = useState(taskStepsTypeEnum['任务进度']);

  const { data, loading, refresh } = useRequest(() => getTaskDetail(taskUserId as string));

  const taskOperatorArr = taskOperatorHandler(data?.taskStatus, data?.taskCreator, data?.taskPrincipal);
  const { runAsync, loading: modifyLoading } = useRequest(modifyTaskStatus, {
    manual: true,
  });
  const { runAsync: acceptanceTaskReq, loading: acceptanceTaskLoading } = useRequest(acceptanceTask, { manual: true });

  const { data: investDetails } = useRequest(() => queryShopInvestRate(taskUserId));
  const { runAsync: investTaskRate } = useRequest(updateInvestRate, { manual: true });

  const handleTerminated = async (remark: string) => {
    await runAsync({
      remark,
      taskStatus: TaskStatusEnum.Terminated,
      taskUserId: +taskUserId!,
    });
    Toast.show('终止成功');
    setCancelTaskPopupVisible(false);
    refresh();
  };
  const handleReject = async (remark: string, status?: number) => {
    await runAsync({
      remark,
      taskStatus: status || TaskStatusEnum.Rejected,
      taskUserId: +taskUserId!,
    });
    Toast.show('拒绝成功');
    setRejectPopupVisible(false);
    refresh();
    // RN刷新通信
    navigator.goBack();
  };
  const handleRebut = async (remark: string) => {
    await acceptanceTaskReq({
      remark,
      taskStatus: 6,
      taskUserId: +taskUserId!,
    });
    Toast.show('驳回成功');
    setRebutPopupVisible(false);
    refresh();
  };

  const { run, loading: setTaskDeadlineLoading } = useRequest(
    (taskUserId: string, taskDeadline: string) => setTaskDeadline(taskUserId, taskDeadline),
    {
      manual: true,
      onSuccess: () => {
        setEndDatePopupStatus((pre) => ({ ...pre, visible: false }));
        Toast.show({
          icon: 'success',
          content: '编辑成功',
          duration: 1000,
          afterClose() {
            refresh();
          },
        });
      },
    },
  );

  // 更新投流费用
  const handleInvest = async (actualCost: number) => {
    if (!actualCost || !/^\d+$/.test(actualCost?.toString())) {
      Toast.show('请填写整数实际推广费用');
      return false;
    }

    await investTaskRate({
      actualCost: Number(actualCost) ?? 0,
      id: +taskUserId!,
    });
    Toast.show('确认投流成功');
    setInvestPopupVisible(false);
  };

  const taskDeadline = dayjs(data?.taskDeadline).toDate();
  const task = {
    taskDeadline,
  };

  const relatedPersonnelBuilder = (type: '创建人' | '处理人') => {
    const fieldMapping: Record<string, 'taskCreator' | 'taskPrincipal'> = {
      创建人: 'taskCreator',
      处理人: 'taskPrincipal',
    };

    return (
      <div className="flex items-center">
        {type === '创建人' ? (
          <IconFont type="icon-tianjiayonghu" className={classNames('text-grey-2 mr-1')} />
        ) : (
          <IconFont type="icon-yonghu" className={classNames('text-grey-2 mr-1')} />
        )}

        <span className="text-[#9E9E9E] shrink-0">{type}:</span>
        <div className="px-1 py-[.125rem] rounded-[2px] bg-primary/20 text-xs leading text-primary mx-1 max-w-[80px] truncate">
          {data?.[fieldMapping[type]]?.roleItems.map((item) => item.roleName).join('、')}
        </div>
        <span className="mr-2 shrink-0">{data?.[fieldMapping[type]]?.nickName}</span>
        <span>{data?.[fieldMapping[type]]?.phone}</span>
        <IconFont
          type="icon-rongqi"
          className={classNames('text-primary icon ml-[6px] -rotate-90')}
          onClick={() => {
            window.location.href = `tel:${data?.[fieldMapping[type]]?.phone}`;
          }}
        />
      </div>
    );
  };

  const handleReceive = async (taskUserId: number) => {
    await runAsync({ taskStatus: TaskStatusEnum.InProgress, taskUserId });
    Toast.show('接受成功');
    refresh();
    setTimeout(() => {
      // RN刷新通信
      navigator.goBack();
    }, 500);
  };
  // 拒绝投流
  const rejectInvest = () => {
    Dialog.confirm({
      content: <div className="text-center text-[#141414] text-base">确认不参与品牌公司统投计划</div>,
      onConfirm: async () => {
        await handleReject('确认不参与品牌公司统投计划', TaskStatusEnum.Rejected);
      },
    });
  };

  return (
    <Loading spinning={loading || modifyLoading || acceptanceTaskLoading || setTaskDeadlineLoading}>
      {!!data && (
        <div className="h-full flex flex-col gap-y-2">
          <div className="flex-grow gap-y-2 flex flex-col overflow-y-scroll">
            <div className="bg-white text-sm leading-[14px] p-4 text-grey">
              <h3 className="text-base leading-[22px] font-medium text-dark">
                {data?.tripartiteTaskType !== 'FLOW' && <NorTaskStatusTag status={data?.taskStatus} className="mr-2" />}
                {data?.taskTitle}
              </h3>
              <h4
                className={`mt-2 mb-2 leading-[22px] break-all ${['SHORT_BALANCE_NOTICE', 'FLOW'].includes(data?.tripartiteTaskType ?? '') ? '' : 'ellipsis-2'}`}
              >
                {data?.taskInfo}
              </h4>
              <span className="flex items-start">
                <div className="text-sm leading-[22px]">
                  <div className="float-left flex items-center">
                    <IconFont
                      type="icon-shijian"
                      className={classNames(
                        ` icon mr-1 ${
                          data?.taskStatus === TaskStatusEnum.Delayed ? 'text-[#FF7B29]' : 'text-[#C7C7C7]'
                        }`,
                      )}
                    />
                    任务截止时间:
                  </div>
                  <span
                    className={`${
                      dayjs().startOf('d').diff(data.taskDeadline, 'd') > 0 ? 'text-[#FF7B29]' : 'text-grey'
                    }`}
                  >
                    {dayjs(data.taskDeadline).format('YYYY年MM月DD号 HH:mm')}
                  </span>
                  {data?.taskStatus !== TaskStatusEnum.Rejected &&
                    data?.taskStatus !== TaskStatusEnum.Terminated &&
                    getDateStatusNode(data.taskStatus, data.taskDeadline, data.taskUpdateTime)}
                </div>
                {taskOperatorArr.some((o) => o === TaskOperatorEnum['修改任务时间']) && (
                  <div className="h-[22px] flex items-center">
                    <IconFont
                      type="icon-bianji"
                      className={classNames('text-primary icon ml-[6px]')}
                      onClick={() => {
                        setEndDatePopupStatus({
                          visible: true,
                          date: dayjs(data?.taskDeadline).toDate(),
                        });
                      }}
                    />
                  </div>
                )}
              </span>
              <div className="rounded-lg bg-[#F7F7F7] pt-4 pb-3 px-3 mt-3 divide-y divide-solid divide-[#E5E5E5]">
                <div className="flex flex-col gap-y-4 pb-[12px]">
                  {relatedPersonnelBuilder('创建人')}
                  {relatedPersonnelBuilder('处理人')}
                </div>
                <div className="py-2 flex flex-col gap-y-3">
                  <div className="relative leading-[22px]">
                    <div className="float-left flex items-center">
                      <IconFont type="icon-wenti" className={classNames('text-grey-2')} />
                      <span className="mx-1 text-[#9E9E9E]">任务类型:</span>
                    </div>
                    {data?.taskType?.taskType || <span className="mx-1 text-[#9E9E9E]">暂无</span>}
                  </div>
                  <div className="relative leading-[22px] text-primary">
                    <div className="float-left flex items-center ">
                      <IconFont type="icon-xinxi" className={classNames('text-grey-2')} />
                      <span className="mx-1 text-[#9E9E9E]">SOP:</span>
                    </div>
                    {(data?.sopInfoList || []).length === 0 ? (
                      <span className="mx-1 text-[#9E9E9E]">暂无</span>
                    ) : (
                      (data?.sopInfoList || []).map((item, idx) => {
                        return (
                          <span
                            onClick={() => {
                              if ((item.fileName || '').endsWith('.pdf')) {
                                setPdfPreviewVisible(true);
                                setPdfPreviewUrl(item.fileUrl!);
                              } else if ((item.fileName || '').endsWith('.mp4')) {
                                setVideoPreviewVisible(true);
                                setVideoPreviewUrl(item.fileUrl || '');
                              } else {
                                // 图片
                                ImageViewer.show({ image: item.fileUrl! });
                              }
                            }}
                            key={item.id}
                          >
                            {idx > 0 && '、'}
                            {idx + 1}
                            {'.'}
                            {item.name}
                          </span>
                        );
                      })
                    )}
                  </div>
                  <div className="relative leading-[22px] text-primary break-all [&:nth-child(1)]:after:content-['aaaaaaaaaaaa'] ">
                    <div className="float-left flex items-center">
                      <IconFont type="icon-wenjianjia" className={classNames('text-grey-2')} />
                      <span className="mx-1 text-[#9E9E9E]">附件:</span>
                    </div>
                    {(data?.taskFileList || []).length === 0 ? (
                      <span className="mx-1 text-[#9E9E9E]">暂无</span>
                    ) : (
                      (data?.taskFileList || []).map((item, idx) => {
                        return (
                          <span
                            onClick={() => {
                              if ((item?.fileName || '').endsWith('.pdf')) {
                                setPdfPreviewVisible(true);
                                setPdfPreviewUrl(item.fileUrl!);
                              } else {
                                // 图片
                                ImageViewer.show({ image: item.fileUrl! });
                              }
                            }}
                            key={item.id}
                          >
                            {idx > 0 && '、'}
                            {idx + 1}
                            {'.'}
                            {item.fileName}
                          </span>
                        );
                      })
                    )}
                  </div>
                </div>
                <div className="pt-2 flex flex-col gap-y-3">
                  <div className="relative leading-[22px]">
                    <div className="float-left flex items-center">
                      <IconFont type="icon-tixing" className={classNames('text-grey-2')} />
                      <span className="mx-1 text-[#9E9E9E]">任务到期提醒:</span>
                    </div>
                    {!!data?.taskExpRemind ? '是' : '否'}
                  </div>
                  <div className="relative leading-[22px]">
                    <div className="float-left flex items-center">
                      <IconFont type="icon-tixing" className={classNames('text-grey-2')} />
                      <span className="mx-1 text-[#9E9E9E]">任务验收设置:</span>
                    </div>
                    {!!data?.checkSwitch ? '开启' : '关闭'}
                  </div>
                  <div className="relative leading-[22px]">
                    <div className="float-left flex items-center">
                      <IconFont type="icon-settings" className={classNames('text-grey-2')} />
                      <span className="mx-1 text-[#9E9E9E]">更新进度附件上传设置:</span>
                    </div>
                    {!!data?.updateAttachRequired ? '必填' : '选填'}
                  </div>
                  <div className="relative leading-[22px]">
                    <div className="float-left flex items-center">
                      <IconFont type="icon-settings" className={classNames('text-grey-2')} />
                      <span className="mx-1 text-[#9E9E9E]">完成任务附件上传设置:</span>
                    </div>
                    {!!data?.finishAttachRequired ? '必填' : '选填'}
                  </div>
                  <div className="relative leading-[22px] text-[#9E9E9E]">
                    <div className="float-left flex items-center ">
                      <IconFont type="icon-chaosongwode-tianchong" className={classNames('text-grey-2')} />
                      <span className="mx-1">抄送人:</span>
                    </div>
                    {(data?.recipientsList || []).length === 0 ? (
                      <span className="mx-1 text-[#9E9E9E]">暂无</span>
                    ) : (
                      (data?.recipientsList || []).map((item) => item.nickName).join('、')
                    )}
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white">
              <Tabs
                style={{
                  '--title-font-size': '1rem',
                }}
                className={styles.iAdmTabs2}
                onChange={(val) => {
                  setTaskStepsType(taskStepsTypeEnum[val as keyof typeof taskStepsTypeEnum]);
                }}
              >
                {['任务进度', '操作记录'].map((item) => {
                  return <Tabs.Tab title={item} key={item} />;
                })}
              </Tabs>
              <div>
                <Steps
                  direction="vertical"
                  className={taskStepsType === taskStepsTypeEnum['任务进度'] ? styles.ISteps : styles.ISteps2}
                  style={{
                    '--title-font-size': '14px',
                    '--indicator-margin-right': '7px',
                    paddingTop: '16px',
                  }}
                >
                  {taskStepsType === taskStepsTypeEnum['任务进度'] &&
                    data?.tripartiteTaskType !== 'FLOW' &&
                    (data.taskScheduleList.length !== 0 ? (
                      data?.taskScheduleList.map((item, idx) => {
                        return (
                          <Step
                            title={
                              <div className="flex justify-between">
                                <div className="text-[#58595B]">
                                  <span className={`inline-block ${idx === 0 ? 'text-[#FF7B29]' : '#9C9C9C'}`}>
                                    进度{item?.scheduleValue}%
                                  </span>
                                  {item?.scheduleMark && (
                                    <>
                                      {'-'}
                                      {item?.scheduleMark}
                                    </>
                                  )}
                                </div>
                                {item.scheduleFileList.length === 0 && (
                                  <span className=" text-[#999999] leading-[20px] shrink-0">
                                    {dayjs(item.createTime).format('YYYY/MM/DD HH:mm')}
                                  </span>
                                )}
                              </div>
                            }
                            status={idx === 0 ? 'finish' : 'wait'}
                            description={
                              <div className="flex justify-between">
                                <div className="flex flex-col gap-y-2 grow">
                                  {(() => {
                                    const pdfFiles: taskFileList = [];
                                    const imageFiles: taskFileList = [];

                                    item?.scheduleFileList.forEach((element) => {
                                      if (element.fileName?.endsWith('.pdf')) {
                                        pdfFiles.push(element);
                                      } else {
                                        imageFiles.push(element);
                                      }
                                    });

                                    return (
                                      <>
                                        {/* pdf */}
                                        {pdfFiles.length !== 0 && (
                                          <div>
                                            {pdfFiles.map((item, idx) => {
                                              return (
                                                <span
                                                  onClick={() => {
                                                    setPdfPreviewVisible(true);
                                                    setPdfPreviewUrl(item.fileUrl!);
                                                  }}
                                                  key={item.id}
                                                  className="text-sm leading-[18px] text-primary"
                                                >
                                                  {idx > 0 && '、'}
                                                  {idx + 1}
                                                  {'.'}
                                                  {item.fileName}
                                                </span>
                                              );
                                            })}
                                          </div>
                                        )}
                                        {/* img */}
                                        {imageFiles.length !== 0 && (
                                          <div className="w-full grid grid-cols-4 gap-y-2">
                                            {imageFiles.map((item) => {
                                              return (
                                                <Image
                                                  src={item.fileUrl}
                                                  className="w-[28px] h-[28px]"
                                                  fit="contain"
                                                  onClick={() => {
                                                    ImageViewer.show({
                                                      image: item.fileUrl,
                                                    });
                                                  }}
                                                />
                                              );
                                            })}
                                          </div>
                                        )}
                                      </>
                                    );
                                  })()}
                                </div>
                                <div className="flex flex-col items-end">
                                  {item.scheduleFileList.length !== 0 && (
                                    <span className="text-13 leading-[13px] shrink-0">
                                      {dayjs(item.createTime).format('YYYY/MM/DD HH:mm')}
                                    </span>
                                  )}
                                  {idx === 0 && taskOperatorArr.some((o) => o === TaskOperatorEnum['修改进度反馈']) && (
                                    <Button
                                      fill="solid"
                                      color="primary"
                                      block
                                      className="mt-4  w-[64px] h-[25px] text-xs flex items-center justify-center"
                                      onClick={() => {
                                        setProgressPopupStatus({
                                          visible: true,
                                          progressMin: data.taskScheduleList[0].scheduleValue || 0,
                                          taskUserId: +taskUserId!,
                                          scheduleId: item?.id,
                                          isFinish: false,
                                          isEdit: true,
                                          finishAttachRequired: data?.finishAttachRequired,
                                          updateAttachRequired: data?.updateAttachRequired,
                                          scheduleMark: item?.scheduleMark,
                                          scheduleFileList: item?.scheduleFileList,
                                        });
                                      }}
                                    >
                                      修改
                                    </Button>
                                  )}
                                </div>
                              </div>
                            }
                            key={item.id}
                          />
                        );
                      })
                    ) : data.taskStatus === TaskStatusEnum.Rejected ? (
                      <div className="flex flex-col gap-y-[12px] mx-auto justify-center items-center mt-[28px] mb-[40px] ">
                        <IconFont type="icon-jujue" className="text-[122px]  text-[#DCDCDC]" />
                        <span className="text-[#858585] text-13 leading-[12px]">任务已拒绝</span>
                      </div>
                    ) : (
                      <div className="flex flex-col gap-y-[12px] mx-auto justify-center items-center mt-[28px] mb-[40px] ">
                        <IconFont type="icon-zanwushuju" className="text-[122px]  text-[#DCDCDC]" />
                        <span className="text-[#858585] text-13 leading-[12px]">
                          {data.taskStatus === TaskStatusEnum.Completed ? '已完成' : '等待确认'}
                        </span>
                      </div>
                    ))}
                  {taskStepsType === taskStepsTypeEnum['操作记录'] &&
                    data?.taskOperationLogList.map((item, idx) => {
                      return (
                        <Step
                          title={
                            <div className="text-[#58595B] flex justify-between">
                              <span>{item.operation}</span>
                              <span className="text-light shrink-0">
                                {dayjs(item.createTime).format('YYYY/MM/DD HH:mm')}
                              </span>
                            </div>
                          }
                          status={idx === data?.taskOperationLogList.length - 1 ? 'finish' : 'wait'}
                          description={<div className="text-13">{item?.reason ? `理由：${item?.reason}` : null}</div>}
                          key={item.id}
                        />
                      );
                    })}
                </Steps>
              </div>
            </div>
            {taskOperatorArr.some((o) => o === TaskOperatorEnum['终止任务']) ? (
              <div className="my-6 text-sm flex flex-col items-center gap-y-3">
                <a
                  className="text-[#979797] underline underline-offset-4"
                  onClick={() => {
                    setCancelTaskPopupVisible(true);
                  }}
                >
                  终止任务
                </a>
                <span className="text-[#C4C4C4]">确认终止任务后无法恢复</span>
              </div>
            ) : (
              <div className="mt-[120px]" />
            )}
          </div>
          <div className="shrink-0">
            {taskOperatorArr.some((o) => o === TaskOperatorEnum['更新任务']) && data?.tripartiteTaskType !== 'FLOW' && (
              <div className="bg-white flex gap-x-3 p-3">
                <Button
                  color="primary"
                  fill="outline"
                  block
                  className="h-[45px] text-base"
                  onClick={() => {
                    setProgressPopupStatus({
                      visible: true,
                      progressMin: data.taskScheduleList[0].scheduleValue || 0,
                      taskUserId: +taskUserId!,
                      isEdit: false,
                      isFinish: false,
                      finishAttachRequired: data?.finishAttachRequired,
                      updateAttachRequired: data?.updateAttachRequired,
                    });
                  }}
                >
                  更新进度
                </Button>
                <Button
                  color="primary"
                  fill="solid"
                  block
                  className="h-[45px] text-base"
                  onClick={() => {
                    setProgressPopupStatus({
                      visible: true,
                      progressMin: 100,
                      taskUserId: +taskUserId!,
                      isFinish: true,
                      isEdit: false,
                      finishAttachRequired: data?.finishAttachRequired,
                      updateAttachRequired: data?.updateAttachRequired,
                    });
                  }}
                >
                  完成任务
                </Button>
              </div>
            )}
            {taskOperatorArr.some((o) => o === TaskOperatorEnum['接收任务']) && (
              <div className="bg-white flex gap-x-3 p-3">
                <Button
                  color="primary"
                  fill="outline"
                  block
                  className="h-[45px] text-base"
                  onClick={() => {
                    // 投流任务
                    if (data?.tripartiteTaskType === 'FLOW') {
                      rejectInvest();
                    } else {
                      setRejectPopupVisible(true);
                    }
                  }}
                >
                  拒绝
                </Button>
                <Button
                  color="primary"
                  fill="solid"
                  block
                  className="h-[45px] text-base"
                  onClick={() => {
                    // 投流任务
                    if (data?.tripartiteTaskType === 'FLOW') {
                      setInvestPopupVisible(true);
                    } else {
                      handleReceive(+taskUserId!);
                    }
                  }}
                >
                  接受
                </Button>
              </div>
            )}
            {/* 只有我管辖下常规任务的待验收状态的任务才有验收和驳回 */}
            {taskOperatorArr.some((o) => o === TaskOperatorEnum['验收任务']) &&
              needCheck &&
              data?.tripartiteTaskType !== 'FLOW' && (
                <div className="bg-white flex gap-x-3 p-3">
                  <Button
                    color="primary"
                    fill="outline"
                    block
                    className="h-[45px] text-base"
                    onClick={() => {
                      setRebutPopupVisible(true);
                    }}
                  >
                    驳回
                  </Button>
                  <Button
                    color="primary"
                    fill="solid"
                    block
                    className="h-[45px] text-base"
                    onClick={async () => {
                      await acceptanceTaskReq({
                        taskStatus: TaskStatusEnum.Completed,
                        taskUserId: +taskUserId!,
                      });
                      Toast.show('验收成功');
                      refresh();
                    }}
                  >
                    验收通过
                  </Button>
                </div>
              )}
          </div>
        </div>
      )}
      <IPopup
        visible={endDatePopupStatus.visible}
        closeOnMaskClick
        onClose={() => {
          setEndDatePopupStatus((pre) => ({
            ...pre,
            visible: false,
          }));
        }}
        title="编辑任务截止时间"
        footer={
          <div className="flex gap-2 p-3">
            <Button
              className="flex-1"
              onClick={() => {
                setEndDatePopupStatus((pre) => ({
                  ...pre,
                  visible: false,
                }));
              }}
              color="primary"
              fill="outline"
            >
              取消
            </Button>
            <Button
              className="flex-1"
              color="primary"
              onClick={() => {
                run(taskUserId!, dayjs(endDatePopupStatus.date).format('YYYY-MM-DD HH:mm'));
              }}
            >
              确定
            </Button>
          </div>
        }
      >
        <Calendar
          selectionMode="single"
          value={endDatePopupStatus.date}
          onChange={(val) => {
            setEndDatePopupStatus((pre) => ({
              ...pre,
              date: val!,
            }));
          }}
          min={dayjs().toDate()}
        />
        <div className="flex justify-between items-center px-4 py-8">
          <span>截止时间</span>
          <CustomerFormFieldWrapper
            value={dayjs(endDatePopupStatus.date).format('HH:mm')}
            onShow={() => {
              setTimePickerVisible(true);
            }}
          >
            <Picker
              columns={[
                Array.from({ length: 24 }, (_, index) => index.toString().padStart(2, '0')),
                Array.from({ length: 60 }, (_, index) => index.toString().padStart(2, '0')),
              ]}
              visible={timePickerVisible}
              onClose={() => {
                setTimePickerVisible(false);
              }}
              value={[
                endDatePopupStatus.date.getHours().toString().padStart(2, '0'),
                endDatePopupStatus.date.getMinutes().toString().padStart(2, '0'),
              ]}
              onConfirm={(v: any) => {
                endDatePopupStatus.date.setHours(v[0], v[1]);
              }}
              style={{ zIndex: 99999999 }}
            />
          </CustomerFormFieldWrapper>
        </div>
        {/* <div className="relative">
          <span
            onClick={() => {
              setTimePickerVisible(true);
            }}
          >
            {dayjs(endDatePopupStatus.date).format('HH:mm')}
          </span>
          <Picker
            columns={[
              Array.from({ length: 24 }, (_, index) => index.toString().padStart(2, '0')),
              Array.from({ length: 60 }, (_, index) => index.toString().padStart(2, '0')),
            ]}
            visible={timePickerVisible}
            onClose={() => {
              setTimePickerVisible(false);
            }}
            onConfirm={(v: any) => {
              endDatePopupStatus.date.setHours(v[0], v[1]);
            }}
            style={{ zIndex: 99999999 }}
          />
        </div> */}
      </IPopup>
      <ProgressPopup
        visible={progressPopupStatus.visible}
        isFinish={progressPopupStatus.isFinish}
        progressMin={progressPopupStatus.progressMin}
        finishAttachRequired={progressPopupStatus.finishAttachRequired}
        updateAttachRequired={progressPopupStatus.updateAttachRequired}
        taskUserId={progressPopupStatus.taskUserId}
        scheduleId={progressPopupStatus.scheduleId}
        isEdit={progressPopupStatus.isEdit}
        scheduleMark={progressPopupStatus.scheduleMark}
        scheduleFileList={progressPopupStatus.scheduleFileList}
        onClose={() =>
          setProgressPopupStatus((prev) => ({
            ...prev,
            visible: false,
          }))
        }
        onSuccess={() => {
          if (roleTypeIsManage()) {
            refresh();
          } else {
            (window as any).ReactNativeWebView.postMessage(
              JSON.stringify({
                action: 'refreshNormalTaskList',
                reloadWebviewUrl: '/tasks/list/normalWork?queryType=under&taskStatus=6&sortBy=desc&workType=normalWork',
              }),
            );
            navigator.goBack();
          }
        }}
      />
      <TextareaPopup
        title="终止理由"
        okText="确认终止"
        onOk={handleTerminated}
        visible={cancelTaskPopupVisible}
        onClose={() => {
          setCancelTaskPopupVisible(false);
        }}
      />
      <PdfViewer visible={pdfPreviewVisible} url={pdfPreviewUrl} onClose={() => setPdfPreviewVisible(false)} />
      <VideoViewer visible={videoPreviewVisible} url={videoPreviewUrl} onClose={() => setVideoPreviewVisible(false)} />
      <TextareaPopup
        visible={rejectPopupVisible}
        title="拒绝理由"
        okText="确认拒绝"
        onClose={() => setRejectPopupVisible(false)}
        onOk={handleReject}
      />
      {investPopupVisible && (
        <InvestPop
          visible={investPopupVisible}
          title="参与投流确认"
          okText="确认"
          investRate={investDetails}
          task={task}
          onClose={() => setInvestPopupVisible(false)}
          onOk={handleInvest}
        />
      )}
      <TextareaPopup
        visible={rebutPopupVisible}
        title="驳回理由"
        okText="确认驳回"
        onClose={() => setRebutPopupVisible(false)}
        onOk={handleRebut}
      />
    </Loading>
  );
}
