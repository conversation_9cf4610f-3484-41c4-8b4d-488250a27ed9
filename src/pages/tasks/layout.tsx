import { createContext, useEffect, useState } from 'react';
import AuthorityEnum from '@src/common/authority';
import { IconFont } from '@src/components';
import { IPopup } from '@src/components/IPopup';
import { userStore } from '@src/store';
import { roleTypeIsManage } from '@src/utils/tokenUtils';
import { useRequest } from 'ahooks';
import { CheckList, ErrorBlock, Popup, Selector, Tabs } from 'antd-mobile';
import cn from 'classnames';
import { observer } from 'mobx-react';
import { Outlet, useNavigate, useSearchParams } from 'react-router-dom';
import { getProcessingTaskCount } from './api';
import { TaskTypeEnum, WorkTypeEnum } from './enum';
import styles from './index.module.scss';

export const TasksContext = createContext<any | undefined>(undefined);

enum CreateTastType {
  Routine = 'Routine',
  Patrol = 'Patrol',
  Unplanned = 'Unplanned',
  Food = 'FOOD_SAFETY_NORMAL',
}

const CreateTastTypeCN: Record<CreateTastType, string> = {
  [CreateTastType.Routine]: '常规任务',
  [CreateTastType.Patrol]: '巡检计划',
  [CreateTastType.Unplanned]: '到店巡检计划外任务',
  [CreateTastType.Food]: '稽核任务',
};

const TasksLayout = observer(() => {
  const [createPopupProps, setCreatePopupProps] = useState<{
    visible: boolean;
  }>({
    visible: false,
  });

  const CreateTastTypeFn: Record<CreateTastType, () => void> = {
    [CreateTastType.Routine]: () => {
      navigate('/tasks/create');
    },
    [CreateTastType.Patrol]: () => {
      navigate(`/tasks/patrol/create?prev=${location.pathname + location.search}`);
    },
    [CreateTastType.Unplanned]: () => {
      navigate(`/tasks/patrol/unplanned?prev=${location.pathname + location.search}`);
    },
    [CreateTastType.Food]: () => {
      navigate('/tasks/patrol/unplanned?subType=FOOD_SAFETY_NORMAL');
    },
  };

  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { data: count, refresh: refreshCount } = useRequest(getProcessingTaskCount);
  // const { data: PrincipalPatrolTaskCount } = useRequest(async () => {
  //   const res = await Promise.all([getPatrolPlanInfo(), getPatrolTaskInfo()]);
  //   return res.reduce((accumulator, currentValue) => {
  //     return accumulator + currentValue.inProgress + currentValue.notStarted;
  //   }, 0);
  // });
  const { waiting = 0 } = count || {};

  const tabs = [
    {
      label: '我处理的',
      value: TaskTypeEnum.Principal,
      auth: AuthorityEnum.我处理的,
      children: [
        {
          label: '常规任务',
          value: WorkTypeEnum.NormalWork,
          count: 0,
          url: '/tasks/list/normalWork',
          auth: AuthorityEnum.PrincipalNormal,
        },
        {
          label: '巡检',
          value: WorkTypeEnum.Patrol,
          count: 0,
          url: '/tasks/list/principal/patrol',

          auth: AuthorityEnum.PrincipalPatrol,
        },
        {
          label: '消杀',
          value: WorkTypeEnum.StructKill,
          count: 0,
          url: '/tasks/list/principal/structKill',
          auth: AuthorityEnum.PrincipalStructKill,
        },
      ].filter((i) => (roleTypeIsManage() ? userStore.permissionsMap.has(i.auth) : true)),
    },
    {
      label: '我派发的',
      value: TaskTypeEnum.Created,
      auth: AuthorityEnum.我派发的,
      children: [
        {
          label: '常规任务',
          value: WorkTypeEnum.NormalWork,
          count: 0,
          url: '/tasks/list/normalWork',
          auth: AuthorityEnum.CreatedNormal,
        },
        {
          label: '紧急消杀',
          value: WorkTypeEnum.EmergencyStructKill,
          count: 0,
          url: '/tasks/list/normalWork',
          auth: AuthorityEnum.紧急消杀,
        },
      ].filter((i) => (roleTypeIsManage() ? userStore.permissionsMap.has(i.auth) : true)),
    },
    {
      label: '抄送我的',
      value: TaskTypeEnum.Recipients,
      auth: AuthorityEnum.抄送我的,
      children: [
        {
          label: '常规任务',
          value: WorkTypeEnum.NormalWork,
          count: 0,
          url: '/tasks/list/normalWork',
          auth: AuthorityEnum.RecipientsNormal,
        },
      ].filter((i) => (roleTypeIsManage() ? userStore.permissionsMap.has(i.auth) : true)),
    },
    {
      label: '我管辖的',
      value: TaskTypeEnum.Under,
      auth: AuthorityEnum.我管辖的,
      children: [
        {
          label: '常规任务',
          value: WorkTypeEnum.NormalWork,
          count: 0,
          url: '/tasks/list/normalWork',
          auth: AuthorityEnum.UnderNormal,
        },

        {
          label: '自检',
          value: WorkTypeEnum.Self,
          url: '/tasks/list/under/self',
          auth: AuthorityEnum.UnderSelfPatrol,
        },
        {
          label: '巡检',
          value: WorkTypeEnum.Patrol,
          url: '/tasks/list/under/patrol',
          auth: AuthorityEnum.UnderPatrol,
        },
        {
          label: '消杀',
          value: WorkTypeEnum.StructKill,
          count: 0,
          url: '/tasks/list/under/structKill',
          auth: AuthorityEnum.UnderStructKill,
        },
      ].filter((i) => (roleTypeIsManage() ? userStore.permissionsMap.has(i.auth) : true)),
    },
  ].filter((i) => (roleTypeIsManage() ? userStore.permissionsMap.has(i.auth) : true));

  useEffect(() => {
    // 需要解决从详情页返回的场景
    const queryTypeParam = searchParams.get('queryType');
    const workTypeUrlParam = searchParams.get('workType');

    if (queryTypeParam || workTypeUrlParam) {
      // 说明是从详情页返回的场景
    } else {
      // 从底部tabs进入
      if (tabs.length) {
        navigate(tabs[0].children[0]?.url, { replace: true });
      }
    }
  }, []);

  const [queryType, setQueryType] = useState<TaskTypeEnum>(() => {
    const searchParam = searchParams.get('queryType');

    return tabs.some((i) => i.value === searchParam) ? (searchParam as TaskTypeEnum) : tabs[0]?.value;
  });

  useEffect(() => {
    if (searchParams.get('queryType')) {
      setQueryType(searchParams.get('queryType') as TaskTypeEnum);
    }
  }, [searchParams.get('queryType')]);

  const [workType, setWorkType] = useState<WorkTypeEnum>(WorkTypeEnum.NormalWork);

  useEffect(() => {
    if (searchParams.get('workType')) {
      setWorkType(searchParams.get('workType') as WorkTypeEnum);
    }
  }, [searchParams.get('workType')]);

  const [tabPopupVisible, setTabPopupVisible] = useState(false);

  if (!tabs.length) {
    return <ErrorBlock status="empty" title="您没有该页面权限" description={<span>请与客服人员联系！</span>} />;
  }
  return (
    <TasksContext.Provider
      value={{
        queryType,
        refreshCount,
        waiting,
      }}
    >
      <div className="flex flex-col h-full">
        <>
          {((roleTypeIsManage() && userStore.permissionsMap.has(AuthorityEnum.督导创建任务)) ||
            (!roleTypeIsManage() && userStore.permissionsMap.has(AuthorityEnum.店长创建任务))) && (
            <div
              className="fixed right-[6px] bottom-[100px] flex flex-col justify-center items-center bg-white w-[60px] h-[60px] rounded-full shadow-[0_2px_5px_0_rgba(0,0,0,0.1)] z-[999] text-[#747578] text-13 leading-[13px]"
              // onClick={() => navigate('/tasks/create')}
              onClick={() =>
                setCreatePopupProps({
                  visible: true,
                })
              }
            >
              <IconFont type="icon-tianjia1" className="text-[22px] mb-[6px] text-[#5E5E5E]" />
              创建
            </div>
          )}
          <Popup
            visible={createPopupProps.visible}
            bodyStyle={{
              borderTopLeftRadius: '8px',
              borderTopRightRadius: '8px',
            }}
            closeOnMaskClick
            onClose={() => {
              setCreatePopupProps({ visible: false });
            }}
          >
            <div>
              <div className="p-4 flex justify-between">
                <span className="text-[18px] leading-none font-medium text-[#000000]">创建任务</span>
                <IconFont
                  type="icon-cross"
                  className="text-base leading-[18px] text-[#999999]"
                  onClick={() => {
                    setCreatePopupProps({ visible: false });
                  }}
                />
              </div>
              <CheckList
                style={{
                  '--padding-left': '16px',
                  '--padding-right': '16px',
                  '--font-size': '16px',
                }}
                onChange={(val: any) => {
                  CreateTastTypeFn[val?.[0] as CreateTastType]();
                  setCreatePopupProps({ visible: false });
                }}
              >
                {((roleTypeIsManage() && userStore.permissionsMap.has(AuthorityEnum.督导创建常规任务)) ||
                  (!roleTypeIsManage() && userStore.permissionsMap.has(AuthorityEnum.店长创建常规任务))) && (
                  <CheckList.Item value={CreateTastType.Routine}>
                    {CreateTastTypeCN[CreateTastType.Routine]}
                  </CheckList.Item>
                )}
                {/* {!userStore.permissionsMap.has(AuthorityEnum.创建巡检计划) && (
                  <CheckList.Item value={CreateTastType.Patrol}>
                    {CreateTastTypeCN[CreateTastType.Patrol]}
                  </CheckList.Item>
                )} */}
                {userStore.permissionsMap.has(AuthorityEnum.督导计划外到店巡检任务) && roleTypeIsManage() && (
                  <CheckList.Item value={CreateTastType.Unplanned}>
                    {CreateTastTypeCN[CreateTastType.Unplanned]}
                  </CheckList.Item>
                )}
                {userStore.permissionsMap.has(AuthorityEnum.督导稽核任务) && (
                  <CheckList.Item value={CreateTastType.Food}>{CreateTastTypeCN[CreateTastType.Food]}</CheckList.Item>
                )}
              </CheckList>
            </div>
          </Popup>
        </>
        <div className={cn('flex bg-white items-start border-[#eeeeee] shrink-0 relative z-[1001]', styles.iAdmTabs)}>
          <Tabs
            style={{
              '--title-font-size': '1rem',
            }}
            className={styles.iAdmTabs}
            activeKey={queryType}
            onChange={(value) => {
              setQueryType(value as TaskTypeEnum);

              const subTabs = tabs.find((v) => v.value === value)?.children || [];
              console.log('676767676', subTabs.length && subTabs[0].value !== WorkTypeEnum.NormalWork);

              subTabs.length ? setWorkType(subTabs[0].value) : setWorkType(WorkTypeEnum.NormalWork);
              navigate('/tasks/list/normalWork', { replace: true });
              // if (
              //   subTabs.length &&
              //   subTabs[0].value !== WorkTypeEnum.NormalWork
              // ) {

              //   navigate(`/tasks/list/${value}/${subTabs[0].value}`, {
              //     replace: true,
              //   });
              // } else {
              //   console.log('aaaa');
              //   navigate('/tasks/list/normalWork', { replace: true });
              // }
            }}
          >
            {tabs.map((item, idx) => (
              <Tabs.Tab
                title={
                  <div
                    className={cn('flex items-center leading-4', {
                      'mr-[22px]': tabs.length === 4 && idx === tabs.length - 1,
                    })}
                  >
                    {item.label}
                  </div>
                }
                key={item.value}
              >
                <Tabs
                  style={{
                    '--title-font-size': '14px',
                  }}
                  className={styles.iAdmTabs3}
                  activeKey={workType}
                  onChange={(value) => {
                    if (value === WorkTypeEnum.NormalWork) {
                      navigate(`/tasks/list/${value}`, { replace: true });
                      setWorkType(value as WorkTypeEnum);
                    } else {
                      navigate(`/tasks/list/${queryType}/${value}`, {
                        replace: true,
                      });
                      setWorkType(value as WorkTypeEnum);
                    }
                  }}
                >
                  {/* <Tabs.Tab title="常规任务" key={WorkTypeEnum.NormalWork}></Tabs.Tab> */}
                  {item.children &&
                    roleTypeIsManage() &&
                    item.children.map((o) => {
                      return (
                        <Tabs.Tab
                          title={
                            <>
                              {o.label}
                              {o?.count !== undefined && o?.count > 0 && (
                                <span
                                  className={cn('text-xs ml-[2px] font-normal', {
                                    'text-light': item.value !== queryType,
                                  })}
                                >
                                  ({o?.count})
                                </span>
                              )}
                            </>
                          }
                          // title={o.label}
                          key={o.value}
                        />
                      );
                    })}
                </Tabs>
              </Tabs.Tab>
            ))}
          </Tabs>
          {tabs.length === 4 && (
            <div className="h-12 flex items-center absolute top-0 right-0 bg-white">
              <div className="w-[1px] h-5 bg-[rgba(0,0,0,0.03)]" />
              <div className="flex h-full justify-center items-center px-3" onClick={() => setTabPopupVisible(true)}>
                <IconFont type="icon-menu" className="text-[#B8B8B8]" />
              </div>
            </div>
          )}
        </div>

        <IPopup title="分类" visible={tabPopupVisible} onClose={() => setTabPopupVisible(false)}>
          <div className="pt-5 px-4 pb-10">
            <Selector
              style={{
                '--checked-color': 'var(--color-primary-1)',
                '--border-radius': '4px',
                fontSize: '13px',
              }}
              value={[queryType]}
              onChange={(value) => setQueryType(value[0])}
              columns={3}
              showCheckMark={false}
              options={tabs.map((i) => ({
                label: `${i.label}`,
                value: i.value,
              }))}
            />
          </div>
        </IPopup>
        <Outlet />
      </div>
    </TasksContext.Provider>
  );
});
export default TasksLayout;
