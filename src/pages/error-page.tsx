import { ErrorBlock } from 'antd-mobile';
import { useRouteError } from 'react-router-dom';

export default function ErrorPage() {
  const error = useRouteError() as any;

  return (
    <div className="mx-auto">
      <ErrorBlock
        status="default"
        style={{
          '--image-height': '200px',
        }}
        description={<i>{error?.statusText || error?.message}</i>}
      ></ErrorBlock>
    </div>
  );
}
