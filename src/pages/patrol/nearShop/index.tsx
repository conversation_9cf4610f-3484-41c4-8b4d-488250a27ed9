import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Loading } from '@src/components';
import { useShopConfig } from '@src/hooks/useShopConfig';
import { SignTypeText } from '@src/pages/reportDetail/patrol/enum';
import { GlobalStore, SignStore } from '@src/store';
import { encrypt } from '@src/utils/utils';
import { H5Bridge } from '@tastien/rn-bridge';
import { useRequest } from 'ahooks';
import { Button, ErrorBlock, SearchBar, Toast } from 'antd-mobile';
import dayjs from 'dayjs';
import { isString } from 'lodash';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { createPlanTask, queryPlanShopList } from '../api';
import { ShopCard } from '../shopCard';
const NearShop = () => {
  const distanceRef = useRef(500);
  const [routerParams] = useSearchParams();
  const [showAll, setShowAll] = useState<boolean>(false);
  const [searchValue, setSearchValue] = useState('');
  const [curLocation, setCurLocation] = useState<{
    longitude: number;
    latitude: number;
  }>();
  const navigate = useNavigate();
  const { config } = useShopConfig();
  const { planId, signType } = useMemo(() => {
    const planId = routerParams.get('planId');
    const signType = routerParams.get('signType');

    return {
      planId,
      signType,
    };
  }, [routerParams]);
  console.log(signType, '=signType');

  const { data, loading } = useRequest(
    async () => {
      if (!planId) throw new Error('null');
      const params = { planId };
      if (!showAll) {
        const Config = await GlobalStore.getCorporationConfig();

        distanceRef.current = Config.CHECK_IN_DISTANCE;
        Object.assign(params, {
          lat: curLocation?.latitude || 0,
          log: curLocation?.longitude || 0,
          distance: +Config?.CHECK_IN_DISTANCE,
        });
      }
      const res = await queryPlanShopList(params);
      return res?.filter((item) => item?.hasEmployee) || [];
    },
    {
      refreshDeps: [planId, showAll, curLocation],
    },
  );

  useEffect(() => {
    // 通知APP获取定位
    if (window?.['ReactNativeWebView']?.['postMessage']) {
      // 安卓
      window['ReactNativeWebView']['postMessage'](
        JSON.stringify({ getLocation: true }),
      );
    } else {
      // ios
      window.parent.postMessage(JSON.stringify({ getLocation: true }), '*');
    }
    function handleEvent(e) {
      if (e.data && isString(e.data)) {
        const result = JSON.parse(e.data);
        setCurLocation(result?.location);
      }
    }
    document.addEventListener('message', handleEvent); // 安卓端监听
    window.addEventListener('message', handleEvent); // ios端监听
    return () => {
      document.removeEventListener('message', handleEvent);
      window.addEventListener('message', handleEvent);
    };
  }, []);
  console.log(config, '=config111');

  const dataSource = useMemo(() => {
    return searchValue
      ? data?.filter((item) => {
          const searchName = item.shopName
            ?.toLocaleLowerCase()
            .includes(searchValue?.toLocaleLowerCase());
          const searchNo = item.shopId
            ?.toLocaleLowerCase()
            .includes(searchValue?.toLocaleLowerCase());
          return searchName || searchNo;
        })
      : data;
  }, [data, searchValue]);
  const { run, loading: createLoading } = useRequest(
    async (data) => {
      const params = {
        planId: planId!,
        shopIds: [data?.shopId],
        taskDate: dayjs().format('YYYY-MM-DD'),
      };
      const res = await createPlanTask(params);
      return {
        ...data,
        taskId: res?.taskIds?.[0],
        shopPhone: data?.shopManagerPhone,
        shopLog: data?.longitude,
        shopLat: data?.latitude,
        signed: false,
      };
    },
    {
      manual: true,
      onSuccess: (res) => {
        Toast.show({
          icon: 'success',
          content: '创建成功',
          duration: 1000,
          afterClose: async () => {
            const isNoSign: boolean =
              (signType === SignTypeText.跟随系统 &&
                +config?.MUST_CHECK_IN === 0) ||
              signType === SignTypeText.无需签到签离;
            if (isNoSign) {
              navigate(`/patrol/shopcheck?taskId=${res?.taskId}`);
            } else {
              await SignStore.setTaskShopInfo(res);
              // navigate(`/patrol/plan/sign`);

              const info = {
                path: 'SupervisorSignIn',
                shopId: res.shopId,
                taskId: res.taskId,
                callBackUrl: `/patrol/shopcheck?taskId=${res.taskId}`,
              };
              await H5Bridge.customPostMessage.customPostMessage({
                module: 'face-module',
                method: 'navigateTo',
                params: {
                  param: encrypt(info),
                },
              });
            }
          },
        });
      },
    },
  );

  if (!showAll) {
    return (
      <Loading spinning={loading || createLoading}>
        {!!data?.length ? (
          <div>
            <div className="text-center text-xs leading-[20px] text-[#5E5E5E] mt-2 mb-2">
              附近{distanceRef.current}米内的门店
            </div>
            <div className="bg-white h-full flex flex-col pl-4 pr-4">
              {data?.map((item) => {
                return (
                  <ShopCard
                    itemInfo={item}
                    onClick={(info) => {
                      run(info);
                    }}
                  />
                );
              })}
            </div>
          </div>
        ) : (
          <div className="bg-white h-full flex flex-col">
            <div className="pt-20 pb-20">
              <ErrorBlock
                status="empty"
                description={`附近${distanceRef.current}米暂无门店`}
              />
            </div>
            <div className="p-4 ">
              <Button
                onClick={() => {
                  setShowAll(true);
                }}
                color="primary"
                fill="solid"
                block
                className="h-[45px] text-base"
              >
                查看全部门店
              </Button>
            </div>
          </div>
        )}
      </Loading>
    );
  }
  return (
    <Loading spinning={loading || createLoading}>
      <div className="flex flex-col p-4 bg-white ">
        <SearchBar
          className={`flex-1`}
          style={{ '--border-radius': '8px', '--height': '2.5rem' }}
          onClear={() => setSearchValue('')}
          onSearch={setSearchValue}
          placeholder="请输入门店名称/门店编号"
        />
        <div className="">
          {dataSource?.map((item) => {
            return (
              <ShopCard
                key={item.shopId}
                itemInfo={item}
                onClick={(info) => {
                  run(info);
                }}
              />
            );
          })}
        </div>
      </div>
    </Loading>
  );
};

export default NearShop;
