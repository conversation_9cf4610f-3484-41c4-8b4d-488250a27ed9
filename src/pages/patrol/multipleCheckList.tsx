import React, { useEffect, useMemo } from 'react';
import { observer } from 'mobx-react';
import { useParams } from 'react-router-dom';
import { PatrolStore, SignStore } from '@src/store';
import { toJS } from 'mobx';
import CheckList from './checkList';
import { Button, ErrorBlock } from 'antd-mobile';
import { ChecklistItem } from '../tasks/api.type';

const MultipleCheckList: React.FC = observer(() => {
  const { patrolWorksheetId } = useParams();
  const { taskId, workSheetList } = PatrolStore;
  const { queryTaskShopInfo } = SignStore;
  console.log(toJS(queryTaskShopInfo), '=queryTaskShopInfo');

  const workSheet = useMemo<Nullable<ChecklistItem>>(() => {
    if (!patrolWorksheetId || workSheetList.length === 0) return null;
    return workSheetList.filter((v) => v.patrolWorksheetId === +patrolWorksheetId)[0];
  }, [workSheetList, patrolWorksheetId]);
  return (
    <div className="flex flex-col fixed inset-x-0 inset-y-0">
      <div className="grow overflow-y-scroll">
        {workSheet ? (
          <CheckList
            taskId={taskId!}
            patrolWorksheetId={+patrolWorksheetId!}
            workSheet={workSheet}
            shopManagerName={queryTaskShopInfo?.shopManagerName}
          />
        ) : (
          <ErrorBlock status="empty" description="暂无数据" />
        )}
      </div>
      <div className="shrink-0">
        <div className="py-2 px-2 bg-[#fff]">
          <Button
            onClick={() => {
              history.back();
            }}
            color="primary"
            fill="solid"
            block
            className="text-base"
          >
            确定
          </Button>
        </div>
      </div>
    </div>
  );
});

export default MultipleCheckList;
