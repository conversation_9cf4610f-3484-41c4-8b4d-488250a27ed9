import React, { useEffect, useMemo, useState } from 'react';
import { IconFont, Loading } from '@src/components';
import { GlobalStore, PatrolStore, SignStore } from '@src/store';
import { navigator } from '@tastien/rn-bridge/lib/h5';
import { useRequest } from 'ahooks';
import { ActionSheet, Button, Checkbox, FloatingBubble, Tabs, TextArea, Toast } from 'antd-mobile';
import { Action } from 'antd-mobile/es/components/action-sheet';
import { toJS } from 'mobx';
import { observer } from 'mobx-react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { submitPatrolSummary } from './api';
import CheckList from './checkList';
import { RectificationType } from './checkList/const';
import PatrolInfo from './patrolInfo';
import { patrolTypeText } from './planDetail/detailCard';
import TaskPhotos from './taskPhotos';
import { TabList, TabsTypeEnum } from '../reportDetail/patrol';
import TaskProgress from '../reportDetail/patrol/taskProgress';
import { getExamSituation, queryChecklistDetail, submitExamConfirm } from '../tasks/api';
import { yytTaskStatusCNToEnEnum } from '../tasks/enum';

const actions: Action[] = [
  { text: '设为“合格”(得满分)', key: 'SET_FULL_SCORE' },
  { text: '设为“不适用”(不计分)', key: 'SET_NOT_APPLY' },
];
const Patrol = observer(() => {
  const navigate = useNavigate();
  const [routerParams] = useSearchParams();
  const { getCorporationConfig } = GlobalStore;
  const { getWorkSheets, workSheetList, summaryText } = PatrolStore;
  const [actionVisible, setActionVisible] = useState(false);
  const [isNoApplyAll, setNoApplyAll] = useState(false);
  const [isExamConfirm, setIsExamConfirm] = useState(false);
  const [foodSafeRemark, setFoodSafeRemark] = useState('');
  const [activeKey, setActiveKey] = useState<string>(TabsTypeEnum.报告详情);
  const { taskId } = useMemo(() => {
    const taskId = routerParams.get('taskId');
    return {
      taskId,
    };
  }, [routerParams]);
  const [taskPhotosMsg, setTaskPhotosMsg] = useState<{
    taskId: number | null;
    visible: boolean;
    shopId?: string;
  }>({
    taskId: null,
    visible: false,
  });
  const [summary, setSummary] = useState<string>(summaryText || '');

  console.log(toJS(workSheetList), '=workSheetList');

  const { data: detail, loading } = useRequest(
    async () => {
      if (!taskId) {
        // eslint-disable-next-line no-throw-literal
        throw null;
      }
      const res = await getWorkSheets(+taskId); // 检查表信息
      // 获取门店信息
      const shopInfo = {
        shopLat: res?.latitude,
        shopLog: res?.longitude,
        shopName: res?.shopName,
        shopId: res?.shopId,
        shopAddress: res?.shopAddress,
        taskId: +taskId!,
        signed: 1,
      };
      SignStore.setTaskShopInfo(shopInfo);
      console.log(res, '=res');

      return res;
    },
    { refreshDeps: [taskId] },
  );
  useEffect(() => setSummary(summaryText || ''), [summaryText]);

  const { data: _ExamData } = useRequest(
    async () => {
      if (detail?.subType === patrolTypeText.食安稽核到店辅导) {
        const res = await getExamSituation(taskId!);
        setIsExamConfirm(res?.confirm);
        setFoodSafeRemark(res?.remark || '');
        return;
      }
      return null;
    },
    { refreshDeps: [detail] },
  );

  const { run: runExamConfirm } = useRequest(
    () => submitExamConfirm({ taskId: +taskId!, confirm: isExamConfirm, remark: foodSafeRemark.trim() }),
    {
      manual: true,
      onSuccess: () => {},
    },
  );

  const { loading: ConfigLoading } = useRequest(
    async () => {
      return await getCorporationConfig();
    },
    { refreshDeps: [] },
  );

  const preview = () => {
    if (detail?.subType === patrolTypeText.食安稽核到店辅导 && !isExamConfirm) {
      return Toast.show({
        content: `请先进行食安到店辅导任务考试情况确认!`,
      });
    }

    let unfilledCount = 0; // 必检项未填
    let qualifiedCount = 0; // 非必检项未填
    let noApplyAll = false;
    // 需要当场整改但是未整改的数量
    let needImmediateRectifyCount = 0;
    for (let i = 0, len = workSheetList.length; i < len; i++) {
      noApplyAll = (workSheetList[i].children || []).every((v) => {
        return (v.children || []).every((v2) => !v2.hasApply);
      });
    }

    workSheetList?.forEach((v) => {
      const groups = v?.children || [];
      for (let j = 0, len = groups.length; j < len; j++) {
        const items = groups[j].children || [];
        for (const item of items) {
          if (!item?.hasFillItem) {
            qualifiedCount++;
            if (item?.accentedTermTags?.includes('NECESSARY')) {
              unfilledCount++;
            }
          } else {
            // 必须是填了，并且是不合格的项
            if (
              !item?.qualified &&
              item?.hasApply &&
              item?.hasRectify &&
              (item?.actualRectifyType === RectificationType.LOCAL || item?.rectifyType === RectificationType.LOCAL)
            ) {
              if (!item?.rectifyReason && !item?.rectifyImages?.length) {
                needImmediateRectifyCount++;
              }
            }
          }
        }
      }
    });

    if (unfilledCount > 0) {
      return Toast.show({
        content: `有${unfilledCount}个必检项未填写，请补充完整后再预览报告。`,
        afterClose: () => {},
      });
    }

    if (needImmediateRectifyCount > 0) {
      return Toast.show({
        content: `有${needImmediateRectifyCount}个需要当场整改但未整改，请整改后再预览报告。`,
        afterClose: () => {},
      });
    }
    if (qualifiedCount > 0) {
      setNoApplyAll(noApplyAll);
      setActionVisible(true);
      return;
    }
    navigate(`/patrol/reportdetail?taskId=${taskId}`, {
      replace: true,
    });
  };

  const { run: summaryRun } = useRequest(() => submitPatrolSummary({ id: +taskId!, context: summary.trim() }), {
    manual: true,
    onSuccess: () => {},
  });
  console.log(taskPhotosMsg, '=TaskPhotosMsg');
  const isOprBtn = useMemo(() => {
    if (detail?.subType === patrolTypeText.诊断任务) {
      return detail?.taskStatus !== yytTaskStatusCNToEnEnum.待开始;
    } else {
      return ![yytTaskStatusCNToEnEnum.已取消, yytTaskStatusCNToEnEnum.已过期].includes(
        detail?.taskStatus as yytTaskStatusCNToEnEnum,
      );
    }
  }, [detail]);
  return (
    <Loading spinning={loading || ConfigLoading}>
      <div className="flex flex-col fixed inset-x-0 inset-y-0">
        {detail?.subType === patrolTypeText.食安稽核到店辅导 && (
          <div className="bg-white">
            <Tabs
              style={{ '--title-font-size': '1rem' }}
              activeKey={activeKey}
              onChange={(key) => {
                setActiveKey(key);
              }}
            >
              {TabList.map(({ key, title }) => (
                <Tabs.Tab title={title} key={key} />
              ))}
            </Tabs>
          </div>
        )}
        {activeKey === TabsTypeEnum.任务进度 && <TaskProgress taskId={+taskId!} />}
        {activeKey === TabsTypeEnum.报告详情 && (
          <React.Fragment>
            <div className="grow overflow-y-scroll mb-2">
              <PatrolInfo patrolDetail={detail!} />
              <CheckList
                workSheetList={workSheetList}
                showSummary
                taskId={+taskId!}
                shopManagerName={detail?.shopManagerName}
                summary={summary}
                taskStatus={detail?.taskStatus}
                setSummary={(v) => {
                  setSummary(v);
                }}
                updateSummary={summaryRun}
              />
              {detail?.subType === patrolTypeText.食安稽核到店辅导 && (
                <div className="bg-white px-4 pb-2">
                  <div className=" text-base">考试情况确认</div>
                  <div className="text-sm my-1 flex items-center">
                    <span className="text-[#F53F3F] ml-1">*</span> <span>请确认门店是否都已完成并通过考试？</span>
                    <Checkbox
                      checked={isExamConfirm}
                      onChange={async (checked) => {
                        await setIsExamConfirm(checked);
                        runExamConfirm();
                      }}
                    >
                      <div className="text-sm text-[#5E5E5E]">已确认</div>
                    </Checkbox>
                  </div>
                  <div className="flex text-sm">
                    <span>备注说明：</span>
                    <TextArea
                      className="bg-[#FAFAFA] p-1 flex-1"
                      value={foodSafeRemark}
                      maxLength={150}
                      onChange={(v) => {
                        setFoodSafeRemark(v);
                      }}
                      onBlur={() => {
                        runExamConfirm();
                      }}
                      placeholder="请输入"
                    />
                  </div>
                </div>
              )}
            </div>
            {isOprBtn && (
              <div className="shrink-0 py-2 px-2 bg-[#fff]">
                <Button onClick={preview as any} color="primary" fill="solid" block className="text-base">
                  预览报告
                </Button>
              </div>
            )}
          </React.Fragment>
        )}
        <ActionSheet
          extra={
            <div className="text-center">
              <div>预览报告失败</div>
              <div>有检查项未填写，请选择处理方案</div>
            </div>
          }
          cancelText="返回继续填写"
          visible={actionVisible}
          actions={actions}
          onAction={(res) => {
            if (res.key === 'SET_NOT_APPLY' && isNoApplyAll) {
              return Toast.show({
                content: '不能所有项都是“不适用”',
              });
            }
            queryChecklistDetail({
              taskId: +taskId!,
              notFilledItemHandleType: res.key,
              filterHasApply: true,
            }).then(() => {
              navigate(`/patrol/reportdetail?taskId=${taskId}&notFilledItemHandleType=${res.key}`, {
                replace: true,
              });
            });
          }}
          onClose={() => setActionVisible(false)}
        />

        <FloatingBubble
          style={{
            '--initial-position-bottom': '60px',
            '--initial-position-right': '8px',
            '--size': '60px',
            '--background': '#FFFFFF',
          }}
          onClick={() => {
            // setTaskPhotosMsg({
            //   visible: true,
            //   taskId: +taskId!,
            //   shopId: detail?.shopId,
            // });

            navigator.push({
              pathname: 'SupervisorGallery',
              message: {
                taskId: +taskId!,
                isUpload: true,
                shopId: detail?.shopId,
              },
            });
          }}
        >
          <div className="flex flex-col items-center">
            <IconFont type="icon-camera" className="text-[26px] text-[#378BFF]" />
            <span className="text-[10px] text-[#5E5E5E]">任务照片</span>
          </div>
        </FloatingBubble>
        <TaskPhotos
          TaskPhotosMsg={taskPhotosMsg}
          setTaskPhotosMsg={() => {
            setTaskPhotosMsg({ visible: false, taskId: null, shopId: undefined });
          }}
        />
      </div>
    </Loading>
  );
});
export default Patrol;
