import React, { memo, useMemo, useState } from 'react';
import { IconFont, Loading } from '@src/components';
import PhonePopup from '@src/pages/reportDetail/patrol/basicInfo/phonePopup';
import { getDiagnosticInfo, getParentReportId } from '@src/pages/tasks/api';
import { yytTaskStatusENToCNEnum } from '@src/pages/tasks/enum';
import { formatDiff } from '@src/utils/utils';
import { H5Bridge } from '@tastien/rn-bridge';
import { useRequest } from 'ahooks';
import { Toast } from 'antd-mobile';
import { RightOutline } from 'antd-mobile-icons';
import dayjs from 'dayjs';
import { useNavigate } from 'react-router-dom';
import { DiagnosticInfo, ReportDetailInfo } from '../api.type';
import { patrolTypeText } from '../planDetail/detailCard';

interface BASICINFO {
  patrolDetail: ReportDetailInfo;
  isDetailPage?: boolean;
}
const warnLevel: { [key: string]: string } = {
  RED: '秩序白银',
  ORANGE: '倔强青铜',
  YELLOW: '尊贵铂金',
};
export const IagnosticInfoCard = memo<{
  patrolDetail: ReportDetailInfo;
}>(({ patrolDetail }) => {
  const { data: iagnosticInfo, loading: iagnosticLoading } = useRequest(
    async () => {
      if (patrolDetail?.taskId && patrolDetail.subType === patrolTypeText.诊断任务) {
        const res = await getDiagnosticInfo({ taskId: patrolDetail?.taskId });
        return res;
      }

      return {} as DiagnosticInfo;
    },
    { refreshDeps: [patrolDetail] },
  );

  return (
    <Loading spinning={iagnosticLoading}>
      <div className="bg-white text-sm font-medium p-4 mb-2">
        <div className="flex justify-between items-center">
          <div>{warnLevel[iagnosticInfo?.alarmLevel!] || ''}</div>
          <div className="text-primary">{yytTaskStatusENToCNEnum[iagnosticInfo?.taskStatus!]}</div>
        </div>
        <div>
          {iagnosticInfo?.shop?.shopId} {iagnosticInfo?.shop?.shopName}
        </div>
        <div>门店等级分:{iagnosticInfo?.score}</div>
        <div>处理人：{iagnosticInfo?.user?.nickname}</div>
        <div>
          执行时间段:{dayjs(iagnosticInfo?.start).format('YYYY/MM/DD')}~{dayjs(iagnosticInfo?.end).format('YYYY/MM/DD')}{' '}
        </div>
        <div>
          {!!iagnosticInfo?.applicant &&
            !!iagnosticInfo?.processor &&
            `原${iagnosticInfo?.applicant?.nickname || ''}应处理任务，由${iagnosticInfo?.processor?.nickname || ''}转派`}
        </div>
      </div>
      <div
        className="flex items-center bg-white px-4 py-2 mb-2"
        onClick={() => {
          H5Bridge.navigator.push({
            pathname: 'DiagnosisDetail',
            title: ' ',
            message: {
              id: iagnosticInfo?.id,
              score: iagnosticInfo?.score,
              start: iagnosticInfo?.start,
              end: iagnosticInfo?.end,
              level: iagnosticInfo?.alarmLevel,
              isNoDistribute: true,
              shopId: iagnosticInfo?.shop?.shopId || '',
              shopName: iagnosticInfo?.shop?.shopName || '',
            },
          });
        }}
      >
        <div className="flex-1 text-base">诊断明细</div>
        <div className="flex items-center">
          <span className="text-primary"> 查看详情</span>
          <RightOutline className="text-[#c5c5c5]  text-sm " />
        </div>
      </div>
    </Loading>
  );
});
const PatrolInfo: React.FC<BASICINFO> = (props: BASICINFO) => {
  const { patrolDetail } = props || {};
  const [phonePopupVisible, setPhonePopupVisible] = useState<boolean>(false);
  const navigate = useNavigate();

  const lastSubmitDate = useMemo(() => {
    const lastPatrolTime = patrolDetail?.lastPatrolDate;
    if (!lastPatrolTime) {
      return '无';
    }
    const str = dayjs(lastPatrolTime).format('MM-DD');
    const dayStr = formatDiff(dayjs(), dayjs(lastPatrolTime));
    return `${str}（${dayStr}）`;
  }, [patrolDetail]);
  const { run: runGetReportId } = useRequest(() => getParentReportId(patrolDetail?.taskId), {
    manual: true,
    onSuccess: (res) => {
      if (res?.existReport) {
        navigate(`/patrol/reportdetail?taskId=${res?.parentId}`);
      } else {
        Toast.show('原报告不存在！');
      }
    },
  });
  return (
    <React.Fragment>
      <div className="leading-[14px] mb-2">
        {patrolDetail?.subType === patrolTypeText.诊断任务 ? (
          <IagnosticInfoCard patrolDetail={patrolDetail} />
        ) : (
          <div className="bg-white text-sm font-medium p-4">
            <div>
              <div className="inline-block w-6 h-6 text-sm font-medium text-center text-white bg-orange-500 rounded mr-2">
                巡
              </div>
              {`${patrolDetail?.shopId} ${patrolDetail?.shopName}`}
            </div>
            <div className="text-[#858585] py-3 px-2 mt-3 bg-[#FAFAFA] rounded-lg">
              <div className="">
                负责人员：
                <span>
                  {patrolDetail?.shopManagerName}({patrolDetail?.shopManagerPhone})
                </span>
              </div>
              <div className="mt-2">
                上次巡检： {lastSubmitDate}
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 得分：
                {typeof patrolDetail?.lastPatrolScore === 'number' ? `${patrolDetail?.lastPatrolScore}分` : '无'}
              </div>
              {patrolDetail?.subType === patrolTypeText.食安稽核到店辅导 && (
                <React.Fragment>
                  <div className="mt-2">
                    任务执行时段：{dayjs(patrolDetail?.beginTime).format('YYYY/MM/DD HH:mm')}~
                    {dayjs(patrolDetail?.expiredTime).format('YYYY/MM/DD HH:mm')}
                  </div>
                  <div className="mt-2">巡检人:{patrolDetail?.treatedUserName}</div>
                  <div
                    className="mt-2 text-primary"
                    onClick={() => {
                      runGetReportId();
                    }}
                  >
                    原食安稽核报告
                  </div>
                </React.Fragment>
              )}
            </div>
            <div className="py-1 mt-1  flex justify-end items-center">
              <div
                className="text-primary  flex  items-center"
                onClick={() => {
                  setPhonePopupVisible(true);
                }}
              >
                <span className="ml-1">联系店长</span>
                <IconFont type="icon-rongqi" className="text-xs" />
              </div>
            </div>
          </div>
        )}
      </div>
      <PhonePopup
        title="联系方式"
        shopId={patrolDetail?.shopId}
        visible={phonePopupVisible}
        onClose={() => setPhonePopupVisible(false)}
      />
    </React.Fragment>
  );
};
export default PatrolInfo;
