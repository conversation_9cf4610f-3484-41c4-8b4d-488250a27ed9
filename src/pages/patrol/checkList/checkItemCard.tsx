import React, { useMemo } from 'react';
import { QuestionTags } from '@src/components/OptionsDetail';
import SampleImage from '@src/components/sampleImage';
import { yytTaskStatusCNToEnEnum } from '@src/pages/tasks/enum';
import { PatrolStore } from '@src/store';
import { observer } from 'mobx-react';
import ButtonGroup from './buttonGroup';
import { RectificationType } from './const';
import { LocalDescription } from './LocalDescription';
import { CheckItem } from '../api.type';
import { patrolTypeText } from '../planDetail/detailCard';

interface Props {
  data: CheckItem;
  updateItem(payload: any): void;
  showTips?(data: { title: string; desc: string }): void;
  showStandard?(standard: string): void;
  showSop?(sopId: number): void;
  onClick?(): void;
  shopManagerName?: string;
}

const CheckItemCard: React.FC<Props> = observer((props: Props) => {
  const { updateItem, data, showStandard, showSop, shopManagerName } = props;
  console.log(data, '=data111');
  const { taskStatus, subType } = PatrolStore;
  const { patrolWorksheetId, itemId, itemSOP, standard, sopId, accentedTermTags, sampleImageUrls } = data;
  const isShowOprBtn = useMemo(() => {
    if (subType === patrolTypeText.诊断任务) {
      return taskStatus !== yytTaskStatusCNToEnEnum.待开始;
    } else {
      return ![yytTaskStatusCNToEnEnum.已取消, yytTaskStatusCNToEnEnum.已过期].includes(
        taskStatus as yytTaskStatusCNToEnEnum,
      );
    }
  }, [taskStatus, subType]);

  const renderLocalDescription = (data: any) => {
    if (data?.qualified === false && data?.hasRectify) {
      if (
        data?.rectifyType === RectificationType.LOCAL ||
        (data?.rectifyType === RectificationType.PATROL_USER_CHOICE &&
          data?.actualRectifyType === RectificationType.LOCAL)
      ) {
        return <LocalDescription data={data} />;
      }
    }

    return null;
  };

  return (
    <div className="p-3 pb-4 mb-[10px] bg-[#FFF] rounded-[4px]">
      <div className="flex justify-center items-center mt-1 mb-2">
        <div className="flex-1 flex flex-wrap gap-1">
          <QuestionTags accentedTermTags={accentedTermTags || []} />
        </div>
        {!!sopId && (
          <div
            className="text-primary mr-2"
            onClick={() => {
              showSop?.(sopId);
            }}
          >
            参考标准
          </div>
        )}
        {!!standard && (
          <div
            className="text-primary"
            onClick={() => {
              showStandard?.(standard);
            }}
          >
            评分标准
          </div>
        )}
      </div>
      <div className="text-sm leading-[22px] break-all">{itemSOP}</div>
      {!!sampleImageUrls?.length && <SampleImage SampleimageList={sampleImageUrls || []} />}
      {isShowOprBtn && (
        <div>
          <ButtonGroup
            shopManagerName={shopManagerName}
            updateItem={(item) => {
              updateItem({
                ...item,
                patrolWorksheetId,
                patrolWorksheetItemId: itemId,
              });
            }}
            data={props.data}
          />
          {renderLocalDescription(props.data)}
        </div>
      )}
    </div>
  );
});
export default CheckItemCard;
