import { useState } from 'react';
import VideoViewer from '@src/components/VideoViewer';
import { Image, ImageViewer } from 'antd-mobile';
import { PlayOutline } from 'antd-mobile-icons';

interface FilePrevProps {
  file: {
    url: string;
    type: string;
    id: string;
    snapshotUrl?: string;
  }[];
}

export const FilePrev = ({ file }: FilePrevProps) => {
  const [videoPreviewUrl, setVideoPreviewUrl] = useState('');
  const [videoPreviewVisible, setVideoPreviewVisible] = useState(false);
  return (
    <>
      <div className="flex flex-wrap gap-2">
        {file?.map((item: any) => {
          const isImg = item?.contentType?.startsWith('image') || item?.type?.startsWith('IMG');
          if (isImg) {
            return (
              <Image
                onClick={(e) => {
                  e.stopPropagation();
                  ImageViewer.show({ image: item?.url });
                }}
                width="70px"
                height="70px"
                className="rounded-lg"
                src={item?.snapshotUrl || item?.url}
              />
            );
          }

          return (
            <div
              onClick={(e) => {
                e.stopPropagation();
                setVideoPreviewVisible(true);
                setVideoPreviewUrl(item.url);
              }}
              className="relative"
            >
              <video
                style={{ objectFit: 'cover' }}
                poster={item?.snapshotUrl}
                className="w-[70px] h-[70px] border"
                src={item.url}
              />
              <div className="absolute left-[50%] top-[50%] -translate-x-[50%] -translate-y-[50%]">
                <PlayOutline fontSize={40} />
              </div>
            </div>
          );
        })}
      </div>
      <VideoViewer
        visible={videoPreviewVisible}
        url={videoPreviewUrl}
        onClose={() => {
          setVideoPreviewVisible(false);
        }}
      />
    </>
  );
};
