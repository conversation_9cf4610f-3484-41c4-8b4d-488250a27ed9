import React, { RefObject, useEffect, useMemo, useRef, useState } from 'react';
import * as Sentry from '@sentry/react';
import { report } from '@src/common/report';
import { IconFont } from '@src/components';
import { IPopupPage } from '@src/components/IPopup';
import { GlobalStore, PatrolStore, userStore } from '@src/store';
import { navigator } from '@tastien/rn-bridge/lib/h5';
import { Select } from 'antd';
import { Button, Checkbox, DatePicker, type DatePickerRef, Form, Radio, Space, TextArea } from 'antd-mobile';
import dayjs from 'dayjs';
import { observer } from 'mobx-react';
import { EXPLAIN } from './buttonGroup';
import { CopyUserSelect } from './components/copyUserSelect';
import SystemImageUpload from './components/systemImageUpload';
import { RectificationType } from './const';
import styles from './index.module.scss';
import TaskPhotos from '../taskPhotos';

// eslint-disable-next-line react-refresh/only-export-components
export const scoreRange = Array.from({ length: 100 }, (_, ind) => {
  return { label: ind + 1, value: ind + 1 };
});
export const LimitRange = Array.from({ length: 30 }, (_, ind) => {
  return { label: ind + 1, value: ind + 1 };
});
interface Props {
  explain: EXPLAIN;
  setExplain: (visible: boolean, isPassBtn: boolean, isSubmitScore: boolean) => void;
  initData: any;
  submit: (data: any) => void;
  shopManagerName?: string;
}

enum ConfigDeadlineEnum {
  到店巡检不合格问题项的默认整改期限 = 'IN_STORE_INSPECTION_NOT_QUALIFIED_ISSUE_ITEM_DEFAULT_DEADLINE',
  视频云巡检不合格问题项的默认整改期限 = 'REPORT_VIDEO_NOT_QUALIFIED_ISSUE_ITEM_DEFAULT_RECTIFY_DEADLINE',
  食安稽核到店辅导不合格问题项的默认整改期限 = 'FOOD_SAFETY_ARRIVE_SHOP_TASK_REPORT_RECTIFY_DEFAULT_DURATION',
}

const Explain: React.FC<Props> = observer((props: Props) => {
  const { initData, explain, setExplain, submit } = props;
  const { cachedConfig } = GlobalStore;
  const { subType } = PatrolStore;
  const { userInfo } = userStore;
  const { taskId, shopId } = PatrolStore;
  const [taskPhotosMsg, setTaskPhotosMsg] = useState<{
    taskId: number | null;
    visible: boolean;
  }>({
    taskId: null,
    visible: false,
  });
  const [upLoading, setUpLoading] = useState<boolean>(false);
  const {
    nonconformityReasons,
    mustChooseNonconformityReasons,
    notQualifiedMustUpload,
    qualifiedMustUpload,
    /* 是否需要整改 */
    hasRectify,
    /* 整改方式 */
    rectifyType,
  } = initData;

  const [form] = Form.useForm();
  const isOther = Form.useWatch('reasons', form);
  const selectImages = Form.useWatch('images', form);
  const hasCycleRectify = Form.useWatch('hasCycleRectify', form);
  const actualRectifyType = Form.useWatch('actualRectifyType', form);
  const isLocalRectifyAndNoCycle = actualRectifyType === RectificationType.LOCAL && hasCycleRectify === 0;

  // useEffect(() => {
  // useEffect(() => {
  //   form.setFieldsValue({
  //     reasons: initData?.reasons || initData?.selectReasons || [],
  //     itemRemark: initData?.itemRemark,
  //     reformLimit: initData?.reformLimit
  //       ? [initData?.reformLimit]
  //       : [+cachedConfig?.IN_STORE_INSPECTION_NOT_QUALIFIED_ISSUE_ITEM_DEFAULT_DEADLINE || 3],
  //     images: initData?.imageURLS || initData?.imageList || [],
  //   });
  // }, [initData, cachedConfig]);

  // 整改期限天数
  const subTypeDeadlineDay = useMemo(() => {
    if (!subType || !cachedConfig) {
      return null;
    }

    const day = {
      VIDEO: cachedConfig[ConfigDeadlineEnum.视频云巡检不合格问题项的默认整改期限],
      FOOD_SAFETY_ARRIVE_SHOP: JSON.parse(
        cachedConfig[ConfigDeadlineEnum.食安稽核到店辅导不合格问题项的默认整改期限] || '{}',
      )?.timeInterval,
    }[subType];

    const dayNum = day ?? cachedConfig[ConfigDeadlineEnum.到店巡检不合格问题项的默认整改期限] ?? 3;

    return +dayNum;
  }, [cachedConfig, subType]);

  const isAlbumUpImg: boolean = useMemo(() => {
    if (initData.allowAlbum === 'ALLOW') {
      return true;
    }
    if (initData.allowAlbum === 'FOLLOWING_SYSTEM') {
      return cachedConfig?.ALLOW_ALBUM === '1';
    }
    return false;
  }, [cachedConfig, initData.allowAlbum]);
  const isFromPassBtn: boolean = useMemo(() => {
    // 是否合格
    return explain.isPassBtn;
  }, [explain]);
  const isShowOther = useMemo(() => {
    return isOther && isOther?.includes(-1);
  }, [isOther]);

  const visibleRef = useRef(explain.visible);
  visibleRef.current = explain.visible;

  useEffect(() => {
    const messageHandler = (event: any) => {
      const messageData = JSON.parse(event.data)?.data;

      if (messageData?.type === 'gallery' && messageData?.itemId === initData?.itemId && !!visibleRef.current) {
        form.setFieldsValue({
          images: messageData?.files,
        });
      }
    };
    document.addEventListener('message', messageHandler);
    window.addEventListener('message', messageHandler);
    return () => {
      document.removeEventListener('message', messageHandler);
      window.removeEventListener('message', messageHandler);
    };
  }, []);
  return (
    <React.Fragment>
      <IPopupPage
        DocumentTitle="情况说明"
        visible={explain.visible}
        onClose={() => {
          setExplain(false, explain.isPassBtn, explain.isSubmitScore);
        }}
        footer={
          <div className="p-3 bg-white">
            <Button
              onClick={async () => {
                const fields = await form.validateFields();
                submit(fields);
                setExplain(false, explain.isPassBtn, explain.isSubmitScore);
              }}
              color="primary"
              loading={upLoading}
              fill="solid"
              block
              className="h-[45px] text-base"
            >
              确定
            </Button>
          </div>
        }
      >
        {explain.visible && (
          <Form
            initialValues={{
              reasons: initData?.reasons
                ? initData?.otherReason
                  ? [...(initData?.reasons || []), -1]
                  : [...(initData?.reasons || [])]
                : initData?.otherReason
                  ? [...(initData?.selectReasons || []), -1]
                  : initData?.selectReasons || [],
              otherReason: initData?.otherReason,
              itemRemark: initData?.itemRemark,
              reformLimit: initData?.reformLimit
                ? [initData?.reformLimit]
                : /* // 如果是食安稽核到店辅导，整改期限需特殊处理
                  subType === 'FOOD_SAFETY_ARRIVE_SHOP'
                  ? [
                      +JSON.parse(cachedConfig?.FOOD_SAFETY_ARRIVE_SHOP_TASK_REPORT_RECTIFY_DEFAULT_DURATION || '{}')
                        ?.timeInterval || 3,
                    ]
                  : [+cachedConfig?.IN_STORE_INSPECTION_NOT_QUALIFIED_ISSUE_ITEM_DEFAULT_DEADLINE || 3] */ [
                    subTypeDeadlineDay || 3,
                  ],
              images: initData?.imageURLS || initData?.imageList || [],
              copyUserIds: initData?.copyUserIds || initData?.copyUsers?.map((v: { id: number }) => v?.id) || [],
              actualRectifyType: initData?.actualRectifyType,
              hasCycleRectify: !!initData?.hasCycleRectify ? 1 : 0,
              rectifyCycleType: initData?.rectifyCycleType,
              rectifyCycleEndTime: initData?.rectifyCycleEndTime ? new Date(initData?.rectifyCycleEndTime) : undefined,
            }}
            layout="vertical"
            form={form}
            className={styles.IForm}
          >
            <div>
              {!!nonconformityReasons?.length && !isFromPassBtn && (
                <Form.Item
                  label={<div className="text-base text-[#141414] font-medium">不合格原因</div>}
                  name="reasons"
                  rules={[
                    {
                      required: mustChooseNonconformityReasons,
                      message: '请选择不合格原因',
                    },
                  ]}
                >
                  <Checkbox.Group>
                    <Space direction="vertical">
                      {nonconformityReasons
                        ?.filter((i: any) => !!i)
                        ?.map((v: string) => {
                          return (
                            <Checkbox
                              style={{
                                '--font-size': '14px',
                                '--icon-size': '16px ',
                                color: '#5E5E5E',
                              }}
                              key={v}
                              value={v}
                            >
                              {v}
                            </Checkbox>
                          );
                        })}
                      <Checkbox
                        style={{
                          '--font-size': '14px',
                          '--icon-size': '16px ',
                        }}
                        value={-1}
                      >
                        其他
                      </Checkbox>
                    </Space>
                  </Checkbox.Group>
                </Form.Item>
              )}
              {isShowOther && (
                <Form.Item
                  name="otherReason"
                  label={<div className="text-base text-[#141414] font-medium">其他原因</div>}
                  rules={[
                    {
                      required: true,
                      message: '请输入其他原因',
                    },
                  ]}
                >
                  <TextArea
                    className="bg-[#FAFAFA] p-1"
                    placeholder="请输入其他不合格原因"
                    style={{
                      '--font-size': '14px',
                      '--color': '#B8B8B8',
                      height: '40px',
                    }}
                    maxLength={50}
                  />
                </Form.Item>
              )}
              <div className="flex justify-between">
                <div className="flex-1">
                  <Form.Item
                    label={<div className="text-base text-[#141414] font-medium">照片</div>}
                    name="images"
                    rules={[
                      {
                        required: isFromPassBtn ? !!qualifiedMustUpload : !!notQualifiedMustUpload,
                        message: '请上传照片',
                      },
                      {
                        validator: (_, value) => {
                          if (
                            (isFromPassBtn ? !!qualifiedMustUpload : !!notQualifiedMustUpload) &&
                            (!value || value.length < initData?.minimumImages || 0)
                          ) {
                            Sentry.captureException('上传图片未达到最小限制', {
                              extra: {
                                taskType: '巡检任务',
                              },
                              tags: {
                                errorType: 'minimumImages',
                                minimumImagesTaskType: '巡检任务',
                              },
                            });
                            report({
                              type: 'ability',
                              page: 'explain',
                              abilityButton: '上传图片未达到最小限制',
                              ext: JSON.stringify({
                                instant: dayjs().format('YYYY-MM-DD HH:mm:ss'),
                                userId: userInfo?.userId,
                                username: userInfo?.nickName,
                                mobile: userInfo?.phone,
                                shopId,
                              }),
                            });
                            return Promise.reject(`请至少上传${initData?.minimumImages}张照片`);
                          }
                          return Promise.resolve();
                        },
                      },
                    ]}
                  >
                    <SystemImageUpload
                      isAlbumUpImg={isAlbumUpImg}
                      setUpLoading={setUpLoading}
                      shopId={shopId}
                      FileUploadType="PATROL"
                    />
                  </Form.Item>
                </div>
                {selectImages?.length < 9 && (
                  <div
                    className="text-primary mt-3 mr-4 "
                    onClick={() => {
                      // setTaskPhotosMsg({ visible: true, taskId: taskId! });
                      navigator.push({
                        pathname: 'SupervisorGallery',
                        message: {
                          taskId: +taskId!,
                          isUpload: false,
                          situationItemImages: selectImages,
                          shopId,
                          itemId: initData?.itemId,
                        },
                      });
                    }}
                  >
                    任务图库
                  </div>
                )}
              </div>
              <Form.Item name="itemRemark" label={<div className="text-base text-[#141414] font-medium">详细说明</div>}>
                <TextArea
                  className="bg-[#FAFAFA] p-1"
                  placeholder="输入详细的情况说明，最多140字"
                  style={{
                    '--font-size': '14px',
                    '--color': '#B8B8B8',
                    height: '80px',
                  }}
                  maxLength={140}
                />
              </Form.Item>
            </div>
            <div className="h-[10px] bg-[#F5F5F5]" />
            {!explain.isPassBtn && hasRectify && rectifyType === RectificationType.PATROL_USER_CHOICE && (
              <>
                <Form.Item
                  label={<div className="text-base text-[#141414] font-medium">整改方式:</div>}
                  layout="horizontal"
                  className={styles.IFormItem}
                  name="actualRectifyType"
                  rules={[
                    {
                      required: true,
                      message: '请选择整改方式',
                    },
                  ]}
                >
                  <Radio.Group>
                    <Space direction="horizontal">
                      <Radio value={RectificationType.SHOP}>门店整改</Radio>
                      <Radio value={RectificationType.LOCAL}>当场整改</Radio>
                    </Space>
                  </Radio.Group>
                </Form.Item>
                <Form.Item
                  label={<div className="text-base text-[#141414] font-medium">是否需要循环整改:</div>}
                  layout="horizontal"
                  className={styles.IFormItem}
                  name="hasCycleRectify"
                  initialValue={0}
                  rules={[
                    {
                      required: true,
                      message: '请选择是否需要循环整改',
                    },
                  ]}
                >
                  <Radio.Group>
                    <Space direction="horizontal">
                      <Radio value={0}>不需要</Radio>
                      <Radio value={1}>需要</Radio>
                    </Space>
                  </Radio.Group>
                </Form.Item>
                {!!hasCycleRectify && (
                  <>
                    <Form.Item
                      label={<div className="text-base text-[#141414] font-medium">循环整改频次:</div>}
                      layout="horizontal"
                      className={styles.IFormItem}
                      name="rectifyCycleType"
                      rules={[
                        {
                          required: true,
                          message: '请选择循环整改频次',
                        },
                      ]}
                    >
                      <Select
                        options={[
                          {
                            label: '每日一次',
                            value: 'EVERY_DAY',
                          },
                          {
                            label: '每周一次',
                            value: 'EVERY_WEEK',
                          },
                        ]}
                        style={{
                          width: '120px',
                        }}
                      />
                    </Form.Item>
                    <Form.Item
                      name="rectifyCycleEndTime"
                      label={<div className="text-base text-[#141414] font-medium">截止时间:</div>}
                      layout="horizontal"
                      trigger="onConfirm"
                      onClick={(e, datePickerRef: RefObject<DatePickerRef>) => {
                        datePickerRef.current?.open();
                      }}
                      rules={[
                        {
                          required: true,
                          message: '请选择截止时间',
                        },
                      ]}
                    >
                      <DatePicker min={dayjs().toDate()}>
                        {(value) => (value ? dayjs(value).format('YYYY-MM-DD') : '请选择日期')}
                      </DatePicker>
                    </Form.Item>
                  </>
                )}
                <div className="h-[10px] bg-[#F5F5F5]" />
              </>
            )}
            {!explain.isPassBtn && hasRectify && rectifyType !== RectificationType.LOCAL && (
              <React.Fragment>
                {!isLocalRectifyAndNoCycle && (
                  <Form.Item
                    layout="horizontal"
                    childElementPosition="right"
                    name="reformLimit"
                    trigger="onConfirm"
                    label={
                      <div className="flex items-center">
                        <IconFont type="icon-hourglass" className="mr-2 text-[20px] text-[#00BBB4]" />
                        <span className="text-base text-[#141414]">整改期限</span>
                      </div>
                    }
                    /* onClick={(_, PickerRef: RefObject<PickerRef>) => {
                      PickerRef.current?.open();
                    }} */
                  >
                    <span className="text-[#CCCCCC]">{subTypeDeadlineDay || 3}天</span>
                    {/* <Picker columns={[LimitRange]}>
                      {(value) => {
                        return !!value?.length ? (
                          <span className="text-[#CCCCCC]">{value[0]?.value}天</span>
                        ) : (
                          <span className="text-[#CCCCCC]">未选择</span>
                        );
                      }}
                    </Picker> */}
                  </Form.Item>
                )}
                {actualRectifyType !== RectificationType.LOCAL && (
                  <>
                    <Form.Item
                      layout="horizontal"
                      childElementPosition="right"
                      name="watchId"
                      label={
                        <div className="flex items-center">
                          <IconFont type="icon-calendar-user" className="mr-2 text-[20px] text-[#00BBB4]" />
                          <span className="text-base text-[#141414]">跟进人</span>
                        </div>
                      }
                    >
                      门店所有员工
                    </Form.Item>
                    <Form.Item
                      layout="horizontal"
                      childElementPosition="right"
                      name="copyUserIds"
                      label={
                        <div className="flex items-center">
                          <IconFont type="icon-calendar-edit" className="mr-2 text-[20px] text-[#00BBB4]" />
                          <span className="text-base text-[#141414]">抄送人</span>
                        </div>
                      }
                    >
                      <CopyUserSelect />
                    </Form.Item>
                  </>
                )}
              </React.Fragment>
            )}
          </Form>
        )}
      </IPopupPage>
      <TaskPhotos
        isSelectMode
        TaskPhotosMsg={taskPhotosMsg}
        initSelectImages={selectImages}
        onOK={(value) => {
          if (value?.length > 0) {
            form.setFieldsValue({
              images: value,
            });
          }
        }}
        setTaskPhotosMsg={() => {
          setTaskPhotosMsg({ visible: false, taskId: null });
        }}
      />
    </React.Fragment>
  );
});
export default Explain;
