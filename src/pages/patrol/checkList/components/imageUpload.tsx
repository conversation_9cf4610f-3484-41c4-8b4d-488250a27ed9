import { Image, ImageViewer, Space, Toast, DotLoading } from 'antd-mobile';
import { AddOutline, CloseOutline } from 'antd-mobile-icons';
import { uploadFile } from '@src/utils/utils';
import { Upload, UploadFile, UploadProps } from 'antd';
import { useMemo, useState } from 'react';
import React from 'react';
import VideoViewer from '@src/components/VideoViewer';
import { uploadImage, uploadVideo } from '@src/common/api';
import { TWatermark } from '@src/common/api.type';
import PLAYVIDEO from '@src/assets/images/PLAYVIDEO.png';
export const ImgUploader: React.FC<UploadProps> = ({ fileList, onChange, ...props }) => {
  const [videoPreviewUrl, setVideoPreviewUrl] = useState('');
  const [videoPreviewVisible, setVideoPreviewVisible] = useState(false);
  const files = useMemo(() => {
    const imageFiles: UploadFile<any>[] = [];
    fileList?.forEach((i) => {
      imageFiles.push(i);
    });
    return imageFiles;
  }, [fileList]);
  console.log(files, '=files');
  return (
    <React.Fragment>
      <Space direction="horizontal" wrap style={{ '--gap': '4px' }}>
        {files.map((i, index) => {
          const isImg = i.type?.startsWith('image') || i.type?.startsWith('IMG');
          let url = i?.url || i.response?.url;
          return (
            <div
              key={index}
              className="flex relative items-center text-[#858585] text-sm rounded-sm leading-[14px] bg-[#F5F5F5]"
            >
              {i?.status === 'uploading' ? (
                <DotLoading color="primary" className="text-xs" />
              ) : isImg ? (
                <Image
                  onClick={() => {
                    ImageViewer.show({ image: url });
                  }}
                  src={url}
                  style={{ width: 80, height: 80 }}
                  fit="cover"
                />
              ) : (
                <video
                  onClick={() => {
                    setVideoPreviewVisible(true);
                    setVideoPreviewUrl(url);
                  }}
                  preload="auto"
                  poster={PLAYVIDEO}
                  className="w-[80px] h-[80px]"
                  src={url}
                />
              )}
              <CloseOutline
                className="absolute right-0 top-0"
                onClick={(e) => {
                  e.stopPropagation();
                  onChange?.({
                    file: i,
                    fileList: (fileList || []).filter((file) => file.uid !== i.uid),
                  });
                }}
              />
            </div>
          );
        })}
        <Upload
          fileList={fileList}
          onChange={(e) => {
            console.log(e, '=e');

            onChange && onChange(e);
          }}
          multiple
          id="myUpload"
          showUploadList={false}
          beforeUpload={(file) => {
            const isImgOrVideo = file.type.startsWith('image') || file.type.startsWith('video');
            console.log(file, '=file');

            if (!isImgOrVideo) {
              Toast.show('只能上传图片或视频文件');
            }
            const isLt20M = file.size / 1024 / 1024 <= 100;
            if (!isLt20M) {
              Toast.show('上传文件不可大于 100M');
            }
            return (isImgOrVideo && isLt20M) || Upload.LIST_IGNORE;
          }}
          customRequest={async ({ file, onSuccess, onError }) => {
            try {
              const res = await uploadFile(file as File, false, true);
              console.log(res, '=res');

              let params = { bucket: res?.bucket!, key: res?.key, originName: res?.originName };

              const result = file?.type?.startsWith('image')
                ? await uploadImage(params, TWatermark.default)
                : await uploadVideo(params);
              console.log(result, '=result');
              onSuccess?.(result);
            } catch (error) {
              Toast.show('上传失败，请重新上传');
              onError?.(error as Error);
            }
          }}
          {...props}
        >
          <div
            style={{
              alignItems: 'center',
            }}
            className="w-[80px]  h-[80px] flex justify-center  align-middle rounded-[4px] bg-[#F5F5F5] border-solid border border-[#DCDCDC]"
          >
            <AddOutline fontSize={24} color="#B8B8B8" />
          </div>
        </Upload>
      </Space>
      <VideoViewer
        visible={videoPreviewVisible}
        url={videoPreviewUrl}
        onClose={() => {
          setVideoPreviewVisible(false);
        }}
      />
    </React.Fragment>
  );
};
