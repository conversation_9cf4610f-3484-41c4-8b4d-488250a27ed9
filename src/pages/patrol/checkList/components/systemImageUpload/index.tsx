import { useEffect, useState } from 'react';
import { FileBusinessTypeMap, uploadImage } from '@src/common/api';
import { TWatermark } from '@src/common/api.type';
import VideoViewer from '@src/components/VideoViewer';
import { uploadFile } from '@src/utils/utils';
import { H5Bridge } from '@tastien/rn-bridge';
import { Col, Row } from 'antd';
import { Dialog, Image, ImageUploadItem, ImageViewer, Popup, SafeArea, Toast } from 'antd-mobile';
import { AddOutline, CloseCircleOutline, PlayOutline } from 'antd-mobile-icons';
import { isString } from 'lodash';
import styles from './index.module.scss';

// base64转文件流
export function base64ToFile(base64Data: any, filename: string) {
  // 将base64的数据部分提取出来
  const parts = base64Data.split(';base64,');
  const contentType = parts[0]?.split(':')[1];
  const raw = window.atob(parts[1]);

  // 将原始数据转换为Uint8Array
  const uInt8Array = new Uint8Array(raw.length);
  for (let i = 0; i < raw.length; ++i) {
    uInt8Array[i] = raw.charCodeAt(i);
  }

  // 使用Blob和提取出的数据内容创建一个新的Blob对象
  const blob = new Blob([uInt8Array], { type: contentType });

  // 创建一个指向Blob对象的URL，并使用这个URL创建一个File对象
  // const blobUrl = URL.createObjectURL(blob);
  let formatFileName = filename.split('/');
  if (formatFileName.length < 2) {
    formatFileName = [...formatFileName, '.png'];
  }
  if (formatFileName.length > 2) {
    const name = formatFileName.slice(0, -1).join('/');
    formatFileName = [name, formatFileName[formatFileName.length - 1]];
  }
  const file = new File([blob], formatFileName.join('.'), {
    type: contentType,
  });
  return file;
}
interface ImgFieldProps {
  value?: [];
  onChange?: (e: any) => void;
  setUpLoading: (val: boolean) => void;
  isAlbumUpImg?: boolean;
  shopId?: string;
  FileUploadType: FileBusinessTypeMap;
}
interface VideoProps {
  contentType: string;
  snapshotUrl: string;
  fileType: string;
  id: string;
  name: string;
  url: string;
}
// eslint-disable-next-line react-refresh/only-export-components
export default ({ value, onChange, isAlbumUpImg, setUpLoading, shopId, FileUploadType }: ImgFieldProps) => {
  const [showFileList, setFileList] = useState<
    {
      url: string;
      id: string;
      contentType?: string;
      type: string;
      snapshotUrl?: string;
    }[]
  >([]);
  const [visible, setVisible] = useState<boolean>(false);
  // const [imageViewerVisible, setImageViewerVisible] = useState<boolean>(false);
  // const [defaultIndex, setDefaultIndex] = useState<number>(0);
  const [videoPreviewUrl, setVideoPreviewUrl] = useState('');
  const [videoPreviewVisible, setVideoPreviewVisible] = useState(false);
  const [location, setLocation] = useState<{ latitude?: number; longitude?: number } | undefined>();

  useEffect(() => {
    // 通知APP获取定位
    if (window?.['ReactNativeWebView']?.['postMessage']) {
      // 安卓
      window['ReactNativeWebView']['postMessage'](JSON.stringify({ getLocation: true }));
    } else {
      // ios
      window.parent.postMessage(JSON.stringify({ getLocation: true }), '*');
    }
    function handleEvent(e: any) {
      if (e.data && isString(e.data)) {
        try {
          const result = JSON.parse(e.data);

          if (result?.location) {
            setLocation(result?.location);
          }
        } catch (error) {
          console.log('error :>> ', error);
        }
      }
    }
    document.addEventListener('message', handleEvent); // 安卓端监听
    window.addEventListener('message', handleEvent); // ios端监听
    return () => {
      document.removeEventListener('message', handleEvent);
      window.addEventListener('message', handleEvent);
    };
  }, []);

  const onhandUploadFile = async (file: File, mime: string, chromaticValue?: string) => {
    try {
      const res: any = await uploadFile(
        base64ToFile(`data:image/jpeg;base64,${file}`, mime) as File,
        false,
        true,
        FileUploadType as any,
      );
      const params = {
        chromaticValue,
        bucket: res?.bucket!,
        key: res?.key,
        originName: res?.originName,
        shopId,
        ...location,
      };

      const result = await uploadImage(params, shopId ? TWatermark.extra : TWatermark.default);

      console.log(result, '=result');
      onChange?.([...showFileList, { url: result.url, id: result.id, contentType: 'image', type: 'IMG' }]);
      setFileList([...showFileList, { url: result.url, id: result.id, contentType: 'image', type: 'IMG' }]);

      Toast.clear();
    } catch (e) {
      console.log(e, '=上传失败信息');
    } finally {
      setUpLoading(false);
    }
  };
  console.log(showFileList, '=showFileList');

  useEffect(() => {
    if (Array.isArray(value)) {
      setFileList(value);
    }
    return () => {
      Toast.clear();
      setUpLoading(false);
    };
  }, [value]);

  const openPicker = async () => {
    // 图库选择
    try {
      Toast.show({
        icon: 'loading',
        content: '上传中...',
        duration: 0,
      });
      setUpLoading(true);
      const {
        data: {
          images: { data, mime },
        },
      } = await H5Bridge.imagePicker.openPicker({
        mlutiple: false,
      });
      await onhandUploadFile(data, mime);
    } catch (e) {
      console.log(e, '=上传失败信息');
    } finally {
      Toast.clear();
      setUpLoading(false);
    }
  };

  const openCamera = async () => {
    // 相机拍照
    try {
      Toast.show({
        icon: 'loading',
        content: '上传中...',
        duration: 0,
      });
      setUpLoading(true);
      const {
        data: {
          image: { data, mime, chromaticValue },
        },
      }: any = await H5Bridge.customPostMessage.customPostMessage({
        module: 'image-picker',
        method: 'openCamera',
        params: {
          checkSolidColor: true,
        },
      });
      console.log(chromaticValue, '=chromaticValue相机拍照');
      await onhandUploadFile(data, mime, chromaticValue);
    } catch (e) {
      console.log(e, '=上传失败信息');
      Toast.clear();
    } finally {
      Toast.clear();
      setUpLoading(false);
    }
  };

  const openVideo = async () => {
    // 相机拍摄
    try {
      Toast.show({
        icon: 'loading',
        content: '上传中...',
        duration: 0,
      });
      setUpLoading(true);
      const data: any = await H5Bridge.customPostMessage.customPostMessage({
        module: 'image-picker',
        method: 'openCameraVideo',
      });
      console.log(data, '摄像回调data');
      const result: VideoProps = data?.data?.video?.data || {};
      console.log(result, '=result');
      onChange?.([
        ...showFileList,
        {
          url: result.url,
          id: result.id,
          contentType: result?.contentType,
          snapshotUrl: result?.snapshotUrl,
          type: 'VIDEO',
        },
      ]);
      setFileList([
        ...showFileList,
        {
          url: result.url,
          id: result.id,
          contentType: result?.contentType,
          snapshotUrl: result?.snapshotUrl,
          type: 'VIDEO',
        },
      ]);

      Toast.clear();
    } catch (e) {
      console.log(e, '=上传失败信息');
      Toast.clear();
    } finally {
      Toast.clear();
      setUpLoading(false);
    }
  };

  window.addEventListener('offline', function () {
    console.log('网络已断开连接！');
    Toast.show('网络连接已断开！');
    // Toast.clear();
  });

  const deletePic = (item: ImageUploadItem, e: any) => {
    e.stopPropagation();
    Dialog.confirm({
      content: <div className="text-center text-[#141414] text-base">确定删除？</div>,
      onConfirm: async () => {
        const fileListAfterDelete = showFileList.filter((o) => o.url !== item.url);
        onChange?.(fileListAfterDelete);
        setFileList(fileListAfterDelete);
      },
    });
  };

  return (
    <div className={styles.imageUploader}>
      <Row gutter={[12, 12]}>
        {showFileList?.map((item, index) => {
          const isImg = item?.contentType?.startsWith('image') || item?.type?.startsWith('IMG');
          return (
            <Col key={item.url}>
              <div className={styles.box}>
                {isImg ? (
                  <Image
                    onClick={() => {
                      ImageViewer.show({ image: item?.url });
                    }}
                    width="100%"
                    height="100%"
                    className="rounded-lg"
                    src={item?.snapshotUrl || item?.url}
                  />
                ) : (
                  <div
                    onClick={() => {
                      setVideoPreviewVisible(true);
                      setVideoPreviewUrl(item.url);
                    }}
                  >
                    <video
                      style={{ objectFit: 'cover' }}
                      poster={item?.snapshotUrl}
                      className="w-[70px] h-[70px] border"
                      src={item.url}
                    />
                    <div className="absolute left-[50%] top-[50%] -translate-x-[50%] -translate-y-[50%]">
                      <PlayOutline fontSize={40} />
                    </div>
                  </div>
                )}
                <div className={styles.deleteIcon} onClick={(e) => deletePic(item, e)}>
                  <CloseCircleOutline />
                </div>
              </div>
            </Col>
          );
        })}
        {showFileList.length >= 9 ? null : (
          <Col>
            <div
              className={`${styles.box} ${styles.addBtn}`}
              onClick={() => {
                setVisible(true);
              }}
            >
              <AddOutline style={{ fontSize: 12 }} />
            </div>
          </Col>
        )}
        {/* {imageViewerVisible && (
          <ImageViewer.Multi
            visible={imageViewerVisible}
            defaultIndex={defaultIndex}
            images={showFileList.map((i) => i.url)}
            onClose={() => setImageViewerVisible(false)}
          />
        )} */}
      </Row>
      <Popup
        visible={visible}
        bodyClassName={styles.pop}
        onMaskClick={() => {
          setVisible(false);
        }}
      >
        <div
          className={styles.takePhoto}
          onClick={() => {
            setVisible(false);
            setTimeout(() => {
              openCamera();
            }, 500);
          }}
        >
          拍照
        </div>
        <div
          className={styles.takePhoto}
          onClick={() => {
            setVisible(false);
            setTimeout(() => {
              openVideo();
            }, 500);
          }}
        >
          录像
        </div>
        {isAlbumUpImg && (
          <div
            className={styles.uploadPhoto}
            onClick={() => {
              setVisible(false);
              openPicker();
            }}
          >
            从手机相册选择
          </div>
        )}
        <div className={styles.line} />
        <div className={styles.cancelBtn} onClick={() => setVisible(false)}>
          取消
        </div>
        <SafeArea position="bottom" />
      </Popup>
      <VideoViewer
        visible={videoPreviewVisible}
        url={videoPreviewUrl}
        onClose={() => {
          setVideoPreviewVisible(false);
        }}
      />
    </div>
  );
};
