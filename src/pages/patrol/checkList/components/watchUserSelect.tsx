import { useEffect, useMemo, useState } from 'react';
import { IPopupPage } from '@src/components/IPopup';
import { CustomerFormFieldWrapper } from '@src/components/CustomerFormFieldWrapper';
import { Button, Radio, SearchBar } from 'antd-mobile';
import { useRequest } from 'ahooks';

import { Loading } from '@src/components';
import { UserBase, getShopAndShopEmpByTaskApi } from '../../api';

interface CopyUserFieldProps {
  value?: any;
  onChange?: any;
  taskId: number;
  shopManagerName?: string;
}

export const WatchUserSelect = ({
  value,
  onChange,
  taskId,
  shopManagerName,
}: CopyUserFieldProps) => {
  const [visible, setVisible] = useState(false);
  const [data, setData] = useState<UserBase[]>([]);
  const [searchValue, setSearchValue] = useState<string>('');

  const [selected, setSelected] = useState<number>();
  console.log(value, '=value');

  const { loading } = useRequest(
    async () => {
      if (visible) {
        let res = await getShopAndShopEmpByTaskApi({ taskId });
        // 处理一下展示，每个人后面跟上角色名称
        const shopEmployees = res?.shopEmployees?.map((v) => ({
          ...v,
          label: `${v.userName}(${v.roleName})`,
        }));
        return {
          ...res,
          shopEmployees,
        };
      }
      return [];
    },

    {
      refreshDeps: [visible],
      onSuccess: (data) => {
        console.log(data, '=data');

        setData(data?.shopEmployees || []);
      },
    },
  );
  useEffect(() => {
    if (visible) {
      setSelected(value);
    }
  }, [visible]);
  const dataSource = useMemo(() => {
    let dataSource = data;

    if (searchValue) {
      dataSource = dataSource?.filter((item) => {
        const searchName = item.userName
          ?.toLocaleLowerCase()
          .includes(searchValue?.toLocaleLowerCase());
        return searchName;
      });
    }
    return dataSource;
  }, [data, searchValue]);

  return (
    <CustomerFormFieldWrapper
      value={!!value ? `已选择` : shopManagerName ? shopManagerName : undefined}
      onShow={() => {
        setVisible(true);
      }}
      placeholder="请选择"
    >
      <IPopupPage
        visible={visible}
        onClose={() => {
          setVisible(false);
        }}
        DocumentTitle="选择跟进人"
        footer={
          <div className="flex">
            {/* <div className="flex-grow pt-3 pl-4 pb-[.375rem] flex flex-col justify-between">
              <div className="flex items-center text-sm gap-[.375rem]">
                已选跟进人:
                <span className="text-primary text-base font-medium">{selected.length}人</span>
              </div>
            </div> */}
            <Button
              onClick={() => {
                history.back();
                onChange?.(selected);
              }}
              color="primary"
              fill="solid"
              block
              style={{ padding: '1.1875rem 2.5625rem', '--border-radius': '0px' }}
            >
              确认选择
            </Button>
          </div>
        }
      >
        <Loading spinning={loading}>
          <div className="px-4 py-3 sticky top-0 left-0 bg-white">
            <SearchBar
              placeholder="搜索跟进人"
              style={{
                '--height': '40px',
              }}
              onSearch={(val) => {
                setSearchValue(val);
              }}
              onClear={() => {
                setSearchValue('');
              }}
            />
          </div>
          <div className="flex flex-col h-full  px-4">
            {visible && (
              <Radio.Group
                onChange={(RadioValue) => {
                  setSelected(RadioValue);
                }}
                value={selected}
              >
                {dataSource?.map((v) => {
                  return (
                    <div key={v?.userId} className="flex items-center  justify-between py-4">
                      <div className="text-dark text-15 grow truncate">{v?.label}</div>
                      <Radio value={v?.userId} />
                    </div>
                  );
                })}
              </Radio.Group>
            )}
          </div>
        </Loading>
      </IPopupPage>
    </CustomerFormFieldWrapper>
  );
};
