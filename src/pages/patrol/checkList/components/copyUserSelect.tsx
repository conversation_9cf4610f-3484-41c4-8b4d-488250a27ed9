import { useEffect, useMemo, useState } from 'react';
import { IPopupPage } from '@src/components/IPopup';
import { CustomerFormFieldWrapper } from '@src/components/CustomerFormFieldWrapper';
import { Button, Checkbox, SearchBar } from 'antd-mobile';
import { useRequest, useSelections } from 'ahooks';
import { Loading } from '@src/components';
import { UserBase } from '../../api';
import { getAllUserBase } from '@src/pages/mission/api';

interface CopyUserFieldProps {
  value?: any;
  onChange?: any;
}

export const CopyUserSelect = ({ value, onChange }: CopyUserFieldProps) => {
  const [visible, setVisible] = useState(false);
  const [data, setData] = useState<UserBase[]>([]);
  const [searchValue, setSearchValue] = useState<string>('');

  const { selected, isSelected, toggle, setSelected } = useSelections(
    data?.map((item) => item?.userId),
    [],
  );

  const { loading } = useRequest(
    async () => {
      if (visible) {
        let res = await getAllUserBase({
          bizType: 14,
          roleCategory: 1,
          // nickname: searchText,
        });
        return res;
      }
      return [];
    },

    {
      refreshDeps: [visible],
      onSuccess: (data) => {
        console.log(data, '=data');
        //过滤重复的抄送人数据
        const filteredArray: any = [];
        for (let i = 0; i < data.length; i++) {
          const currentObject = data[i];
          const isDuplicate = filteredArray.some((obj) => {
            return obj?.userId === currentObject?.userId;
          });

          if (!isDuplicate) {
            filteredArray.push(currentObject);
          }
        }
        console.log(filteredArray, '=filteredArray');
        setData(filteredArray || []);
      },
    },
  );
  useEffect(() => {
    if (visible) {
      // 更新选中态
      setSelected(value?.map((o: any) => o) || []);
    }
  }, [visible]);
  const dataSource = useMemo(() => {
    let dataSource = data;

    if (searchValue) {
      dataSource = dataSource?.filter((item) => {
        const searchName = item?.nickname.includes(searchValue);
        return searchName;
      });
    }
    return dataSource;
  }, [data, searchValue]);
  console.log(data, '=data');

  return (
    <CustomerFormFieldWrapper
      value={!!value?.length ? `已选择${value?.length}人` : undefined}
      onShow={() => {
        setVisible(true);
      }}
      placeholder="请选择"
    >
      <IPopupPage
        visible={visible}
        onClose={() => {
          setVisible(false);
        }}
        DocumentTitle="选择抄送人"
        footer={
          <div className="flex">
            <div className="flex-grow pt-3 pl-4 pb-[.375rem] flex flex-col justify-between">
              <div className="flex items-center text-sm gap-[.375rem]">
                已选抄送人:
                <span className="text-primary text-base font-medium">{selected?.length}人</span>
                {/* <UpOutline className="text-primary text-base" /> */}
              </div>
            </div>
            <Button
              onClick={() => {
                history.back();
                onChange?.(selected);
              }}
              color="primary"
              fill="solid"
              style={{ padding: '1.1875rem 2.5625rem', '--border-radius': '0px' }}
            >
              确认选择
            </Button>
          </div>
        }
      >
        <Loading spinning={loading}>
          <div className="px-4 py-3 sticky top-0 left-0 bg-white">
            <SearchBar
              placeholder="搜索抄送人"
              style={{
                '--height': '40px',
              }}
              onSearch={(val) => {
                setSearchValue(val);
              }}
              onClear={() => {
                setSearchValue('');
              }}
            />
          </div>
          <div className="flex flex-col h-full  px-4">
            {visible &&
              dataSource?.map((v) => {
                return (
                  <div
                    key={v?.userId}
                    className="flex items-center  justify-between py-4"
                    onClick={() => {
                      if (isSelected(v?.userId)) {
                        setSelected((pre) => pre.filter((item) => item !== v?.userId));
                      } else {
                        setSelected((pre) => [...pre, v?.userId]);
                      }
                    }}
                  >
                    <div className="text-dark text-15 grow truncate">{v?.nickname}</div>
                    <Checkbox
                      className="mr-4 mt-1"
                      style={{
                        '--icon-size': '18px',
                        '--font-size': '24px',
                      }}
                      checked={isSelected(v?.userId)}
                    />
                  </div>
                );
              })}
          </div>
        </Loading>
      </IPopupPage>
    </CustomerFormFieldWrapper>
  );
};
