import React, { useState } from 'react';
import { IconFont } from '@src/components';
import { formatDateToUTC } from '@src/utils/utils';
import { Picker } from 'antd-mobile';
import cn from 'classnames';
import dayjs from 'dayjs';
import _ from 'lodash';
import { observer } from 'mobx-react';
import Explain from './explain';
import styles from './index.module.scss';
import { CheckItem } from '../api.type';

const scoreTypeText = {
  REDUCE_SCORE: '扣',
  ADD_SCORE: '得',
};

export enum buttonTypeEnum {
  判断型 = 'JUDGE',
  打分型 = 'SCORE',
}
export enum scoreTypeEnum {
  得分项 = 'ADD_SCORE',
  扣分项 = 'REDUCE_SCORE',
}
export const scoreRange = (scoreType: scoreTypeEnum, scoreLimit: number) => {
  return Array.from({ length: scoreLimit }, (_, ind) => {
    return scoreType === scoreTypeEnum.得分项 ? { label: ind, value: ind } : { label: ind + 1, value: ind + 1 };
  });
};
export const LimitRange = Array.from({ length: 30 }, (_, ind) => {
  return { label: ind + 1, value: ind + 1 };
});
interface Props {
  data: CheckItem;
  updateItem(data: {
    score?: number | null;
    qualified: boolean | null;
    hasApply: boolean;
    itemId?: number;
    reasons?: string;
    otherReason?: string;
    images?: any;
    itemRemark?: string;
    reformLimit?: number;
    copyUserIds?: number[];
    watchId?: number;
  }): void;
  shopManagerName?: string;
}
export type EXPLAIN = {
  visible: boolean;
  isPassBtn: boolean;
  isSubmitScore: boolean;
};

const ButtonGroup: React.FC<Props> = observer((props: Props) => {
  const [explain, setExplain] = useState<EXPLAIN>({
    visible: false,
    isPassBtn: false,
    isSubmitScore: false,
  });
  const [scoreVisible, setScoreVisible] = useState(false);
  const [submitScore, setSubmitScore] = useState();
  const { updateItem, data, shopManagerName } = props;
  const {
    type, // JUDGE：判断型，SCORE：打分型
    notQualifiedButtonCustomName,
    qualifiedButtonCustomName,
    fullScore,
    scoreType, // ADD_SCORE：得分项，REDUCE_SCORE：扣分项
    score,
    hasApply,
    qualified,
    scoreMax,
    scoreMin,
    itemId,
    itemRemark,
    qualifiedMustUpload,
    reportDisplayScore,
  } = data;
  const [applyShow, setApplyShow] = useState(hasApply === false);
  const notQualified =
    _.isNumber(score) &&
    ((scoreType === scoreTypeEnum.得分项 && score < fullScore) || (scoreType === scoreTypeEnum.扣分项 && score < 0));
  const scoreText = notQualified ? `${scoreType === scoreTypeEnum.扣分项 ? '扣' : ''}${score}分` : '打分';

  return (
    <React.Fragment>
      {type === buttonTypeEnum.判断型 ? ( // 判断型
        <div className={styles.operationArea}>
          <span
            role="button"
            onClick={() => {
              setExplain({
                visible: true,
                isPassBtn: false,
                isSubmitScore: false,
              });
            }}
            style={{ width: applyShow ? '32%' : '42%' }}
            // className={`${styles.active} ${!hasApply || qualified !== false ? '' : styles.fail}`}
            className={cn(styles.active, {
              [styles.fail]: hasApply && qualified === false,
            })}
          >
            {notQualifiedButtonCustomName || '不合格'}
            {fullScore === 0 || !reportDisplayScore
              ? ''
              : scoreType === scoreTypeEnum.扣分项
                ? `（扣${fullScore}分）`
                : '（0分）'}
          </span>
          <span
            role="button"
            style={{ width: applyShow ? '32%' : '42%' }}
            onClick={() => {
              if (qualifiedMustUpload) {
                setExplain({
                  visible: true,
                  isPassBtn: true,
                  isSubmitScore: false,
                });
              } else {
                setExplain({
                  visible: false,
                  isPassBtn: true,
                  isSubmitScore: false,
                });
                updateItem({
                  score: scoreMax,
                  qualified: true,
                  hasApply: true, // 改为适用
                });
              }

              setApplyShow(false);
            }}
            className={`${styles.active} ${!hasApply || !qualified ? '' : styles.pass}`}
          >
            {qualifiedButtonCustomName || '合格'}
            {fullScore === 0 || !reportDisplayScore
              ? ''
              : scoreType === scoreTypeEnum.扣分项
                ? '（不扣分）'
                : `（${fullScore}分）`}
          </span>
          {applyShow ? (
            <span
              style={{ width: '32%' }}
              className={`${styles.active} ${typeof hasApply === 'boolean' && !hasApply ? styles.pass : ''}`}
              onClick={() => {
                updateItem({
                  hasApply: false,
                  qualified: null, // 是否合格恢复为不选
                });
              }}
            >
              不适用
            </span>
          ) : (
            <div
              onClick={() => {
                setApplyShow(true);
              }}
            >
              <IconFont type="icon-menu-horizontal" />
            </div>
          )}
        </div>
      ) : (
        <div className={styles.operationArea}>
          <span
            role="button"
            style={{ width: applyShow ? '32%' : '42%' }}
            className={`${styles.active} ${!hasApply || !notQualified ? '' : styles.fail}`}
            onClick={() => {
              setExplain({
                visible: false,
                isPassBtn: false,
                isSubmitScore: true,
              });
              setScoreVisible(true);
            }}
          >
            {scoreText}
          </span>
          <span
            role="button"
            style={{ width: applyShow ? '32%' : '42%' }}
            className={`${styles.active} ${!hasApply || !qualified ? '' : styles.pass}`}
            onClick={() => {
              if (qualifiedMustUpload) {
                setExplain({
                  visible: true,
                  isPassBtn: true,
                  isSubmitScore: false,
                });
              } else {
                setExplain({
                  visible: false,
                  isPassBtn: true,
                  isSubmitScore: false,
                });
                updateItem({
                  score: scoreMax,
                  qualified: true,
                  hasApply: true, // 改为适用
                });
              }

              setApplyShow(false);
            }}
          >
            {scoreType === scoreTypeEnum.扣分项 ? '合格（不扣分）' : `合格（${fullScore}分）`}
          </span>
          {applyShow ? (
            <span
              style={{ width: '32%' }}
              className={`${styles.active} ${typeof hasApply === 'boolean' && !hasApply ? styles.pass : ''}`}
              onClick={() => {
                updateItem({
                  hasApply: false,
                  score: null, // 打分恢复默认
                  qualified: null, // 是否合格恢复为不选
                });
              }}
            >
              不适用
            </span>
          ) : (
            <div
              onClick={() => {
                setApplyShow(true);
              }}
            >
              <IconFont type="icon-menu-horizontal" />
            </div>
          )}
        </div>
      )}
      <div className="flex flex-col">
        {_.isNumber(score) && hasApply && (
          <div
            className="bg-[#FAFAFA] my-1 p-1"
            onClick={() => {
              setExplain({
                visible: true,
                isPassBtn: !notQualified,
                isSubmitScore: explain.isSubmitScore,
              });
            }}
          >
            <div className="break-all mb-2">{itemRemark || '情况说明'}</div>
          </div>
        )}
      </div>

      <Picker
        columns={[
          [{ label: scoreTypeText[scoreType], value: 0 }],
          scoreRange(scoreType, scoreType === scoreTypeEnum.得分项 ? scoreMax : Math.abs(scoreMin)),
          [{ label: '分', value: 0 }],
        ]}
        visible={scoreVisible}
        onClose={() => {
          setScoreVisible(false);
        }}
        onConfirm={(v) => {
          setExplain({
            visible: true,
            isPassBtn: false,
            isSubmitScore: true,
          });
          setSubmitScore(v?.[1] as any);
        }}
      />
      <Explain
        explain={explain}
        shopManagerName={shopManagerName}
        setExplain={(e: boolean, isPass: boolean, isSubmit: boolean) => {
          setExplain({
            visible: e,
            isPassBtn: isPass,
            isSubmitScore: isSubmit,
          });
        }}
        submit={(callBackData: any) => {
          const { actualRectifyType, hasCycleRectify, rectifyCycleType, rectifyCycleEndTime } = callBackData;
          console.log(callBackData, '=回调参数');
          let score;
          let qualified: boolean;
          if (explain.isSubmitScore) {
            score = scoreType === scoreTypeEnum.扣分项 ? +`-${submitScore}` : submitScore;
          } else if (type === buttonTypeEnum.打分型 && !explain.isPassBtn) {
            score = scoreMin;
          } else if (!explain.isPassBtn) {
            score = scoreMin;
          } else {
            score = data?.score;
          }
          if (explain.isPassBtn) {
            qualified = true;
            score = scoreMax;
          } else if (!explain.isPassBtn) {
            qualified = false;
          } else {
            // eslint-disable-next-line prefer-destructuring
            qualified = data.qualified;
          }
          updateItem({
            itemId,
            reformLimit: callBackData?.reformLimit?.[0],
            hasApply: true,
            qualified,
            itemRemark: callBackData?.itemRemark,
            reasons: callBackData?.reasons,
            otherReason: callBackData?.otherReason,
            score,
            images: callBackData?.images,
            copyUserIds: callBackData?.copyUserIds,
            watchId: callBackData?.watchId,
            actualRectifyType,
            hasCycleRectify: !!hasCycleRectify,
            rectifyCycleType,
            rectifyCycleEndTime: rectifyCycleEndTime
              ? formatDateToUTC(dayjs(rectifyCycleEndTime).endOf('day'))
              : undefined,
          });
          setApplyShow(false);
        }}
        initData={data}
      />
    </React.Fragment>
  );
});
export default ButtonGroup;
