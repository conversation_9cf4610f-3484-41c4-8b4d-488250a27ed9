import { useState } from 'react';
import Spot from '@src/assets/images/spot.png';
import VideoViewer from '@src/components/VideoViewer';
import { PatrolStore } from '@src/store';
import { observer } from 'mobx-react';
import { LocalExplain } from './LocalExplain';
import { localRectify } from '../api';

export const LocalDescription = observer(({ data }: { data: any }) => {
  const { updateWorkSheet } = PatrolStore;
  const [localExplainVisible, setLocalExplainVisible] = useState(false);
  const [videoPreviewUrl, setVideoPreviewUrl] = useState('');
  const [videoPreviewVisible, setVideoPreviewVisible] = useState(false);
  return (
    <>
      <div>
        <div className="flex gap-x-2 items-center">
          <img src={Spot} alt="" className="size-3" />
          <span className="text-[#3D3D3D] text-sm">当场整改情况：</span>
        </div>
        <div
          className="bg-[#eee] p-2"
          onClick={() => {
            setLocalExplainVisible(true);
          }}
        >
          {!!data?.rectifyReason || !!data?.rectifyImages?.length ? (
            <div className="flex flex-col gap-2">
              <span className="break-all">{data?.rectifyReason || '当场整改情况'}</span>
              {/* <div className="flex flex-wrap gap-2">
                {data?.rectifyImageUrls?.map((item: any) => {
                  const isImg = item?.contentType?.startsWith('image') || item?.type?.startsWith('IMG');
                  if (isImg) {
                    return (
                      <Image
                        onClick={(e) => {
                          e.stopPropagation();
                          ImageViewer.show({ image: item?.url });
                        }}
                        width="70px"
                        height="70px"
                        className="rounded-lg"
                        src={item?.snapshotUrl || item?.url}
                      />
                    );
                  }

                  return (
                    <div
                      onClick={(e) => {
                        e.stopPropagation();
                        setVideoPreviewVisible(true);
                        setVideoPreviewUrl(item.url);
                      }}
                      className="relative"
                    >
                      <video
                        style={{ objectFit: 'cover' }}
                        poster={item?.snapshotUrl}
                        className="w-[70px] h-[70px] border"
                        src={item.url}
                      />
                      <div className="absolute left-[50%] top-[50%] -translate-x-[50%] -translate-y-[50%]">
                        <PlayOutline fontSize={40} />
                      </div>
                    </div>
                  );
                })}
              </div> */}
            </div>
          ) : (
            <span className="text-red-500">请添加当场整改情况</span>
          )}
        </div>
      </div>
      <LocalExplain
        visible={localExplainVisible}
        onClose={() => setLocalExplainVisible(false)}
        initData={data}
        onFinish={async (value) => {
          try {
            await localRectify({
              id: data?.itemId,
              reason: value?.reason,
              images: (value.images?.filter((v: any) => !!v) || [])?.map((v: any) => v?.id || v?.response?.id),
            });
            setLocalExplainVisible(false);
            updateWorkSheet({
              localExplainTypePayload: {
                rectifyReason: value?.reason,
                rectifyImages: (value.images?.filter((v: any) => !!v) || [])?.map((v: any) => v?.id || v?.response?.id),
              },
              patrolWorksheetId: data?.checkListId,
              checkListTypeId: data?.checkListTypeId,
              itemId: data?.itemId,
            } as any);
          } catch {}
        }}
      />
      <VideoViewer
        visible={videoPreviewVisible}
        url={videoPreviewUrl}
        onClose={() => {
          setVideoPreviewVisible(false);
        }}
      />
    </>
  );
});
