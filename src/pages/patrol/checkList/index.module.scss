.Tabs {
  :global {
    .adm-tabs-header {
      border-bottom: none;
    }
  }
}
.activeColor {
  color: #000000;
  font-weight: 500;
}
.Collapse {
  :global {
    .adm-list-body-inner {
      .adm-list-item {
        padding-left: 16px;
      }
    }
    .adm-collapse-panel-content-inner {
      .adm-list-item {
        background-color: #fff;
        padding-left: 8px;
        .adm-list-item-content {
          padding-right: 8px;
          border-color: none;
          .adm-list-item-content-main {
            padding: 10px 0 0 0;
          }
        }
      }
    }
    .adm-collapse-arrow {
      font-size: 12px;
      color: #b8b8b8;
    }
    .adm-list-default .adm-list-body {
      border-top: none;
    }
  }
}
.operationArea {
  display: flex;
  justify-content: space-between;
  margin-top: 12px;

  > span {
    height: 32px;
    // width: 32%;
    font-size: 14px;
    line-height: 32px;
    border-radius: 4px;
    font-weight: 400px;
    border: 1px solid;
    text-align: center;
    cursor: pointer;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .pass {
    color: #378bff;
    border-color: #378bff;

    &.active {
      color: #fff;
      background-color: #378bff;
    }
  }

  .fail {
    color: #f53f3f;
    border-color: #f53f3f;

    &.active {
      color: #fff;
      background-color: #f53f3f;
    }
  }
}
.Selector {
  :global {
    .adm-selector-item {
      text-align: left;
    }
  }
}
.IForm {
  :global {
    .adm-list-item-content-arrow {
      font-size: 12px;
    }
  }
}
.IFormItem {
  :global {
    .adm-list-item-content-prefix {
      width: auto;
      flex: none;
      padding-right: var(--prefix-padding-right);
    }
  }
}
