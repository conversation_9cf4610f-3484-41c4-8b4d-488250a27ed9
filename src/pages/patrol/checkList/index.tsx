import React, { useEffect, useMemo, useState } from 'react';
import { IconFont, Loading } from '@src/components';
import { IPopup } from '@src/components/IPopup';
import SopPopup from '@src/components/OptionsDetail/SopPopup';
import StandardPopup from '@src/components/OptionsDetail/StandardPopup';
import { PatrolStore } from '@src/store';
import { useRequest } from 'ahooks';
import { Checkbox, Collapse, SearchBar, Selector, Tabs, TextArea } from 'antd-mobile';
import cn from 'classnames';
import { toJS } from 'mobx';
import { observer } from 'mobx-react';
import CheckItemCard from './checkItemCard';
import styles from './index.module.scss';
import { updateChecklist } from '../api';
import { DetailItem } from '../api.type';
interface Props {
  taskId: number;
  workSheetList: DetailItem[];
  shopManagerName?: string | undefined;
  showSummary?: boolean;
  summary?: string;
  subType?: string;
  taskStatus?: string;
  onSearchModeChange?: (val: boolean) => void;
  setSummary?: (val: string) => void;
  updateSummary?: () => void;
}
const CheckList: React.FC<Props> = observer((props: Props) => {
  const { taskId, workSheetList, summary, setSummary, updateSummary, showSummary, shopManagerName } = props;
  const [groupId, setGroupId] = useState(-1);
  const [tabPopupVisible, setTabPopupVisible] = useState(false);
  const [sopPopup, setSopPopup] = useState<{
    visible: boolean;
    sopId: number | undefined;
  }>({
    visible: false,
    sopId: undefined,
  });
  const [standardPopup, setStandardPopup] = useState<{
    visible: boolean;
    standard: any;
  }>({
    visible: false,
    standard: undefined,
  });
  const [curSheetId, setCurSheetId] = useState<number>();
  const [isOPenLeft, setIsOPenLeft] = useState(true);
  const [searchMode, setSearchMode] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const [collapseKeys, setCollapseKeys] = useState([]);
  useEffect(() => {
    setCurSheetId(workSheetList[0]?.patrolWorksheetId);
    if (workSheetList?.length === 1) {
      setIsOPenLeft(false);
    }
  }, [workSheetList[0]?.patrolWorksheetId]);
  const curWorkSheet = useMemo(() => {
    return workSheetList?.find((item) => item?.patrolWorksheetId === curSheetId);
  }, [curSheetId, workSheetList]);
  // 只看未填写项（未打分）
  const [unfinished, setUnFinished] = useState<boolean>(false);
  const { updateWorkSheet } = PatrolStore;
  const { run } = useRequest(
    async (payload) => {
      console.log('🚀 ~ payload:', payload);
      const imagesList = payload.images?.filter((v: any) => !!v) || [];
      const res = await updateChecklist({
        taskId,
        itemList: [
          {
            hasApply: true,
            ...payload,
            patrolWorksheetId: curSheetId,
            images: imagesList?.map((v: any) => v?.id || v?.response?.id),
            // eslint-disable-next-line eqeqeq
            reasons: payload?.reasons?.filter((v: any) => v != -1),
          },
        ],
      });
      return {
        ...res,
        patrolWorksheetId: curSheetId,
        payload,
        imageURLS: payload.images,
      };
    },
    {
      manual: true,
      onSuccess: (data: any) => {
        updateWorkSheet(data);
      },
    },
  );
  const list = useMemo(() => {
    if (!curWorkSheet) return [];

    let groups = (toJS(curWorkSheet.children) || []).filter((v) =>
      searchMode || groupId === -1 ? v.patrolWorksheetCategoryId !== 0 : v.patrolWorksheetCategoryId === groupId,
    );
    if ((searchMode && !searchValue) || !groups.length) return [];

    // 过滤未填写项（未打分）+ 没有选择适用
    console.log(toJS(groups), '=groups');

    if (unfinished) {
      groups = groups.map((v) => ({
        ...v,
        children: v.children.filter((v2) => !v2.hasFillItem),
      }));
    }

    const data: Array<(any & { listType: 'item' }) | (any & { listType: 'group' })> = [];
    for (let i = 0, len = groups.length; i < len; i++) {
      !searchMode && data.push({ ...groups[i], listType: 'group' });

      const items = groups[i].children || [];

      for (const item of items) {
        if (!searchMode || (item.itemSOP && item.itemSOP.toUpperCase().includes(searchValue.toUpperCase()))) {
          data.push({ ...item, listType: 'item' });
        }
      }
    }

    return data;
  }, [curWorkSheet, groupId, searchMode, searchValue, unfinished]);
  console.log(list, '=list');

  useEffect(() => {
    const init: any = curWorkSheet?.children?.map((v) => {
      return `${v?.patrolWorksheetCategoryId}`;
    });
    // setGroupId(-1);
    setCollapseKeys(init);
  }, [curWorkSheet]);
  return (
    <Loading spinning={false}>
      {searchMode ? (
        <div className="">
          <div className="px-4 py-2 bg-[#fff]">
            <SearchBar
              placeholder="请输入内容"
              showCancelButton={() => true}
              onChange={(val) => {
                console.log(val, '=val');

                setSearchValue(val);
              }}
              onCancel={() => {
                setSearchValue('');
                setSearchMode(false);
              }}
            />
          </div>
          {list?.map((data: any) => {
            return (
              <CheckItemCard
                key={data?.itemId}
                data={toJS(data)}
                updateItem={(item) => {
                  run(item);
                }}
                showStandard={(value) => {
                  setStandardPopup({
                    visible: true,
                    standard: value,
                  });
                }}
                showSop={(value: any) => {
                  setSopPopup({
                    visible: true,
                    sopId: value,
                  });
                }}
              />
            );
          })}
        </div>
      ) : (
        <React.Fragment>
          <div className="flex bg-white">
            <div className=" bg-[#F8F8F8]">
              {isOPenLeft && (
                <div>
                  {workSheetList?.map((v) => {
                    return (
                      <div
                        className={cn('p-4 w-[100px]', {
                          'bg-white': v.patrolWorksheetId === curSheetId,
                        })}
                        key={v.patrolWorksheetId}
                        onClick={() => {
                          setCurSheetId(v.patrolWorksheetId);
                          setGroupId(-1);
                        }}
                      >
                        {v.patrolWorksheetName}
                      </div>
                    );
                  })}
                </div>
              )}

              <div
                className="absolute left-1 bottom-20 z-10"
                onClick={() => {
                  setIsOPenLeft(!isOPenLeft);
                }}
              >
                <IconFont type="icon-shouqi" className="text-[#378BFF]" />
              </div>
            </div>
            <div className="flex-1 flex-col overflow-y-scroll ">
              <div className="leading-[14px]">
                <div className="flex p-4 pb-3 justify-between items-center">
                  <div className="text-base leading-[16px] font-medium">明细</div>
                  <div className="flex items-center">
                    <div className="flex justify-between items-center">
                      <Checkbox
                        style={{
                          '--icon-size': '18px',
                          '--font-size': '24px',
                          '--gap': '6px',
                        }}
                        checked={unfinished}
                        onChange={(checked) => {
                          setUnFinished(checked);
                        }}
                      >
                        <div className="text-sm text-[#5E5E5E]">未填</div>
                      </Checkbox>
                    </div>
                    <div
                      className="flex items-center ml-2 text-[#5E5E5E] bg-[#F5F5F5] px-2 py-1 rounded-[48px]"
                      onClick={() => {
                        setSearchMode(true);
                      }}
                    >
                      <IconFont type="icon-search" className="text-base" />
                      <span className="text-sm ">搜索</span>
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-white flex items-center pl-1">
                <Tabs
                  className={styles.Tabs}
                  activeKey={groupId as any}
                  onChange={(value) => setGroupId(+value as number)}
                >
                  <Tabs.Tab
                    title={
                      <div
                        className={cn('flex text-sm items-center leading-[14px] text-[#858585]', {
                          [styles.activeColor]: groupId === -1,
                        })}
                      >
                        全部
                        <span>
                          ({curWorkSheet?.filledCount}/{curWorkSheet?.itemCount})
                        </span>
                      </div>
                    }
                    key={-1}
                  />
                  {(curWorkSheet?.children || [])?.map((item) => (
                    <Tabs.Tab
                      title={
                        <div
                          className={cn('flex text-sm items-center leading-[14px] text-[#858585]', {
                            [styles.activeColor]: +item?.patrolWorksheetCategoryId === +groupId,
                          })}
                        >
                          <div className="max-w-[100px]  text-ellipsis overflow-hidden whitespace-nowrap">
                            {item.patrolWorksheetCategoryName}
                          </div>
                          <div>
                            ({item.filledCount}/{item.itemCount})
                          </div>
                        </div>
                      }
                      key={item.patrolWorksheetCategoryId}
                    />
                  ))}
                </Tabs>
                {curWorkSheet && curWorkSheet?.children?.length > 2 && (
                  <>
                    <div className="bg-[rgba(0,0,0,0.03)]" />
                    <div
                      className="flex h-full justify-center items-center px-3"
                      onClick={() => setTabPopupVisible(true)}
                    >
                      <IconFont type="icon-menu" className="text-[#858585] text-sm" />
                    </div>
                  </>
                )}
              </div>
              <div>
                {
                  <Collapse
                    className={styles.Collapse}
                    onChange={(e: any) => {
                      setCollapseKeys(e);
                    }}
                    activeKey={collapseKeys}
                  >
                    {list?.map((data) => {
                      return (
                        data?.listType === 'group' && (
                          <Collapse.Panel
                            key={`${data?.patrolWorksheetCategoryId}`}
                            title={
                              <div className="text-[#141414] text-sm leading-[14px] font-medium  max-w-[200px]  text-ellipsis overflow-hidden whitespace-nowrap ">
                                {data?.patrolWorksheetCategoryName} {data.filledCount}/{data.itemCount}
                              </div>
                            }
                          >
                            {data?.children?.map((item: any) => {
                              return (
                                <div className="border-b border-solid border-[#F0F0F0]">
                                  <CheckItemCard
                                    key={item.itemId}
                                    data={toJS(item)}
                                    updateItem={run}
                                    shopManagerName={shopManagerName}
                                    showStandard={(value) => {
                                      setStandardPopup({
                                        visible: true,
                                        standard: value,
                                      });
                                    }}
                                    showSop={(value: any) => {
                                      setSopPopup({
                                        visible: true,
                                        sopId: value,
                                      });
                                    }}
                                  />
                                </div>
                              );
                            })}
                          </Collapse.Panel>
                        )
                      );
                    })}
                  </Collapse>
                }
              </div>
              {showSummary && (
                <div className="bg-white p-4">
                  <div className="text-base leading-[16px] font-medium mb-2">总结</div>
                  <TextArea
                    className="bg-[#FAFAFA] p-1"
                    disabled={!taskId}
                    value={summary}
                    onChange={(v) => {
                      setSummary && setSummary(v);
                    }}
                    onBlur={() => {
                      updateSummary && updateSummary();
                    }}
                    placeholder="请输入"
                    style={{
                      '--font-size': '14px',
                      '--color': '#B8B8B8',
                      height: '80px',
                    }}
                    maxLength={300}
                  />
                </div>
              )}
            </div>
          </div>
        </React.Fragment>
      )}
      <IPopup title="分类" visible={tabPopupVisible} onClose={() => setTabPopupVisible(false)}>
        <div className="pt-5 px-4 pb-10">
          <Selector
            style={{
              '--checked-color': 'var(--color-primary-1)',
              '--border-radius': '4px',
              fontSize: '14px',
            }}
            className={styles.Selector}
            value={[groupId]}
            onChange={(value) => setGroupId(value[0])}
            columns={1}
            showCheckMark={false}
            options={
              curWorkSheet?.children?.map((i) => ({
                label: `${i.patrolWorksheetCategoryName}`,
                value: i.patrolWorksheetCategoryId,
              })) as any
            }
          />
        </div>
      </IPopup>
      <StandardPopup
        title="评分标准"
        visible={standardPopup.visible}
        standard={standardPopup.standard}
        onClose={() => {
          setStandardPopup({
            visible: false,
            standard: undefined,
          });
        }}
      />
      <SopPopup
        sopId={sopPopup.sopId}
        title="参考标准"
        visible={sopPopup.visible}
        onClose={() => {
          setSopPopup({
            visible: false,
            sopId: undefined,
          });
        }}
      />
    </Loading>
  );
});
export default CheckList;
