import { useEffect, useMemo, useRef, useState } from 'react';
import { IPopupPage } from '@src/components/IPopup';
import { GlobalStore, PatrolStore } from '@src/store';
import { navigator } from '@tastien/rn-bridge/lib/h5';
import { Button, Form, TextArea, Toast } from 'antd-mobile';
import { observer } from 'mobx-react';
import SystemImageUpload from './components/systemImageUpload';
import styles from './index.module.scss';

interface LocalExplainProps {
  visible: boolean;
  onClose: () => void;
  initData: any;
  onFinish: (value: any) => void;
}

export const LocalExplain = observer(({ visible, onClose, initData, onFinish }: LocalExplainProps) => {
  const { reformMustUpload } = initData;

  const visibleRef = useRef(visible);
  visibleRef.current = visible;

  const [form] = Form.useForm();
  const { cachedConfig } = GlobalStore;
  const [upLoading, setUpLoading] = useState<boolean>(false);
  const { taskId, shopId } = PatrolStore;
  const selectImages = Form.useWatch('images', form);
  const isAlbumUpImg: boolean = useMemo(() => {
    if (initData.allowAlbum === 'ALLOW') {
      return true;
    }
    if (initData.allowAlbum === 'FOLLOWING_SYSTEM') {
      return cachedConfig?.ALLOW_ALBUM === '1';
    }
    return false;
  }, [cachedConfig, initData.allowAlbum]);

  useEffect(() => {
    const messageHandler = (event: any) => {
      const messageData = JSON.parse(event.data)?.data;

      if (messageData?.type === 'gallery' && messageData?.itemId === initData?.itemId && !!visibleRef.current) {
        form.setFieldsValue({
          images: messageData?.files,
        });
      }
    };
    document.addEventListener('message', messageHandler);
    window.addEventListener('message', messageHandler);
    return () => {
      document.removeEventListener('message', messageHandler);
      window.removeEventListener('message', messageHandler);
    };
  }, []);

  return (
    <IPopupPage
      DocumentTitle="当场整改情况说明"
      visible={visible}
      onClose={onClose}
      footer={
        <div className="p-3 bg-white">
          <Button
            onClick={async () => {
              form.submit();
            }}
            color="primary"
            loading={upLoading}
            fill="solid"
            block
            className="h-[45px] text-base"
          >
            确定
          </Button>
        </div>
      }
    >
      <Form
        initialValues={{
          images: initData?.rectifyImageUrls || [],
          reason: initData?.rectifyReason,
        }}
        layout="vertical"
        form={form}
        className={styles.IForm}
        onFinish={(value) => {
          const { images, reason } = value;
          if (!images?.length && !reason) {
            Toast.show('请上传照片或填写详细说明');
            return;
          }
          onFinish(value);
        }}
      >
        <div className="flex justify-between">
          <div className="flex-1">
            <Form.Item
              label={<div className="text-base text-[#141414] font-medium">照片</div>}
              name="images"
              rules={[
                {
                  required: !!reformMustUpload,
                  message: '请上传照片',
                },
              ]}
            >
              <SystemImageUpload
                isAlbumUpImg={isAlbumUpImg}
                setUpLoading={setUpLoading}
                shopId={shopId}
                FileUploadType="PATROL"
              />
            </Form.Item>
          </div>
          {selectImages?.length < 9 && (
            <div
              className="text-primary mt-3 mr-4 "
              onClick={() => {
                navigator.push({
                  pathname: 'SupervisorGallery',
                  message: {
                    taskId: +taskId!,
                    isUpload: false,
                    situationItemImages: selectImages,
                    shopId,
                    itemId: initData?.itemId,
                  },
                });
              }}
            >
              任务图库
            </div>
          )}
        </div>
        <Form.Item name="reason" label={<div className="text-base text-[#141414] font-medium">详细说明</div>}>
          <TextArea
            className="bg-[#FAFAFA] p-1"
            placeholder="输入详细的情况说明，最多140字"
            style={{
              '--font-size': '14px',
              '--color': '#B8B8B8',
              height: '80px',
            }}
            maxLength={140}
          />
        </Form.Item>
      </Form>
    </IPopupPage>
  );
});
