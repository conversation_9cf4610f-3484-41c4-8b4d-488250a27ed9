import { useState } from 'react';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { IconFont } from '@src/components';
import { PlanShopInfo } from '@src/pages/tasks/api.type';
import { OutlineTag } from '@src/pages/tasks/components/OutlineTag';
import { shopStatusEnumCN, shopType } from '@src/pages/tasks/enum';
import ShopRecords from '@src/pages/tasks/unplannedTask/components/Records';
import { formatDiff } from '@src/utils/utils';
import { Checkbox } from 'antd-mobile';
import dayjs from 'dayjs';

interface ShopCardProps {
  itemInfo: PlanShopInfo;
  isCheckbox?: boolean;
  checked?: boolean;
  onClick?: (e: PlanShopInfo) => void;
}

export const ShopCard = ({ itemInfo, isCheckbox, checked, onClick }: ShopCardProps) => {
  const {
    shopId,
    shopName,
    shopAddress,
    shopManagerName,
    shopManagerPhone,
    lastPatrolTime,
    lastPatrolScore,
    shopStatus,
    necessaryFlag,
  } = itemInfo || {};
  const time = dayjs(lastPatrolTime || null);
  const isValid = time.isValid();
  const [visible, setVisible] = useState(false);
  return (
    <div
      className="flex items-start text-sm text-[#5E5E5E] border-b border-[#F0F0F0] pb-4 pt-3"
      key={shopId}
      onClick={() => {
        onClick && onClick(itemInfo);
      }}
    >
      {isCheckbox && (
        <Checkbox
          className="mr-4 mt-1"
          style={{
            '--icon-size': '18px',
            '--font-size': '24px',
          }}
          checked={checked}
          // onChange={(checked) => {
          //   setUnqualified(checked);
          // }}
        />
      )}
      <div className="flex-1">
        <div className="break-all">
          <div className="flex items-center justify-start text-base font-medium leading-[24px] text-[#141414] flex-wrap">
            {!!necessaryFlag && (
              <div className="mr-1 shrink-0">
                <OutlineTag text="必检" />
              </div>
            )}
            {!!shopId && <div className="mr-2">{shopId}</div>}
            <div className="flex items-center justify-start">
              {shopName}
              {itemInfo?.shopType ? (
                <span className="ml-2 shrink-0 text-[#5E5E5E] font-light">({shopType[itemInfo?.shopType]})</span>
              ) : null}
              {shopStatus && (
                <span className="text-[#5E5E5E] font-light">【{(shopStatusEnumCN as any)?.[shopStatus]}】</span>
              )}
              {shopStatus === 'OFFLINE' && (
                <div
                  onClick={() => {
                    setVisible(true);
                  }}
                >
                  <QuestionCircleOutlined />
                </div>
              )}
            </div>
          </div>

          <div className="flex items-center my-1">
            <IconFont type="icon-user-1" className="mr-1 text-sm" />
            <div>
              {shopManagerName}({shopManagerPhone})
            </div>
          </div>

          <div className="flex items-start">
            <IconFont type="icon-location-1" className="mr-1  mt-[3px]" />
            <div className="address flex-1">{shopAddress}</div>
          </div>
          <div className="flex items-start bg-[#FAFAFA] text-[#858585] leading-[22px] mt-2 p-2">
            上次巡检：
            {isValid ? `${time.format('MM-DD')}（${formatDiff(dayjs(), time)}）` : '无'}
            &nbsp;&nbsp;&nbsp; 得分：
            {typeof lastPatrolScore === 'number' ? `${lastPatrolScore}分` : '无'}
          </div>
        </div>
      </div>
      <ShopRecords
        visible={visible}
        onClosed={() => setVisible(false)}
        shopId={String(shopId)}
        shopName={`${shopName}-${shopStatus && shopStatus in shopStatusEnumCN ? shopStatusEnumCN[shopStatus as keyof typeof shopStatusEnumCN] : ''}`}
      />
    </div>
  );
};
