// import React, { useEffect } from 'react';
// import { observer } from 'mobx-react';
// import { PatrolStore, SignStore } from '@src/store';
// import { Button, NoticeBar } from 'antd-mobile';
// import { toJS } from 'mobx';
// import CheckItemCard from './checkList/checkItemCard';
// import { useRequest } from 'ahooks';
// import { updateChecklist } from './api';
// import { UpdateChecklistItem } from './api.type';

// const RequireList: React.FC = observer(() => {
//   const { requiredList, getRequiredList, updateWorkSheet, updateRequiredList, taskId } =
//     PatrolStore;
//   const { queryTaskShopInfo } = SignStore;
//   useEffect(() => {
//     getRequiredList();
//   }, []);

//   console.log(toJS(requiredList), '=requiredList');
//   // const { run: reRequired } = useRequest(async () => {
//   //   return getRequiredList();
//   // });
//   const { run } = useRequest(
//     async (payload: Omit<UpdateChecklistItem, 'patrolWorksheetId'>, patrolWorksheetId: number) => {
//       const res = await updateChecklist({
//         taskId: taskId!,
//         itemList: [
//           {
//             hasApply: true,
//             ...payload,
//             patrolWorksheetId: patrolWorksheetId,
//             images: payload.images?.map((v) => v?.id || v?.response?.id),
//           },
//         ],
//       });
//       return { ...res, patrolWorksheetId, payload, imageURLS: payload.images };
//     },
//     {
//       manual: true,
//       onSuccess: (data) => {
//         console.log(data, '=返回数据');
//         updateWorkSheet(data);
//         updateRequiredList({ ...data, itemId: data?.payload?.patrolWorksheetItemId });
//       },
//     },
//   );
//   console.log(toJS(requiredList), '=requiredList');

//   return (
//     <div className="flex flex-col fixed inset-x-0 inset-y-0">
//       <div className="grow overflow-y-scroll">
//         <NoticeBar content="请将下述的检查项填写完整" color="alert" icon={null} />
//         {requiredList?.map((data) => {
//           return (
//             <CheckItemCard
//               key={data.itemId}
//               data={toJS(data)}
//               shopManagerName={queryTaskShopInfo?.shopManagerName}
//               updateItem={(item) => {
//                 run(item, data?.patrolWorksheetId, data?.itemId);
//               }}
//             />
//           );
//         })}
//       </div>
//       <div className="shrink-0">
//         <div className="py-2 px-2 bg-[#fff]">
//           <Button
//             onClick={() => {
//               history.back();
//             }}
//             color="primary"
//             fill="solid"
//             block
//             className="text-base"
//           >
//             确定
//           </Button>
//         </div>
//       </div>
//     </div>
//   );
// });

// export default RequireList;
