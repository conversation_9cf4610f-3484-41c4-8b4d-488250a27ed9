import React, { useEffect, useState } from 'react';
import { IPopupPage } from '@src/components/IPopup';
import VideoViewer from '@src/components/VideoViewer';
import { uploadFile } from '@src/utils/utils';
import { H5Bridge } from '@tastien/rn-bridge';
import { useRequest } from 'ahooks';
import { Button, Checkbox, ErrorBlock, Image, ImageViewer, Popup, Toast } from 'antd-mobile';
import { PlayOutline } from 'antd-mobile-icons';
import { isString } from 'lodash';
import { observer } from 'mobx-react';
import { base64ToFile } from './checkList/components/systemImageUpload';
import styles from './index.module.scss';
import { getTaskPhotos, uploadGalleryImage, uploadGalleryVideo } from '../tasks/api';
// eslint-disable-next-line react-refresh/only-export-components
export const scoreRange = Array.from({ length: 100 }, (_, ind) => {
  return { label: ind + 1, value: ind + 1 };
});
export const LimitRange = Array.from({ length: 30 }, (_, ind) => {
  return { label: ind + 1, value: ind + 1 };
});
interface SelectITEM {
  id: string;
  url: string;
  contentType: string;
}

interface TaskPhotosProps {
  TaskPhotosMsg: {
    taskId: number | null;
    visible: boolean;
    shopId?: string;
  };
  setTaskPhotosMsg: () => void;
  isSelectMode?: boolean;
  initSelectImages?: {
    id: string;
    url: string;
    contentType: string;
  }[];
  onOK?: (
    val: {
      id: string;
      url: string;
      contentType: string;
    }[],
  ) => void;
}

const TaskPhotos: React.FC<TaskPhotosProps> = observer((props: TaskPhotosProps) => {
  const { TaskPhotosMsg, setTaskPhotosMsg, isSelectMode, onOK, initSelectImages } = props;
  const [selectedItems, setSelectedItems] = useState<SelectITEM[]>([]);
  const [visible, setVisible] = useState<boolean>(false);
  const [videoPreviewVisible, setVideoPreviewVisible] = useState(false);
  const [videoPreviewUrl, setVideoPreviewUrl] = useState('');

  const [uploading, setUploading] = useState(false);
  const { data: photosData, run: reFresh } = useRequest(
    async () => {
      // eslint-disable-next-line no-throw-literal
      if (!TaskPhotosMsg?.taskId) throw null;
      const res = await getTaskPhotos({
        taskId: TaskPhotosMsg?.taskId,
        taskType: 'PATROL',
      });
      return res;
    },
    { refreshDeps: [TaskPhotosMsg?.taskId] },
  );

  const [location, setLocation] = useState<{ latitude?: number; longitude?: number } | undefined>();

  useEffect(() => {
    // 通知APP获取定位
    if (window?.['ReactNativeWebView']?.['postMessage']) {
      // 安卓
      window['ReactNativeWebView']['postMessage'](JSON.stringify({ getLocation: true }));
    } else {
      // ios
      window.parent.postMessage(JSON.stringify({ getLocation: true }), '*');
    }
    function handleEvent(e: any) {
      if (e.data && isString(e.data)) {
        try {
          const result = JSON.parse(e.data);

          if (result?.location) {
            setLocation(result?.location);
          }
        } catch (error) {
          console.log('error :>> ', error);
        }
      }
    }
    document.addEventListener('message', handleEvent); // 安卓端监听
    window.addEventListener('message', handleEvent); // ios端监听
    return () => {
      document.removeEventListener('message', handleEvent);
      window.addEventListener('message', handleEvent);
    };
  }, []);

  useEffect(() => {
    if (initSelectImages && initSelectImages.length > 0) {
      setSelectedItems(initSelectImages);
    } else {
      setSelectedItems([]);
    }
  }, [initSelectImages, TaskPhotosMsg.visible]);

  const onhandUploadFile = async (file: File, mime: string, chromaticValue?: string) => {
    Toast.show({
      icon: 'loading',
      content: '上传中…',
      duration: 0,
    });
    setUploading(true);
    try {
      const res: any = await uploadFile(
        base64ToFile(`data:image/jpeg;base64,${file}`, mime) as File,
        false,
        true,
        'PATROL',
      );
      console.log(res, '=res');
      const params = {
        chromaticValue,
        bucket: res?.bucket!,
        key: res?.key,
        originName: res?.originName,
        shopId: TaskPhotosMsg?.shopId,
        ...location,
      };
      await uploadGalleryImage({
        ...params,
        taskId: TaskPhotosMsg?.taskId!,
        taskType: 'PATROL',
      });
      Toast.clear();
      reFresh();
    } catch (e) {
      console.log(e, '=上传失败信息');
      Toast.show('上传失败，请重新上传');
    } finally {
      setUploading(false);
    }
  };
  const openCamera = async () => {
    // 相机拍照
    try {
      const {
        data: {
          image: { data, mime, chromaticValue },
        },
      }: any = await H5Bridge.customPostMessage.customPostMessage({
        module: 'image-picker',
        method: 'openCamera',
        params: {
          checkSolidColor: true,
        },
      });

      onhandUploadFile(data, mime, chromaticValue);
    } catch (e) {
      console.log(e, '=上传失败信息');
      setUploading(false);
    }
  };

  const openVideo = async () => {
    // 相机录像
    try {
      Toast.show({
        icon: 'loading',
        content: '上传中…',
        duration: 0,
      });
      setUploading(true);
      const data: any = await H5Bridge.customPostMessage.customPostMessage({
        module: 'image-picker',
        method: 'openCameraVideo',
      });
      console.log(data, '摄像回调data');
      const result = data?.data?.video || {};
      const params = {
        bucket: result?.bucket!,
        key: result?.data?.name,
        originName: result?.mime,
      };
      await uploadGalleryVideo({
        ...params,
        taskId: TaskPhotosMsg?.taskId!,
        taskType: 'PATROL',
      });
      Toast.clear();
      reFresh();
    } catch (e) {
      console.log(e, '=上传失败信息');
    } finally {
      Toast.clear();
      setUploading(false);
    }
  };
  return (
    <React.Fragment>
      <IPopupPage
        DocumentTitle="任务照片"
        visible={TaskPhotosMsg.visible}
        onClose={() => {
          setTaskPhotosMsg();
        }}
        footer={
          <div className="p-3 bg-white">
            <Button
              onClick={async () => {
                if (isSelectMode) {
                  onOK && onOK(selectedItems);
                  // eslint-disable-next-line no-restricted-globals
                  history.back();
                } else {
                  setVisible(true);
                }
              }}
              color="primary"
              loading={uploading}
              loadingText="上传中"
              fill="solid"
              block
              className="h-[45px] text-base"
            >
              {isSelectMode ? '确定' : '上传'}
            </Button>
          </div>
        }
      >
        {TaskPhotosMsg.visible && (
          <React.Fragment>
            {!!photosData?.length ? (
              <div className="p-4 flex flex-wrap  gap-4">
                {photosData?.map((item: any) => {
                  const { id, url, contentType, snapshotUrl } = item || {};
                  console.log(item, '=item');

                  const isImg = contentType?.startsWith('image') || contentType?.startsWith('IMG');
                  return (
                    <div key={id} className="h-[90px] w-[90px] relative">
                      {isImg ? (
                        <Image
                          onClick={() => {
                            ImageViewer.show({ image: url });
                          }}
                          src={snapshotUrl || url}
                          className="h-[90px] w-[90px] "
                        />
                      ) : (
                        <div
                          onClick={() => {
                            setVideoPreviewVisible(true);
                            setVideoPreviewUrl(item.url);
                          }}
                        >
                          <video
                            style={{ objectFit: 'cover' }}
                            className="border h-[90px]  w-[90px]"
                            poster={item?.snapshotUrl}
                            src={item.url}
                          />
                          <div className="absolute left-[50%] top-[50%] -translate-x-[50%] -translate-y-[50%]">
                            <PlayOutline fontSize={40} />
                          </div>
                        </div>
                      )}
                      {isSelectMode && (
                        <div
                          className="absolute left-2 top-2"
                          onClick={() => {
                            const selectedIds = selectedItems?.map((y) => {
                              return y?.id;
                            });
                            const isCurSed = selectedIds?.includes(id);
                            if (isCurSed) {
                              setSelectedItems((items) => {
                                return items?.filter((v) => v?.id !== id);
                              });
                            } else {
                              if (selectedItems?.length >= 9) {
                                Toast.show('最多只能有9张！');
                                return;
                              } else {
                                setSelectedItems((items) => {
                                  return [...items, { id, url, contentType, snapshotUrl }];
                                });
                              }
                            }
                          }}
                        >
                          <Checkbox checked={selectedItems?.some((value) => value.id === id)} />
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            ) : (
              <ErrorBlock status="empty" description="这里空空如也~" />
            )}
          </React.Fragment>
        )}

        <Popup
          visible={visible}
          bodyClassName={styles.pop}
          onMaskClick={() => {
            setVisible(false);
          }}
        >
          <div
            className={styles.takePhoto}
            onClick={() => {
              setVisible(false);
              setTimeout(() => {
                openCamera();
              }, 500);
            }}
          >
            拍照
          </div>
          <div
            className={styles.takePhoto}
            onClick={() => {
              setVisible(false);
              setTimeout(() => {
                openVideo();
              }, 500);
            }}
          >
            录像
          </div>

          <div className={styles.line} />
          <div className={styles.cancelBtn} onClick={() => setVisible(false)}>
            取消
          </div>
        </Popup>

        <VideoViewer
          visible={videoPreviewVisible}
          url={videoPreviewUrl}
          onClose={() => {
            setVideoPreviewVisible(false);
          }}
        />
      </IPopupPage>
    </React.Fragment>
  );
});
export default TaskPhotos;
