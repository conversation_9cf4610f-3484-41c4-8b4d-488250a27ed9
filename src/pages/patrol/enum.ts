export const CAN_SELECT_SHOP_STATUS = {
  true: ['PREPARING', 'OPEN', 'OFFLINE', 'TO_BE_OPENED'],
  false: ['OPEN', 'OFFLINE'],
};

export const TOAST_TITLE = {
  current: '该门店暂未营业',
  PREPARING: '该门店正在筹备中',
  CLOSE: '该门店正在闭店中',
  TO_BE_OPENED: '该门店待营业中',
};
export enum TASK_STATUS {
  /**
   * @description 待开始
   */
  pending = 'NOT_READY',

  /**
   * @description 进行中
   */
  processing = 'PROCESS',

  /**
   * @description 已完成
   */
  completed = 'COMPLETED',

  /**
   * @description 已过期
   */
  expired = 'EXPIRED',

  /**
   * @description 已取消
   */
  canceled = 'CANCELED',
}

export enum BUSINESS_TATUS {
  待开始 = 'WAIT_START',
  进行中 = 'PROCESS',
  已完成 = 'FINISH',
  已过期 = 'EXPIRED',
  已取消 = 'CANCELED',
}

export const patrolDetailCard = [
  // { title: '计划巡检门店数', key: 'planPatrolShopCount' },
  // { title: '已巡检门店数', key: 'patrolShopCount' },
  // { title: '计划报告数', key: 'planReportCount' },
  // { title: '提交报告数', key: 'submitReportCount' },
  // { title: '通过报告数', key: 'passReportCount' },
  // { title: '未通过报告数', key: 'failReportCount' },
  { title: '门店完成率', key: 'patrolFinishRatio' },
  { title: '必检门店完成率', key: 'necessaryFinishRatio' },
  { title: '报告完成率', key: 'reportFinishRatio' },
  { title: '报告平均分', key: 'averageScore' },
  { title: '检查项合格率', key: 'itemPassRatio' },
  { title: '巡检通过率', key: 'reportPassRatio' },
];
