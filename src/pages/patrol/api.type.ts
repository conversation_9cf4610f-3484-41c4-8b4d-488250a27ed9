import { scoreTypeEnum } from './checkList/buttonGroup';
import type { RectificationType } from './checkList/const';
import type { AppealStatus } from '../selfCheckComment/api.type';

export interface Shop {
  shopId: string;
  shopName: string;
  shopCode?: any;
  ymStoreId?: any;
  status: number;
  shopType: number;
  valid: number;
}
type AlarmColor = 'RED' | 'ORANGE' | 'YELLOW';
export interface diagnosticInfoItem {
  user?: any;
  start: string;
  end: string;
  alarmLevel: AlarmColor;
  score: number;
  shop: Shop;
  taskId: number;
}

export type ShopListRes = {
  shopId: number;
  shopName: string;
  shopNo: string;
  latitude: number;
  longitude: number;
  shopManagerName: string;
  shopManagerPhone: string;
  shopAddress: string;
  lastPatrolTime: string; // 上次巡检时间
  lastPatrolScore: number; // 上次巡检得分
  deviceCount: number; // 摄像头数量
  deviceStatus: number;
};
export type UpdateChecklistItem = {
  hasApply?: boolean;
  patrolWorksheetId: number;
  patrolWorksheetItemId: number;
  qualified?: 0 | 1 | boolean;
  score?: number;
  itemRemark?: string;
  images?: any;
  reformLimit?: number;
  shopRemark?: string;
  reasons?: any[];
  copyUserIds?: number[];
};
export type UpdateChecklistParams = {
  taskId: number;
  itemList: UpdateChecklistItem[];
};
export type CheckItem = {
  patrolWorksheetId: number | null;
  itemId: number;
  itemSOP: string;
  type: 'JUDGE' | 'SCORE'; // JUDGE：判断，SCORE：打分
  listType: string;
  // hasApply?: boolean;
  score?: number;
  itemRemark?: string;
  imageURLS: {
    url: string;
    type: 'IMG' | 'VIDEO';
    id?: string;
  }[];
  reformLimit?: number;
  necessaryFlag: 0 | 1; // 必检项
  fineFlag: 0 | 1; // 罚款项
  redLine: 0 | 1; // S项
  deductType: 'CATEGORY' | 'WORKSHEET' | 'REPORT'; // S项扣分类型，CATEGORY：检查表分类，WORKSHEET：检查表，REPORT：最终报告
  deductRatio: 80; // S项扣分比例
  standard?: string;
  fullScore: number;
  notQualifiedButtonCustomName?: string;
  qualifiedButtonCustomName?: string;
  sopId?: number;
  accentedTermTags?: string[];
  hasApply: boolean;
  qualified: boolean;
  reportReviewStandard: string;
  scoreMax: number;
  scoreMin: number;
  notQualifiedMustUpload?: number;
  reportDisplayScore: boolean; // 是否展示分数
  qualifiedMustUpload?: number;
  scoreType: scoreTypeEnum; // ADD_SCORE：得分项，REDUCE_SCORE：扣分项
  copyUsers: {
    id: number;
    name: string;
  }[];
  shopRemark?: string;
  watchId: number | null;
  watchName: string | null;
  ALLOW_ALBUM: 0 | 1 | 2; // 0：跟随系统设置，1：允许，2：不允许 v2.3新增字段
  selectReasons: string[] | null;
  otherReason: string[] | null;
  hasReviewPermission: boolean;
  reportReviewInfo: {
    content: string;
    status: string;
    qualified: boolean;
  };
  imageList: any;
  reasons?: string[];
  nonconformityReasons: string[];
  mustChooseNonconformityReasons: boolean;
  actualRectifyType: RectificationType;
  rectifyType: RectificationType;
  hasRectify: boolean;
  sampleImageUrls: { snapshotUrl: string; url: string }[];
  appealStatus: AppealStatus;
};
export type ChecklistCategory = {
  patrolWorksheetCategoryId: number;
  patrolWorksheetCategoryName: string;
  itemCount: number;
  filledCount: number;
  children: CheckItem[];
};
export type ChecklistItem = {
  patrolWorksheetId: number;
  patrolWorksheetName: string;
  itemCount?: number;
  filledCount?: number;
  actualTotalScore: number;
  importance?: number;
  children?: ChecklistCategory[];
};
export type QueryReportDetailParams = {
  taskId: number;
  filterHasApply?: boolean;
  reportId?: number;
  notFilledItemHandleType?: string | undefined | null;
};
export type WeekSheetItemProperty = {
  /**
   * S项
   * deductType: CATEGORY-检查表分类得分 WORKSHEET-检查表得分 REPORT-最终报告得分
   * deductRatio: 扣分比例
   */
  redLine: 0 | 1;
  deductType: 'CATEGORY' | 'WORKSHEET' | 'REPORT';
  deductRatio: number;
  /**
   * 罚款项
   */
  fineFlag: 0 | 1;
  /**
   * 必检项
   */
  necessaryFlag: 0 | 1;
  keyItem: 0 | 1;
  yellowItem: 0 | 1;
};

export type TReportSecondReviewInfo = WeekSheet['children'][0]['reportReviewInfo'];

export type WeekSheet = {
  patrolWorksheetCategoryId: number;
  patrolWorksheetCategoryName: string;
  itemCount: number;
  filledCount: number;
  actualScore: 90; // 分类实际得分 v1.4新增
  actualTotalScore: 160; // 分类实际总分 v1.4新增
  validCount: 160; // 合格项 v1.4新增
  total: 160; // 总项 v1.4新增
  checkListTypeList?: [];
  children: ({
    itemId: number;
    itemName: string;
    patrolWorksheetId: number;
    checkListTypeName: string;
    itemSOP: '检查项名称';
    score: number;
    itemRemark: '用户录入的检查项备注';
    selectReasons: string[];
    otherReason: string;
    imageList: { id: string; url: string; type: 'IMG' | 'VIDEO' }[];
    reasons?: string[];
    shopRemark: '门店情况说明';
    imageURLS: { url: string; type: 'IMG' | 'VIDEO'; id?: string }[] | undefined;
    hasFillItem: boolean;
    style: 1 | 2;
    status: 0 | 1;
    fullScore: number;
    hasApply: boolean;
    qualified: boolean;
    scoreType: string;
    standard: string;
    sopId: number;
    accentedTermTags: Nullable<string[]>;
    reportReviewInfo: {
      content: string;
      status: string;
      qualified: boolean;
    };
    /**
     * 第二点评信息
     */
    reportSecondReviewInfo?: TReportSecondReviewInfo;
    reportReviewMethod: 'MANUAL_JUDGMENT' | 'NO_REVIEWS_NEEDED';
    hasReviewPermission: boolean;
    worksheetItemId: number;
    existLastIssueReport: boolean;
    rectifyReason?: string;
    rectifyImages?: string[];
    actualRectifyType: RectificationType;
    rectifyType: RectificationType;
    hasRectify: boolean;
  } & WeekSheetItemProperty)[];
};
export type DetailItem = {
  patrolWorksheetId: number; // '检查表ID';
  patrolWorksheetName: '检查表名称';
  total: 20;
  score: 80;
  weight: number;
  validCount: 10;
  actualScore: 90; // 报告实际得分 v1.4新增
  actualTotalScore: 160; // 报告实际总分 v1.4新增
  redLineCount: 1; // 有1个分类下存在S项不合格 v1.5新增
  vetoCount: 2; // 有2个否决项不合格 v1.5新增
  additionalCount?: number; // 附加项不合格数量，对最终报告扣分 v2.1新增字段
  additionalDeductRatio?: number; // 对最终报告扣分比例 v2.1新增字段
  fineCount: 3; // 有3个罚款项不合格 v1.5新增
  itemCount: number;
  isCheckRequired?: boolean;
  filledCount: number;
  children: WeekSheet[];
  issueDTOS: WeekSheet[];
  importance?: Nullable<number>;
  notApplyCount?: number;
  allCategoryCount: number;
  validCategoryCount: number;
  reportReviewDeadline: string;
  hasReviewPermission: boolean;
  existNeedReviewItem: boolean; // 是否存在点评项
  secondReviewDeadline?: string; // 第二点评截止时间
  hasCanAppeal?: boolean;
};

export type ReportDetailInfo = {
  appPatrolReport: {
    actualScore: number;
    actualTotalScore: number;
    additionalCount: number;
    additionalDeductRatio: number;
    allCategoryCount: number;
    data: DetailItem[];
    fillCount: number;
    invalidCount: number;
    itemCountMap: {};
    passItemCount: number;
    passScore: number;
    score: number;
    total: number;
    valid: number;
    validCategoryCount: number;
    worksheetPassRatio: number;
  };
  itemCountMap: {
    configType: string;
    quantity: number;
    rate: number;
    tag: string;
  }[];
  shopSummary: string;
  buttonEnums: string[];
  confirmUserId: number;
  confirmUserName: string;
  cross: boolean;
  filledCount: number;
  generateRectification: false;
  issuesCount: number;
  itemCount: number;
  lastPatrolDate: string;
  lastPatrolScore: number;
  treatedUserName: string;
  latitude: number;
  leaveTime: string;
  longitude: number;
  noReformCount: number;
  waitRectifiedCount: number;
  beginTime: string;
  expiredTime: string;
  operateList: {
    operateType: 'REFORM_SUBMIT' | 'REFORM_PASS' | 'REFORM_REJECT'; // REFORM_SUBMIT:提交整改;REFORM_PASS:整改审核通过;REFORM_REJECT：整改审核驳回
    operateUserId: number;
    operateUserName: string;
    operateUserAvatar: string;
    operateTime: string;
  }[];
  reformStatus: 'REFORM_FEEDBACK' | 'WAIT_AUDIT' | 'EXPIRED' | 'PASS';
  reportDate: string;
  reportStatus: string;
  reviewStatus: string;
  hasNeedReview: boolean;
  shopAddress: string;
  shopId: string;
  shopManagerName: string;
  shopManagerPhone: number;
  shopName: string;
  signTime: string;
  signType: string;
  subType: string;
  submitUserId: number;
  submitUserName: string;
  summaryText: string;
  taskDate: string;
  taskId: number;
  taskName: string;
  taskStatus: string;
  waitAuditCount: number;
  headMobile: number;
  reportOverallReview: string;
  /**
   * 报告第二点评评论
   */
  reportOverallSecondReview: string;
  /**
   * 是否需要第二点评
   */
  hasNeedSecondReview: boolean;
  /**
   * 是否有第二点评权限
   */
  hasSecondReviewPermission?: boolean;
  /**
   * 第二点评截止时间
   */
  secondReviewDeadline?: string;
};

export type DiagnosticInfo = {
  alarmLevel: 'RED' | 'ORANGE' | 'YELLOW' | 'GREEN';
  end: string;
  score: number;
  shop: {
    shopCode: string;
    shopId: string;
    shopName: string;
    shopType: number;
    status: number;
    valid: number;
    ymStoreId: string;
  };
  start: string;
  user: {
    alive: boolean;
    nickname: string;
    open: boolean;
    phone: string;
    userId: number;
  };
  taskStatus: string;
  id: number;
  taskId: number;
  applicant?: DiagnosticInfo['user'];
  processor?: DiagnosticInfo['user'];
};
export type TReviewSaveParams = {
  overallReviewContent: string;
  checkListReviews: {
    checkListItemId: number;
    patrolWorksheetId: number;
    reviewContent: string;
  }[];
  taskReportId: number;
};
export type SignParams = {
  taskId: number;
  lat: number;
  log: number;
  distance: number;
  image: string;
};
export type GeoLocation = {
  longitude: number;
  latitude: number;
  address: string | undefined;
  title: string | undefined;
};

export type TaskListParams = {
  createTimeOrderByType?: 'desc';
  pageNum: number;
  pageSize: number;
  planId: string;
  taskDateOrderByType?: 'desc';
};
export type patrolTaskListItem = {
  shopId: number;
  taskId: number;
  shopName: string;
  shopNo: string;
  taskDate: string;
  createByName: string;
  /** 创建用户名称 */
  createUserName: string;
  createByAvatar: string;
  shopAddress: string;
  shopLat: number;
  shopLog: number;
  shopManagerId: number;
  shopManagerName: string;
  shopPhone: string;
  phoneNumber: string;
  status: string;
  planStatus: string;
  businessStatus: string;
  statusDesc: string;
  taskType: number;
  createTime: string;
  signed: boolean;
  selectionCompleted: boolean;
  selectorNumber: number;
  shopType: 'JOIN' | 'DIRECT';
  subType:
    | 'NORMAL'
    | 'VIDEO'
    | 'CROSS'
    | 'FOOD_SAFETY_NORMAL'
    | 'FOOD_SAFETY_VIDEO'
    | 'DIAGNOSTIC'
    | 'FOOD_SAFETY_ARRIVE_SHOP'; // NORMAL-到店巡检(权限内),VIDEO-视屏云巡检,CROSS-到店巡检(权限外) FOOD_SAFETY_ARRIVE_SHOP-食安稽核到店辅导
  signType: 'SYSTEM' | 'SIGN_NEITHER' | 'SIGN_IN_OUT' | 'SIGN_IN';
  cross: boolean;
  leaveStatus?: number | undefined;
  diagnosticInfo?: diagnosticInfoItem;
};
export type patrolTaskListResp = patrolTaskListItem[];
export type SopListItem = {
  id: number;
  name: string;
  categoryName: string;
  categoryId: number;
  statusEnum: 'PUBLISH' | 'UNPUBLISH';
  fileUrl: string;
  imageUrls: string[];
  fileEnum: 'VIDEO' | 'PDF' | 'IMG';
  refNum: number;
  usePermissionIds?: number[];
  usePermissionScope?: 'ALL' | 'BY_ROLE' | 'BY_ORGANIZATION';
  createBy: string;
  createTime: string;
};
export type PlanShopInfo = GeoLocation & {
  shopId: string;
  shopName: string;
  shopNo: string;
  shopManagerName: string;
  shopManagerPhone: string;
  shopAddress: string;
  necessaryFlag: number; // 是否是必检门店，1：是 0：否
  shopType: 'DIRECT' | 'JOIN';
  /**
   * 其实是这种格式，但是不好排序 '2021-07-03'
   */
  lastPatrolTime: number; // 上次巡检日期
  lastPatrolScore: 98; // 上次巡检得分
  hasPatrol: 1; // 1：本次已巡检 0：本次未巡检
  deviceCount?: number;
  deviceStatus?: number;
  /**
   * 中台门店状态
   * 1.筹备中
   * 2.营业中
   * 3.停业中
   * 4.闭店中
   * 8.待营业
   */
  shopStatus?: number;
  // 是否有员工信息
  hasEmployee: boolean;
};
export type ButtonEnums =
  | 'MODIFY_SCORE'
  | 'SUBMIT_REPORT'
  | 'RECALL_REPORT'
  | 'REJECT_REPORT'
  | 'CONFIRM_REPORT'
  | 'PROBLEM_DETAIL'
  | 'SHOP_MANAGER_WAIT_REVIEW'
  | 'REVOKE_REPORT_COMMENT'
  | 'SUBMIT_REPORT_COMMENT'
  | 'PREVIEW_SELF_REPORT';
export type operateList = {
  operateType: string;
  operateUserId: number;
  operateUserName: string;
  operateUserAvatar: string;
  operateTime: string;
};
export type CategoryItem = {
  groupTypeId: number;
  groupTypeName: string;
  total: number;
  validCount: number;
  filledCount: number;
  itemCount: number;
  children: CheckItem[];
  actualScore: number;
  actualTotalScore: number;
};
type CheckListsItem = {
  checkListId: number;
  checkListName: string;
  total: number;
  valid: number;
  importance: number;
  score: number;
  redLineCount: number;
  vetoCount: number;
  fineCount: number;
};
export type DetailListItem = {
  checkListId: number;
  checkListName: string;
  total: number;
  validCount: number;
  redLineCount: number;
  vetoCount: number;
  additionalCount: number; // 有2个附加项不合格 v2.1新增字段
  additionalDeductRatio: number; // 对最终报告得分扣80% v2.1新增字段
  fineCount: number;
  keyItemCount: number;
  yellowItemCount: number;
  score: number;
  actualScore: number;
  actualTotalScore: number;
  filledCount: number;
  itemCount: number;
  children: CategoryItem[];
  notApplyCount?: number;
  importance: any;
  validCategoryCount?: number;
  allCategoryCount?: number;
};
export type SelfReportDetailItem = {
  generateRectification: boolean; // 是否生成整改报告
  hasRectificationOperation: boolean; // 是否有整改操作
  taskId: number;
  reportId: number;
  reportName: string;
  beginTime: string;
  expiredTime: string;
  shopId: number;
  shopName: string;
  shopNo: string;
  status: 1 | 4 | 5;
  statusDesc: '待提交' | '待点评 ' | '已点评';
  alreadyExpired: boolean; // 报告是否过期
  fillItemFlag: 0 | 1; // 0:预览报告, 2:提交点评
  submitUserName: string;
  submitUserId: number;
  reformStatus: number;
  issuesCount: number;
  operateList: operateList[];
  submitUserAvator: string;
  submitTime: string;
  reviewUserName: string;
  reviewUserAvator: string;
  reviewTime: string;
  summaryText: string;
  buttonEnums: ButtonEnums[];
  revocation: boolean; // true:可撤回；false:不可撤回
  filledCount: number;
  itemCount: number;
  shopSummary: string;
  noReformCount: number; // 未整改数量
  waitAuditCount: number; // 待审核数量
  latestRectificationSubmitTime: string;
  count: {
    sum: {
      total: number;
      valid: number;
      score: number;
      redLineCount: number;
      vetoCount: number;
      fineCount: number;
      actualScore: number;
      actualTotalScore: number;
      additionalCount: number;
      additionalDeductRatio: number;
      validCategoryCount: number;
      allCategoryCount: number;
    };
    checkLists: CheckListsItem[];
  };
  details: DetailListItem[];
};
