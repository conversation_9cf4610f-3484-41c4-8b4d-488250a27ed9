import { useMemo, useState } from 'react';
import { Loading } from '@src/components';
import WithKeepAlive from '@src/components/WithKeepAlive';
import { formatScore } from '@src/pages/mission/components/core';
import { InfiniteScrollBuilder } from '@src/pages/tasks/components/InfiniteScrollBuilder';
import { weeks } from '@src/utils/utils';
import { useRequest } from 'ahooks';
import { Button, Grid, PullToRefresh } from 'antd-mobile';
import { DownOutline, UpOutline } from 'antd-mobile-icons';
import dayjs from 'dayjs';
import { groupBy } from 'lodash';
import { observer } from 'mobx-react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import DetailCard from './detailCard';
import { getPatrolTaskList, getPatrolTaskStatistics } from '../api';
import { patrolTaskListItem } from '../api.type';
import { patrolDetailCard } from '../enum';
type TaskStatus = 'WAIT_START' | 'PROCESS' | 'FINISHED' | 'CANCELED';
const TASK_STATUS_TEXT = {
  WAIT_START: '待开始',
  PROCESS: '进行中',
  FINISHED: '已完成',
  CANCELED: '已取消',
  等接口1: '执行确认中',
  等接口2: '转派中',
  等接口3: '执行修改审核',
};

const formatDate = (date?: string) => {
  const day = dayjs(date);
  return `${day.format('YYYY年MM月DD日')} 星期${weeks[day.get('day')]}`;
};

const RenderChildren = (data: patrolTaskListItem[]) => {
  console.log(data, '=data1111');

  const newData = (): [string, patrolTaskListItem[]][] => {
    const dataGroup = groupBy(data.flat(), 'taskDate');
    return Object.keys(dataGroup).map((date) => [formatDate(date), dataGroup[date]]);
  };
  console.log(newData(), '=dnewData1111');
  return newData().map(([date, list]) => (
    <div key={date} className="mb-4 text-xs">
      <div className="mb-2 text-xs pl-2">{date}</div>
      {list.map((item: patrolTaskListItem) => (
        <DetailCard key={item?.taskId} initial={item} />
      ))}
    </div>
  ));
};

const PlanDetail: React.FC = observer(() => {
  const [routerParams] = useSearchParams();
  const navigate = useNavigate();
  const [changeTime, setChangeTime] = useState(new Date());
  const { planId } = useMemo(() => {
    const planId = routerParams.get('planId');

    return {
      planId,
    };
  }, [routerParams]);

  const [visible, setVisible] = useState<boolean>(false);
  const {
    data: statisticsData,
    loading: statisticsLoading,
    refresh,
  } = useRequest(
    async () => {
      // eslint-disable-next-line no-throw-literal
      if (!planId) throw null;

      const res = await getPatrolTaskStatistics({ planId: +planId });

      ['patrolFinishRatio', 'necessaryFinishRatio', 'reportFinishRatio', 'itemPassRatio', 'reportPassRatio'].forEach(
        (key) => {
          res[key] = typeof res[key] === 'number' ? `${formatScore((res[key] * 1000) / 10)}%` : '-';
        },
      );
      res.averageScore = res?.averageScore ? formatScore(res?.averageScore) : '-';
      return res;
    },
    { refreshDeps: [] },
  );
  const showPatrolDetailList = useMemo(() => {
    return visible ? patrolDetailCard : patrolDetailCard?.slice(0, 3);
  }, [visible]);
  const { planName, endDate, startDate, status, signType } = statisticsData || {};
  return (
    <PullToRefresh
      onRefresh={async () => {
        setChangeTime(new Date());
        await refresh();
      }}
    >
      <Loading spinning={statisticsLoading}>
        <div className="flex flex-col fixed inset-x-0 inset-y-0 gap-y-2">
          <div className="grow overflow-y-scroll">
            <div className="bg-white text-sm leading-[14px] pl-4 pr-4 pt-4 pb-3  text-grey">
              <h3 className="ellipsis-2 text-[#141414] text-base leading-[22px] font-semibold">{planName}</h3>
              <div className="mt-2">
                {startDate && endDate ? (
                  <div className="text-primary">
                    <span className="mr-2"> {TASK_STATUS_TEXT[status as TaskStatus]}</span>
                    <span>
                      {dayjs(startDate).format('YYYY/MM/DD')} ~ {dayjs(endDate).format('YYYY/MM/DD')}
                    </span>
                  </div>
                ) : null}

                {/* {userName && <div className="text-[#858585] mt-2">巡检人：{userName}</div>} */}
              </div>
              <div className="mt-4 pb-4 border-0 border-b-[0.5px] border-solid border-[#f0f0f0]">
                <Grid columns={3} gap={[8, 20]}>
                  {showPatrolDetailList.map(({ title, key }) => {
                    return (
                      <Grid.Item key={key}>
                        <div className="flex flex-col items-center">
                          <div style={{ fontWeight: '600' }} className="text-base text-[#141414]">
                            {statisticsData?.[key]}
                          </div>
                          <div className="text-xs text-[#858585]">{title}</div>
                        </div>
                      </Grid.Item>
                    );
                  })}
                </Grid>
              </div>
              <div className="flex justify-center items-center mt-3 " onClick={() => setVisible(!visible)}>
                <span className="text-[#858585]">{visible ? '收起' : '更多结果'}</span>
                {visible ? <UpOutline className="text-xs ml-1" /> : <DownOutline className="text-xs ml-1" />}
              </div>
            </div>
            <div className={`grow overflow-y-scroll p-2`}>
              <div className="text-base pl-2 mb-1 mt-2" style={{ fontWeight: '500', color: '#3D3D3D' }}>
                任务明细
              </div>
              <InfiniteScrollBuilder
                key="DetailCard"
                searchParams={{
                  planId,
                  changeTime,
                  pageSize: 5,
                  createTimeOrderDirection: 'DESC',
                  taskDateOrderDirection: 'DESC',
                }}
                api={getPatrolTaskList}
                renderChildren={RenderChildren}
              />
            </div>
          </div>

          {['PROCESS'].includes(status) && (
            <div className="pb-8 pt-4 px-4  bg-white">
              <div className="flex justify-between">
                <Button
                  block
                  className="border-[0.5px] text-base text-[#5E5E5E] border-[#DCDCDC]"
                  onClick={() => {
                    navigate(`/patrol/plan/add?planId=${planId}&hasPreparingShop=${statisticsData?.hasPreparingShop}`);
                  }}
                >
                  提前排班
                </Button>
                <Button
                  block
                  color="primary"
                  className="border-[0.5px] ml-2 text-base text-[#FFFFFF] border-primary"
                  onClick={() => {
                    navigate(`/patrol/plan/nearshop?planId=${planId}&signType=${signType}`);
                  }}
                >
                  快速开始巡检
                </Button>
              </div>
            </div>
          )}
        </div>
      </Loading>
    </PullToRefresh>
  );
});
export default WithKeepAlive(PlanDetail);
