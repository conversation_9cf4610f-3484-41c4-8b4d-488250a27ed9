/* eslint-disable react-refresh/only-export-components */
import { useState } from 'react';
import { IconFont } from '@src/components';
import { useShopConfig } from '@src/hooks/useShopConfig';
import { SignTypeText } from '@src/pages/reportDetail/patrol/enum';
import { TSignType } from '@src/pages/tasks/api.type';
import { shopType } from '@src/pages/tasks/enum';
import { SignStore } from '@src/store';
import { encrypt } from '@src/utils/utils';
import { H5Bridge } from '@tastien/rn-bridge';
import { Button, SwipeAction, Toast } from 'antd-mobile';
import dayjs from 'dayjs';
import { useNavigate } from 'react-router-dom';
import { cancelTask } from '../api';
import { patrolTaskListItem } from '../api.type';
import { BUSINESS_TATUS, TASK_STATUS } from '../enum';

// export const StatusTag = {
//   WAIT_START: '待开始',
//   PROCESS: '进行中',
//   FINISH: '已完成',
//   EXPIRE: '已过期',
//   CANCEL: '已取消',
//   // 我管辖的-自检
//   CREATE: '待开始',
//   IN_PROGRESS: '进行中',
//   COMPLETE: '已完成',
//   OVERDUE: '已过期',
//   // FINISH: '已结束',
//   IN_PROGRESS_EXPIRED: '逾期进行中',
//   // 我处理的-巡检
//   NOT_READY: '待开始',
//   CANCELED: '已取消',
//   COMPLETED: '已完成',
// };
export const SubTypeTag = {
  NORMAL: '到店巡检',
  VIDEO: '视频云巡检',
  CROSS: '权限外',
  FOOD_SAFETY_NORMAL: '食安线下稽核',
  FOOD_SAFETY_VIDEO: '食安线上稽核',
  DIAGNOSTIC: '诊断任务',
  FOOD_SAFETY_ARRIVE_SHOP: '食安稽核到店辅导',
};
export const SubTypeValue = {
  NORMAL: 'NORMAL',
  VIDEO: 'VIDEO',
  CROSS: 'CROSS',
  FOOD_SAFETY_NORMAL: 'FOOD_SAFETY_NORMAL',
  FOOD_SAFETY_VIDEO: 'FOOD_SAFETY_VIDEO',
  DIAGNOSTIC: 'DIAGNOSTIC',
  FOOD_SAFETY_ARRIVE_SHOP: 'FOOD_SAFETY_ARRIVE_SHOP',
};
export const SelfStatusTag = {
  // 我管辖的-自检
  FINISHED: '已结束',
  IN_PROGRESS: '进行中',
  COMPLETED: '已完成',
  IN_PROGRESS_EXPIRED: '逾期进行中',
};
export enum patrolTypeText {
  视频云巡检 = 'VIDEO',
  到店巡检 = 'NORMAL',
  线下稽核 = 'FOOD_SAFETY_NORMAL',
  诊断任务 = 'DIAGNOSTIC',
  食安稽核到店辅导 = 'FOOD_SAFETY_ARRIVE_SHOP',
}
export const statusTagMap: { [key: string]: React.ReactNode } = {
  WAIT_START: <div className="text-base text-[#378BFF]">待开始</div>,
  PROCESS: <div className="text-base text-[#6CCD5F]">进行中</div>,
  FINISH: <div className="text-base text-[#3DB86D]">已完成</div>,
  EXPIRED: <div className="text-base text-[#EA0000]">已过期</div>,
  CANCEL: <div className="text-base text-[#EA0000]">已取消</div>,
  // 我管辖的-自检
  CREATE: <div className="text-base text-[#378BFF]">待开始</div>,
  IN_PROGRESS: <div className="text-base text-[#6CCD5F]">进行中</div>,
  COMPLETE: <div className="text-base text-[#3DB86D]">已完成</div>,
  OVERDUE: <div className="text-base text-[#EA0000]">已过期</div>,
  // FINISH: '已结束',

  IN_PROGRESS_EXPIRED: <div className="text-base text-[#EA0000]">逾期进行中</div>,
  // 我处理的-巡检
  NOT_READY: <div className="text-base text-[#378BFF]">待开始</div>,
  CANCELED: <div className="text-base text-[#EA0000]">已取消</div>,
  COMPLETED: <div className="text-base text-[#3DB86D]">已完成</div>,
};

export const alarmTagMap = {
  RED: (
    <div className="ml-1 text-xs bg-[#CD3E36]/10 border border-[#CD3E36] text-[#CD3E36] rounded px-[2px] py-[1px]">
      秩序白银
    </div>
  ),
  // ORANGE: <div className={createAlarmStyle('#FD621C')}>橙色预警</div>,
  ORANGE: (
    <div className="ml-1 text-xs bg-[#FD621C]/10 border border-[#FD621C] text-[#FD621C] rounded px-[2px] py-[1px]">
      倔强青铜
    </div>
  ),

  YELLOW: (
    //  <div className={createAlarmStyle('#FFB169')}>黄色预警</div>
    <div className="ml-1 text-xs bg-[#FFB169]/10 border border-[#FFB169] text-[#FFB169] rounded px-[2px] py-[1px]">
      尊贵铂金
    </div>
  ),
};

interface DetailCardProps {
  initial: patrolTaskListItem;
  isSelf?: boolean;
  isFormTaskCenter?: boolean;
}
export const showBtnFn = (
  subType: string,
  complete: boolean,
  signed: boolean,
  config: any,
  signType: TSignType,
  cross: boolean,
) => {
  // 1、完成后跳转查看报告
  // 2、subType视频云巡检隐藏签到 签离 巡店，只保留查看报告
  // 3、后台设置，当选择必须要签到时，
  //      还未签到，签到并巡店。
  //      签到完以后，“巡店”
  //   设置非必须签到时，巡店

  const showBtn: {
    type?: 'report' | 'patrol' | 'sign' | 'diagnosticSign' | 'diagnosticCheck';
    text?: string;
  } = {};
  if (subType !== patrolTypeText.诊断任务) {
    if (complete) {
      showBtn.type = 'report';
      showBtn.text = '查看报告';
      return showBtn;
    }
    if (cross) {
      if (!signed) {
        showBtn.type = 'sign';
        showBtn.text = '签到并巡店';
      } else {
        showBtn.type = 'patrol';
        showBtn.text = '巡店';
      }
      return showBtn;
    }

    if (
      ([patrolTypeText.到店巡检, patrolTypeText.线下稽核].includes(subType as any) &&
        signType === 'SYSTEM' &&
        +config?.MUST_CHECK_IN !== 0) ||
      ['SIGN_IN_OUT', 'SIGN_IN'].includes(signType)
    ) {
      if (!signed) {
        showBtn.type = 'sign';
        showBtn.text = '签到并巡店';
      } else {
        showBtn.type = 'patrol';
        showBtn.text = '巡店';
      }
    } else {
      showBtn.type = 'patrol';
      showBtn.text = '巡店';
    }
  } else {
    if ((signType === 'SYSTEM' && +config?.MUST_CHECK_IN !== 0) || ['SIGN_IN_OUT', 'SIGN_IN'].includes(signType)) {
      if (complete) {
        showBtn.type = 'report';
        showBtn.text = '查看报告';
        return showBtn;
      } else if (!signed) {
        showBtn.type = 'diagnosticSign';
        showBtn.text = '签到';
      } else {
        showBtn.type = 'diagnosticCheck';
        showBtn.text = '去处理';
      }
    }
  }

  return showBtn;
};
const DetailCard: React.FC<DetailCardProps> = ({ initial, isSelf = true, isFormTaskCenter }) => {
  const navigate = useNavigate();
  const { config } = useShopConfig();
  const { signed, subType, signType, status, leaveStatus, cross, taskId, diagnosticInfo } = initial;
  const [curStatus, setCurStatus] = useState<string>(status);

  // @ts-ignore
  const operateDisabled: boolean = [BUSINESS_TATUS.已取消, BUSINESS_TATUS.已过期].includes(curStatus);
  const complete = curStatus === BUSINESS_TATUS.已完成 || curStatus === TASK_STATUS.completed;
  const showBtn = showBtnFn(subType, complete, signed, config, signType, cross);

  return (
    <SwipeAction
      style={{ borderRadius: '8px', marginBottom: '8px' }}
      rightActions={
        // @ts-ignore
        [BUSINESS_TATUS.已取消, BUSINESS_TATUS.已完成, BUSINESS_TATUS.已过期].includes(curStatus) ||
        initial?.subType === patrolTypeText.诊断任务 ||
        initial?.subType === patrolTypeText.食安稽核到店辅导
          ? []
          : isSelf
            ? [
                {
                  key: 'cancel',
                  text: (
                    <div className="text-xs">
                      <div>
                        <IconFont className="text-[25px]" type="icon-trash-2" />
                      </div>
                      取消任务
                    </div>
                  ),
                  color: 'danger',
                },
              ]
            : []
      }
      onAction={async (e) => {
        if (e.key === 'cancel') {
          await cancelTask(taskId);
          Toast.show({
            icon: 'success',
            content: '取消成功',
          });
          setCurStatus(BUSINESS_TATUS.已取消);
        }
      }}
    >
      <div className="bg-white p-3  rounded [&:not(:first-child)]:mt-[12px]">
        <div className="flex justify-between  items-center">
          <div className="flex  items-center">
            <div className="ml-1 text-xs bg-primary/10 border border-primary text-primary rounded px-[2px] py-[1px]">
              {SubTypeTag[subType]}
            </div>
            {alarmTagMap[diagnosticInfo?.alarmLevel!]}
            {subType !== patrolTypeText.诊断任务 && (
              <div className="ml-1 text-xs">
                {`由 ${initial.subType === patrolTypeText.食安稽核到店辅导 ? initial?.createUserName : initial.createByName}  ${dayjs(initial?.createTime)?.format('MM-DD HH:mm')}  创建`}
              </div>
            )}
          </div>
          {statusTagMap[curStatus]}
          {/* <div className="text-primary text-sm"> {StatusTag[curStatus]}</div> */}
        </div>
        <div
          className="flex  flex-col  items-start mt-[6px] p-3 bg-[#FAFAFA] rounded-[4px]"
          onClick={() => {
            if (operateDisabled) {
              navigate(`/patrol/shopcheck?taskId=${initial?.taskId}`);
            }
          }}
        >
          <div style={{ fontWeight: '600' }} className="text-sm text-[#141414]">
            {`${initial?.shopId}  ${initial?.shopName}`}
            {initial?.shopType ? (
              <span className="ml-2 text-[#5E5E5E] font-light">({shopType[initial?.shopType]})</span>
            ) : null}
          </div>
          {subType !== patrolTypeText.诊断任务 && (
            <div className="flex items-start mt-2 mb-2">
              <IconFont type="icon-location-1" className="text-sm text-[#5E5E5E] mt-[3px]" />
              <span className="ml-1 text-sm text-[#5E5E5E]">{initial?.shopAddress}</span>
            </div>
          )}
          {subType !== patrolTypeText.诊断任务 && (
            <div
              className="text-primary text-sm flex  items-center"
              onClick={() => {
                if (!initial?.shopPhone && !initial?.phoneNumber) {
                  Toast.show({
                    content: '暂无联系方式',
                  });
                  return;
                }
                window.location.href = `tel:${initial?.shopPhone || initial?.phoneNumber}`;
              }}
            >
              <IconFont type="icon-rongqi" />
              <span className="ml-1">电话联系</span>
            </div>
          )}
          {subType === patrolTypeText.诊断任务 && (
            <div>
              <div className="text-base font-semibold">
                <span>{diagnosticInfo?.score} </span>
                <span className="text-sm">分</span>{' '}
              </div>
              <div className="text-[#9C9C9C] text-xs">
                执行时段：{diagnosticInfo?.start} ~ {diagnosticInfo?.end}
              </div>
            </div>
          )}
        </div>
        <div className="flex justify-between">
          {(() => {
            // cross是否权限外、subTyp任务类型：到店or视频云巡检
            // signed是否签到
            const shouldShowButton =
              (!cross &&
                [patrolTypeText.到店巡检, patrolTypeText.线下稽核].includes(subType as any) &&
                signType === SignTypeText.跟随系统 &&
                (+config?.MUST_CHECK_IN === 0 || (+config?.MUST_CHECK_IN === 1 && signed))) ||
              signType === SignTypeText.无需签到签离;
            // (signType === SignTypeText.无需签到签离 && isSelf);
            if (shouldShowButton && isSelf) {
              return (
                <Button
                  disabled={operateDisabled}
                  block
                  className="mt-3 mb-1 h-8 border-[0.5px] text-sm leading-4 border-[#DCDCDC]"
                  onClick={async () => {
                    // await SignStore.setTaskShopInfo(initial);
                    // navigate(`/patrol/plan/sign`);
                    const info = {
                      path: signed ? 'SupervisorCheckout' : 'SupervisorSignIn',
                      shopId: initial.shopId,
                      taskId: initial.taskId,
                      callBackUrl: signed
                        ? `/tasks/list/principal/patrol?queryType=principal&workType=patrol`
                        : `/patrol/shopcheck?taskId=${initial.taskId}`,
                      selectionCompleted: initial.selectionCompleted,
                      selectorNumber: initial.selectorNumber,
                    };

                    await H5Bridge.customPostMessage.customPostMessage({
                      module: 'face-module',
                      method: 'navigateTo',
                      params: {
                        param: encrypt(info),
                      },
                    });
                  }}
                >
                  {signed ? (leaveStatus ? '已签离' : '签离') : '签到'}
                </Button>
              );
            }
            return null;
          })()}
          {!isSelf && !complete ? null : (
            <Button
              onClick={async () => {
                SignStore.setTaskShopInfo(initial);
                const info = {
                  path: 'SupervisorSignIn',
                  shopId: initial.shopId,
                  taskId: initial.taskId,
                  callBackUrl: `/patrol/shopcheck?taskId=${initial.taskId}`,
                  selectionCompleted: initial.selectionCompleted,
                  selectorNumber: initial.selectorNumber,
                };

                switch (showBtn.type) {
                  case 'report': // 查看报告
                    navigate(`/patrol/reportdetail?taskId=${initial?.taskId}`);
                    break;
                  case 'sign': // 签到并巡店
                    // navigate(`/patrol/plan/sign`);
                    await H5Bridge.customPostMessage.customPostMessage({
                      module: 'face-module',
                      method: 'navigateTo',
                      params: {
                        param: encrypt(info),
                      },
                    });
                    break;
                  case 'patrol': // 巡店;
                    if (!initial.selectionCompleted && isFormTaskCenter) {
                      await H5Bridge.customPostMessage.customPostMessage({
                        module: 'navigation',
                        method: 'navigateTo',
                        params: {
                          pathname: 'SupervisorOptions',
                          data: encrypt({
                            taskId: initial?.taskId, // 批量签到
                            callBackUrl: `/patrol/shopcheck?taskId=${initial?.taskId}`,
                            selectionCompleted: initial.selectionCompleted,
                            selectorNumber: initial.selectorNumber,
                          }),
                        },
                      });

                      return;
                    }
                    navigate(`/patrol/shopcheck?taskId=${initial?.taskId}`);
                    break;
                  case 'diagnosticSign': // 诊断签到;
                    if (dayjs().isBefore(dayjs(diagnosticInfo?.start))) {
                      Toast.show({ content: '任务执行时间未到,请耐心等待！' });
                    } else {
                      await H5Bridge.customPostMessage.customPostMessage({
                        module: 'face-module',
                        method: 'navigateTo',
                        params: {
                          param: encrypt(info),
                        },
                      });
                    }
                    break;
                  case 'diagnosticCheck': // 诊断去处理;
                    navigate(`/patrol/shopcheck?taskId=${initial.taskId}`);
                    break;
                }
              }}
              disabled={operateDisabled}
              block
              className="mt-3 mb-1 h-8 border-[0.5px] text-sm leading-4 border-[#DCDCDC] ml-2"
            >
              {showBtn.text}
            </Button>
          )}
        </div>
      </div>
    </SwipeAction>
  );
};

export default DetailCard;
