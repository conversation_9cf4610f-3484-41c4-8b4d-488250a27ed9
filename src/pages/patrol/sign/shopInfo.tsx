import React from 'react';
import { IconFont } from '@src/components';
import { patrolTaskListItem } from '@src/pages/tasks/api.type';
import { shopType } from '@src/pages/tasks/enum';
import { observer } from 'mobx-react';
interface ShopInfo {
  info: patrolTaskListItem;
}

const ShopInfoCard: React.FC<ShopInfo> = observer((props: ShopInfo) => {
  const { info } = props;
  return (
    <div className="flex  flex-col w-full items-start p-3 bg-[#fff] rounded-[4px]">
      <div style={{ fontWeight: '600' }} className="text-sm text-[#141414]">
        {`${info?.shopId}  ${info?.shopName}`}
        {info?.shopType ? (
          <span className="ml-2 text-[#5E5E5E] font-light">
            ({shopType[info?.shopType]})
          </span>
        ) : null}
      </div>
      <div className="flex items-start mt-2 mb-2">
        <IconFont type="icon-location-1" className="text-sm mt-[2px]" />
        <span className="ml-1 text-sm text-[#5E5E5E]">{info?.shopAddress}</span>
      </div>
      <div
        className="text-primary text-sm flex  items-center"
        onClick={() => {
          window.location.href = `tel:${info?.headMobile || info?.shopPhone}`;
        }}
      >
        <IconFont type="icon-rongqi" />
        <span className="ml-1">电话联系</span>
      </div>
    </div>
  );
});

export default ShopInfoCard;
