import React, { useEffect, useMemo, useState } from 'react';
import mapUrl from '@src/assets/images/map.png';
import { uploadImage } from '@src/common/api';
import { IconFont, Loading } from '@src/components';
import { IPopup } from '@src/components/IPopup';
import { QueryTaskListItem, TWatermark } from '@src/pages/tasks/api.type';
import { GlobalStore, SignStore } from '@src/store';
import { uploadFile } from '@src/utils/utils';
import { H5Bridge } from '@tastien/rn-bridge';
import { useRequest } from 'ahooks';
import { Upload } from 'antd';
import { Button, Dialog, Image, Toast } from 'antd-mobile';
import { isString } from 'lodash';
import { toJS } from 'mobx';
import { observer } from 'mobx-react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import ShopInfoCard from './shopInfo';
import { getShopDetail, reportShopLocation, submitPatrolReport, taskSignIn, taskSignOut } from '../api';
import { base64ToFile } from '../checkList/components/systemImageUpload';

type newSelectAddress = {
  address: string;
  name: string;
  location: string;
};
const Sign: React.FC = observer(() => {
  const [routerParams] = useSearchParams();
  const navigate = useNavigate();

  const { shopId, taskId, signed, isSubmitSignOutReport, summaryText, notFilledItemHandleType } = useMemo(() => {
    const taskId = routerParams.get('taskId')?.split('?')?.[0];
    const shopId = routerParams.get('shopId')?.split('?')?.[0];
    const signed = routerParams.get('signed')?.split('?')?.[0]; // 0-签到;1-签离
    const isSubmitSignOutReport = routerParams.get('isSubmitSignOutReport')?.split('?')?.[0];
    const summaryText = routerParams.get('summaryText')?.split('?')?.[0];
    const notFilledItemHandleType = routerParams.get('notFilledItemHandleType')?.split('?')?.[0];
    return {
      shopId,
      taskId,
      signed,
      isSubmitSignOutReport,
      summaryText,
      notFilledItemHandleType,
    };
  }, [routerParams]);
  const [uploadImgInfo, setUploadImgInfo] = useState({ id: '', url: '' });
  const [uploading, setUploading] = useState(false);
  const [errorPopupVisible, setErrorPopupVisible] = useState(false);
  const [subLoading, setSubLoading] = useState(false);
  const [signText, setSignText] = useState('');
  const [newSelectAddress, setNewSelectAddress] = useState<newSelectAddress>();
  useEffect(() => {
    const initData = async () => {
      const config = await GlobalStore.getCorporationConfig();
      console.log('config=', config);
      if (shopId && taskId && signed) {
        const shopInfo = await getShopDetail({ shopIds: [shopId] });
        SignStore.setTaskShopInfo({ ...shopInfo?.[0], taskId, signed });
        SignStore.initial({
          shopInfo: shopInfo?.[0],
          shopLocation: {
            latitude: shopInfo?.[0]?.latitude,
            longitude: shopInfo?.[0]?.longitude,
          },
          signDistance: +config?.CHECK_IN_DISTANCE,
        });
      } else {
        const info = toJS(SignStore.queryTaskShopInfo);
        SignStore.initial({
          shopInfo: info,
          shopLocation: { latitude: info?.shopLat, longitude: info?.shopLog },
          signDistance: +config?.CHECK_IN_DISTANCE,
        });
      }
    };
    initData();
  }, [shopId, taskId, signed]);
  const { run: feedbackError, loading: feedbackLoading } = useRequest(
    async () => {
      const addressInfo = newSelectAddress?.location?.split(',');
      const params = {
        longitude: +addressInfo?.[0]!,
        latitude: +addressInfo?.[1]!,
        title: newSelectAddress?.name,
        address: isString(newSelectAddress?.address) ? newSelectAddress?.address : '-',
      };
      await reportShopLocation({
        shopId: SignStore?.queryTaskShopInfo?.shopId as number,
        ...params,
      });
      return params;
    },
    {
      manual: true,
      onSuccess: (data) => {
        Toast.show({
          icon: 'success',
          content: '反馈成功',
          duration: 1000,
          afterClose: () => {
            SignStore.edited = true;
            SignStore.setShopLocation(data as any);
            setErrorPopupVisible(false);
          },
        });
      },
    },
  );
  console.log(SignStore?.queryTaskShopInfo, '=SignStore?.queryTaskShopInfo');

  useEffect(() => {
    console.log(SignStore?.queryTaskShopInfo, '=SignStore?.queryTaskShopInfo');
    const isSigned = +signed === 1;
    const signText = isSigned ? '签离' : '签到';
    setSignText(signText);
  }, [signed]);
  console.log(JSON.stringify(SignStore?.userLocation), '=当前定位');
  console.log(JSON.stringify(SignStore?.shopLocation), '=门店地址：');
  console.log(JSON.stringify(SignStore?.distance), '=distance');

  const shopInfo: QueryTaskListItem = useMemo(() => {
    return SignStore.queryTaskShopInfo!;
  }, [SignStore.queryTaskShopInfo]);

  useEffect(() => {
    SignStore.setUserLocation({
      longitude: 0,
      latitude: 0,
      address: '',
      title: '',
    });
    // 通知APP获取定位
    if (window?.['ReactNativeWebView']?.['postMessage']) {
      // 安卓
      window['ReactNativeWebView']['postMessage'](JSON.stringify({ getLocation: true }));
    } else {
      // ios
      window.parent.postMessage(JSON.stringify({ getLocation: true }), '*');
    }
    function handleEvent(e) {
      if (e.data && isString(e.data)) {
        const result = JSON.parse(e.data);
        console.log(result, '=result1111');
        // 监听选址回调格式：{"address": "xxx", "location": "116.372939,39.910957", "name": "sss"}
        // 监听定位回调格式：{"location":{"longitude":116.37296,"latitude":39.91095}}
        if (result?.location || result?.address) {
          result?.address ? setNewSelectAddress(result) : SignStore.setUserLocation(result?.location);
        }
      }
    }
    document.addEventListener('message', handleEvent); // 安卓端监听
    window.addEventListener('message', handleEvent); // ios端监听
    return () => {
      document.removeEventListener('message', handleEvent);
      window.addEventListener('message', handleEvent);
    };
  }, []);
  console.log(toJS(SignStore), '=SignStore');
  const sign = async () => {
    if (!SignStore.distance && SignStore.distance !== 0) {
      Toast.show({
        content: '门店经纬度信息缺失，请联系管理员更新',
      });
      return;
    }
    try {
      const params = {
        image: uploadImgInfo?.id,
        taskId: SignStore?.queryTaskShopInfo?.taskId!,
        lat: SignStore?.userLocation?.latitude,
        log: SignStore?.userLocation?.longitude,
        distance: SignStore?.distance,
      };
      setSubLoading(true);
      if (signText === '签到') {
        await taskSignIn(params);
        navigate(`/patrol/shopcheck?taskId=${SignStore?.queryTaskShopInfo?.taskId}`, {
          replace: true,
        });
      } else {
        // 签离
        await taskSignOut(params);
        if (isSubmitSignOutReport) {
          await submitPatrolReport({
            id: +SignStore?.queryTaskShopInfo?.taskId!,
            summaryContext: summaryText || '',
            notFilledItemHandleType: notFilledItemHandleType || 'SET_FULL_SCORE',
          });
          navigate(`/patrol/reportdetail?taskId=${SignStore?.queryTaskShopInfo?.taskId}`, {
            replace: true,
          });
          return;
        }
        navigate(`/tasks/list/principal/patrol?queryType=principal&workType=patrol`, {
          replace: true,
        });
      }
    } catch (error) {
      console.log(error, '=error');
    } finally {
      setSubLoading(false);
    }
  };

  const onhandUploadFile = async (file: File, mime: string) => {
    Toast.show({
      icon: 'loading',
      content: '上传中…',
      duration: 0,
    });
    try {
      setUploading(true);
      const res: any = await uploadFile(base64ToFile(`data:image/jpeg;base64,${file}`, mime) as File, false, true);
      console.log(res, '=res');
      const params = {
        bucket: res?.bucket!,
        key: res?.key,
        originName: res?.originName,
      };
      const result = await uploadImage(params, TWatermark.default);
      setUploadImgInfo(result || {});
    } catch {
      Toast.show('上传失败，请重新上传');
    } finally {
      Toast.clear();
      setUploading(false);
    }
  };

  return (
    <Loading spinning={uploading} loadingText="上传中">
      <div className="flex flex-col justify-between h-full items-center ">
        <ShopInfoCard info={shopInfo} />
        <div>
          {!!uploadImgInfo?.url && (
            <Image
              src={uploadImgInfo?.url}
              onClick={async () => {
                try {
                  const {
                    data: {
                      image: { data, mime },
                    },
                  } = await H5Bridge.imagePicker.openCamera();
                  onhandUploadFile(data, mime);
                } catch (e) {
                  Toast.show('上传失败');
                }
              }}
              style={{ width: 200, height: 200 }}
              fit="contain"
            />
          )}
        </div>
        {SignStore.out ? (
          <div className="flex flex-col justify-center rounded-[80px] items-center w-[160px] h-[160px] bg-[#B8B8B8]">
            <IconFont type="icon-camera-disabled" className="text-[52px] text-[#FFFFFF]" />
            <span className="text-base text-[#FFFFFF]">无法{signText}</span>
          </div>
        ) : (
          <React.Fragment>
            {uploadImgInfo?.id ? (
              <div>
                <Button
                  block
                  color="primary"
                  loading={subLoading}
                  onClick={() => {
                    sign();
                  }}
                >
                  {isSubmitSignOutReport ? '签离并提交报告' : '确定'}
                </Button>
              </div>
            ) : (
              <div
                onClick={async () => {
                  try {
                    const {
                      data: {
                        image: { data, mime },
                      },
                    } = await H5Bridge.imagePicker.openCamera();
                    onhandUploadFile(data, mime);
                  } catch (e) {
                    Toast.show('上传失败');
                  }
                }}
                className="flex flex-col justify-center rounded-[80px] items-center w-[160px] h-[160px] bg-[#00BBB4]"
              >
                <IconFont type="icon-camera" className="text-[52px] text-[#FFFFFF]" />
                <span className="text-base text-[#FFFFFF]">拍照{signText}</span>
              </div>
            )}
          </React.Fragment>
        )}
        {SignStore.edited && <div className="text-primary text-sm">您已上传纠正地址</div>}
        <Upload
          id="signUpload"
          showUploadList={false}
          beforeUpload={(file) => {
            const isImgOrPdf = file.type.startsWith('image');
            if (!isImgOrPdf) {
              Toast.show('只能上传图片文件');
            }
            const isLt20M = file.size / 1024 / 1024 <= 20;
            if (!isLt20M) {
              Toast.show('上传文件不可大于 20M');
            }
            return (isImgOrPdf && isLt20M) || Upload.LIST_IGNORE;
          }}
          customRequest={async ({ file, onError }) => {
            try {
              setUploading(true);
              const res = await uploadFile(file as File, false, true, 'PATROL');
              const params = {
                bucket: res?.bucket!,
                key: res?.key,
                originName: res?.originName,
              };
              const result = await uploadImage(params, TWatermark.default);
              setUploadImgInfo(result || {});
            } catch (error) {
              Toast.show('上传失败，请重新上传');
              onError?.(error as Error);
            } finally {
              setUploading(false);
            }
          }}
        >
          <div style={{ display: 'none' }}>上传控件</div>
        </Upload>
        <div
          className="flex relative items-center justify-between w-full bg-[#fff] p-4 h-[80px]"
          onClick={() => {
            if (SignStore.out) {
              setErrorPopupVisible(true);
            }
          }}
        >
          <div className="flex items-center">
            <span className="absolute left-[12px] top-[-20px]">
              <Image width={88} src={mapUrl} />
            </span>
            {SignStore.out ? (
              <div className="ml-[100px] ">
                <div className="text-[#141414] text-sm leading-[22px]">当前位置超出{signText}范围</div>
                <div className="text-[#858585] text-sm leading-[22px]">请检查异常</div>
              </div>
            ) : (
              <div className="text-[#141414] text-sm leading-[22px] ml-[100px] ">已进入到{signText}范围</div>
            )}
          </div>
          <div>{SignStore.out && <IconFont type="icon-chevron-right" className="text-base text-[#B8B8B8]" />}</div>
        </div>
      </div>
      <IPopup
        title="检查门店定位是否准确"
        visible={errorPopupVisible}
        closeOnMaskClick={false}
        onClose={() => setErrorPopupVisible(false)}
      >
        <div className="px-4">
          <div className="text-[#141414] text-sm leading-[14px] mt-4 mb-1">门店详细地址:</div>
          <div className="text-[#5E5E5E] text-sm leading-[22px]">{SignStore?.queryTaskShopInfo?.shopAddress}</div>
          <div
            className="mt-7 text-[#141414] text-sm leading-[14px]"
            onClick={() => {
              Dialog.confirm({
                content: (
                  <div className="text-center text-[#141414] text-base">
                    请选择您当前真实的门店地址。我们会将您纠正后的门店地址上报给管理人员进行数据改进。
                  </div>
                ),
                onConfirm: async () => {
                  if (window?.['ReactNativeWebView']?.['postMessage']) {
                    // 安卓
                    window['ReactNativeWebView']['postMessage'](JSON.stringify({ selectLocation: true }));
                  } else {
                    // ios
                    window.parent.postMessage(JSON.stringify({ selectLocation: true }), '*');
                  }
                },
              });
            }}
          >
            您当前的位置：
            {newSelectAddress ? (
              <div>
                <div className="text-[#5E5E5E] text-sm my-2">{newSelectAddress?.name}</div>
                <div className="text-[#5E5E5E] text-sm ">{newSelectAddress?.address}</div>
              </div>
            ) : (
              <span className="leading-[20px] text-primary">
                去选择
                <IconFont type="icon-chevron-right" />
              </span>
            )}
          </div>
          <div className="mt-8">
            <Button
              block
              color="primary"
              loading={feedbackLoading}
              disabled={!newSelectAddress}
              onClick={feedbackError}
            >
              反馈异常
            </Button>
          </div>
        </div>
      </IPopup>
    </Loading>
  );
});

export default Sign;
