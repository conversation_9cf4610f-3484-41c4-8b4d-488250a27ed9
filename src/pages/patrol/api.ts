import { get, post, put } from '@src/api';
import { userStore } from '@src/store';
import { roleTypeIsManage } from '@src/utils/tokenUtils';
import { round } from 'lodash';
import {
  GeoLocation,
  patrolTaskListResp,
  PlanShopInfo,
  QueryReportDetailParams,
  ReportDetailInfo,
  SelfReportDetailItem,
  ShopListRes,
  SignParams,
  SopListItem,
  TaskListParams,
  TReviewSaveParams,
  UpdateChecklistParams,
} from './api.type';
import { reportStatusEnum } from '../reportDetail/patrol';

export const getShopDetail = async (params: { shopIds: string[] }) =>
  post<ShopListRes[]>('/om-api/patrol/play/shop/list', { data: params });

// 获取巡检点评详情
export const getPatrolReviewDetail = (taskId: number | string) => {
  return get(`/om-api/common/patrol/report/${taskId}/review/detail`, {});
};
/** 报告点评保存 */
export const saveReview = async (data: TReviewSaveParams) => {
  return post(`/om-api/common/patrol/report/review/save`, {
    data,
  });
};
/** 报告点评提交 */
export const submitReview = async (taskReportId: number) => {
  return post(`/om-api/common/patrol/report/${taskReportId}/review/submit`, {});
};

export const updateChecklist = (data: UpdateChecklistParams) => {
  return post<{
    taskId?: number;
    checkTypeId?: number;
    filledItemCount?: number;
    relatedFilledItemCount?: number; // 必检项
    allFilledItemCount?: number;
    copyUserIds?: number[];
    status?: number;
  }>('/om-api/corp/patrol/report/save/item', { data });
};

export const localRectify = (data: { id: number; reason: string; images: number[] }) => {
  return post('/om-api/corp/patrol/report/item/local-rectify', { data });
};

export type UserBase = {
  userId: number;
  nickname: string;
};
/** 获取抄送人(巡检人)数据 */
export const queryAllUserBase = async () =>
  post<UserBase[]>(`/yy-api/user/queryAllUserBase`, {
    data: { authPermission: true },
  });
type GetShopAndShopEmpByTaskRes = {
  shopDTO: {
    name?: string;
  };
  shopEmployees: {
    id: 0;
    shopId: 0;
    userId: 0;
    userName: '';
    roleId: 0;
    corpId: 0;
    roleName: '';
    roleCode: '';
  }[];
};
/** 获取跟进人数据 */
export const getShopAndShopEmpByTaskApi = (data: { taskId: number }) => {
  return post<GetShopAndShopEmpByTaskRes>('/yy-api/v1/patrol/report/getShopAndShopEmpByTask', {
    data,
  });
};
const getReportDetailButtonEnums = (data: ReportDetailInfo, isSuper?: boolean) => {
  const { reportStatus, hasNeedReview } = data;

  let buttonEnums: string[] = [];

  if (roleTypeIsManage()) {
    // 督导
    if (reportStatus === reportStatusEnum.待提交) {
      buttonEnums = ['MODIFY_SCORE', 'SUBMIT_REPORT'];
    }
    if (reportStatus === reportStatusEnum.已提交 || (isSuper && reportStatus === reportStatusEnum.已确认)) {
      buttonEnums = ['RECALL_REPORT'];
    }
    if (hasNeedReview) {
      // 巡检报告已确认未点评时显示点评按钮
      buttonEnums.push('SUBMIT_PATROL_REVIEW');
    }
  } else {
    // 店长
    if (reportStatus === reportStatusEnum.已提交) {
      buttonEnums = ['REJECT_REPORT', 'CONFIRM_REPORT'];
    }
  }
  return buttonEnums;
};
// 巡检报告详情
export const queryPatrolReportDetail = async (params: QueryReportDetailParams) => {
  const data = await post<ReportDetailInfo>(`/om-api/common/patrol/report/detail`, {
    data: {
      taskId: params.taskId,
      notFilledItemHandleType: params?.notFilledItemHandleType,
      filterHasApply: params?.filterHasApply,
    },
  });
  return data;
};
// 报告任务进度

export type ProgressType =
  | 'CREATED'
  | 'FOOD_SAFETY_ARRIVE_SHOP_CONFIRM'
  | 'FOOD_SAFETY_ARRIVE_SHOP_EXE_PERIOD_AUDIT'
  | 'FOOD_SAFETY_ARRIVE_SHOP_CONFIRM_EXAM_PASS'
  | 'REVIEWS'
  | 'SUBMIT'
  | 'MODIFY_SCORE'
  | 'REVOCATION_REPORT'
  | 'REVOCATION_REVIEWS'
  | 'CONFIRM';
export type ProgressRes = {
  id: number;
  type: ProgressType;
  remark: string;
  createTime: string;
  operatorUserName: string;
};
// 公司员工任务进度
export const getCompanyWorkProgress = (taskId: number | string) => {
  return get<ProgressRes[]>(`/om-api/corp/patrol/task/food-safety/arrive-shop/${taskId}/progress-list`, {});
};
//
export const getShopWorkProgress = (taskId: number | string) => {
  return get<ProgressRes[]>(`/om-api/shop/patrol/task/food-safety/arrive-shop/${taskId}/progress-list`, {});
};
export type ReportLogsRes = {
  createTime: string;
  id: number;
  operationType: 'SUBMIT' | 'REVOCATION' | 'CONFIRM';
  oprUserName: string;
};
export type ManagPhoneRes = {
  alive: number;
  isOpen: boolean;
  nickname: string;
  phone: string;
  userId: number;
  username: string;
};
// 获取巡检报告操作日志
export const getPatrolReportLogs = (taskId: number | string) => {
  return get<ReportLogsRes[]>(`/om-api/common/patrol/report/${taskId}/logs`, {});
};
// 获取自检检报告操作日志
export const getSelfReportLogs = (taskId: number | string) => {
  return get<ReportLogsRes[]>(`/om-api/common/self/task/report/${taskId}/logs`, {});
};
// 获取店长信息
export const getManagePhone = (shopId: number | string) => {
  return get<ManagPhoneRes[]>(`/om-api/common/patrol/report/getUserByShopIdAndRole`, { params: { shopId } });
};
// 签到
export const taskSignIn = (params: SignParams) => {
  return post('/om-api/corp/patrol/task/sign-in', { data: params });
};
// 签离
export const taskSignOut = (params: SignParams) => {
  return post('/om-api/corp/patrol/task/sign-out', { data: params });
};
export const reportShopLocation = (
  params: GeoLocation & {
    shopId: number;
  },
) => {
  return post('/om-api/upload/error/address', { data: params });
};
// 查询巡检计划统计信息
export const getPatrolTaskStatistics = async (params: { planId: number }) => {
  const data = await get('/om-api/corp/app/patrol/plan/statistic-info', {
    params,
  });
  return data
    ? {
        ...data,
        reportCount: data.reportCount || 0,
        patrolFinishRatio: round(data.patrolShopCount / data.planPatrolShopCount, 2) || null,
        necessaryFinishRatio: round(data.necessaryCount / data.planNecessaryCount, 2) || null,
        reportFinishRatio: round(data.reportCount / data.planReportCount, 2) || null,
      }
    : {};
};

// 查询巡检任务计划下任务明细列表
export const getPatrolTaskList = (params: TaskListParams) => {
  return post<patrolTaskListResp>('/om-api/corp/app/patrol/task/list', {
    data: params,
  });
};
export const cancelTask = (taskId: number) => {
  return post('/om-api/corp/patrol/task/cancel', { data: { taskId } });
};
export const readSop = async (sopId: number) => post<boolean>(`/om-api/sop/read?sopId=${sopId}`, {});
export const querySopDetail = async (sopId: number) =>
  get<SopListItem[]>(`/om-api/common/sop/${sopId}/per/content`, {});
// 获取巡检门店列表
export const queryPlanShopList = async (data: {
  planId: string;
  groupId?: number;
  shopIds?: number[];
  distance?: number;
  lat?: number;
  log?: number;
  withDevice?: 1 | 0;
  deviceStatus?: 1 | 0;
}) =>
  post<PlanShopInfo[]>('/om-api/corp/patrol/task/shop/list', {
    data,
  });
// 创建巡检计划任务
export const createPlanTask = async (params: { planId: string; shopIds: number[]; taskDate?: string }) => {
  return post<{ taskIds: number[] }>('/om-api/corp/patrol/task/add-by-plan', {
    data: params,
  });
};
const getSelfReportDetailButtonEnums = (
  detail: SelfReportDetailItem,
  notFilledItemHandleType?: string | undefined | null,
) => {
  const { status, issuesCount, noReformCount, generateRectification, hasRectificationOperation } = detail;
  const {
    yytUserInfo: { corpRoleList, shopManager },
  } = userStore;
  const isAdmin = corpRoleList?.some((v) => v.roleCode === 'PATROL_SUPER');
  let buttonEnums: string[] = [];

  if (shopManager) {
    if (status === 1) {
      buttonEnums = ['SUBMIT_REPORT'];
    }
    if (status === 4) {
      buttonEnums = ['RECALL_REPORT'];
    }
  } else {
    /** 是否展示撤回点评按钮 */
    const showRevokeReportComment = (() => {
      // 已点评状态
      if (status === 5) {
        // 没有有配置发起整改
        if (!generateRectification) {
          return true;
        } else {
          // 没有问题项
          if (issuesCount === 0) {
            return true;
          } else {
            // 有问题项，已全部整改完毕
            if (noReformCount === 0) {
              return false;
            }
            // 有问题项，门店还未操作时
            if (!hasRectificationOperation) {
              return true;
            } else {
              // 有问题项，门店已有操作
              return isAdmin;
            }
          }
        }
      }
    })();

    if (status === 4) {
      buttonEnums = notFilledItemHandleType === 'SET_FULL_SCORE' ? ['SUBMIT_REPORT_COMMENT'] : ['PREVIEW_SELF_REPORT'];
    }
    if (showRevokeReportComment) {
      buttonEnums = ['REVOKE_REPORT_COMMENT'];
    }
  }
  return buttonEnums;
};
// 查询自检报告详情
export const querySelfReportDetail = async (params: QueryReportDetailParams) => {
  const detail = await post<SelfReportDetailItem>(`/yy-api/v1/patrol/self/report/detail`, {
    data: {
      reportId: params?.reportId,
      notFilledItemHandleType: params?.notFilledItemHandleType,
    },
  });
  return {
    ...detail,
    buttonEnums: getSelfReportDetailButtonEnums(detail!, params?.notFilledItemHandleType),
  };
};
// 确认报告
export const confirmReport = async (id: number) => post('/om-api/shop/patrol/report/confirm', { data: { id } });
// 撤回巡检报告
export const cancelReport = async (data: { id: number }) => post('/om-api/corp/patrol/report/revocation', { data });

// 提交巡检报告
export const submitPatrolReport = async (data: {
  id: number;
  summaryContext?: string;
  notFilledItemHandleType?: 'SET_FULL_SCORE' | 'SET_NOT_APPLY' | string;
}) =>
  post('/om-api/corp/patrol/report/submit', {
    data: {
      notFilledItemHandleType: 'SET_FULL_SCORE', // 默认未填写设置为满分
      ...data,
    },
  });
export const submitPatrolSummary = async (data: { context: string; id: number }) =>
  post('/om-api/corp/patrol/report/summary', { data });

/** 巡检报告点评项 */
export const savePatrolReviewItem = async (data: {
  content: string;
  itemId: number;
  qualified: boolean;
  taskId: number;
}) => await post(`/om-api/corp/patrol/report/review/item`, { data });

/** 巡检报告第二点评项 */
export const savePatrolSecondReviewItem = async (data: {
  secondContent: string;
  itemId: number;
  secondQualified: boolean;
  taskId: number;
}) => await post('/om-api/corp/patrol/report/second/review/item', { data });

/** 巡检报告点评提交 */
export const patrolReportReviewSubmit = async (data: { overallReviewContent?: string; taskId: number }) =>
  await put(`/om-api/corp/patrol/report/review/submit`, { data });

/** 巡检报告第二点评提交 */
export const patrolReportSecondReviewSubmit = async (data: { secondOverallReviewContent?: string; taskId: number }) =>
  await post('/om-api/corp/patrol/report/second/review/submit', { data });
