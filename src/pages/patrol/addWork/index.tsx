import { useMemo, useState } from 'react';
import { CustomerFilter, IconFont, Loading } from '@src/components';
import { AddWorkFilter, init } from '@src/components/AddWorkFilter';
import { CAN_SELECT_SHOP_STATUS, TOAST_TITLE } from '@src/pages/patrol/enum';
import { useRequest } from 'ahooks';
import { Button, DatePicker, SearchBar, Toast } from 'antd-mobile';
import cn from 'classnames';
import dayjs from 'dayjs';
import { has, keys } from 'lodash';
import { useAliveController } from 'react-activation';
import { useSearchParams } from 'react-router-dom';
import styles from './index.module.scss';
import { createPlanTask, queryPlanShopList } from '../api';
import { ShopCard } from '../shopCard';
const AddWork = () => {
  const [routerParams] = useSearchParams();
  const [searchValue, setSearchValue] = useState<string>('');
  const [searchParams, setSearchParams] = useState<any>();
  const [filterVisible, setFilterVisible] = useState(false);
  const [selectedShopIds, setSelectedShopIds] = useState<number[]>([]);
  const [calendarVisible, setCalendarVisible] = useState(false);
  const [date, setDate] = useState(dayjs().toDate());
  const { refresh } = useAliveController();
  const { planId, hasPreparingShop } = useMemo(() => {
    const planId = routerParams.get('planId');
    const hasPreparingShop = routerParams.get('hasPreparingShop');
    return {
      planId,
      hasPreparingShop,
    };
  }, [routerParams]);
  const { data, loading } = useRequest(
    async () => {
      if (!planId) throw new Error('null');
      const res = await queryPlanShopList({
        planId,
        ...searchParams,
      });
      return res?.filter((item) => item?.hasEmployee) || [];
    },
    {
      refreshDeps: [planId, searchParams?.groupId, searchParams?.shopIds],
    },
  );
  const { run, loading: createLoading } = useRequest((data) => createPlanTask(data), {
    manual: true,
    onSuccess: () => {
      Toast.show({
        icon: 'success',
        content: '创建成功',
        duration: 1000,
        afterClose: () => {
          refresh('/patrol/planList/planDetail');
          setTimeout(() => {
            // eslint-disable-next-line no-restricted-globals
            history.back();
          }, 500);
        },
      });
    },
  });

  console.log(routerParams, '=routerParams');
  console.log(hasPreparingShop, '=hasPreparingShop');

  const dataSource = useMemo(() => {
    let dataSource = data;

    if (searchValue) {
      dataSource = dataSource?.filter((item) => {
        const searchName = item.shopName?.toLocaleLowerCase().includes(searchValue?.toLocaleLowerCase());
        const searchNo = item?.shopId?.toLocaleLowerCase().includes(searchValue?.toLocaleLowerCase());
        return searchName || searchNo;
      });
    }

    const { type, tag, sorter } = searchParams || {};
    if (type) {
      dataSource = dataSource?.filter((item) => item.shopType === type);
    }

    if (tag) {
      tag === 'hasPatrol'
        ? (dataSource = dataSource?.filter((item: any) => !item[tag]))
        : (dataSource = dataSource?.filter((item: any) => !!item[tag]));
    }
    if (sorter) {
      sorter === 'lastPatrolTime'
        ? (dataSource = dataSource?.sort((a, b) => {
            return dayjs(a.lastPatrolTime).unix() - dayjs(b.lastPatrolTime).unix();
          }))
        : (dataSource = dataSource?.sort((a, b) => {
            return a.lastPatrolScore - b.lastPatrolScore;
          }));
    }
    return dataSource;
  }, [data, searchValue, searchParams]);
  console.log(searchParams, '=searchParams');

  const canSelectShopStatus = CAN_SELECT_SHOP_STATUS[(hasPreparingShop as 'true' | 'false') || false];
  return (
    <Loading spinning={loading || createLoading}>
      <div className="flex flex-col fixed inset-x-0 inset-y-0">
        <div className="grow overflow-y-scroll">
          <div className="bg-white  pl-4 pr-4 pt-4 pb-2 mb-2">
            <SearchBar
              className={`${styles.iAdmSearchBar} flex-1`}
              style={{ '--border-radius': '8px', '--height': '2.5rem' }}
              onClear={() => setSearchValue('')}
              onSearch={setSearchValue}
              placeholder="请输入门店名称/门店编号"
            />
            <div className="flex justify-between items-center  mt-2">
              <CustomerFilter
                // value={{ groupId: searchParams?.groupId, shopId: searchParams?.shopIds }}
                onChange={(e: any) => {
                  setSearchParams((pre: any) => ({
                    ...pre,
                    groupId: e?.shopCodes?.length > 0 ? undefined : e.groupId,
                    shopIds: e.shopCodes,
                  }));
                }}
              />
              <button
                className={cn(
                  `text-sm flex items-center leading-[14px] ${
                    filterVisible ? 'text-[#141414]' : 'text-[#5E5E5E]'
                  } focus:outline-none`,
                  {
                    'text-primary': keys(init).some((o) => !!searchParams?.[o] && has(searchParams, o)),
                  },
                )}
                onClick={() => {
                  setFilterVisible(true);
                }}
              >
                <div className="w-[2px] h-5 bg-black/[0.03] mr-[18px]" />
                筛选
                <IconFont type="icon-a-1111-copy" className="ml-1 text-xs" />
              </button>
            </div>
          </div>
          <div className="bg-white pl-4 pr-4">
            {dataSource?.map((item: any) => {
              return (
                <ShopCard
                  itemInfo={item}
                  isCheckbox
                  checked={selectedShopIds?.includes(item.shopId)}
                  onClick={(info) => {
                    const { shopStatus, shopId } = info;
                    if (!shopId && shopId !== 0) {
                      return;
                    }
                    const toastTitle =
                      TOAST_TITLE[(shopStatus as 'current' | 'PREPARING' | 'CLOSE' | 'TO_BE_OPENED') || 'current'];
                    console.log(canSelectShopStatus, '=canSelectShopStatus');
                    if (!canSelectShopStatus?.includes(shopStatus as string)) {
                      return Toast.show({
                        content: toastTitle,
                      });
                    }
                    setSelectedShopIds((shopIds) =>
                      shopIds.includes(shopId) ? shopIds.filter((id) => id !== shopId) : [...shopIds, shopId],
                    );
                  }}
                />
              );
            })}
          </div>
        </div>
        <div className="pb-8 pt-4 px-4 shrink-0  bg-white">
          <Button
            block
            style={{
              '--background-color': '#378BFF',
              '--text-color': '',
            }}
            disabled={!selectedShopIds?.length}
            className="border-[0.5px] text-base text-[#FFFFFF] border-primary"
            onClick={() => {
              setCalendarVisible(true);
            }}
          >
            {selectedShopIds.length ? `加入任务（${selectedShopIds?.length}）` : '请选择门店'}
          </Button>
        </div>
      </div>
      <DatePicker
        title="选择日期"
        visible={calendarVisible}
        onClose={() => {
          setCalendarVisible(false);
        }}
        min={dayjs().toDate()}
        value={dayjs(date).toDate()}
        onConfirm={(e) => {
          setDate(e);
          run({
            planId,
            shopIds: selectedShopIds,
            taskDate: dayjs(e).format('YYYY-MM-DD'),
          });
        }}
      />
      <AddWorkFilter
        value={searchParams}
        open={filterVisible}
        onClose={() => setFilterVisible(false)}
        onChange={(value: Record<string, any>) => {
          setSearchParams((pre: any) => ({ ...pre, ...value }));
          setFilterVisible(false);
        }}
      />
    </Loading>
  );
};

export default AddWork;
