import { useEffect, useMemo, useState } from 'react';
import { report } from '@src/common/report';
import { DateFilter, IconFont } from '@src/components';
import Loading from '@src/components/Loading';
import { ShopTree } from '@src/components/ShopTree';
import { InfiniteScroll, Tabs } from 'antd-mobile';
import classNames from 'classnames';
import clsx from 'clsx';
import dayjs from 'dayjs';
import { ERoleType } from '..';
import { TReportItem, useSelfReportList } from '../api';
import { DZSelect } from '../components/Select';
import { SelfPatrolTaskCard } from '../components/SelfPatrolTaskCard';
import { UnderPatrolFilter } from '../components/UnderPatrolFilter';

// 店长店助 -> 待整改   督导 -> 待审核
export enum ERreformStatus {
  待整改 = 'PEFORM_FEEDBACK',
  待审核 = 'WAIT_AUDIT',
  已过期 = 'EXPIRED',
  已整改 = 'PASS',
}

const cyclicalPatternsMap = {
  ALL: '自检任务',
  DAY: '日任务',
  WEEK: '周任务',
  MONTH: '月任务',
  ZHENGGAI: '整改任务',
};

type TRequestType = {
  recordStatus?: 'SUBMITTED' | 'REVIEWED';
  reportDateEnd: string;
  reportDateStart: string;
  shopCodes: any[];
  groupId?: number;
  pageNo?: number;
  pageSize?: number;
  // ZGENGGAI,ALL是前端自己维护的 真实的只有 day week month
  cyclicalPatterns?: 'DAY' | 'WEEK' | 'MONTH' | 'ZHENGGAI' | 'ALL';
  reformStatus?: ERreformStatus;
  taskIds?: number;
  workSheetId?: number;
};

const defaultParams: TRequestType = {
  reportDateStart: dayjs().subtract(6, 'day').format('YYYY-MM-DD'),
  reportDateEnd: dayjs().format('YYYY-MM-DD'),
  shopCodes: [],
  recordStatus: undefined,
  // groupId: '',
  cyclicalPatterns: 'ALL',
};

/**
 * 自检
 *
 *  督导：
 *    状态：待点评 已点评 全部
 *
 *  店长：
 *    状态：待提交 带点评 已点评 全部
 */

type TProps = {
  roleType: ERoleType;
};

export function SelfPage({ roleType }: TProps) {
  const [params, setParams] = useState<TRequestType>({ ...defaultParams });
  const [filterVisible, setFilterVisible] = useState(false);
  // const { data, refresh } = useRequest(
  //   () =>
  //     getSelfReportCount({
  //       taskIds: params.taskIds ? [params.taskIds] : undefined,
  //       workSheetId: params.workSheetId,
  //       groupId: params.groupId,
  //       shopIds: params.shopCodes,
  //       recordStatus: params.recordStatus,
  //       reportDateStart: dayjs(params.reportDateStart).startOf('day').valueOf(),
  //       reportDateEnd: dayjs(params.reportDateEnd).endOf('day').valueOf(),
  //       cyclicalPatterns:
  //         roleType === ERoleType.店长
  //           ? undefined
  //           : params.cyclicalPatterns !== 'ZHENGGAI' && params.cyclicalPatterns !== 'ALL'
  //             ? params.cyclicalPatterns
  //             : undefined,
  //       // 督导只会看 待审核
  //       reformStatus: params.cyclicalPatterns === 'ZHENGGAI' ? ERreformStatus.待审核 : undefined,
  //       roleType,
  //     }),
  //   {
  //     ready: roleType === ERoleType.督导,
  //     refreshDeps: [params],
  //   },
  // );

  const {
    data: reportData,
    isLoading,
    isFetching,
    hasNextPage,
    fetchNextPage,
    refetch: refetchList,
  } = useSelfReportList(
    ['selfReport', params],
    {
      pageSize: 10,
      pageNo: 1,
      taskIds: params.taskIds ? [params.taskIds] : undefined,
      workSheetId: params.workSheetId,
      groupId: params.groupId,
      shopIds: params.shopCodes,
      recordStatus: params.recordStatus,
      reportDateStart: dayjs(params.reportDateStart).startOf('day').valueOf(),
      reportDateEnd: dayjs(params.reportDateEnd).endOf('day').valueOf(),
      cyclicalPatterns:
        roleType === ERoleType.店长
          ? undefined
          : params.cyclicalPatterns !== 'ZHENGGAI'
            ? params.cyclicalPatterns
            : undefined,
      // 督导只会看 待审核
      reformStatus: params.cyclicalPatterns === 'ZHENGGAI' ? ERreformStatus.待审核 : undefined,
      roleType,
    },
    {
      refetchOnWindowFocus: false,
      enabled: params.groupId !== undefined,
    },
  );

  const listArr = reportData?.pages.reduce((arr, page) => {
    if (page) {
      return arr.concat(page.result || []);
    }
    return arr;
  }, [] as any[]);

  const statusText = useMemo(() => {
    if (params.recordStatus === 'REVIEWED') {
      return '已点评';
    } else if (params.recordStatus === 'SUBMITTED') {
      return '待点评';
    } else {
      return '状态';
    }
  }, [params]);

  useEffect(() => {
    if (roleType === ERoleType.督导) {
      report({
        type: 'ability',
        page: '自检报告',
        abilityButton: cyclicalPatternsMap?.[params?.cyclicalPatterns!],
      });
    }
  }, [roleType, params.cyclicalPatterns]);

  useEffect(() => {
    window.addEventListener('popstate', () => {
      refetchList();
    });
  }, []);

  return (
    <>
      {roleType === ERoleType.督导 && (
        <Tabs
          className=" bg-white"
          activeKey={params.cyclicalPatterns}
          onChange={(key) => {
            setParams({ ...params, cyclicalPatterns: key as any });
          }}
          style={{
            '--title-font-size': '0.875rem',
          }}
        >
          {/* <Tabs.Tab
            title={
              <div className=" flex justify-center items-center">
                日任务{' '}
                {!!data?.dayCount && (
                  <div className=" w-2 h-2 bg-red-500 ml-2 rounded-full" />
                )}
              </div>
            }
            key={'DAY'}
          />
          <Tabs.Tab
            title={
              <div className=" flex justify-center items-center">
                周任务{' '}
                {!!data?.weekCount && (
                  <div className=" w-2 h-2 bg-red-500 ml-2 rounded-full" />
                )}
              </div>
            }
            key={'WEEK'}
          />
          <Tabs.Tab
            title={
              <div className=" flex justify-center items-center">
                月任务{' '}
                {!!data?.mouthCount && (
                  <div className=" w-2 h-2 bg-red-500 ml-2 rounded-full" />
                )}
              </div>
            }
            key={'MONTH'}
          /> */}
          <Tabs.Tab title={<div className=" flex justify-center items-center">自检任务</div>} key={'ALL'} />
          <Tabs.Tab title={<div className=" flex justify-center items-center">整改任务</div>} key={'ZHENGGAI'} />
        </Tabs>
      )}

      <div
        className=" flex items-center bg-white my-2 px-4 overflow-x-auto"
        style={{
          width: window.innerWidth,
        }}
      >
        <DateFilter
          noBg={true}
          value={[dayjs(params.reportDateStart).toDate(), dayjs(params.reportDateEnd).toDate()]}
          onChange={(e) => {
            setParams((p) => ({
              ...p,
              reportDateStart: dayjs(e[0]).format('YYYY-MM-DD'),
              reportDateEnd: dayjs(e[1]).format('YYYY-MM-DD'),
            }));
          }}
          key="month"
          type="date"
          className="mr-6 flex-shrink-0"
        />

        <ShopTree
          isInPermission
          onSelectChange={(params) => {
            setTimeout(() => {
              setParams((p) => ({
                ...p,
                shopCodes: params.shopId,
                groupId: params.groupId,
              }));
            }, 50);
          }}
        />

        {params.cyclicalPatterns !== 'ZHENGGAI' && (
          <DZSelect
            className=" ml-6 flex-shrink-0"
            showText={statusText}
            value={params?.recordStatus!}
            renderSource={[
              {
                name: '全部',
                key: undefined as any,
              },
              {
                name: '待点评',
                key: 'SUBMITTED',
              },
              {
                name: '已点评',
                key: 'REVIEWED',
              },
            ]}
            onChange={(val) => {
              setParams({ ...params, recordStatus: val as any });
            }}
          />
        )}

        <button
          className={classNames(
            `text-sm flex items-center leading-[14px] flex-shrink-0 ${
              filterVisible ? 'text-[#141414]' : 'text-[#5E5E5E]'
            } focus:outline-none`,
          )}
          onClick={() => {
            setFilterVisible(true);
          }}
        >
          <div className="w-[1px] h-5 bg-black/[0.03] mr-[18px]" />
          筛选
          <IconFont type="icon-a-1111-copy" className="ml-1 text-xs text-[#B8B8B8]" />
        </button>
      </div>

      <Loading spinning={isLoading || isFetching}>
        <div
          className={clsx('overflow-y-auto px-2')}
          style={{
            height: 'calc(100vh - 100px)',
          }}
        >
          {listArr?.map((item: TReportItem) => (
            <SelfPatrolTaskCard info={item} key={item.taskId} roleType={roleType} />
          ))}
          {/* @ts-ignore */}
          <InfiniteScroll loadMore={fetchNextPage} hasMore={hasNextPage} />
        </div>
      </Loading>
      <UnderPatrolFilter
        isSelfCheck
        value={params}
        open={filterVisible}
        onClose={() => setFilterVisible(false)}
        onChange={(value: Record<string, any>) => {
          setParams((pre: any) => ({ ...pre, ...value }));
          setFilterVisible(false);
        }}
        roleType={roleType}
        showPlan
        showWorkSheet
        showTimeStatus={params.cyclicalPatterns !== 'ZHENGGAI' ? true : false}
      />
    </>
  );
}
