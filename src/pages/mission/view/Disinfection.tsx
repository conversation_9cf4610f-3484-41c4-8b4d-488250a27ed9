import { useEffect, useMemo, useState } from 'react';
import { DateFilter, IconFont, Loading } from '@src/components';
import { ShopTree } from '@src/components/ShopTree';
import { DisinfectionTaskCard } from '@src/pages/tasks/components/DisinfectionTaskCard';
import { DisinfectionTaskFilter } from '@src/pages/tasks/components/DisinfectionTaskFilter';
import { formatDateToUTC } from '@src/utils/utils';
import { InfiniteScroll } from 'antd-mobile';
import classNames from 'classnames';
import clsx from 'clsx';
import dayjs from 'dayjs';
import { ERoleType } from '..';
import { DisinfectionItem, EPStatus, useDisinfectionReportList } from '../api';
import { DZSelect } from '../components/Select';

const taskTypeOptions = [
  { name: '日常消杀', key: 'DAILY' },
  { name: '紧急消杀', key: 'EXIGENCY' },
  { name: '一道结构消杀', key: 'FIRST_STRUCTURE' },
  { name: '二道结构消杀', key: 'SECOND_STRUCTURE' },
];

type TRequestType = {
  endDate: string;
  startDate: string;
  shopIds: any[];
  groupId?: number;
  pageNo?: number;
  pageSize?: number;
  status?: EPStatus;
  disinfectionType: string | undefined;
  disinfectionCompanyIds?: number[]; // 消杀公司
  workSheetIds?: number[] /** 检查表ID */;
  disinfectionUserIds?: number[]; // 消杀人员列表
};

const defaultParams: TRequestType = {
  startDate: dayjs().subtract(6, 'day').format('YYYY-MM-DD'),
  endDate: dayjs().format('YYYY-MM-DD'),
  disinfectionType: undefined,
  shopIds: [],
  // groupId: ,
};
type TProps = {
  roleType: ERoleType;
};

export function Disinfection({ roleType }: TProps) {
  const [params, setParams] = useState<TRequestType>({ ...defaultParams });
  const [filterVisible, setFilterVisible] = useState(false);
  console.log(roleType, '=roleType');

  const {
    data: patrolData,
    isLoading,
    isFetching,
    hasNextPage,
    fetchNextPage,
    refetch,
  } = useDisinfectionReportList(
    ['disinfectionReportList', params],
    {
      pageSize: 10,
      pageNo: 1,
      ...params,
      reportCreateTimeStart: formatDateToUTC(dayjs(params.startDate).startOf('day')),
      reportCreateTimeEnd: formatDateToUTC(dayjs(params.endDate).endOf('day')),
      shopIds: params?.shopIds?.length ? params?.shopIds : undefined,
      reportPositive: params?.reportPositive === 'ALL' ? undefined : params?.reportPositive,
    },
    {
      refetchOnWindowFocus: false,
      enabled: params.groupId !== undefined,
    },
  );

  // const listArr = list.reduce((arr, item) => arr.concat(item), []);
  const listArr = patrolData?.pages.reduce((arr, page) => {
    if (page) {
      return arr.concat(page?.data || []);
    }
    return arr;
  }, [] as any[]);

  useEffect(() => {
    window.addEventListener('popstate', () => {
      refetch();
    });
  }, []);

  useEffect(() => {
    const messageHandler = (event: any) => {
      if (event?.data) {
        const messageData = JSON.parse(event?.data)?.data;
        if (messageData?.type === 'refresh') {
          refetch();
        }
      }
    };
    document.addEventListener('message', messageHandler);
    window.addEventListener('message', messageHandler);
    return () => {
      document.removeEventListener('message', messageHandler);
      window.removeEventListener('message', messageHandler);
    };
  }, []);

  const statusText = useMemo(() => {
    const taskTypeInfo = taskTypeOptions.find((item) => item.key === params?.disinfectionType);
    if (!taskTypeInfo) {
      return '任务类型';
    }
    return taskTypeInfo.name || '';
  }, [params]);

  return (
    <>
      <div className=" flex items-center bg-white my-2 px-4 overflow-auto">
        <DateFilter
          noBg={true}
          value={[dayjs(params?.startDate).toDate(), dayjs(params?.endDate).toDate()]}
          onChange={(e) => {
            setParams((p) => ({
              ...p,
              startDate: dayjs(e?.[0]).format('YYYY-MM-DD'),
              endDate: dayjs(e?.[1]).format('YYYY-MM-DD'),
            }));
          }}
          key="month"
          type="date"
          className="mr-2 flex-shrink-0"
          noMaxFlag={true}
        />
        <ShopTree
          isInPermission
          onSelectChange={(params) => {
            setTimeout(() => {
              setParams((p) => ({
                ...p,
                shopIds: params.shopId,
                groupId: params.groupId,
              }));
            }, 50);
          }}
        />
        <DZSelect
          className=" ml-2 flex-shrink-0"
          showText={statusText}
          value={params?.disinfectionType!}
          renderSource={[
            {
              name: '全部',
              key: undefined as any,
            },
            ...taskTypeOptions,
          ]}
          onChange={(val) => {
            setParams({ ...params, disinfectionType: val as any });
          }}
        />
        <button
          className={classNames(
            `text-sm flex items-center leading-[14px] flex-shrink-0 ${
              filterVisible ? 'text-[#141414]' : 'text-[#5E5E5E]'
            } focus:outline-none`,
          )}
          onClick={() => {
            setFilterVisible(true);
          }}
        >
          <div className="w-[1px] h-5 bg-black/[0.03] mr-[18px]" />
          筛选
          <IconFont type="icon-a-1111-copy" className="ml-1 text-xs text-[#B8B8B8]" />
        </button>
      </div>

      <Loading spinning={isLoading || isFetching}>
        <div
          className={clsx('overflow-y-auto px-2')}
          style={{
            height: `calc(100vh -'100px')`,
          }}
        >
          {listArr?.map((item: DisinfectionItem) => <DisinfectionTaskCard initial={item} />)}
          {/* @ts-ignore */}
          <InfiniteScroll loadMore={fetchNextPage} hasMore={hasNextPage} />
        </div>
      </Loading>

      <DisinfectionTaskFilter
        value={params}
        open={filterVisible}
        onClose={() => setFilterVisible(false)}
        onChange={(value: Record<string, any>) => {
          setParams((pre: any) => ({ ...pre, ...value }));
          setFilterVisible(false);
        }}
      />
    </>
  );
}
