import { useEffect, useState } from 'react';
import { DateFilter, IconFont, Loading } from '@src/components';
import { ShopTree } from '@src/components/ShopTree';
import { CheckWayCNToEnEnum } from '@src/pages/tasks/enum';
import { formatDateToUTC } from '@src/utils/utils';
import { useRequest } from 'ahooks';
import { InfiniteScroll, Toast } from 'antd-mobile';
import classNames from 'classnames';
import clsx from 'clsx';
import dayjs from 'dayjs';
import { useNavigate } from 'react-router-dom';
import { ERoleType } from '..';
import { delPatrolReport, EPStatus, TPatrolItem, usePatrolReportList } from '../api';
import { DiagnosisTask } from '../components/DiagnosisTask';
import { InspectionTask } from '../components/InspectionTask';
import { UnderPatrolFilter } from '../components/UnderPatrolFilter';

enum ETaskType {
  到点巡检 = 'NORMAL',
  视频云巡检 = 'VIDEO',
  诊断巡检 = 'DIAGNOSTIC',
}

type TRequestType = {
  submitTimeEnd: string;
  submitTimeStart: string;
  shopCodes: any[];
  groupId?: number;
  pageNo?: number;
  pageSize?: number;
  status?: EPStatus;
  taskType?: ETaskType;
  /** 任务表ID */
  planId?: number;
  /** 检查表ID */
  workSheetId?: string;
  checkUserIds?: number;
  alarmLevel?: string;
};

const defaultParams: TRequestType = {
  submitTimeStart: dayjs().subtract(6, 'day').format('YYYY-MM-DD'),
  submitTimeEnd: dayjs().format('YYYY-MM-DD'),
  shopCodes: [],
  // groupId: ,
};

/**
 * 巡检
 *
 *  督导 ：
 *    巡检状态：待提交 已提交 已确认 全部
 *    巡检类型：到店巡检（权限内） 视频云巡检 到店巡检（权限外（单独入口））
 *
 *  店长：
 *    巡检状态：全部 待确认（SUBMITTED） 已确认（CONFIRMED）
 *    巡检类型：到店巡检（本质是权限内 不展示） 视频云巡检
 */
type TProps = {
  roleType: ERoleType;
};

export function PatrolPage({ roleType }: TProps) {
  const [params, setParams] = useState<TRequestType>({ ...defaultParams });
  const [filterVisible, setFilterVisible] = useState(false);
  const navigate = useNavigate();

  const { runAsync } = useRequest((params) => delPatrolReport(params), {
    onSuccess: () => {
      Toast.show('删除成功');
      // reload();
      refetch();
    },
    manual: true,
  });

  const {
    data: patrolData,
    isLoading,
    isFetching,
    hasNextPage,
    fetchNextPage,
    refetch,
  } = usePatrolReportList(
    ['patrolReportList', params],
    {
      ...params,
      pageSize: 10,
      pageNo: 1,
      submitTimeStart: formatDateToUTC(dayjs(params.submitTimeStart).startOf('day')),
      submitTimeEnd: formatDateToUTC(dayjs(params.submitTimeEnd).endOf('day')),
      checkUserIds: params.checkUserIds ? [params.checkUserIds] : undefined,
      shopIdList: params.shopCodes.length ? params.shopCodes : undefined,
      shopCodes: undefined,
      alarmLevel: params?.taskType === 'DIAGNOSTIC' ? params?.alarmLevel : undefined,
    },
    {
      refetchOnWindowFocus: false,
      enabled: params.groupId !== undefined,
    },
  );

  // const listArr = list.reduce((arr, item) => arr.concat(item), []);
  const listArr = patrolData?.pages.reduce((arr, page) => {
    if (page) {
      return arr.concat(page || []);
    }
    return arr;
  }, [] as any[]);
  console.log('listArr', listArr);

  useEffect(() => {
    window.addEventListener('popstate', () => {
      refetch();
    });
  }, []);

  return (
    <>
      <div className=" flex items-center bg-white my-2 px-4 overflow-auto">
        <DateFilter
          noBg={true}
          value={[dayjs(params.submitTimeStart).toDate(), dayjs(params.submitTimeEnd).toDate()]}
          onChange={(e) => {
            setParams((p) => ({
              ...p,
              submitTimeStart: dayjs(e[0]).format('YYYY-MM-DD'),
              submitTimeEnd: dayjs(e[1]).format('YYYY-MM-DD'),
            }));
          }}
          key="month"
          type="date"
          className="mr-6 flex-shrink-0"
        />
        <ShopTree
          isInPermission
          onSelectChange={(params) => {
            setTimeout(() => {
              setParams((p) => ({
                ...p,
                shopCodes: params.shopId,
                groupId: params.groupId,
              }));
            }, 50);
          }}
        />

        <button
          className={classNames(
            `text-sm flex items-center leading-[14px] flex-shrink-0 ${
              filterVisible ? 'text-[#141414]' : 'text-[#5E5E5E]'
            } focus:outline-none`,
          )}
          onClick={() => {
            setFilterVisible(true);
          }}
        >
          <div className="w-[1px] h-5 bg-black/[0.03] mr-[18px]" />
          筛选
          <IconFont type="icon-a-1111-copy" className="ml-1 text-xs text-[#B8B8B8]" />
        </button>
      </div>
      {roleType === ERoleType.督导 && (
        <div
          className=" bg-white py-2 flex justify-end mb-2 px-1 "
          onClick={() => {
            navigate('/missionOutSide');
          }}
        >
          <div className="text-[#378BFF] border border-solid border-[#378BFF] rounded-2xl px-2 mr-2">
            到店巡检(权限外)
          </div>
        </div>
      )}

      <Loading spinning={isLoading || isFetching}>
        <div
          className={clsx('overflow-y-auto px-2')}
          style={{
            height: `calc(100vh - ${roleType === ERoleType.督导 ? '145px' : '100px'})`,
          }}
        >
          {listArr?.map((item: TPatrolItem) =>
            item.subType === CheckWayCNToEnEnum.诊断巡检 ? (
              <DiagnosisTask info={item} key={item?.taskId} roleType={roleType} />
            ) : (
              <InspectionTask
                info={item}
                key={item?.taskId}
                roleType={roleType}
                delReportFn={async () => {
                  await runAsync({ taskId: item.taskId });
                }}
              />
            ),
          )}
          {/* @ts-ignore */}
          <InfiniteScroll loadMore={fetchNextPage} hasMore={hasNextPage} />
        </div>
      </Loading>

      <UnderPatrolFilter
        value={params}
        open={filterVisible}
        onClose={() => setFilterVisible(false)}
        onChange={(value: Record<string, any>) => {
          setParams((pre: any) => ({ ...pre, ...value }));
          setFilterVisible(false);
        }}
        roleType={roleType}
        showStatus
        showTaskType
        showPlan
        showUser
        showWorkSheet
        showReviewStatus
        showSecondReviewStatus
        showTask
      />
    </>
  );
}
