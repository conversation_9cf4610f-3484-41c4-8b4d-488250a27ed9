import { getZTGroupShop } from '@src/common/api';
import { useRequest } from 'ahooks';
import { Select } from 'antd';

interface ShopSelectProps {
  value?: any;
  onChange?: any;
}

export const ShopSelect = ({ value, onChange }: ShopSelectProps) => {
  const { data } = useRequest(getZTGroupShop, {});
  return (
    <div className="flex items-center justify-between">
      <h3 className="text-[#141414] text-sm leading-[14px]">门店</h3>
      <Select
        showSearch
        style={{ width: 200 }}
        options={data || []}
        fieldNames={{
          value: 'shopId',
          label: 'shopName',
        }}
        value={value}
        onChange={onChange}
        allowClear
        placeholder="请输入门店名称或门店编号"
        filterOption={(input, option) => {
          const shopName = (option?.shopName ?? '').toLowerCase();
          const shopId = (option?.shopId ?? '').toString().toLowerCase();
          const searchText = input.toLowerCase();
          return shopName.includes(searchText) || shopId.includes(searchText);
        }}
        mode="multiple"
        optionRender={(option) => (
          <div>
            {option.data.shopId} {option.data.shopName}
          </div>
        )}
        labelRender={(option) => {
          return (
            <div>
              {option.value} {option.label}
            </div>
          );
        }}
      />
    </div>
  );
};
