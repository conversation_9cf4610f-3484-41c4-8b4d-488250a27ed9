import { alarmTagMap } from '@src/pages/patrol/planDetail/detailCard';
import classNames from 'classnames';
import { useNavigate } from 'react-router-dom';
import { reviewStatusMap } from './InspectionTask';
import { ERoleType } from '..';
import { EPStatus, TPatrolItem } from '../api';

/**
 * 督导枚举
 */
const supervisionTagMap = {
  NOT_STARTED: <div className=" text-[#378BFF]">待提交</div>,
  SUBMITTED: <div className=" text-[#378BFF]">已提交</div>,
  CONFIRMED: <div className=" text-[#378BFF]">已确认</div>,
};

/**
 *
 * 店长枚举
 */
const managerTagMap = {
  SUBMITTED: <div className=" text-[#378BFF]">待确认</div>,
  CONFIRMED: <div className=" text-[#378BFF]">已确认</div>,
};

function getTagMapByRole(status: EPStatus, roleType: ERoleType) {
  if (roleType === ERoleType.店长) {
    // @ts-ignore
    return managerTagMap[status];
  } else {
    return supervisionTagMap[status];
  }
}

// const subTypeText = {
//   NORMAL: '到店巡检(权限内)',
//   VIDEO: '视频云巡检',
//   CROSS: '到店巡检(权限外)',
//   FOOD_SAFETY_NORMAL: '食安线下稽核',
//   FOOD_SAFETY_VIDEO: '食安线上稽核',
//   DIAGNOSTIC: '诊断巡检',
// };

/**
 * 店长
 *  待确认状态 -> 去确认
 *  已确认状态 -> 未整改问题项中 如果存在 待整改状态 (issuesCount) -> 去整改
 * 督导
 *  已确认状态 -> 未整改问题项中 如果存在 待审核 (waitAuditCount) -> 去审核
 */

/**
 * 是否发起整改
 *  有发起整改 同时 未整改数量为0 展示整改完成图标
 *  未发起整改 不展示 待整改数量
 */

type TProps = {
  hasPermission?: Boolean; // 用于判断是否是权限内还是权限外的页面用
  info: TPatrolItem;
  roleType: ERoleType;
};

export function DiagnosisTask({ info, roleType, hasPermission = true }: TProps) {
  const navigate = useNavigate();
  const showNoReformCount = () => {
    if ([EPStatus.督导待确认, EPStatus.待提交].includes(info.status)) {
      return false;
    }
    if (info.score === 100) {
      return false;
    }
    if (!info?.issuesCount) {
      return false;
    }
    return info.itemsCount > 0;
  };
  return (
    <div className=" mb-2">
      <div
        className=" bg-white pt-4 pb-2"
        onClick={() => {
          if (info.status === EPStatus.待提交) {
            navigate(`/patrol/shopcheck?taskId=${info.taskId}`);
          } else {
            navigate(`/patrol/reportdetail?taskId=${info.taskId}&hasPermission=${hasPermission}`);
          }
        }}
      >
        <div className=" px-4">
          <div>
            <div className=" flex justify-between items-center">
              <div> {alarmTagMap[info?.diagnosticInfo?.alarmLevel!]}</div>
              <div>
                {info?.status === EPStatus.已确认 && reviewStatusMap[info?.reviewStatus]}
                {getTagMapByRole(info.status, roleType)}
              </div>
            </div>
          </div>
          <div className=" flex justify-between items-center bg-[#fafafa] px-2 py-2 mt-2">
            <div>
              <div className=" text-sm font-semibold">
                {info.shopNo} {info.shopName}
              </div>
              <div className=" font-semibold">
                <span className="text-lg">{info?.diagnosticInfo?.score} </span>
                <span className="text-sm">分</span>
              </div>
            </div>
            {/* {info.issuesCount > 0 && info.status === EPStatus.已确认 && info.noReformCount === 0 && (
              <div>
                <img src={successPNG} className=" size-[64px]" alt="" />
              </div>
            )} */}
          </div>
          <div className=" flex items-center justify-between my-2">
            <div className=" flex flex-col justify-center items-center w-full">
              <div className=" text-[#378BFF] text-base">{info.itemsCount}个</div>
              <div className=" text-[#858585]">检查项</div>
            </div>
            <div className=" flex flex-col justify-center items-center  w-full">
              <div className=" text-[#378BFF] text-base">{info?.unqualifiedItemNum}个</div>
              <div className=" text-[#858585]">问题项</div>
            </div>
            {showNoReformCount() && (
              <div className=" flex flex-col justify-center items-center  w-full">
                <div
                  className={classNames(' text-base', {
                    'text-[#EA0000]': !!info.noReformCount,
                    'text-[#378BFF]': info.noReformCount === 0,
                  })}
                >
                  {info.noReformCount}个
                </div>
                <div className=" text-[#858585]">未整改项</div>
              </div>
            )}
          </div>
          {/* <div className=" w-full border border-t border-[#f7f7f7]" /> */}
        </div>
        {info?.hasNeedReview && roleType === ERoleType.督导 && (
          <div className=" w-full bg-[#edf1f7] flex justify-between items-center px-2 py-2">
            <div className=" text-[#378BFF] text-sm" />
            <div
              className="bg-[#378BFF] text-[#FFFFFF] text-xs px-3.5 rounded py-1.5"
              onClick={(e) => {
                e.stopPropagation();
                navigate(`/patrol/reportdetail?taskId=${info.taskId}&hasPermission=${hasPermission}`);
              }}
            >
              去点评
            </div>
          </div>
        )}
        {roleType === ERoleType.店长 &&
          info.status === EPStatus.已确认 &&
          // !!info.waitAuditCount && (
          !!((info.noReformCount || 0) - (info.waitAuditCount || 0)) && (
            <div className=" w-full bg-[#edf1f7] flex justify-between items-center px-4 py-2">
              <div className=" text-[#378BFF] text-sm">
                {(info.noReformCount || 0) - (info.waitAuditCount || 0)}
                个问题待整改
              </div>
              <div
                className="bg-[#378BFF] text-[#FFFFFF] text-xs px-3.5 rounded py-1.5"
                onClick={(e) => {
                  e.stopPropagation();
                  navigate(
                    `/tasks/pollingAbarbeitung?taskId=${info.taskId}&pollingType=NormalPolling&cross=${hasPermission}`,
                  );
                }}
              >
                去整改
              </div>
            </div>
          )}

        {roleType === ERoleType.督导 && info.status === EPStatus.已确认 && !!info.waitAuditCount && (
          <div className=" w-full bg-[#edf1f7] flex justify-between items-center px-4 py-2">
            <div className=" text-[#378BFF] text-sm">{info.waitAuditCount}个问题待审核</div>
            <div
              className="bg-[#378BFF] text-[#FFFFFF] text-xs px-3.5 rounded py-1.5"
              onClick={(e) => {
                e.stopPropagation();
                navigate(
                  `/tasks/pollingAbarbeitung?taskId=${info.taskId}&pollingType=NormalPolling&cross=${hasPermission}`,
                );
              }}
            >
              去审核
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
