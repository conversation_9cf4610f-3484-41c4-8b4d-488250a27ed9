import successPNG from '@src/assets/images/success.png';
import classNames from 'classnames';
import dayjs from 'dayjs';
import { useNavigate } from 'react-router-dom';
import { formatScore } from './core';
import { TaskStatusTagSelf } from './TaskStatusTagSelf';
import { ERoleType } from '..';
import { EStatus, TReportItem } from '../api';
interface PrincipalPatrolTaskCardProps {
  info: TReportItem;
  roleType: ERoleType;
}
export const SelfPatrolTaskCard: React.FC<PrincipalPatrolTaskCardProps> = ({
  info,
  roleType,
}: PrincipalPatrolTaskCardProps) => {
  const navigate = useNavigate();
  const isToComment =
    (info.recordStatus === EStatus.待点评 &&
      roleType === ERoleType.督导 &&
      info.unreviewed) ||
    (info.hasSecondReview && !info.secondReview);
  return (
    <div
      className="divide-y-[1px] divide-black/[0.03] flex flex-col bg-white rounded-lg  pt-3 mb-2"
      onClick={() => {
        if (roleType === ERoleType.店长) {
          navigate(`/self/reportdetail?taskId=${info.taskId}`);
        } else {
          // 督导
          if (isToComment) {
            navigate(`/self/review?taskId=${info.taskId}`);
          } else {
            navigate(`/self/reportdetail?taskId=${info.taskId}`);
          }
        }
      }}
    >
      <div className=" px-2">
        <div>
          <div className="flex justify-start">
            <TaskStatusTagSelf status={info.recordStatus} />
            <div className="ml-2">{info.shopName}</div>
          </div>
          <div className="rounded py-2 flex flex-col gap-y-1">
            <h3 className="text-[#141414] flex ">
              <span className="text-sm leading-[22px]">{info.taskName}</span>
            </h3>
            {info.recordStatus === EStatus.已点评 && (
              <div className=" flex justify-between items-center">
                <div className=" text-[#858585]">检查得分</div>
                <div className=" text-[#141414] font-medium text-2xl">
                  {formatScore(info.score)}
                </div>
              </div>
            )}
            <div className=" flex justify-between items-center bg-[#fafafa] px-2 py-2">
              <div>
                {info.submitUserName && (
                  <div className="flex items-center">
                    <span className=" text-[#858585] text-xs leading-[12px]">
                      {info.submitUserName}{' '}
                      {dayjs(info.submitTime).format('MM-DD HH:mm')} 提交
                    </span>
                  </div>
                )}
                {info.reviewUserName && (
                  <div className="flex items-center mt-2">
                    <span className=" text-[#858585] text-xs leading-[12px]">
                      {info.reviewUserName}{' '}
                      {dayjs(info.reviewTime).format('MM-DD HH:mm')} 点评
                    </span>
                  </div>
                )}
              </div>
              {info.recordStatus === EStatus.已点评 &&
                info.alreadyAudit &&
                info.noReformCount === 0 && (
                  <div>
                    <img src={successPNG} className=" size-[64px]" alt="" />
                  </div>
                )}
            </div>
          </div>

          {info.recordStatus !== EStatus.待点评 && (
            <div className=" flex items-center justify-between my-2">
              <div className=" flex flex-col justify-center items-center w-full">
                <div className=" text-[#378BFF] text-base">
                  {info.itemsCount}个
                </div>
                <div className=" text-[#858585]">检查项</div>
              </div>
              <div className=" flex flex-col justify-center items-center  w-full">
                <div className=" text-[#378BFF] text-base">
                  {info.issuesCount}个
                </div>
                <div className=" text-[#858585]">问题项</div>
              </div>
              {info.alreadyAudit && (
                <div className=" flex flex-col justify-center items-center  w-full">
                  <div
                    className={classNames(' text-base', {
                      'text-[#EA0000]': !!info.noReformCount,
                      'text-[#378BFF]': info.noReformCount === 0,
                    })}
                  >
                    {info.noReformCount}个
                  </div>
                  <div className=" text-[#858585]">未整改项</div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* bg-current */}
        <div className=" text-13 leading-[21px] text-85 flex justify-between items-center py-1">
          <div className=" flex flex-wrap">
            {info.checkListNames.map((item, index) => (
              <div
                className="rounded-sm border border-[#DCDCDC] px-2 mr-2 mb-2"
                key={index}
              >
                {item}
              </div>
            ))}
          </div>
        </div>
      </div>

      {isToComment && (
        <div className=" w-full bg-[#edf1f7] flex justify-between items-center px-2 py-2">
          <div className=" text-[#378BFF] text-sm" />
          <div
            className="bg-[#378BFF] text-[#FFFFFF] text-xs px-3.5 rounded py-1.5"
            onClick={(e) => {
              e.stopPropagation();
              navigate(`/self/review?taskId=${info.taskId}`);
            }}
          >
            去点评
          </div>
        </div>
      )}

      {roleType === ERoleType.店长 &&
        info.recordStatus === EStatus.已点评 &&
        !!info.waitAuditCount && (
          <div className=" w-full bg-[#edf1f7] flex justify-between items-center px-2 py-2">
            <div className=" text-[#378BFF] text-sm">
              {info.waitAuditCount}个问题待整改
            </div>
            <div
              className="bg-[#378BFF] text-[#FFFFFF] text-xs px-3.5 rounded py-1.5"
              onClick={(e) => {
                e.stopPropagation();
                navigate(
                  `/tasks/pollingAbarbeitung?taskId=${info.taskId}&pollingType=SelfPolling`,
                );
              }}
            >
              去整改
            </div>
          </div>
        )}

      {roleType === ERoleType.督导 &&
        info.recordStatus === EStatus.已点评 &&
        !!info.waitAuditCount && (
          <div className=" w-full bg-[#edf1f7] flex justify-between items-center px-4 py-2">
            <div className=" text-[#378BFF] text-sm">
              {info.waitAuditCount}个问题待审核
            </div>
            <div
              className="bg-[#378BFF] text-[#FFFFFF] text-xs px-3.5 rounded py-1.5"
              onClick={(e) => {
                e.stopPropagation();
                navigate(
                  `/tasks/pollingAbarbeitung?taskId=${info.taskId}&pollingType=SelfPolling`,
                );
              }}
            >
              去审核
            </div>
          </div>
        )}
    </div>
  );
};
