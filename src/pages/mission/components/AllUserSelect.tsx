import { useEffect, useState } from 'react';
import { useRequest } from 'ahooks';
import { Select, SelectProps } from 'antd';
import { getAllUserBase } from '../api';

interface AllUserSelectProps {
  label?: string;
  value?: number;
  onChange?: any;
}

export const AllUserSelect = ({
  label = '检查人',
  value,
  onChange,
  ...selectProps
}: AllUserSelectProps & SelectProps) => {
  const [searchText, setSearchText] = useState<string>('');
  const { data, refresh } = useRequest(() =>
    getAllUserBase({
      bizType: 14,
      roleCategory: 1,
      nickname: searchText,
    }),
  );
  useEffect(() => {
    refresh();
  }, [searchText]);
  useEffect(() => {
    if (!value) {
      setSearchText('');
    }
  }, [value]);
  return (
    <div className="flex items-center justify-between">
      <h3 className="text-[#141414] text-sm leading-[14px]">{label}</h3>
      <Select
        // mode="multiple"
        showSearch
        style={{ width: 200 }}
        options={data}
        fieldNames={{
          value: 'userId',
          label: 'nickname',
        }}
        value={value}
        onChange={onChange}
        filterOption={false}
        allowClear
        onSearch={(val) => {
          setSearchText(val);
        }}
        onClear={() => {
          setSearchText('');
        }}
        placeholder={`请输入${label}名称`}
        {...selectProps}
      />
    </div>
  );
};
