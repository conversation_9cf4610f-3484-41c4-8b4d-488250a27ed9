import { useEffect, useState } from 'react';
import { getKillCompanyUser } from '@src/pages/tasks/api';
import { useRequest } from 'ahooks';
import { Select } from 'antd';
import { ESheetType } from '../api';
interface KillPollingUserSelectProps {
  value?: number;
  onChange?: any;
  sheetType: ESheetType;
}

export const KillPollingUserSelect = ({ value, onChange, sheetType }: KillPollingUserSelectProps) => {
  //   console.log('223323sheetType', sheetType);
  const [searchText, setSearchText] = useState<string>('');
  const { data, refresh } = useRequest(() => getKillCompanyUser(), {
    refreshDeps: [sheetType],
  });

  useEffect(() => {
    refresh();
  }, [searchText]);
  return (
    <div className="flex items-center justify-between">
      <h3 className="text-[#141414] text-sm leading-[14px]">巡检人</h3>
      <Select
        showSearch
        style={{ width: 200 }}
        options={data}
        fieldNames={{
          value: 'id',
          label: 'name',
        }}
        value={value}
        onChange={onChange}
        allowClear
        filterOption={false}
        placeholder="请输入巡检人"
        onSearch={(val) => {
          setSearchText(val);
        }}
        onClear={() => {
          setSearchText('');
        }}
      />
    </div>
  );
};
