import { useEffect, useState } from 'react';
import { getKillCompany } from '@src/pages/tasks/api';
import { useRequest } from 'ahooks';
import { Select } from 'antd';
import { ESheetType } from '../api';
interface KillCompanySlectProps {
  value?: number;
  onChange?: any;
  sheetType: ESheetType;
}

export const KillCompanySelect = ({ value, onChange, sheetType }: KillCompanySlectProps) => {
  //   console.log('223323sheetType', sheetType);
  const [searchText, setSearchText] = useState<string>('');
  const { data, refresh } = useRequest(() => getKillCompany(), {
    refreshDeps: [sheetType],
  });

  useEffect(() => {
    refresh();
  }, [searchText]);
  return (
    <div className="flex items-center justify-between">
      <h3 className="text-[#141414] text-sm leading-[14px]">消杀公司</h3>
      <Select
        showSearch
        style={{ width: 200 }}
        options={data}
        fieldNames={{
          value: 'id',
          label: 'name',
        }}
        value={value}
        onChange={onChange}
        allowClear
        filterOption={false}
        placeholder="请输消杀公司"
        onSearch={(val) => {
          setSearchText(val);
        }}
        onClear={() => {
          setSearchText('');
        }}
      />
    </div>
  );
};
