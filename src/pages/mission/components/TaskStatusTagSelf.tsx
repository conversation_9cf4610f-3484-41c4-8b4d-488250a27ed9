import { EStatus } from '../api';

export const TaskStatusTagSelf = ({ status, style, className, ...props }: any) => {
  const taskStatusMap = {
    [EStatus.待点评]: {
      bg: '#2DA55D',
      color: 'white',
      text: '待点评',
    },
    [EStatus.已点评]: {
      bg: '#2DA55D',
      color: 'white',
      text: '已点评',
    },
  };
  const { bg, color, text, ...other } = (taskStatusMap as any)[status];

  return (
    <span
      {...props}
      className={`inline-block px-1 py-[3px] rounded-sm text-white text-xs leading-[12px] ${
        className || ''
      }`}
      style={{ backgroundColor: bg, color: color, ...other, ...style }}
      // @ts-ignore
      attribute="tag"
    >
      {text}
    </span>
  );
};
