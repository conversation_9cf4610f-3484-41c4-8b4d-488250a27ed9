import { useEffect, useState } from 'react';
import { DataViewTypeEnum } from '@src/pages/dataViewBoard';
import { getTacticsChecklistSimplelist } from '@src/pages/dataViewBoard/tactics/api';
import { useDebounceFn, useRequest } from 'ahooks';
import { Select } from 'antd';
import { ESheetType, getWorkSheet } from '../api';
interface WorkSheetProps {
  type?: string;
  taskType: 0 | 1;
  value?: number;
  onChange?: any;
  /** 旧系统过滤检查表类型 */
  sheetType?: ESheetType;
  /** 策略过滤检查表类型 */
  worksheetTypes?: ESheetType[];
  /** 是否多选 */
  shouldUseMultipleMode?: boolean;
}

export const WorkSheet = ({
  type,
  value,
  onChange,
  sheetType,
  worksheetTypes,
  shouldUseMultipleMode = false,
}: WorkSheetProps) => {
  const [searchText, setSearchText] = useState<string>('');

  const { data, refresh } = useRequest(
    () =>
      ([DataViewTypeEnum['自检看板 (新)'], DataViewTypeEnum.巡检不合格情况].includes(type as DataViewTypeEnum)
        ? getTacticsChecklistSimplelist
        : getWorkSheet)({
        sheetName: searchText,
        planType: sheetType,
        worksheetTypes,
      }),
    {
      refreshDeps: [sheetType, type],
    },
  );

  const { run: debounceRun } = useDebounceFn(
    (val) => {
      setSearchText(val);
    },
    { wait: 300 },
  );

  useEffect(() => {
    refresh();
  }, [searchText]);

  return (
    <div className="flex items-center justify-between">
      <h3 className="text-[#141414] text-sm leading-[14px]">检查表</h3>
      <Select
        showSearch
        style={{ width: 200 }}
        options={data}
        fieldNames={{
          value: 'id',
          label: 'sheetName',
        }}
        value={value}
        onChange={(v) => {
          onChange?.(v);

          // 多选模式清空 tag 重置文本框输入内容
          if (shouldUseMultipleMode && Array.isArray(v) && !v?.length) {
            setSearchText('');
          }
        }}
        allowClear
        filterOption={false}
        mode={shouldUseMultipleMode ? 'multiple' : undefined}
        placeholder="请输入检查表"
        onSearch={(val) => {
          debounceRun(val);
        }}
        onClear={() => {
          setSearchText('');
        }}
      />
    </div>
  );
};
