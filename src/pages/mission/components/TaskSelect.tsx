import { useEffect, useState } from 'react';
import { useRequest } from 'ahooks';
import { Select } from 'antd';
import { getRoutineTaskByName, getSelfTaskByName, getTaskByName } from '../api';
interface WorkSheetProps {
  value?: string;
  onChange?: any;
  isSelfCheck?: boolean;
  isRoutine?: boolean;
}

export const TaskSelect = ({ value, onChange, isSelfCheck, isRoutine }: WorkSheetProps) => {
  const [searchText, setSearchText] = useState<string>('');
  const { data, refresh } = useRequest(
    () => {
      if (isSelfCheck) {
        return getSelfTaskByName({
          planName: searchText,
        });
      } else {
        if (isRoutine) {
          return getRoutineTaskByName(searchText || '');
        } else {
          return getTaskByName(searchText || '');
        }
      }
    },
    { manual: isSelfCheck ? false : true, refreshDeps: [searchText] },
  );

  useEffect(() => {
    if (!isSelfCheck && searchText.length >= 2) {
      refresh();
    }
  }, [searchText, isSelfCheck]);

  useEffect(() => {
    if (isSelfCheck) {
      refresh();
    }
  }, [isSelfCheck]);
  return (
    <div className="flex items-center justify-between">
      <h3 className="text-[#141414] text-sm leading-[14px]">{isSelfCheck ? '任务' : isRoutine ? '任务' : '计划'}</h3>
      <Select
        showSearch
        style={{ width: 200 }}
        options={isSelfCheck ? data : searchText.length < 2 ? [] : data}
        fieldNames={{
          value: 'id',
          label: isSelfCheck ? 'planName' : 'name',
        }}
        value={value}
        onChange={onChange}
        filterOption={false}
        onSearch={(val) => {
          console.log('bbbb', val);
          setSearchText(val || '');
        }}
        onClear={() => {
          setSearchText('');
        }}
        allowClear
        placeholder={`请输入${isSelfCheck ? '任务' : isRoutine ? '任务' : '计划'}名称`}
      />
    </div>
  );
};
