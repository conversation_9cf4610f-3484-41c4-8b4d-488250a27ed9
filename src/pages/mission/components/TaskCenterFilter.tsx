import { useEffect, useMemo, useState } from 'react';
import { Drawer, DrawerProps } from 'antd';
import { Button } from 'antd-mobile';
import { AllUserSelect } from './AllUserSelect';
import ContentInput from './ContentInput';
import { getStatusListByRole, getTaskTypeByRole } from './core';
import styles from './index.module.scss';
import { TaskSelect } from './TaskSelect';
import { WorkSheet } from './WorkSheet';
import { ERoleType } from '..';
import { ESheetType } from '../api';

interface FilterDrawerProps extends DrawerProps {
  children: React.ReactNode;
  onOk: () => void;
  onClear: () => void;
}
const FilterDrawer = ({ children, onOk, onClear, ...props }: FilterDrawerProps) => {
  const drawerHeight = useMemo(
    () =>
      `${
        document.body.clientHeight -
        (document.getElementById('filterDrawerContainer')?.getBoundingClientRect().bottom || 0)
      }px`,
    [props.open],
  );
  return (
    <Drawer
      {...props}
      placement="top"
      // getContainer={() => document.getElementById('filterDrawerContainer')!}
      rootStyle={{
        position: 'absolute',
        top: document.getElementById('filterDrawerContainer')?.getBoundingClientRect().bottom,
        left: 0,
        height: drawerHeight,
      }}
      rootClassName={styles.drawer}
      closable={false}
      height="auto"
    >
      <div className="pt-7 pb-1 px-3">{children}</div>
      <div className="p-4 flex gap-3">
        <Button
          block
          // style={{
          //   '--background-color': '#D8D8D8',
          //   '--text-color': '#58595B',
          // }}
          className="h-[42px] text-sm"
          color="primary"
          fill="outline"
          onClick={onClear}
        >
          清空筛选
        </Button>
        <Button onClick={onOk} block className="h-[42px] text-sm" color="primary">
          确定筛选
        </Button>
      </div>
    </Drawer>
  );
};

interface UnderPatrolFilterProps extends DrawerProps {
  value: Record<string, any>;
  onChange: (value: Record<string, any>) => void;
  onClose: () => void;
  /**
   * 是否是自检
   */
  isSelfCheck?: boolean;
  roleType: ERoleType;
  /** 是否支持筛选巡检状态 */
  showStatus?: boolean;
  /** 是否支持筛选巡检类型 */
  showTaskType?: boolean;
  /** 是否支持任务筛选 */
  showTask?: boolean;
  /** 是否支持巡检人筛选 */
  showUser?: boolean;
  /** 是否支持检查表筛选 */
  showWorkSheet?: boolean;
  /** 是否支持问题项内容筛选 */
  showQuestion?: boolean;
}

export const TaskCenterFilter = ({
  value,
  onChange,
  onClose,
  isSelfCheck = false,
  roleType,
  showStatus = false,
  showTaskType = false,
  showTask = false,
  showUser = false,
  showWorkSheet = false,
  showQuestion = false,
  ...props
}: UnderPatrolFilterProps) => {
  const [activity, setActivity] = useState<any>(value);
  const onOk = () => {
    onChange(activity);
  };
  const onClear = () => {
    if (isSelfCheck) {
      setActivity({
        ...activity,
        groupId: activity.groupId,
        taskPlanId: undefined,
        worksheetId: undefined,
        keyword: undefined,
        // keyword: '',
      });
    } else {
      setActivity({
        ...activity,
        groupId: activity.groupId,
        // status: undefined,
        // taskType: undefined,
        planId: undefined,
        // checkUserIds: undefined,
        worksheetId: undefined,
        keyword: undefined,
        // keyword: ''
      });
    }
  };

  useEffect(() => {
    console.log('33333', value);

    setActivity(value);
  }, [value]);
  return (
    <FilterDrawer
      {...props}
      onClose={() => {
        // 重置一下activity
        setActivity(value);
        onClose();
      }}
      onOk={onOk}
      onClear={onClear}
    >
      <div className="flex flex-col gap-2">
        {showStatus && (
          <div>
            <div className="flex flex-col gap-3">
              <h3 className="text-[#141414] text-sm leading-[14px]">巡检状态</h3>
              <div className="flex flex-wrap gap-2">
                {getStatusListByRole(roleType).map((item) => {
                  return (
                    <button
                      key={item.value}
                      className={`px-4 h-[30px] rounded ${
                        item.value === activity.status ? 'text-primary bg-primary/10' : 'text-58 bg-black/[0.03]'
                      }  text-sm left-[14px]`}
                      onClick={() => {
                        setActivity((pre: any) => {
                          if (activity?.status === item.value) {
                            return {
                              ...pre,
                            };
                          } else {
                            return {
                              ...pre,
                              status: item.value,
                            };
                          }
                        });
                      }}
                    >
                      {item.label}
                    </button>
                  );
                })}
              </div>
            </div>
          </div>
        )}

        {showTaskType && (
          <div>
            <div className="flex flex-col gap-3">
              <h3 className="text-[#141414] text-sm leading-[14px]">巡检类型</h3>
              <div className="flex flex-wrap gap-2">
                {getTaskTypeByRole(roleType).map((item) => {
                  return (
                    <button
                      key={item.value}
                      className={` px-4 h-[30px] rounded ${
                        activity?.taskType === item.value ? 'text-primary bg-primary/10' : 'text-58 bg-black/[0.03]'
                      }  text-sm left-[14px]`}
                      onClick={() => {
                        setActivity((pre: any) => {
                          if (activity?.taskType === item.value) {
                            return { ...pre };
                          } else {
                            return {
                              ...pre,
                              taskType: item.value,
                            };
                          }
                        });
                      }}
                    >
                      {item.label}
                    </button>
                  );
                })}
              </div>
            </div>
          </div>
        )}

        {showTask && (
          <TaskSelect
            isSelfCheck={isSelfCheck}
            value={isSelfCheck ? activity.taskPlanId : activity.planId}
            onChange={(val: number) => {
              if (isSelfCheck) {
                setActivity((pre: any) => ({
                  ...pre,
                  taskPlanId: val,
                }));
              } else {
                setActivity((pre: any) => ({
                  ...pre,
                  planId: val,
                }));
              }
            }}
          />
        )}

        {showUser && (
          <AllUserSelect
            value={activity.checkUserIds}
            onChange={(val: number) => {
              setActivity((pre: any) => ({
                ...pre,
                checkUserIds: val,
              }));
            }}
          />
        )}

        {showWorkSheet && (
          <WorkSheet
            sheetType={isSelfCheck ? ESheetType.自检 : ESheetType.巡检}
            taskType={1}
            value={activity.worksheetId}
            onChange={(val: number) => {
              setActivity((pre: any) => ({
                ...pre,
                worksheetId: val,
              }));
            }}
          />
        )}
        {showQuestion && (
          // <div>
          //     <div className="flex items-center justify-between">
          //         <div className="w-19">{'问题内容'}</div>
          //         <div>
          //             <Input
          //                 placeholder='请输入内容'
          //                 value={activity.keyword}
          //                 onChange={val => {
          //                     setActivity((pre: any) => ({
          //                         ...pre,
          //                         keyword: val.replace(/\s/g, ''),
          //                     }));
          //                 }}
          //             />
          //         </div>
          //     </div>
          // </div>

          <ContentInput
            label="问题内容"
            placeholder="请输入问题内容"
            value={activity.keyword}
            onChange={(e: any) => {
              setActivity((pre: any) => ({
                ...pre,
                // keyword: e.target?.value.replace(/\s/g, ''),
                keyword: e,
              }));
            }}
          />
        )}
      </div>
    </FilterDrawer>
  );
};
