
import { Input } from 'antd';
// import { Input } from 'antd-mobile'
import { forwardRef, useEffect, useState } from 'react';
import debounce from 'lodash/debounce';
type IProps = {
    label: string;
    placeholder?: string;
    fieldNames?: {
        name?: string;
    };
    pagination?: boolean;
    // value?: { name: string };
    value?: string | undefined;
    onChange: any;
};

const ContentInput = ({ label, placeholder, onChange, value }: IProps,
) => {
    const [keyword, setKeyword] = useState('');
    useEffect(() => {
        if (typeof value === 'undefined') {
            setKeyword('');
        } else {
            setKeyword(value);
        }
    }, [value]);
    return (
        <div>
            <div className="flex items-center justify-between">
                <div className="w-19">{label}</div>
                <div>
                    <Input
                        type="text"
                        name="keyword"
                        value={keyword}
                        style={{ width: 200 }}
                        placeholder={placeholder}
                        // onChange={debounce(onChange, 300)}
                        // onChange={(event) => {
                        //     // console.log('678', event.target.value);
                        //     if (!value) {
                        //         return null
                        //     }
                        //     if (event.target.value.length <= value.length) {
                        //         debounce(onChange, 300)

                        //     }
                        // }}
                        // allowClear
                        onBlur={(e) => {
                            // if (keyword !== value) {
                            //     onChange('');
                            //     setTimeout(() => {
                            console.log('失去焦点了keyword', keyword);
                            console.log('失去焦点了value', value);

                            //         setKeyword(keyword);
                            //     });


                            // }
                            onChange(keyword);
                            // setKeyword(e.target.value)
                        }}
                        onChange={(e) => {
                            console.log('123', e.target.value);
                            // debounce(onChange(e), 300)
                            // onChange(e);
                            setKeyword(e.target.value);
                            // onChange(e);
                            // setKeyword(e.detail.value);

                        }}
                    // onFocus={() => {
                    //     onChange('')
                    // }}
                    />
                </div>
            </div>
        </div>
    );
};

export default forwardRef(ContentInput);