import { ERoleType } from '..';

/**
 * 根据角色 获取不同的巡检状态选项
 */
export const getStatusListByRole = (roleType: ERoleType) => {
  if (roleType === ERoleType.督导) {
    return [
      { label: '全部', value: undefined },
      // { label: '待提交', value: 'NOT_STARTED' },
      { label: '已提交', value: 'SUBMITTED' },
      { label: '已确认', value: 'CONFIRMED' },
    ];
  } else {
    return [
      { label: '全部', value: undefined },
      { label: '待确认', value: 'SUBMITTED' },
      { label: '已确认', value: 'CONFIRMED' },
    ];
  }
};
export const getReviewStatus = () => {
  return [
    { label: '全部', value: undefined },
    { label: '已点评', value: 'REVIEWED' },
    { label: '待点评', value: 'NOT_REVIEWED' },
    { label: '无需点评', value: 'NO_NEEDED_REVIEWS' },
  ];
};

/**
 * 第二点评状态选项
 */
export const secondReviewStatusList = [
  {
    label: '全部',
    value: undefined,
  },
  {
    label: '无',
    value: 'NOT_SECOND_REVIEW',
  },
  {
    label: '合格',
    value: 'QUALIFIED',
  },
  {
    label: '不合格',
    value: 'UNQUALIFIED',
  },
];

/**
 * 根据角色 获取不同巡检类型选项
 */
export const getTaskTypeByRole = (roleType: ERoleType) => {
  if (roleType === ERoleType.督导) {
    return [
      { label: '全部', value: undefined },
      { label: '到店巡检(权限内)', value: 'NORMAL' },
      { label: '视频云巡检', value: 'VIDEO' },
      { label: '食安线下稽核', value: 'FOOD_SAFETY_NORMAL' },
      { label: '食安线上稽核', value: 'FOOD_SAFETY_VIDEO' },
      { label: '诊断巡检', value: 'DIAGNOSTIC' },
      // 督导/报告中心/巡检
      { label: '食安稽核到店辅导', value: 'FOOD_SAFETY_ARRIVE_SHOP' },
    ];
  } else {
    return [
      { label: '全部', value: undefined },
      { label: '到店巡检', value: 'NORMAL' },
      { label: '视频云巡检', value: 'VIDEO' },
      { label: '食安线下稽核', value: 'FOOD_SAFETY_NORMAL' },
      { label: '食安线上稽核', value: 'FOOD_SAFETY_VIDEO' },
      { label: '诊断巡检', value: 'DIAGNOSTIC' },
      // 督导/报告中心/巡检
      { label: '食安稽核到店辅导', value: 'FOOD_SAFETY_ARRIVE_SHOP' },
    ];
  }
};

/**
 * 格式化分数
 *  如果是整数返回整数，否则四舍五入一位小数点
 */
export function formatScore(score: number | string) {
  const _score = Number(score || 0);
  /** 是否是小数 */
  const isDecimal = !Number.isInteger(_score);
  return isDecimal ? _score.toFixed(1) : _score;
}
