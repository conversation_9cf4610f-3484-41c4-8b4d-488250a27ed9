import { useMemo } from 'react';
import { patrolTypeText } from '@src/pages/patrol/planDetail/detailCard';
import { SwipeAction } from 'antd-mobile';
import classNames from 'classnames';
import dayjs from 'dayjs';
import { useNavigate } from 'react-router-dom';
import { formatScore } from './core';
import { ERoleType } from '..';
import { EPStatus, TPatrolItem } from '../api';

/**
 * 督导枚举
 */
const supervisionTagMap = {
  NOT_STARTED: <div className=" text-[#378BFF]">待提交</div>,
  SUBMITTED: <div className=" text-[#378BFF]">已提交</div>,
  CONFIRMED: <div className=" text-[#378BFF]">已确认</div>,
};

/**
 *
 * 店长枚举
 */
const managerTagMap = {
  SUBMITTED: <div className=" text-[#378BFF]">待确认</div>,
  CONFIRMED: <div className=" text-[#378BFF]">已确认</div>,
};

/**
 * 第二点评状态映射
 */
const secondReviewStatusMap: Record<TPatrolItem['secondReviewStatus'], TPatrolItem['reviewStatus']> = {
  // 未进行第二点评 -> 待点评
  NOT_SECOND_REVIEW: 'NOT_REVIEWED',
  // 合格、不合格 都为已点评
  QUALIFIED: 'REVIEWED',
  UNQUALIFIED: 'REVIEWED',
};

/**
 *
 * 点评状态
 */
export const reviewStatusMap = {
  REVIEWED: <div className=" text-[#378BFF]">已点评</div>,
  NOT_REVIEWED: <div className=" text-[#378BFF]">待点评</div>,
  NO_NEEDED_REVIEWS: <div className=" text-[#378BFF]">无需点评</div>,
};

function getTagMapByRole(status: EPStatus, roleType: ERoleType) {
  if (roleType === ERoleType.店长) {
    // @ts-ignore
    return managerTagMap[status];
  } else {
    return supervisionTagMap[status];
  }
}

const subTypeText = {
  NORMAL: '到店巡检(权限内)',
  VIDEO: '视频云巡检',
  CROSS: '到店巡检(权限外)',
  FOOD_SAFETY_NORMAL: '食安线下稽核',
  FOOD_SAFETY_VIDEO: '食安线上稽核',
  FOOD_SAFETY_ARRIVE_SHOP: '食安稽核到店辅导',
};

/**
 * 店长
 *  待确认状态 -> 去确认
 *  已确认状态 -> 未整改问题项中 如果存在 待整改状态 (issuesCount) -> 去整改
 * 督导
 *  已确认状态 -> 未整改问题项中 如果存在 待审核 (waitAuditCount) -> 去审核
 */

/**
 * 是否发起整改
 *  有发起整改 同时 未整改数量为0 展示整改完成图标
 *  未发起整改 不展示 待整改数量
 */

type TProps = {
  hasPermission?: Boolean; // 用于判断是否是权限内还是权限外的页面用
  info: TPatrolItem;
  roleType: ERoleType;
  delReportFn: () => void;
};

export function InspectionTask({ info, roleType, delReportFn, hasPermission = true }: TProps) {
  const navigate = useNavigate();
  const showNoReformCount = () => {
    if ([EPStatus.督导待确认, EPStatus.待提交].includes(info.status)) {
      return false;
    }
    if (info.score === 100) {
      return false;
    }
    if (!info?.issuesCount) {
      return false;
    }
    return info.itemsCount > 0;
  };

  const rightAction = useMemo(() => {
    if (roleType === ERoleType.督导 && info.status === EPStatus.待提交) {
      return [
        {
          key: 'pin',
          text: '删除',
          color: 'light',
          onClick: delReportFn,
        },
      ];
    }
    return [];
  }, [info, roleType]);

  return (
    <div className=" mb-2">
      <SwipeAction rightActions={rightAction}>
        <div
          className=" bg-white pt-4 pb-2"
          onClick={() => {
            if (info.status === EPStatus.待提交) {
              navigate(`/patrol/shopcheck?taskId=${info.taskId}`);
            } else {
              navigate(`/patrol/reportdetail?taskId=${info.taskId}&hasPermission=${hasPermission}`);
            }
          }}
        >
          <div className=" px-4">
            <div>
              <div className=" flex justify-between items-center">
                <div className=" text-base font-semibold">
                  {info.shopNo} {info.shopName}
                </div>
                <div>
                  {info.status === EPStatus.已确认 &&
                    reviewStatusMap[
                      info?.reviewStatus === 'REVIEWED' && info?.hasOpenSecondReview
                        ? secondReviewStatusMap[info?.secondReviewStatus]
                        : info?.reviewStatus
                    ]}
                  {getTagMapByRole(info.status, roleType)}
                </div>
              </div>
              <div className=" text-sm text-[#858585]">
                {/* 食安稽核到店辅导-报告列表暂不显示完成截止日期 */}
                {info.subType === 'FOOD_SAFETY_ARRIVE_SHOP' ? '' : dayjs(info.reportDate).format('MM/DD')}{' '}
                {subTypeText[info?.subType]}
              </div>
              <div className=" text-sm text-[#858585]">{info.planName}</div>
            </div>
            {/* 食安稽核到店辅导-隐藏检查得分 */}
            {info.subType !== patrolTypeText.食安稽核到店辅导 && (
              <div className=" flex justify-between items-center">
                <div className=" text-[#858585]">检查得分</div>
                <div className=" text-[#141414] font-medium text-2xl">{formatScore(info.score)}</div>
              </div>
            )}
            <div className=" flex justify-between items-center bg-[#fafafa] px-2 py-2">
              <div>
                {info.submitUserName && (
                  <div className="flex items-center">
                    <span className=" text-[#858585] text-xs leading-[12px]">
                      {info.submitUserName} {dayjs(info.submitTime).format('MM-DD HH:mm')} 提交
                    </span>
                  </div>
                )}
                {info.confirmUserName && (
                  <div className="flex items-center mt-2">
                    <span className=" text-[#858585] text-xs leading-[12px]">
                      {info.confirmUserName} {dayjs(info.confirmTime).format('MM-DD HH:mm')} 确认
                    </span>
                  </div>
                )}
              </div>
              {/* 
                店长：   问题项>0 报告状态已确认 未整改项为0 展示
                督导：   问题项>0 报告状态已确认 未整改项为0 展示
              */}
              {/* {info.noReformCount === 0 && info.status !== EPStatus.待提交 && ( */}
              {/* {info.issuesCount > 0 && info.status === EPStatus.已确认 && info.noReformCount === 0 && (
                <div>
                  <img src={successPNG} className=" size-[64px]" alt="" />
                </div>
              )} */}
            </div>
            <div className=" flex items-center justify-between my-2">
              <div className=" flex flex-col justify-center items-center w-full">
                <div className=" text-[#378BFF] text-base">{info.itemsCount}个</div>
                <div className=" text-[#858585]">检查项</div>
              </div>
              <div className=" flex flex-col justify-center items-center  w-full">
                <div className=" text-[#378BFF] text-base">{info?.unqualifiedItemNum || 0}个</div>
                <div className=" text-[#858585]">不合格项</div>
              </div>
              {showNoReformCount() && (
                <div className=" flex flex-col justify-center items-center  w-full">
                  <div
                    className={classNames(' text-base', {
                      'text-[#EA0000]': !!info.noReformCount,
                      'text-[#378BFF]': info.noReformCount === 0,
                    })}
                  >
                    {info.noReformCount}个
                  </div>
                  <div className=" text-[#858585]">未整改项</div>
                </div>
              )}
            </div>
            <div className=" w-full border border-t border-[#f7f7f7]" />
            <div className=" text-13 leading-[21px] text-85 flex justify-between items-center py-1">
              <div className=" flex mt-1">
                {info.worksheetNames?.map((item, index) => (
                  <div className="rounded-sm border border-[#DCDCDC] px-2 mr-2" key={index}>
                    {item}
                  </div>
                ))}
              </div>
            </div>
          </div>
          {info?.hasNeedReview && roleType === ERoleType.督导 && (
            <div className=" w-full bg-[#edf1f7] flex justify-between items-center px-2 py-2">
              <div className=" text-[#378BFF] text-sm" />
              <div
                className="bg-[#378BFF] text-[#FFFFFF] text-xs px-3.5 rounded py-1.5"
                onClick={(e) => {
                  e.stopPropagation();
                  navigate(`/patrol/reportdetail?taskId=${info.taskId}&hasPermission=${hasPermission}`);
                }}
              >
                去点评
              </div>
            </div>
          )}

          {roleType === ERoleType.店长 &&
            info.status === EPStatus.已确认 &&
            // !!info.waitAuditCount && (
            !!((info.noReformCount || 0) - (info.waitAuditCount || 0)) && (
              <div className=" w-full bg-[#edf1f7] flex justify-between items-center px-4 py-2">
                <div className=" text-[#378BFF] text-sm">
                  {(info.noReformCount || 0) - (info.waitAuditCount || 0)}
                  个问题待整改
                </div>
                <div
                  className="bg-[#378BFF] text-[#FFFFFF] text-xs px-3.5 rounded py-1.5"
                  onClick={(e) => {
                    e.stopPropagation();
                    navigate(
                      `/tasks/pollingAbarbeitung?taskId=${info.taskId}&pollingType=NormalPolling&cross=${hasPermission}`,
                    );
                  }}
                >
                  去整改
                </div>
              </div>
            )}

          {roleType === ERoleType.督导 && info.status === EPStatus.已确认 && !!info.waitAuditCount && (
            <div className=" w-full bg-[#edf1f7] flex justify-between items-center px-4 py-2">
              <div className=" text-[#378BFF] text-sm">{info.waitAuditCount}个问题待审核</div>
              <div
                className="bg-[#378BFF] text-[#FFFFFF] text-xs px-3.5 rounded py-1.5"
                onClick={(e) => {
                  e.stopPropagation();
                  navigate(
                    `/tasks/pollingAbarbeitung?taskId=${info.taskId}&pollingType=NormalPolling&cross=${hasPermission}`,
                  );
                }}
              >
                去审核
              </div>
            </div>
          )}
        </div>
      </SwipeAction>
    </div>
  );
}
