import AuthorityEnum from '@src/common/authority';
import { IconFont } from '@src/components';
import { userStore } from '@src/store';
import { CheckList, Popup, Toast } from 'antd-mobile';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';

enum CreateTastType {
  Routine = 'Routine',
  Patrol = 'Patrol',
  Unplanned = 'Unplanned',
}

const CreateTastTypeCN: Record<CreateTastType, string> = {
  [CreateTastType.Routine]: '常规任务',
  [CreateTastType.Patrol]: '巡检计划',
  [CreateTastType.Unplanned]: '到店巡检_计划外任务',
};

export function CreateTaskNode() {
  const navigate = useNavigate();
  const [createPopupProps, setCreatePopupProps] = useState<{
    visible: boolean;
  }>({
    visible: false,
  });
  const webEntry = userStore.getWebEntry();

  const CreateTastTypeFn: Record<CreateTastType, () => void> = {
    [CreateTastType.Routine]: () => {
      navigate('/tasks/create');
    },
    [CreateTastType.Patrol]: () => {
      if (webEntry === 'feishu') {
        Toast.show({
          content: '请前往塔塔工作台APP进行查看',
        });
      } else {
        navigate(`/tasks/patrol/create?prev=${location.pathname + location.search}`);
      }
    },
    [CreateTastType.Unplanned]: () => {
      if (webEntry === 'feishu') {
        Toast.show({
          content: '请前往塔塔工作台APP进行查看',
        });
      } else {
        navigate(`/tasks/patrol/unplanned?prev=${location.pathname + location.search}`);
      }
    },
  };
  return (
    <>
      {userStore.permissionsMap.has(AuthorityEnum.创建任务) && (
        <div
          className="fixed right-[6px] bottom-[100px] flex flex-col justify-center items-center bg-white w-[60px] h-[60px] rounded-full shadow-[0_2px_5px_0_rgba(0,0,0,0.1)] z-[999] text-[#747578] text-13 leading-[13px]"
          // onClick={() => navigate('/tasks/create')}
          onClick={() =>
            setCreatePopupProps({
              visible: true,
            })
          }
        >
          <IconFont type="icon-tianjia1" className="text-[22px] mb-[6px] text-[#5E5E5E]" />
          创建
        </div>
      )}
      <Popup
        visible={createPopupProps.visible}
        bodyStyle={{
          borderTopLeftRadius: '8px',
          borderTopRightRadius: '8px',
        }}
        closeOnMaskClick
        onClose={() => {
          setCreatePopupProps({ visible: false });
        }}
      >
        <div>
          <div className="p-4 flex justify-between">
            <span className="text-[18px] leading-none font-medium text-[#000000]">创建任务</span>
            <IconFont
              type="icon-cross"
              className="text-base leading-[18px] text-[#999999]"
              onClick={() => {
                setCreatePopupProps({ visible: false });
              }}
            />
          </div>
          <CheckList
            style={{
              '--padding-left': '16px',
              '--padding-right': '16px',
              '--font-size': '16px',
            }}
            onChange={(val: any) => {
              CreateTastTypeFn[val?.[0] as CreateTastType]();
              setCreatePopupProps({ visible: false });
            }}
          >
            {/* 常规任务是 作战小队的东西 这回不涉及 直接隐藏 */}
            {/* {userStore.permissionsMap.has(AuthorityEnum.创建常规任务) && (
              <CheckList.Item value={CreateTastType.Routine}>
                {CreateTastTypeCN[CreateTastType.Routine]}
              </CheckList.Item>
            )} */}
            {userStore.permissionsMap.has(AuthorityEnum.创建巡检计划) && (
              <CheckList.Item value={CreateTastType.Patrol}>
                {CreateTastTypeCN[CreateTastType.Patrol]}
              </CheckList.Item>
            )}
            {userStore.permissionsMap.has(AuthorityEnum.到店巡检_计划外任务) && (
              <CheckList.Item value={CreateTastType.Unplanned}>
                {CreateTastTypeCN[CreateTastType.Unplanned]}
              </CheckList.Item>
            )}
          </CheckList>
        </div>
      </Popup>
    </>
  );
}
