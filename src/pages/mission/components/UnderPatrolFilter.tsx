import { useEffect, useMemo, useState } from 'react';
import useQuerySearchParams from '@src/hooks/useQuerySearchParams';
import { AlertColorEnum, CheckWayCNToEnEnum, ColorCNToEnEnum } from '@src/pages/tasks/enum';
import { Drawer, DrawerProps } from 'antd';
import { Button } from 'antd-mobile';
import cn from 'classnames';
import { AllUserSelect } from './AllUserSelect';
import ContentInput from './ContentInput';
import { getReviewStatus, getStatusListByRole, getTaskTypeByRole, secondReviewStatusList } from './core';
import styles from './index.module.scss';
import { TaskSelect } from './TaskSelect';
import { WorkSheet } from './WorkSheet';
import { ERoleType } from '..';
import { ESheetType } from '../api';

interface FilterDrawerProps extends DrawerProps {
  children: React.ReactNode;
  onOk: () => void;
  onClear: () => void;
}
const FilterDrawer = ({ children, onOk, onClear, ...props }: FilterDrawerProps) => {
  const drawerHeight = useMemo(
    () =>
      `${
        document.body.clientHeight -
        (document.getElementById('filterDrawerContainer')?.getBoundingClientRect().bottom || 0)
      }px`,
    [props.open],
  );
  return (
    <Drawer
      {...props}
      placement="top"
      // getContainer={() => document.getElementById('filterDrawerContainer')!}
      rootStyle={{
        position: 'absolute',
        top: document.getElementById('filterDrawerContainer')?.getBoundingClientRect().bottom,
        left: 0,
        height: drawerHeight,
      }}
      rootClassName={styles.drawer}
      closable={false}
      height="auto"
    >
      <div className="pt-7 pb-5 px-3 max-h-[90vh] overflow-auto">{children}</div>
      <div className="p-4 flex gap-3">
        <Button
          block
          // style={{
          //   '--background-color': '#D8D8D8',
          //   '--text-color': '#58595B',
          // }}
          className="h-[42px] text-sm"
          color="primary"
          fill="outline"
          onClick={onClear}
        >
          清空筛选
        </Button>
        <Button onClick={onOk} block className="h-[42px] text-sm" color="primary">
          确定筛选
        </Button>
      </div>
    </Drawer>
  );
};

interface UnderPatrolFilterProps extends DrawerProps {
  value: Record<string, any>;
  onChange: (value: Record<string, any>) => void;
  onClose: () => void;
  /**
   * 是否是自检
   */
  isSelfCheck?: boolean;
  roleType: ERoleType;
  /** 是否支持筛选巡检状态 */
  showStatus?: boolean;
  /** 是否支持筛选巡检类型 */
  showTaskType?: boolean;
  /** 是否支持计划筛选 */
  showPlan?: boolean;
  /** 是否支持巡检人筛选 */
  showUser?: boolean;
  /** 是否支持检查表筛选 */
  showWorkSheet?: boolean;
  /** 是否支持问题项内容筛选 */
  showQuestion?: boolean;
  /** 是否支持日周月时间筛选 */
  showTimeStatus?: boolean;
  /** 是否支持点评状态筛选 */
  showReviewStatus?: boolean;
  /** 是否支持二次点评状态筛选 */
  showSecondReviewStatus?: boolean;
  /** 是否支持任务筛选 */
  showTask?: boolean;
}

export const UnderPatrolFilter = ({
  value,
  onChange,
  onClose,
  isSelfCheck = false,
  roleType,
  showStatus = false,
  showTaskType = false,
  showReviewStatus = false,
  showSecondReviewStatus = false,
  showPlan = false,
  showUser = false,
  showWorkSheet = false,
  showQuestion = false,
  showTimeStatus = false,
  showTask = false,
  ...props
}: UnderPatrolFilterProps) => {
  const [activity, setActivity] = useState<any>(value);
  const [searchParams] = useQuerySearchParams();
  const onOk = () => {
    onChange(activity);
  };
  const onClear = () => {
    if (isSelfCheck) {
      setActivity({
        ...activity,
        groupId: activity.groupId,
        taskIds: undefined,
        routinePlanId: undefined,
        workSheetId: undefined,
        word: undefined,
      });
    } else {
      setActivity({
        ...activity,
        groupId: activity.groupId,
        status: undefined,
        reviewStatus: undefined,
        secondReviewStatus: undefined,
        taskType: undefined,
        planId: undefined,
        routinePlanId: undefined,
        checkUserIds: undefined,
        workSheetId: undefined,
        word: undefined,
      });
    }
  };

  useEffect(() => {
    setActivity(value);
  }, [value]);
  return (
    <FilterDrawer
      {...props}
      onClose={() => {
        // 重置一下activity
        setActivity(value);
        onClose();
      }}
      onOk={onOk}
      onClear={onClear}
    >
      <div className="flex flex-col gap-6">
        {showStatus && (
          <div>
            <div className="flex flex-col gap-3">
              <h3 className="text-[#141414] text-sm leading-[14px]">巡检状态</h3>
              <div className="flex flex-wrap gap-2">
                {getStatusListByRole(roleType).map((item) => {
                  return (
                    <button
                      key={item.value}
                      className={`px-4 h-[30px] rounded ${
                        item.value === activity.status ? 'text-primary bg-primary/10' : 'text-58 bg-black/[0.03]'
                      }  text-sm left-[14px]`}
                      onClick={() => {
                        setActivity((pre: any) => {
                          if (activity?.status === item.value) {
                            return {
                              ...pre,
                            };
                          } else {
                            return {
                              ...pre,
                              status: item.value,
                            };
                          }
                        });
                      }}
                    >
                      {item.label}
                    </button>
                  );
                })}
              </div>
            </div>
          </div>
        )}

        {showTaskType && (
          <div>
            <div className="flex flex-col gap-3">
              <h3 className="text-[#141414] text-sm leading-[14px]">巡检类型</h3>
              <div className="flex flex-wrap gap-2">
                {getTaskTypeByRole(roleType).map((item) => {
                  return (
                    <button
                      key={item.value}
                      className={` px-4 h-[30px] rounded ${
                        activity?.taskType === item.value ? 'text-primary bg-primary/10' : 'text-58 bg-black/[0.03]'
                      }  text-sm left-[14px]`}
                      onClick={() => {
                        setActivity((pre: any) => {
                          if (activity?.taskType === item.value) {
                            return { ...pre };
                          } else {
                            return {
                              ...pre,
                              taskType: item.value,
                            };
                          }
                        });
                      }}
                    >
                      {item.label}
                    </button>
                  );
                })}
              </div>
            </div>
          </div>
        )}
        {showReviewStatus && (
          <div>
            <div className="flex flex-col gap-3">
              <h3 className="text-[#141414] text-sm leading-[14px]">点评状态</h3>
              <div className="flex flex-wrap gap-2">
                {getReviewStatus().map((item) => {
                  return (
                    <button
                      key={item.value}
                      className={cn(
                        `px-4 h-[30px] rounded ${
                          item.value === activity.reviewStatus
                            ? 'text-primary bg-primary/10'
                            : 'text-58 bg-black/[0.03]'
                        }  text-sm left-[14px]`,
                      )}
                      onClick={() => {
                        setActivity((pre: any) => {
                          if (activity?.reviewStatus === item.value) {
                            return {
                              ...pre,
                            };
                          } else {
                            return {
                              ...pre,
                              reviewStatus: item.value,
                            };
                          }
                        });
                      }}
                    >
                      {item.label}
                    </button>
                  );
                })}
              </div>
            </div>
          </div>
        )}
        {showSecondReviewStatus && (
          <div className="flex flex-col gap-3">
            <h3 className="text-[#141414] text-sm leading-[14px]">第二点评状态</h3>
            <div className="flex flex-wrap gap-2">
              {secondReviewStatusList.map((item) => {
                return (
                  <button
                    key={item.value}
                    className={cn(
                      `px-4 h-[30px] rounded ${
                        item.value === activity.secondReviewStatus
                          ? 'text-primary bg-primary/10'
                          : 'text-58 bg-black/[0.03]'
                      }  text-sm left-[14px]`,
                    )}
                    onClick={() => {
                      setActivity((pre: any) => {
                        if (activity?.secondReviewStatus === item.value) {
                          return {
                            ...pre,
                          };
                        } else {
                          return {
                            ...pre,
                            secondReviewStatus: item.value,
                          };
                        }
                      });
                    }}
                  >
                    {item.label}
                  </button>
                );
              })}
            </div>
          </div>
        )}
        {activity?.taskType === CheckWayCNToEnEnum.诊断巡检 && (
          <div className="flex flex-col gap-3">
            <h3 className="text-[#141414] text-sm leading-[14px]">诊断类型</h3>
            <div className="flex flex-wrap gap-2">
              {[
                {
                  label: '倔强青铜',
                  value: ColorCNToEnEnum.倔强青铜,
                },
                {
                  label: '秩序白银',
                  value: ColorCNToEnEnum.秩序白银,
                },
                {
                  label: '尊贵铂金',
                  value: ColorCNToEnEnum.尊贵铂金,
                },
              ].map((item) => {
                return (
                  <button
                    key={item.value}
                    className={`px-4 h-[30px] rounded ${
                      activity?.alarmLevel === ColorCNToEnEnum[item.label as keyof typeof AlertColorEnum]
                        ? 'text-primary bg-primary/10'
                        : 'text-58 bg-black/[0.03]'
                    }  text-sm left-[14px]`}
                    onClick={() => {
                      setActivity((pre: any) => {
                        return {
                          ...pre,
                          alarmLevel: item.value,
                        };
                      });
                    }}
                  >
                    {item.label}
                  </button>
                );
              })}
            </div>
          </div>
        )}
        {showPlan && (
          <>
            <TaskSelect
              isSelfCheck={isSelfCheck}
              value={isSelfCheck ? activity.taskIds : activity.planId}
              onChange={(val: number) => {
                if (isSelfCheck) {
                  setActivity((pre: any) => ({
                    ...pre,
                    taskIds: val,
                  }));
                } else {
                  setActivity((pre: any) => ({
                    ...pre,
                    planId: val,
                  }));
                }
              }}
            />
            {showTask && (
              <TaskSelect
                isSelfCheck={isSelfCheck}
                value={activity.routinePlanId}
                onChange={(val: number) => {
                  if (isSelfCheck) {
                    setActivity((pre: any) => ({
                      ...pre,
                      routinePlanId: val,
                    }));
                  } else {
                    setActivity((pre: any) => ({
                      ...pre,
                      routinePlanId: val,
                    }));
                  }
                }}
                isRoutine
              />
            )}
          </>
        )}

        {showUser && (
          <AllUserSelect
            value={activity.checkUserIds}
            onChange={(val: number) => {
              setActivity((pre: any) => ({
                ...pre,
                checkUserIds: val,
              }));
            }}
          />
        )}

        {showWorkSheet && (
          <WorkSheet
            sheetType={isSelfCheck ? ESheetType.自检 : ESheetType.巡检}
            taskType={1}
            value={activity.workSheetId}
            onChange={(val: number) => {
              setActivity((pre: any) => ({
                ...pre,
                workSheetId: val,
              }));
            }}
          />
        )}
        {showQuestion && (
          <ContentInput
            label="问题内容"
            placeholder="请输入问题内容"
            value={activity.word}
            onChange={(e: any) => {
              setActivity((pre: any) => ({
                ...pre,
                // word: e.target?.value.replace(/\s/g, ''),
                word: e,
              }));
            }}
          />
        )}
        {showTimeStatus && (
          <div>
            <div className="flex flex-col gap-3">
              <h3 className="text-[#141414] text-sm leading-[14px]">任务类型</h3>
              <div className="flex flex-wrap gap-2">
                {[
                  { label: '全部', value: 'ALL' },
                  { label: '日任务', value: 'DAY' },
                  { label: '周任务', value: 'WEEK' },
                  { label: '月任务', value: 'MONTH' },
                ].map((item) => {
                  return (
                    <button
                      key={item.value}
                      className={`px-4 h-[30px] rounded ${
                        item.value === activity.cyclicalPatterns
                          ? 'text-primary bg-primary/10'
                          : 'text-58 bg-black/[0.03]'
                      }  text-sm left-[14px]`}
                      onClick={() => {
                        setActivity((pre: any) => {
                          if (activity?.cyclicalPatterns === item.value) {
                            return {
                              ...pre,
                            };
                          } else {
                            return {
                              ...pre,
                              cyclicalPatterns: item.value,
                            };
                          }
                        });
                      }}
                    >
                      {item.label}
                    </button>
                  );
                })}
              </div>
            </div>
          </div>
        )}
      </div>
    </FilterDrawer>
  );
};
