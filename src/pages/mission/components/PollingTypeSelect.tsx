import { IconFont } from '@src/components';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>er, Popup } from 'antd-mobile';
import { useEffect, useRef, useState } from 'react';

type TProps = {
    renderSource: { name: string; key: string }[];
    noBg?: boolean;
    showText: string;
    className?: string;
    value: string;
    onChange: (val: string) => void;
};

export function PollingTypeSelect({
    renderSource,
    showText,
    noBg = true,
    className,
    value,
    onChange,
}: TProps) {
    const [visible, setVisible] = useState(false);

    const activeRef = useRef<string>();
    console.log('666value', value);
    const [selected, setSelected] = useState<string>(() => value);
    console.log('666selected', selected);
    useEffect(() => {
        activeRef.current = value;
        setSelected(value)
    }, [value]);

    return (
        <div className=" flex-shrink-0">
            <div
                className={`flex items-center leading-[22px] ${noBg ? '' : 'bg-primary-1 px-2 py-[3px] min-w-14 rounded'
                    } ${className}`}
                onClick={() => {
                    setVisible(true);
                }}
            >
                {showText}
                <IconFont type="icon-chevron-down" className="text-xs ml-1" />
            </div>
            <Popup
                position="top"
                visible={visible}
                bodyClassName="p-4 box-border"
                onMaskClick={() => {
                    setVisible(false);
                    // setActiveKey(currentActiveRef.current);
                }}
            >
                <div>
                    <CheckList
                        value={[selected]}
                        onChange={(val) => {
                            console.log('vvv', val);
                            if (!val.length) {
                                return;
                            }
                            setSelected(val?.[0] as any);
                        }}
                    >
                        {renderSource.map((item) => (
                            <CheckList.Item value={item.key}>{item.name}</CheckList.Item>
                        ))}
                    </CheckList>
                    <div className="flex gap-2 pt-2">
                        <Button
                            className="flex-1"
                            onClick={() => {
                                setVisible(false);
                                // setActiveKey(currentActiveRef.current);
                            }}
                            color="primary"
                            fill="outline"
                        >
                            取消
                        </Button>
                        <Button
                            className="flex-1"
                            color="primary"
                            onClick={() => {
                                setVisible(false);
                                onChange(selected);
                            }}
                        >
                            确定
                        </Button>
                    </div>
                </div>
            </Popup>
        </div>
    );
}
