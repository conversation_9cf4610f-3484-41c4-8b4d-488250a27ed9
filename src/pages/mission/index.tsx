import { useState } from 'react';
import AuthorityEnum from '@src/common/authority';
import WithKeepAlive from '@src/components/WithKeepAlive';
import { userStore } from '@src/store';
import { roleTypeIsManage } from '@src/utils/tokenUtils';
import { ErrorBlock, Tabs } from 'antd-mobile';
import { useLocation } from 'react-router-dom';
import { CreateTaskNode } from './components/createTaskNode';
import { Disinfection } from './view/Disinfection';
import { PatrolPage } from './view/Patrol';
import { SelfPage } from './view/Self';

export enum ERoleType {
  店长 = 'manager',
  督导 = 'supervision',
}
export enum ETabType {
  自检 = 'self-examination',
  巡检 = 'patrol',
}

type activeTabType = { type: 'self-examination' | 'patrol' | 'disinfection' };
export type TRoleType = ERoleType;

function Mission() {
  const tabs = [
    {
      label: '自检',
      value: 'self-examination',
      auth: roleTypeIsManage() ? AuthorityEnum.自检报告 : AuthorityEnum.报告中心,
    },
    {
      label: '巡检',
      value: 'patrol',
      auth: roleTypeIsManage() ? AuthorityEnum.巡检报告 : AuthorityEnum.报告中心,
    },
    {
      label: '消杀',
      value: 'disinfection',
      auth: roleTypeIsManage() ? AuthorityEnum.消杀报告 : AuthorityEnum.报告中心,
    },
  ].filter((i) => userStore.permissionsMap.has(i.auth));
  const location = useLocation();

  const searchParams = new URLSearchParams(location.search);

  const [activeTab, setActiveTab] = useState<activeTabType['type']>(() => {
    const searchParam = searchParams.get('tabtype');

    return tabs.some((i) => i.value === searchParam) ? (searchParam as any) : tabs[0]?.value;
  });

  const roletype = searchParams.get('roletype') as ERoleType;

  if (!tabs.length) {
    return <ErrorBlock status="empty" title="您没有该页面权限" description={<span>请与客服人员联系！</span>} />;
  }
  return (
    <div className="h-full">
      <Tabs
        className=" bg-white"
        activeKey={activeTab}
        onChange={(key) => {
          setActiveTab(key as any);
        }}
        style={{
          '--title-font-size': '0.875rem',
        }}
      >
        {tabs.map((i) => (
          <Tabs.Tab title={i.label} key={i.value} />
        ))}
      </Tabs>
      {activeTab === 'self-examination' && <SelfPage roleType={roletype} />}
      {activeTab === 'patrol' && <PatrolPage roleType={roletype} />}
      {activeTab === 'disinfection' && <Disinfection roleType={roletype} />}
      <CreateTaskNode />
    </div>
  );
}

export default WithKeepAlive(Mission);
