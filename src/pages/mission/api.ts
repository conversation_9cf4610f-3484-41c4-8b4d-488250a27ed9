import { get, post } from '@src/api';
import { ClientError } from '@src/api/react-query';
import { QueryKey, useInfiniteQuery } from '@tanstack/react-query';
import { diagnosticInfoItem } from '../patrol/api.type';
import { ERoleType } from '.';

export enum EStatus {
  待点评 = 'SUBMITTED',
  已点评 = 'REVIEWED',
}

export enum ESheetType {
  自检 = 'SELF',
  巡检 = 'PATROL',
  消杀 = 'DISINFECTION',
}

/**
 * 巡检
 *  有问题项时：
 *    督导：去审核
 *    门店：去整改
 */

export type TReportItem = {
  checkListNames: string[];
  exitOverdueProduct: boolean;
  issuesCount: number;
  itemsCount: number;
  noReformCount: number;
  reformStatus: string;
  reviewTime: string;
  reviewUserAvator: string;
  reviewUserName: string;
  score: number;
  shopId: string;
  shopName: string;
  statusDesc: string;
  submitTime: string;
  submitUserAvator: string;
  submitUserName: string;
  taskId: number;
  taskName: string;
  hasSecondReview: boolean;
  secondReview: boolean;
  unreviewed: boolean;
  recordStatus: EStatus;
  waitAuditCount: number;
  /** 是否发起整改 */
  alreadyAudit: boolean;
};

/**
 * 自检页面数据接口 - 督导
 */
export const fetchSelfReportList = async (data: any) => {
  return post<{
    total: number;
    result: TReportItem[];
  }>('/om-api/corp/app/selfReport/list', { data });
};

export function useSelfReportList(queryKey: QueryKey, variables: any, config: any = {}) {
  return useInfiniteQuery<{ page: number; total: number; result: TPatrolItem[] }, ClientError>({
    queryKey,
    queryFn: ({ pageParam }) => {
      const { roleType, ...args } = variables;
      return post(
        roleType === ERoleType.督导 ? '/om-api/corp/app/selfReport/list' : '/om-api/shop/self-task/selfReport/list',
        {
          data: { ...args, pageNo: pageParam },
        },
      );
    },
    initialPageParam: 1,
    getNextPageParam: (last, all) => {
      const lastLength = last?.result?.length || 0;
      const hasNext = lastLength !== 0;
      if (hasNext) {
        return all.length + 1;
      }
      return;
    },
    ...config,
  });
}

/**
 * 自检页面数据接口 - 店长
 */
export const fetchSelfReportListDZ = async (data: any) => {
  return post<{
    total: number;
    result: TReportItem[];
  }>('/om-api/shop/self-task/selfReport/list', { data });
};

/*
  获取人员信息
*/
export const getAllUserBase = async (data: any) =>
  // post<any>('/cc-api/gzt/user/role/getUserInfoList', {
  post<any>('/cc-api//gzt/user/getInspectionPersonneldetails', {
    data,
  });

/**
 * 获取检查表
 */
export const getWorkSheet = async (data: any) =>
  post('/om-api/corp/worksheet/effective/list/simple', {
    data,
  });
/**
 * 获取消杀公司
 */
export const getDisinfectionCompany = async () =>
  get<{ id: number; name: string }[]>('/om-api/common/disinfection/company/list/simple');
/**
 * 获取消杀巡检人
 */
export const getDisinfectionPeople = async () =>
  get<{ id: number; name: string }[]>('/om-api/common/disinfection/company/employee/list/simple');

// 旧接口
// export function getSelfReportCount(data: any) {
//   return post<{
//     dayCount: number;
//     weekCount: number;
//     mouthCount: number;
//     rectificationCount: number;
//   }>('/om-api/corp/app/selfReport/count', { data });
// }
export function getSelfReportCount(data: any) {
  return post<{
    dayCount: number;
    weekCount: number;
    mouthCount: number;
    rectificationCount: number;
    selfCount: number;
  }>('/om-api/corp/app/selfReport/todo', { data });
}

/**
 * 巡检的 任务表筛选
 */
export const getTaskByName = async (word: string) => get<any>(`/om-api/common/patrol/plan/name?word=${word}`);
export const getRoutineTaskByName = async (word: string) =>
  get<any>(`/om-api/common/routine/patrol/plan/name?word=${word}`);

/**
 * 自检的 任务表筛选
 */
export const getSelfTaskByName = async (data: any) =>
  post<any>(`/om-api/corp/self-task/selfTask/queryTaskSimpleInfo`, { data });

export enum EPStatus {
  待提交 = 'NOT_STARTED',
  已提交 = 'SUBMITTED',
  已确认 = 'CONFIRMED',
  督导待确认 = 'SUBMITTED',
}

export type TPatrolItem = {
  checkListName: string;
  checkUserName: string;
  confirmTime: string;
  confirmUserName: string;
  cross: boolean;
  issuesCount: number;
  itemsCount: number;
  noReformCount: number;
  operateList: string[];
  planId: number;
  planName: string;
  planType: string;
  reformStatus: string;
  reportDate: string;
  score: number;
  shopId: string;
  shopName: string;
  shopNo: string;
  status: EPStatus;
  reviewStatus: 'REVIEWED' | 'NOT_REVIEWED' | 'NO_NEEDED_REVIEWS';
  /**
   * 第二点评状态
   * QUALIFIED: 合格
   * UNQUALIFIED: 不合格
   * NOT_SECOND_REVIEW: 未进行第二点评
   */
  secondReviewStatus: 'QUALIFIED' | 'UNQUALIFIED' | 'NOT_SECOND_REVIEW';
  /**
   * 是否开启第二点评
   */
  hasOpenSecondReview: boolean;
  submitTime: string;
  submitUserId: number;
  submitUserName: string;
  taskId: number;
  hasNeedReview: boolean;
  waitAuditCount: number;
  worksheetNames: string[];
  // 食安稽核到店辅导-FOOD_SAFETY_ARRIVE_SHOP
  subType:
    | 'NORMAL'
    | 'VIDEO'
    | 'CROSS'
    | 'FOOD_SAFETY_ARRIVE_SHOP'
    | 'FOOD_SAFETY_NORMAL'
    | 'FOOD_SAFETY_VIDEO'
    | 'FOOD_SAFETY_ARRIVE_SHOP';
  diagnosticInfo?: diagnosticInfoItem;
};
export type DisinfectionItem = {
  id: number;
  shopId: string;
  shopName: string;
  disinfectionType: 'FIRST_STRUCTURE' | 'SECOND_STRUCTURE' | 'DAILY' | 'EXIGENCY';
  reportStatus: string;
  taskStatus: string;
  reportScore: number;
  processList: [
    {
      id: number;
      taskId: number;
      operateAction: string;
      operateType: number;
      operatorId: number;
      operatorPlatformCode: number;
      operatorName: string;
      operatorPhone: string;
      remark: string;
      images: string[];
      resourceImages: [
        {
          url: string;
          type: string;
          id: string;
        },
      ];
      createTime: string;
      updateTime: string;
    },
  ];
  reportPassed: string;
  createTime: {
    seconds: number;
    nanos: number;
  };
  createUserId: number;
  createUserName: string;
  createUserPhone: string;
  reportCheckCount: number;
  reportIssueCount: number;
  disinfectionCompanyName?: string;
  arriveTime?: string;
  reportNoReformCount: number;
  taskStartTime: string;
  taskClosedTime: string;
  worksheetList: {
    worksheetName: string;
    worksheetId: number;
  }[];
  reportPositive: boolean;
};
/**
 * 巡检页面数据接口
 */
export const fetchPatrolReportList = async (data: any) => {
  return post<TPatrolItem[]>('/om-api/common/patrol/report/list', { data });
};

// 巡检报告
export function usePatrolReportList(queryKey: QueryKey, variables: any, config: any = {}) {
  return useInfiniteQuery<TPatrolItem[], ClientError>({
    queryKey,
    queryFn: ({ pageParam }) =>
      post('/om-api/common/patrol/report/list', {
        data: { ...variables, pageNo: pageParam },
      }),
    initialPageParam: 1,
    getNextPageParam: (last, all) => {
      const lastLength = last?.length || 0;
      const hasNext = lastLength !== 0;
      if (hasNext) {
        return all.length + 1;
      }
      return;
    },
    ...config,
  });
}
// 消杀报告
export function useDisinfectionReportList(queryKey: QueryKey, variables: any, config: any = {}) {
  return useInfiniteQuery<
    {
      data: DisinfectionItem[];
    },
    ClientError
  >({
    queryKey,
    queryFn: ({ pageParam }) =>
      post('/om-api/common/disinfection/report/app-page', {
        data: { ...variables, pageNo: pageParam },
      }),
    initialPageParam: 1,
    getNextPageParam: (last, all) => {
      console.log(last, '=last');
      console.log(all, '=all');

      const lastLength = last?.data?.length || 0;
      const hasNext = lastLength !== 0;
      if (hasNext) {
        return all?.length + 1;
      }
      return;
    },
    ...config,
  });
}
/**
 * 删除巡检报告
 */
export const delPatrolReport = async (data: { taskId: number }) => {
  return post<TPatrolItem[]>('/om-api/corp/patrol/task/delete', { data });
};
