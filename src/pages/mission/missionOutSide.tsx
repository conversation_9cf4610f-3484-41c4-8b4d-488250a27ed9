import WithKeepAlive from '@src/components/WithKeepAlive';
import { PatrolOutSidePage } from './view/PatrolOutSide';
import { useLocation } from 'react-router-dom';
import { CreateTaskNode } from './components/createTaskNode';

export enum ERoleType {
  店长 = 'manager',
  督导 = 'supervision',
}

export type TRoleType = ERoleType;

/**
 * 到店巡检->权限外
 */
function MissionOutSide() {
  let location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const roletype = searchParams.get('roletype') as ERoleType;
  return (
    <div className="h-full overflow-hidden">
      <PatrolOutSidePage roleType={roletype} />
      <CreateTaskNode />
    </div>
  );
}

export default WithKeepAlive(MissionOutSide);
