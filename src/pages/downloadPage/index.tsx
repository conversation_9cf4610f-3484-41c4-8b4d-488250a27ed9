import { useEffect } from 'react';
import downloadBg from '@src/assets/images/downloadBg.png';
import { isInWeChatBrowser } from '@src/utils/utils';
import { Button, Image } from 'antd';
import { Modal } from 'antd-mobile';
import CallApp from 'callapp-lib';
import { useSearchParams } from 'react-router-dom';

export default function DownloadPage() {
  const [params] = useSearchParams();

  const appstore = 'https://apps.apple.com/us/app/%E5%A1%94%E5%A1%94%E8%BF%90%E8%90%A5%E9%80%9A/id6476599498';
  const androdiApk = 'https://tastientech.com/shm-om-fundation-appupdate/shm-prod.apk';

  const client = new CallApp({
    appstore,
    scheme: {
      protocol: 'om-fundation',
    },
    fallback: androdiApk,
    timeout: 5000,
  });

  const open = () => {
    const paramsWithoutUrl = Object.fromEntries(
      Array.from(new URLSearchParams(params)).filter(([key]) => key !== 'url'),
    );
    if (params.get('type') === 'app') {
      client.open({
        path: params.get('url') ?? '',
        param: paramsWithoutUrl,
      });
      return;
    }
    client.open({
      path: 'webview',
      param: {
        url: params.get('url'),
        ...paramsWithoutUrl,
      },
    });
  };

  useEffect(() => {
    const isWx = isInWeChatBrowser();

    if (isWx) {
      Modal.alert({
        title: '提示',
        content: '请点击右上角操作，在浏览器打开',
        showCloseButton: false,
      });
    }
  }, []);

  return (
    <div className="w-full h-full">
      <Image src={downloadBg} className="w-full h-full" />
      <div style={{ position: 'fixed', bottom: '17%' }} className="w-full text-center flex flex-col">
        <Button className="mx-10" type="primary" onClick={open}>
          打开APP
        </Button>
        <Button
          className="mt-3"
          type="link"
          onClick={() => {
            const ua = navigator.userAgent || ''; // 版本号比较
            const isIos = /iphone|ipad|ipod/i.test(ua);
            const a = document.createElement('a');
            if (isIos) {
              a.href = appstore;
            } else {
              a.href = androdiApk;
            }

            a.click();
          }}
        >
          立即下载
        </Button>
      </div>
    </div>
  );
}
