export enum SelfTendencyNames {
  average = '报告平均分',
  complete = '报告完成率',
  comment = '报告点评率',
  standard = '自检通过率',
}
export const SelfTendencyNameMap = {
  [SelfTendencyNames.average]: 'commentedAvgScore',
  [SelfTendencyNames.complete]: 'completedReportRatio',
  [SelfTendencyNames.comment]: 'commentedReportRatio',
  [SelfTendencyNames.standard]: 'commentedPassReportRatio',
};
export const selfConditionSort = [
  {
    label: SelfTendencyNames.average,
    value: SelfTendencyNameMap[SelfTendencyNames.average],
  },
  {
    label: SelfTendencyNames.complete,
    value: SelfTendencyNameMap[SelfTendencyNames.complete],
    isRate: true,
  },
  {
    label: SelfTendencyNames.comment,
    value: SelfTendencyNameMap[SelfTendencyNames.comment],
    isRate: true,
  },
  {
    label: SelfTendencyNames.standard,
    value: SelfTendencyNameMap[SelfTendencyNames.standard],
    isRate: true,
  },
];

export enum PatrolTendencyNames {
  average = '报告平均分',
  passReport = '巡检通过率',
  redLine = '按S项不合格门店率',
  rectify = '按门店整改率',
}
export const PatrolTendencyNameMap = {
  [PatrolTendencyNames.average]: 'averageScore',
  [PatrolTendencyNames.passReport]: 'passReportRatio',
  [PatrolTendencyNames.redLine]: 'redLineShopRatio',
  [PatrolTendencyNames.rectify]: 'shopRectifyRatio',
};
export const patrolConditionSort = [
  {
    label: PatrolTendencyNames.average,
    value: PatrolTendencyNameMap[PatrolTendencyNames.average],
  },
  {
    label: PatrolTendencyNames.passReport,
    value: PatrolTendencyNameMap[PatrolTendencyNames.passReport],
    isRate: true,
  },
  {
    label: PatrolTendencyNames.redLine,
    value: PatrolTendencyNameMap[PatrolTendencyNames.redLine],
    isRate: true,
  },
  {
    label: PatrolTendencyNames.rectify,
    value: PatrolTendencyNameMap[PatrolTendencyNames.rectify],
    isRate: true,
  },
];

export enum diagnosticTendencyNames {
  rectify = '按门店整改率',
}

export const diagnosticTendencyNameMap = {
  [diagnosticTendencyNames.rectify]: 'shopRectifyRatio',
};

export const diagnosticConditionSort = [
  {
    label: diagnosticTendencyNames.rectify,
    value: diagnosticTendencyNameMap[diagnosticTendencyNames.rectify],
    isRate: true,
  },
];

/** 巡检不合格情况 排序选项 */
export const patrolUnqualifiedSituationSort = [
  {
    label: '检查项不合格率',
    value: 'noPassRatio',
    isRate: true,
  },
  {
    label: '巡检次数',
    value: 'patrolCount',
  },
  {
    label: '问题发生次数',
    value: 'issuesReportCount',
  },
  {
    label: '不合格门店数',
    value: 'refShopCount',
  },
];
