import { ReactNode, useEffect, useState } from 'react';
import { ESheetType } from '@src/pages/mission/api';
import { AllUserSelect } from '@src/pages/mission/components/AllUserSelect';
import { WorkSheet } from '@src/pages/mission/components/WorkSheet';
import { FilterDrawer } from '@src/pages/tasks/components/FilterDrawer';
import { yytShopTypeEnum } from '@src/pages/tasks/enum';
import { DrawerProps, Select, SelectProps } from 'antd';
import { twMerge } from 'tailwind-merge';
import { DataViewTypeEnum } from '../..';
import { checkItemAttributeOptions, patrolTypeOptions } from '../const';

function FilterItem({ label, className, children }: { label: string; className?: string; children: ReactNode }) {
  return (
    <div className={twMerge('flex flex-row justify-between items-center', className)}>
      <h3 className="text-[#141414] text-sm leading-[14px]">{label}</h3>
      {children}
    </div>
  );
}

function ShopTypFilter({
  label = '门店类型',
  value,
  options = [
    { label: '加盟T', value: yytShopTypeEnum.加盟T },
    { label: '加盟M', value: yytShopTypeEnum.加盟M },
  ],
  onChange,
}: {
  label?: string;
  value: (typeof options)[0]['value'];
  options?: { label: string; value: string }[];
  onChange?: (value?: (typeof options)[0]['value']) => void;
}) {
  return (
    <FilterItem label={label} className="flex-col items-start gap-y-3">
      <div className="flex flex-wrap gap-2">
        {options?.map((m) => (
          <button
            key={m.value}
            className={`w-20 h-[30px] rounded ${
              value === yytShopTypeEnum[m.label as keyof typeof yytShopTypeEnum]
                ? 'text-primary bg-primary/10'
                : 'text-58 bg-black/[0.03]'
            }  text-sm left-[14px]`}
            onClick={() => {
              onChange?.(value === m.value ? undefined : m.value);
            }}
          >
            {m.label}
          </button>
        ))}
      </div>
    </FilterItem>
  );
}

function SelectFilter({ label, ...selectProps }: { label: string } & SelectProps) {
  return (
    <FilterItem label={label}>
      <Select placeholder={`请选择${label}`} style={{ width: 200 }} {...selectProps} />
    </FilterItem>
  );
}

interface TypeFilterProps extends DrawerProps {
  value: Record<string, any>;
  onChange: (value: Record<string, any>) => void;
  onClose: () => void;
  type: string;
}

export default function TypeFilter({ value, onChange, onClose, type, ...props }: TypeFilterProps) {
  const [activity, setActivity] = useState<any>(value);

  const onOk = () => {
    onChange(activity);
  };

  const onClear = () => {
    setActivity({
      shopType: undefined,
      patrolUserIds: undefined,
      workSheetId: undefined,
      worksheetIds: undefined,
      subType: undefined,
      checkItemAttribute: undefined,
    });
  };

  useEffect(() => {
    setActivity(value);
  }, [type, value]);

  return (
    <FilterDrawer
      {...props}
      onClose={() => {
        setActivity(value);
        onClose();
      }}
      onOk={onOk}
      onClear={onClear}
    >
      <div className="flex flex-col gap-3">
        <ShopTypFilter
          value={activity?.shopType}
          onChange={(shopType) => {
            setActivity((pre: any) => ({ ...pre, shopType }));
          }}
        />
        {[DataViewTypeEnum.巡检不合格情况].includes(type as DataViewTypeEnum) && (
          <AllUserSelect
            label="巡检人"
            value={activity?.patrolUserIds}
            mode="multiple"
            onChange={(val: number) => {
              setActivity((pre: any) => ({
                ...pre,
                patrolUserIds: val,
              }));
            }}
          />
        )}
        <WorkSheet
          type={type}
          taskType={0}
          worksheetTypes={
            {
              [DataViewTypeEnum['自检看板 (新)']]: [ESheetType.自检],
              [DataViewTypeEnum.巡检不合格情况]: [ESheetType.巡检],
            }[type]!
          }
          value={type === DataViewTypeEnum.巡检不合格情况 ? activity?.worksheetIds : activity?.workSheetId}
          shouldUseMultipleMode={type === DataViewTypeEnum.巡检不合格情况}
          onChange={(worksheetId: number | number[]) => {
            setActivity((pre: any) => ({
              ...pre,
              ...(type === DataViewTypeEnum.巡检不合格情况
                ? { worksheetIds: worksheetId }
                : { workSheetId: worksheetId }),
            }));
          }}
        />
        {[DataViewTypeEnum.巡检不合格情况].includes(type as DataViewTypeEnum) && (
          <>
            <SelectFilter
              label="巡检类型"
              value={activity?.subType}
              options={patrolTypeOptions}
              onChange={(subType) => {
                setActivity((pre: any) => ({
                  ...pre,
                  subType,
                }));
              }}
            />
            <SelectFilter
              label="检查项属性"
              value={activity?.workSheetItemTag}
              options={checkItemAttributeOptions}
              onChange={(workSheetItemTag) => {
                setActivity((pre: any) => ({
                  ...pre,
                  workSheetItemTag,
                }));
              }}
            />
          </>
        )}
      </div>
    </FilterDrawer>
  );
}
