export enum StrategyPatrolType {
  /** 到店巡检 */
  NORMAL = 'NORMAL',
  /** 视频云巡检 */
  VIDEO = 'VIDEO',
  /** 食安线下稽核 */
  FOOD_SAFETY_NORMAL = 'FOOD_SAFETY_NORMAL',
  /** 食安线上稽核 */
  FOOD_SAFETY_VIDEO = 'FOOD_SAFETY_VIDEO',
  /** 诊断巡检 */
  DIAGNOSTIC = 'DIAGNOSTIC',
  /** 食安稽核到店辅导 */
  FOOD_SAFETY_ARRIVE_SHOP = 'FOOD_SAFETY_ARRIVE_SHOP',
  /** 差异项到店任务 */
  DIFFERENCE_ITEM_ARRIVE_SHOP = 'DIFFERENCE_ITEM_ARRIVE_SHOP',
}

export const StrategyPatrolTypeCN: Record<StrategyPatrolType, string> = {
  [StrategyPatrolType.NORMAL]: '到店巡检',
  [StrategyPatrolType.VIDEO]: '视频云巡检',
  [StrategyPatrolType.FOOD_SAFETY_NORMAL]: '食安线下稽核',
  [StrategyPatrolType.FOOD_SAFETY_VIDEO]: '食安线上稽核',
  [StrategyPatrolType.DIAGNOSTIC]: '诊断任务',
  [StrategyPatrolType.FOOD_SAFETY_ARRIVE_SHOP]: '食安稽核到店辅导',
  [StrategyPatrolType.DIFFERENCE_ITEM_ARRIVE_SHOP]: '差异项到店任务',
};

/** 巡检类型选项 */
export const patrolTypeOptions = Object.keys(StrategyPatrolTypeCN || {}).map((key) => ({
  label: StrategyPatrolTypeCN[key as keyof typeof StrategyPatrolTypeCN],
  value: key as keyof typeof StrategyPatrolTypeCN,
}));

export enum CheckItemAttribute {
  RED_LINE = 'RED_LINE',
  NECESSARY = 'NECESSARY',
  YELLOW = 'YELLOW',
  KEY = 'KEY',
  PENALTY = 'PENALTY',
  POSITIVE = 'POSITIVE', // 阳性指标项
}

export const CheckItemAttributeCN: Record<CheckItemAttribute, string> = {
  [CheckItemAttribute.RED_LINE]: 'S项',
  [CheckItemAttribute.NECESSARY]: '必检项',
  [CheckItemAttribute.YELLOW]: 'M项',
  [CheckItemAttribute.KEY]: '关键项',
  [CheckItemAttribute.PENALTY]: '罚款项',
  [CheckItemAttribute.POSITIVE]: '阳性指标项',
};

/** 检查项属性选项 */
export const checkItemAttributeOptions = Object.keys(CheckItemAttributeCN || {}).map((key) => ({
  label: CheckItemAttributeCN[key as keyof typeof CheckItemAttributeCN],
  value: key as keyof typeof CheckItemAttributeCN,
}));
