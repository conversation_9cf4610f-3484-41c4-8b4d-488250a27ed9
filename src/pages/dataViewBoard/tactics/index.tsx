import { useEffect, useState } from 'react';
import { PageContext } from '@src/common/page-context';
import { report } from '@src/common/report';
import { BackTop, CustomerFilter, DateFilter, IconFont, Loading } from '@src/components';
import { init } from '@src/components/DataBoardFilter';
import WithKeepAlive from '@src/components/WithKeepAlive';
import { useGroupTree } from '@src/hooks';
import { DataBoardStore, userStore } from '@src/store';
import { getDataViewBoardEnum } from '@src/utils/utils';
import { Card, ErrorBlock, SafeArea, Tabs } from 'antd-mobile';
import cn from 'classnames';
import dayjs from 'dayjs';
import { has, keys } from 'lodash';
import { observer } from 'mobx-react';
import { parse, stringify } from 'qs';
import { useLocation, useNavigate, useSearchParams } from 'react-router-dom';
import TypeFilter from './components/TypeFilter';
import styles from './index.module.scss';
import { DataViewTypeEnum } from '..';
import SubGroupCondition from '../subGroupCondition';

const defaultParams = {
  beginDate: dayjs().format('YYYY-MM-DD'),
  endDate: dayjs().format('YYYY-MM-DD'),
};

const tabList = [
  {
    key: DataViewTypeEnum['自检看板 (新)'],
    title: '自检看板',
    permission: getDataViewBoardEnum('自检实时情况_自检看板'),
    onClick: () => {
      report({
        type: 'ability',
        page: '数据看板',
        abilityButton: '营运通数据-自检看板 (新)',
      });
    },
  },
  {
    key: DataViewTypeEnum.巡检不合格情况,
    title: '巡检不合格情况',
    permission: getDataViewBoardEnum('数据实时情况_巡检不合格情况'),
    onClick: () => {
      report({
        type: 'ability',
        page: '数据看板',
        abilityButton: '营运通数据-巡检不合格情况',
      });
    },
  },
];

const DataViewBoard = WithKeepAlive(
  observer(() => {
    const [search] = useSearchParams();
    const location = useLocation();
    const { permissionsMap } = userStore;
    const { pid, getGroupTree, tagIdObj } = useGroupTree();
    const [filterVisible, setFilterVisible] = useState<boolean>(false);

    const activeTab = (search.get('activeTab') as DataViewTypeEnum) || tabList[0].key;
    const [activeKey, setActiveKey] = useState<DataViewTypeEnum>(activeTab);
    const navigate = useNavigate();

    const {
      searchObj,
      setSearchObj,
      selfDataChartView,
      SelfOrgsSituation: selfOrgsSituation,
      patrolUnqualifiedSituation,
    } = DataBoardStore;

    const [searchParams, setSearchParams] = useState<any>({
      ...defaultParams,
      ...searchObj,
    });

    /**
     * ios 二级路由返回时，title有问题，这里强制改一下
     */
    useEffect(() => {
      if (location.pathname === '/dataViewBoard') {
        document.title = '数据看板';
      }
    }, [location]);

    useEffect(() => {
      setSearchObj(searchParams);

      const fetchMap = {
        [DataViewTypeEnum['自检看板 (新)']]: () => {
          return Promise.all([
            selfDataChartView.run({ ...searchParams, isTactics: true }),
            selfOrgsSituation.run({ ...searchParams, isTactics: true }),
          ]);
        },
        [DataViewTypeEnum.巡检不合格情况]: () => {
          return patrolUnqualifiedSituation.run(searchParams);
        },
      };

      fetchMap?.[activeKey as keyof typeof fetchMap]?.();
    }, [activeKey, searchParams]);

    useEffect(() => {
      return () => {
        setSearchObj({});
      };
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    if (!permissionsMap.has(getDataViewBoardEnum('数据实时情况') as string)) {
      return <ErrorBlock status="empty" title="您没有该页面权限" description={<span>请与客服人员联系！</span>} />;
    }

    return (
      <Loading spinning={selfOrgsSituation.loading || patrolUnqualifiedSituation.loading}>
        <PageContext.Provider value={{ pid, getGroupTree, tagIdObj }}>
          <div className=" h-full overflow-y-auto" id="boardsPage">
            <div className="bg-white pl-1">
              <Tabs
                className={styles.iAdmTabs}
                style={{ '--title-font-size': '1rem' }}
                activeKey={activeKey}
                onChange={(key) => {
                  const { activeTab: _activeTab, ...rest } = parse(location.search.slice(1));
                  const reportIndex = tabList.findIndex((i) => i.key === key);

                  setActiveKey(() => key as DataViewTypeEnum);
                  setSearchParams(defaultParams);
                  tabList?.[reportIndex]?.onClick();
                  navigate(`${location.pathname}?${stringify({ ...rest, activeTab: key })}`, {
                    replace: true,
                  });
                }}
                stretch={false}
              >
                {tabList
                  .filter((f) =>
                    f?.permission && typeof f?.permission === 'string'
                      ? userStore.permissionsMap.has(f.permission)
                      : f?.permission,
                  )
                  .map(({ key, title }) => (
                    <Tabs.Tab title={title} key={key} />
                  ))}
              </Tabs>
            </div>
            <Card className="rounded-none border-b border-solid border-line border-px mb-2 " bodyClassName="py-2">
              <div className="flex justify-between items-start">
                <CustomerFilter
                  groupMaxWidth="3.625rem"
                  value={{
                    groupId: searchParams.groupId,
                    shopCodes: searchParams.shopIds || [],
                  }}
                  onChange={(e: any) => {
                    setSearchParams((pre: any) => ({
                      ...pre,
                      groupId: e?.shopCodes?.length > 0 ? undefined : e.groupId,
                      shopIds: e.shopCodes,
                    }));
                  }}
                />
                <DateFilter
                  onlyDate
                  min={dayjs().subtract(6, 'day').toDate()}
                  value={[dayjs(searchParams.beginDate).toDate(), dayjs(searchParams.endDate).toDate()]}
                  customizeDateRange={7}
                  onChange={(e) => {
                    setSearchParams((p: any) => ({
                      ...p,
                      beginDate: e?.[0],
                      endDate: e?.[1],
                    }));
                  }}
                  key="range"
                />
                <button
                  className={cn(
                    `text-sm flex items-center py-[3px] leading-[14px] ${
                      filterVisible ? 'text-primary' : 'text-[#5E5E5E]'
                    } focus:outline-none`,
                    {
                      'text-primary': keys(init).some((o) => !!searchParams && has(searchParams, o)),
                    },
                  )}
                  onClick={() => {
                    setFilterVisible(true);
                  }}
                >
                  <div className="w-[1px] h-5 bg-black/[0.03] mr-[18px]" />
                  筛选
                  <IconFont type="icon-a-1111-copy" className="ml-1 text-xs text-[#B8B8B8]" />
                </button>
              </div>
            </Card>
            {/* <OverBoard type={activeKey} /> */}
            <SubGroupCondition type={activeKey} />
            {/* <UnqualifiedItem type={activeKey} /> */}

            <BackTop domId="boardsPage" />
          </div>
          <SafeArea position="bottom" />
          <TypeFilter
            value={searchParams}
            open={filterVisible}
            type={activeKey}
            onClose={() => setFilterVisible(false)}
            onChange={(value: Record<string, any>) => {
              setSearchParams((pre: any) => ({ ...pre, ...value }));
              setFilterVisible(false);
            }}
          />
        </PageContext.Provider>
      </Loading>
    );
  }),
);

export default DataViewBoard;
