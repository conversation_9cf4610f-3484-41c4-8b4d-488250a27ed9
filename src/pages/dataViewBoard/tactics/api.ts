import { post } from '@src/api';
import { checkDataParams } from '../api.type';

/** 自检不合格列表 */
export const getTacticsUnqualified = (params: checkDataParams) => {
  return post('/tm-api/selfReport/issues/list', { data: params });
};

/** 策略-数据概览图表 */
export const queryTacticsSelfReportView = async (params: checkDataParams) =>
  post('/tm-api/app/selfReport/querySelfIndexChart', { data: params });

/** 策略-数据概览图表统计 */
export const queryTacticsSelfReportCompleted = async (params: checkDataParams) => {
  return post('/tm-api/app/selfReport/querySelfIndex', { data: params });
};

/** 策略-自检数据概览列表 */
export const queryTacticsSelfReportDetailList = async (params: checkDataParams) =>
  post('/tm-api/statistic/shop/self/list', { data: params });

/** 策略-自检数据概览列表统计 */
export const queryTacticsSelfReportListTotal = async (params: checkDataParams) =>
  post('/tm-api/statistic/shop/self/summary', { data: params });

/** 策略-自检子组织折线趋势图数据 */
export const queryTacticsSelfReportGroupRank = async (params: checkDataParams) => {
  return post('/tm-api/app/selfReport/querySelfReportGroupRank', {
    data: params,
  });
};

export interface GetChecklistSimplelistReq {
  labelIds?: number[] | null;
  sheetName?: null | string;
  worksheetTypes?: string[] | null;
  [property: string]: any;
}

/** 获取策略检查表列表 */
export const getTacticsChecklistSimplelist = async (data: GetChecklistSimplelistReq) =>
  await post(`/tm-api/common/worksheet/effective/list/simple`, { data });

/** 策略-巡检不合格情况 */
export const getTacticsPatrolUnqualified = (data: any) => {
  return post('/tm-api/app/v2/statistic/issue-report-list', { data });
};

export interface IUnqualifiedReasonReq {
  nonconformityReasons: {
    reason: string;
    count: number;
    total: number;
    workSheetId: number;
    workSheetCategoryId: number;
    workSheetItemId: number;
    reasonRatio: string;
  }[];
  otherReasonRatio: string;
  otherReasons: any[];
  total: number;
}

/** 策略-不合格原因 */
export function getTacticsUnqualifiedReason(data: any) {
  return post<IUnqualifiedReasonReq>('/tm-api/app/v2/statistic/issue-report/unqualified-reason', { data });
}

export interface IUnqualifiedShopItem {
  shopId: string;
  shopName: string;
  shopType: string;
  patrolCount: number;
  issuesReportCount: number;
  noPassRatio: string;
  rectifyCompleteCount: number;
  rectifyRatio: string;
}

/** 策略-不合格门店 */
export function getTacticsUnqualifiedShop(data: any) {
  return post<IUnqualifiedShopItem[]>('/tm-api/app/v2/statistic/issue-report/unqualified-shop-list', { data });
}
