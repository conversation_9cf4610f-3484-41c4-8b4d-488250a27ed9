.iAdmTabs {
  :global {
    .adm-tabs-tab-list {
      color: #58595b;
    }
    .adm-tabs-tab-active {
      font-weight: 600;
    }
    // .adm-tabs-header {
    //   border-bottom: none;
    // }
  }
}
.iAdmTabs1 {
  :global {
    .adm-tabs-tab-list {
      color: #58595b;
    }
    .adm-tabs-tab-active {
      font-weight: 600;
    }
    .adm-tabs-header {
      border-bottom: none;
    }
  }
}
.iAdmTabs2 {
  :global {
    .adm-tabs-tab-list {
      color: #777;
    }
    .adm-tabs-header {
      border-bottom: none;
    }
  }
}
.iAdmSearchBar {
  :global {
    .adm-input-element {
      font-size: 0.8125rem;
    }
  }
}

:global {
  .adm-ellipsis {
    line-height: inherit;
    a {
      color: var(--adm-color-primary);
      // padding-left: 0.375rem;
    }
  }
}

.boardsChildren {
  :global {
    // .ant-table-bordered .ant-table-header > table,
    // .ant-table-bordered .ant-table-body > table,
    // .ant-table-bordered .ant-table-fixed-left table,
    // .ant-table-bordered .ant-table-fixed-right table {
    //   border-left: none;
    // }
    // .ant-table-bordered {
    //   .ant-table-thead > tr > th:last-child {
    //     border-right: none;
    //   }
    //   .ant-table-tbody > tr > td:last-child {
    //     border-right: none;
    //   }
    // }
    // .ant-table-tbody > tr:last-child > td {
    //   border-bottom: none;
    // }
    .ant-table.ant-table-bordered {
      border-radius: 8px;
      .ant-table-container {
        border: none !important;
        .ant-table-thead {
          tr:first-child {
            th {
              border-color: rgba(0, 0, 0, 0.03);
              border-inline-end-color: rgba(0, 0, 0, 0.03) !important;
            }
            th:last-child {
              border-inline-end: none !important;
            }
          }
        }
        .ant-table-tbody {
          tr {
            td {
              border-color: rgba(0, 0, 0, 0.03);
              border-inline-end-color: rgba(0, 0, 0, 0.03) !important;
            }
            td:last-child {
              border-inline-end: none !important;
            }
          }
          tr:last-child {
            td {
              border-bottom: none;
            }
          }
        }
      }
    }
    .ant-table-thead tr th.ant-table-cell,
    .ant-table-thead tr td.ant-table-cell {
      background: rgba(55, 139, 255, 0.1);
      color: #58595b;
      font-weight: normal;
    }
    .ant-table-cell {
      padding: 0.75rem 0.25rem !important;
      color: #58595b !important;
      font-size: 12px !important;
    }
    .ant-table-cell-row-hover {
      background: transparent !important;
    }
  }
}

//处理二级指标，最后一个指标圆角
.IndicatorCardSubWrap {
  .IndicatorCardSub:last-child {
    border-bottom-right-radius: 0.5rem /* 8px */;
    border-bottom-left-radius: 0.5rem /* 8px */;
    .IndicatorSubContent {
      border-bottom-right-radius: 0.5rem /* 8px */;
      border-bottom-left-radius: 0.5rem /* 8px */;
      border-bottom: 1px solid rgba(0, 0, 0, 0.03);
    }
  }
}
