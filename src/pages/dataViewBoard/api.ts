import { post } from '@src/api';
import { checkDataParams } from './api.type';

/** 自检不合格列表 */
export const getUnqualified = (params: checkDataParams) => {
  return post('/om-api/selfReport/issues/list', { data: params });
};

/** 自检不合格原因 */
export const querySelfTaskNonconformity = (params: checkDataParams) => {
  return post('/om-api/selfReport/issues/nonconformityReason', {
    data: params,
  });
};

/** 巡检不合格原因 */
export const queryPatrolTaskNonconformity = (params: checkDataParams) => {
  return post('/om-api/statistic/issuesReport/nonconformityReason', {
    data: params,
  });
};

/** 自检不合格门店 */
export const querySelfTaskNoPassShop = (params: checkDataParams) => {
  return post('/om-api/selfReport/issues/shopList', { data: params });
};
// 数据概览图表
export const querySelfReportView = async (params: checkDataParams) =>
  post('/om-api/app/selfReport/querySelfIndexChart', { data: params });

// 数据概览图表统计
export const querySelfReportCompleted = async (params: checkDataParams) => {
  return post('/om-api/app/selfReport/querySelfIndex', { data: params });
};

// 数据概览列表
export const querySelfReportDetailList = async (params: checkDataParams) =>
  post('/om-api/statistic/shop/self/list', { data: params });

// 自检数据概览列表统计
export const querySelfReportListTotal = async (params: checkDataParams) =>
  post('/om-api/statistic/shop/self/summary', { data: params });

// 自检门店排行
export const querySelfReportShopRank = async (params: checkDataParams) =>
  post('/om-api/app/selfReport/querySelfReportShopRank', { data: params });
// 巡检数据概览统计
export const queryPatrolReportCompleted = async (params: checkDataParams) =>
  post('/om-api/statistic/shop/patrol/summary', { data: params });
// 巡检数据概览
export const queryPatrolDataView = async (params: checkDataParams) =>
  post('/om-api/statistic/shop/app/queryPatrolIndex', { data: params });
export const queryDiagnosticSummary = async (params: checkDataParams) =>
  post('/om-api/statistic/shop/patrol/diagnostic/summary', { data: params });

export const queryPatrolDataChart = async (params: checkDataParams) =>
  post('/om-api/statistic/shop/app/queryPatrolDataChart', { data: params });
export const queryPatrolDataIfnfo = async (params: checkDataParams) =>
  post('/om-api/statistic/shop/patrol/list', { data: params });

export const queryDiagnosticDataIfnfo = async (params: checkDataParams) =>
  post('/om-api/statistic/shop/patrol/diagnostic/list', { data: params });
// 巡检门店排行
export const queryPatrolReportShopRank = async (params: checkDataParams) =>
  post('/om-api/statistic/shop/app/patrol/queryPatrolShopRank', {
    data: params,
  });
/** 巡检不合格列表 */
export const getPatrolUnqualified = async (params: checkDataParams) => {
  return post('/om-api/statistic/issuesReport/list', { data: params });
};

/** 巡检不合格门店 */
export const queryPatrolSelfTaskNoPassShop = async (params: checkDataParams) => {
  return post('/om-api/statistic/issuesReport/shopList', { data: params });
};

// 自检子组织折线趋势图数据
export const querySelfReportGroupRank = async (params: checkDataParams) => {
  return post('/om-api/app/selfReport/querySelfReportGroupRank', {
    data: params,
  });
};
// 巡检子组织折线趋势图数据
export async function queryPatrolGroupRank(params: checkDataParams) {
  return post('/om-api/statistic/shop/app/queryPatrolGroupRank', {
    data: params,
  });
}
// 巡检子组织折线趋势图数据
export async function queryDiagnosticGroupRank(params: checkDataParams) {
  return post('/om-api/statistic/shop/app/queryDiagnosticGroupRank', {
    data: params,
  });
}
//  门店学习情况列表（按组织）
export async function queryStudyGroupList(params: checkDataParams) {
  return post('/om-api/common/statistic/shop/study-by-group', {
    data: params,
  });
}
