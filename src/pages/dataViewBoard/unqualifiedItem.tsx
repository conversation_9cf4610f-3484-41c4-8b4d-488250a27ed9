import { useEffect, useMemo, useState } from 'react';
import { IconFont } from '@src/components';
import { DataBoardStore } from '@src/store';
import { Picker } from 'antd-mobile';
import _ from 'lodash';
import { observer } from 'mobx-react';
import { createSearchParams, useNavigate } from 'react-router-dom';
import SheetCard from './components/sheetCard';
import TitleViewCard from './components/titleViewCard';
import { DataViewTypeEnum } from '.';

type IProps = {
  type: string;
};

const propertyOption = [
  { value: 'all', label: '全部' },
  { value: 'RED_LINE', label: 'S项' },
  { value: 'NECESSARY', label: '必检项' },
  { value: 'YELLOW', label: 'M项' },
  { value: 'KEY', label: '关键项' },
  { value: 'PENALTY', label: '罚款项' },
];
const UnqualifiedItem = observer(({ type }: IProps) => {
  const [visible, setVisible] = useState<boolean>(false);
  const [filterValue, setFilterValue] = useState<string[]>([]);

  useEffect(() => {
    setFilterValue([]);
  }, [type]);

  const { selfUnqualifiedItem, patrolUnqualifiedItem, searchObj } =
    DataBoardStore;
  const navigater = useNavigate();

  const dataScoure = useMemo(() => {
    const dataScoure =
      type === DataViewTypeEnum.自检看板
        ? selfUnqualifiedItem.data
        : patrolUnqualifiedItem.data;

    if (!filterValue.length || filterValue[0] === 'all')
      return dataScoure.slice(0, 10);

    return dataScoure
      .filter((o: any) => (o.worksheetItemTags || []).includes(filterValue[0]))
      .slice(0, 9);
  }, [selfUnqualifiedItem.data, patrolUnqualifiedItem.data, filterValue]);

  return (
    <>
      <TitleViewCard
        label="不合格检查项 >"
        onJump={() => {
          navigater({
            pathname: `/dataViewBoard/checkItemRank`,
            search: `?${createSearchParams({
              checkType: type,
            })}`,
          });
        }}
        otherRender={
          <div onClick={() => setVisible(true)}>
            {filterValue.length
              ? _.find(propertyOption, (o) => o.value === filterValue[0])?.label
              : '属性'}
            <IconFont type="icon-chevron-down" className="ml-1" />
          </div>
        }
        onReload={() => {
          type === DataViewTypeEnum.自检看板
            ? selfUnqualifiedItem.run(searchObj)
            : patrolUnqualifiedItem.run(searchObj);
        }}
      >
        <div className="mt-3">
          {dataScoure?.map((item: any, index) => {
            return <SheetCard ItemData={item} rankIndex={index} type={type} />;
          })}
        </div>
      </TitleViewCard>
      <Picker
        columns={[propertyOption]}
        visible={visible}
        onClose={() => {
          setVisible(false);
        }}
        value={filterValue}
        onConfirm={(v: any) => {
          setFilterValue(v);
        }}
      />
    </>
  );
});

export default UnqualifiedItem;
