import React, { useEffect, useMemo, useState } from 'react';
import useDebounceState from '@src/hooks/useDebounceState';
import { DataBoardStore } from '@src/store';
import { SearchBar } from 'antd-mobile';
import { observer } from 'mobx-react';
import {
  createSearchParams,
  useNavigate,
  useSearchParams,
} from 'react-router-dom';
import { DataViewTypeEnum } from '..';
import CheckItemDetail from '../checkItemDetail';
import SheetCard from '../components/sheetCard';

const CheckItemRank: React.FC = () => {
  const [routerParams] = useSearchParams();
  const navigate = useNavigate();
  const { workSheetId, checkType } = useMemo(() => {
    const workSheetId = routerParams.get('workSheetId');
    const checkType = routerParams.get('checkType');

    return {
      workSheetId,
      checkType,
    };
  }, [routerParams]);
  const { selfUnqualifiedItem, patrolUnqualifiedItem } = DataBoardStore;

  useEffect(() => {}, []);

  const dataScoure = useMemo(() => {
    return checkType === DataViewTypeEnum.自检看板
      ? selfUnqualifiedItem.data
      : patrolUnqualifiedItem.data;
  }, [selfUnqualifiedItem.data, patrolUnqualifiedItem.data, checkType]);
  const [searchValue, setSearchValue] = useDebounceState<string>('');
  const [curItemData, setCurItemData] = useState({});
  const showList = useMemo(() => {
    return dataScoure?.filter((e: any) =>
      e?.workSheetItemContent?.includes(searchValue),
    );
  }, [searchValue, dataScoure]);

  return (
    <div className="py-2 px-3">
      {workSheetId ? (
        <React.Fragment>
          <CheckItemDetail ItemData={curItemData} type={checkType!} />
        </React.Fragment>
      ) : (
        <React.Fragment>
          <SearchBar
            style={{
              '--border-radius': '8px',
              '--height': '2.5rem',
              '--background': 'white',
            }}
            onClear={() => setSearchValue('')}
            onChange={setSearchValue}
            placeholder="请输入检查项名称"
          />
          <div className=" mt-2 ">
            {showList?.map((item: any, index) => {
              return (
                <div
                  className="bg-white rounded-lg"
                  key={index}
                  onClick={() => {
                    setCurItemData({ ...item, index });
                    navigate({
                      pathname: `/dataViewBoard/checkItemRank`,
                      search: `?${createSearchParams({
                        checkType,
                        worksheetCategoryId: item?.workSheetCategoryId,
                        workSheetId: item?.workSheetId,
                        worksheetItemId: item?.workSheetItemId,
                      } as any)}`,
                    });
                  }}
                >
                  <SheetCard
                    ItemData={item}
                    rankIndex={index}
                    type={checkType!}
                  />
                </div>
              );
            })}
            {/* <Empty text="暂无数据~" /> */}
          </div>
        </React.Fragment>
      )}
    </div>
  );
};

export default observer(CheckItemRank);
