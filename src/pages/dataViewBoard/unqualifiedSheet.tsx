import { useEffect, useMemo } from 'react';
import { DataBoardStore } from '@src/store';
import { observer } from 'mobx-react';
import SheetCard from './components/sheetCard';
import TitleViewCard from './components/titleViewCard';
import { DataViewTypeEnum } from '.';

type IProps = {
  type?: string;
};

const UnqualifiedSheet: React.FC<IProps> = () => {
  useEffect(() => {}, []);

  const { selfUnqualifiedSheet } = DataBoardStore;
  const dataScoure = useMemo(() => {
    return selfUnqualifiedSheet.data;
  }, [selfUnqualifiedSheet.data]);

  return (
    <TitleViewCard label="不合格检查表" onJump={() => {}} onReload={() => {}}>
      <div className="mt-3">
        {dataScoure?.map((item: any, index) => {
          return (
            <SheetCard
              ItemData={item}
              rankIndex={index}
              type={DataViewTypeEnum.自检看板}
            />
          );
        })}
      </div>
    </TitleViewCard>
  );
};

export default observer(UnqualifiedSheet);
