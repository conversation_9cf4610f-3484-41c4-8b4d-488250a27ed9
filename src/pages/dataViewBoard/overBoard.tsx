import { useEffect, useMemo, useRef, useState } from 'react';
import { DataBoardStore } from '@src/store';
import { formatPer } from '@src/utils/helper';
import classNames from 'classnames';
import ReactECharts, { EChartsOption } from 'echarts-for-react';
import _ from 'lodash';
import { observer } from 'mobx-react';
import TitleViewCard from './components/titleViewCard';
import PopupTip from '../boards/components/IndicatorCard/PopupTip';
import { DataViewTypeEnum } from '.';

type IProps = {
  type: string;
};

const selfDataCardView = [
  { name: '报告完成率', key: 'completedReportRatio', isRate: true },
  { name: '报告点评率', key: 'commentedReportRatio', isRate: true },
  { name: '自检通过率', key: 'commentedPassReportRatio', isRate: true },
];
const patrolCardView = [
  { name: '巡检通过率', key: 'passReportRatio', isRate: true },
  { name: 'S项不合格门店率', key: 'redLineShopRatio', isRate: true },
  { name: '门店整改率', key: 'shopRectifyRatio', isRate: true },
];

const OverBoard: React.FC<IProps> = observer(({ type }) => {
  const echartsRef = useRef<ReactECharts>(null);

  const [curView, setCurView] = useState<string>('');
  const { selfDataChartView, patrolChartView, searchObj, diagnosisChartView } =
    DataBoardStore;

  useEffect(() => {
    setCurView(
      type === DataViewTypeEnum.自检看板 ? 'commentedAvgScore' : 'averageScore',
    );
  }, [type]);

  const dataScoure =
    type === DataViewTypeEnum.自检看板
      ? selfDataChartView.viewData
      : patrolChartView.viewData;

  const summaryCardData: any = useMemo(() => {
    return type === DataViewTypeEnum.自检看板
      ? selfDataChartView?.summaryData
      : type === DataViewTypeEnum.诊断巡检看板
        ? diagnosisChartView?.summaryData
        : patrolChartView?.summaryData;
  }, [
    type,
    selfDataChartView?.summaryData,
    diagnosisChartView?.summaryData,
    patrolChartView?.summaryData,
  ]);

  const showCardView = useMemo(() => {
    return type === DataViewTypeEnum.自检看板
      ? selfDataCardView
      : patrolCardView;
  }, [type]);
  const options: EChartsOption = useMemo(() => {
    const timeList =
      dataScoure?.map((v: any) => {
        return v?.statisticDate;
      }) || [];
    const numList =
      dataScoure?.map((v: any) => {
        return v?.[curView] || 0;
      }) || [];
    const curItem = showCardView?.find((v) => {
      return v.key === curView;
    });
    const defautOption = {
      xAxis: {
        type: 'category',
        data: timeList,
      },
      grid: {
        // left: '50%',
        // right: '4%',
        top: '10%',
        bottom: '14%',
      },
      tooltip: {
        trigger: 'axis',
        formatter: (param: any) => {
          if (!param?.[0]) return '-';

          const lineData = param?.[0];
          const itemValue = curItem?.isRate
            ? `${(lineData.value * 100).toFixed(1)}%`
            : lineData.value;

          return `${lineData.name}\n ${lineData.marker} ${itemValue}`;
        },
      },
      yAxis: {
        type: 'value',
      },
      series: [
        {
          data: numList,
          type: 'line',
        },
      ],
    };

    return defautOption;
  }, [dataScoure, showCardView]);

  useEffect(() => {
    echartsRef.current
      ?.getEchartsInstance()
      .dispatchAction({ type: 'hideTip' });
    echartsRef.current?.getEchartsInstance().resize();
  }, [dataScoure]);

  return (
    <TitleViewCard
      label="数据概览"
      onReload={() => {
        type === DataViewTypeEnum.自检看板
          ? selfDataChartView.run(searchObj)
          : patrolChartView.run(searchObj);
      }}
      command={
        type === DataViewTypeEnum.诊断巡检看板
          ? '按门店整改率进行排行'
          : undefined
      }
    >
      <div className="flex mt-2">
        <div
          className={classNames(
            'w-1 rounded-tl-base bg-primary rounded-bl-base',
          )}
        />
        <div
          className={`flex-1 justify-between  px-4 py-2 border border-solid border-[#F0F0F0] rounded-lg `}
          style={{
            background:
              'linear-gradient(90deg, rgba(55, 139, 255, 0.11) 0%, rgba(0, 187, 180, 0) 86%)',
          }}
        >
          <div className="text-dark">
            {type !== DataViewTypeEnum.诊断巡检看板 ? (
              <>
                <div className="flex items-center text-sm leading-[22px]">
                  <div>报告平均分</div>
                  <PopupTip title={'报告平均分'} list={[]} />
                </div>
                <div className="text-[20px] leading-[28px] mt-1 font-semibold">
                  {summaryCardData?.commentedAvgScore ||
                    summaryCardData?.averageScore}
                </div>
              </>
            ) : (
              <>
                <div className="flex items-center text-sm leading-[22px]">
                  <div>门店整改率</div>
                  <PopupTip title={'门店整改率'} list={[]} />
                </div>
                <div className="text-[20px] leading-[28px] mt-1 font-semibold">
                  {formatPer(summaryCardData?.shopRectifyRatio)}
                </div>
              </>
            )}
          </div>
        </div>
      </div>
      {type !== DataViewTypeEnum.诊断巡检看板 && (
        <>
          <div
            className={`flex justify-between whitespace-nowrap border border-solid border-[#F0F0F0] rounded-lg px-3 py-5`}
          >
            {showCardView?.map((v) => {
              return (
                <div
                  key={v.key}
                  className={classNames('text-center', {
                    active: v.key === curView,
                  })}
                  onClick={() => {
                    setCurView(v.key);
                  }}
                >
                  <div className="text-base text-[#000000] mb-2">
                    {!_.isNil(summaryCardData?.[v?.key]) ? (
                      <div>
                        {v?.isRate
                          ? (summaryCardData?.[v?.key] * 100).toFixed(1)
                          : summaryCardData?.[v?.key]}
                        {v.isRate && summaryCardData?.[v.key] !== null && '%'}
                      </div>
                    ) : (
                      <div>-</div>
                    )}
                  </div>
                  <div className="text-xs text-[#858585]">{v?.name}</div>
                </div>
              );
            })}
          </div>
          <div className="mt-3">
            <ReactECharts
              ref={echartsRef}
              key="overBoard"
              option={options}
              style={{ height: '100%', minHeight: '300px' }}
            />
          </div>
        </>
      )}
    </TitleViewCard>
  );
});

export default OverBoard;
