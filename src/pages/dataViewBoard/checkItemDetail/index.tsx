import { useMemo, useState } from 'react';
import { shopType } from '@src/pages/tasks/enum';
import { DataBoardStore } from '@src/store';
import { formatDateToUTC } from '@src/utils/utils';
import { useRequest } from 'ahooks';
import { Table } from 'antd';
import dayjs from 'dayjs';
import { round } from 'lodash';
import { observer } from 'mobx-react';
import { nanoid } from 'nanoid';
import { DataViewTypeEnum } from '..';
import {
  queryPatrolSelfTaskNoPassShop,
  queryPatrolTaskNonconformity,
  querySelfTaskNonconformity,
  querySelfTaskNoPassShop,
} from '../api';
import SheetCard from '../components/sheetCard';
import SortControl from '../components/sortControl';

const shopColumns = [
  {
    title: '门店',
    dataIndex: 'shopName',
    key: 'shopName',
    width: 150,
    fixed: true,
  },
  {
    title: '门店类型',
    dataIndex: 'shopType',
    key: 'shopType',
    width: 100,
    render: (text: any) => shopType[text as keyof typeof shopType] || '-',
  },
  {
    title: `检查次数`,
    dataIndex: 'patrolCount',
    key: 'patrolCount',
    sorter: (a: any, b: any) => a.patrolCount - b.patrolCount,
    width: 110,
  },
  {
    title: '问题发生次数',
    dataIndex: 'issuesReportCount',
    key: 'issuesReportCount',
    sorter: (a: any, b: any) => a.issuesReportCount - b.issuesReportCount,
    width: 150,
  },
  {
    title: '不合格率',
    dataIndex: 'noPassRatio',
    key: 'noPassRatio',
    sorter: (a: any, b: any) => a.noPassRatio - b.noPassRatio,
    width: 100,
  },
  {
    title: '已整改次数',
    dataIndex: 'rectifyCompleteCount',
    width: 120,
    key: 'rectifyCompleteCount',
    sorter: (a: any, b: any) => a.rectifyCompleteCount - b.rectifyCompleteCount,
  },
  {
    title: '整改率',
    dataIndex: 'rectifyRatio',
    width: 120,
    key: 'rectifyRatio',
    sorter: (a: any, b: any) => a.rectifyRatio - b.rectifyRatio,
    render: (text: any) => (typeof text === 'number' ? `${round(text ? text * 100 : 0)}%` : text),
  },
];

const reasonColumns = [
  {
    title: '不合格原因',
    dataIndex: 'reason',
    width: 200,
    key: 'reason',
    fixed: true,
  },
  {
    title: '次数',
    dataIndex: 'count',
    width: 110,
    key: 'count',
    sorter: (a: any, b: any) => a.count - b.count,
  },
  {
    title: '占比',
    dataIndex: 'reasonRatio',
    width: 120,
    key: 'reasonRatio',
    sorter: (a: any, b: any) => a.reasonRatio - b.reasonRatio,
  },
];

type IProps = {
  type: string;
  ItemData: any;
};

enum AscCurrent {
  '原因',
  '门店',
}

const CheckItemDetail: React.FC<IProps> = ({ type, ItemData }) => {
  const [typeCurrent, setTypeCurrent] = useState(AscCurrent.原因);
  const { searchObj } = DataBoardStore;
  const newParams = {
    ...searchObj,
    workSheetCategoryId: ItemData.workSheetCategoryId,
    workSheetId: ItemData.workSheetId,
    workSheetItemId: ItemData.workSheetItemId,
    startDate: formatDateToUTC(dayjs(searchObj?.beginDate).startOf('day')),
    endDate: formatDateToUTC(dayjs(searchObj?.endDate).endOf('day')),
    shopIds: searchObj?.shopIds,
    groupId: searchObj?.groupId,
    planId: searchObj?.planId,
    shopType: searchObj?.shopType,
    subType: searchObj?.subType,
    patrolUserIds: searchObj?.patrolUserIds ? [searchObj?.patrolUserIds] : undefined,
    roleIdList: !!searchObj?.roleIdList?.length ? searchObj?.roleIdList : undefined,
  };
  const { data: reasonDetail, loading: reasonLoading } = useRequest(
    async () => {
      return type === DataViewTypeEnum.自检看板
        ? await querySelfTaskNonconformity({
            ...newParams,
          })
        : await queryPatrolTaskNonconformity({ ...newParams });
    },
    { refreshDeps: [ItemData] },
  );
  const { data: shopDetail, loading: shopLoading } = useRequest(
    async () => {
      return type === DataViewTypeEnum.自检看板
        ? await querySelfTaskNoPassShop(newParams)
        : await queryPatrolSelfTaskNoPassShop(newParams);
    },
    { refreshDeps: [ItemData] },
  );
  const showData = useMemo(() => {
    let reasonData = [];

    reasonData = reasonDetail?.nonconformityReasons;

    if (reasonDetail?.otherReasons?.length > 0) {
      reasonData = [
        {
          reason: '其他',
          isOther: true,
          otherReasonList: reasonDetail?.otherReasons,
          count: reasonDetail?.otherReasons?.length,
          reasonRatio: reasonDetail?.otherReasonRatio,
        },
        ...reasonData,
      ];
    }

    return typeCurrent === AscCurrent.原因 ? reasonData : shopDetail;
  }, [typeCurrent, reasonDetail, shopDetail]);

  console.log(showData, '=showData');

  return (
    <div>
      <div className="bg-white  rounded-lg">
        <SheetCard type={type} ItemData={ItemData} rankIndex={ItemData?.index} />
      </div>
      <div className="flex justify-between items-center ">
        <div className="text-xs">默认按占比/不合格排名</div>
        <div>
          <SortControl values={['原因', '门店']} onClick={(e) => setTypeCurrent(e)} current={typeCurrent} />
        </div>
      </div>
      <div className="mt-3">
        <Table
          rowKey={nanoid()}
          loading={shopLoading || reasonLoading}
          showSorterTooltip={false}
          bordered
          columns={typeCurrent === AscCurrent.原因 ? reasonColumns : shopColumns}
          dataSource={showData}
          pagination={false}
          scroll={{ x: 450 }}
        />
      </div>
    </div>
  );
};

export default observer(CheckItemDetail);
