import React from 'react';

type IProps = {
  values: string[];
  onClick: (e: any) => void;
  current: number;
};

const SortControl = ({ values, onClick, current }: IProps) => {
  return (
    <div className="flex text-xs leading-7 text-center text-grey">
      {values?.map((e, index) => (
        <div
          key={index}
          className={`w-14 border border-solid border-[#F0F0F0] ${
            index === 0 ? 'rounded-l-base' : 'rounded-r-base'
          } ${index === current ? 'bg-primary-1 text-primary border-primary' : 'border-line '}`}
          onClick={() => {
            onClick(index);
          }}
        >
          {e}
        </div>
      ))}
    </div>
  );
};

export default SortControl;
