import { useMemo } from 'react';
import { ProgressBar } from 'antd-mobile';
import { round } from 'lodash';
import { observer } from 'mobx-react';
import { DataViewTypeEnum } from '..';

type IProps = {
  type: string;
  ItemData: any;
  rankIndex: number;
};

const SlefcountRender = [
  {
    lable: '自检次数',
    key: 'selfCheckCount',
  },
  {
    lable: '点评次数',
    key: 'commentCount',
  },
  {
    lable: '不合格次数',
    key: 'noPassCount',
  },
];
const PatrolcountRender = [
  {
    lable: '巡检次数',
    key: 'patrolCount',
  },
  {
    lable: '问题项',
    key: 'issuesReportCount',
  },
];

/* 
const propertyOption = [
  { value: 'all', label: '全部' },
  { value: 'RED_LINE', label: 'S项' },
  { value: 'NECESSARY', label: '必检项' },
  { value: 'YELLOW', label: 'M项' },
  { value: 'KEY', label: '关键项' },
  { value: 'PENALTY', label: '罚款项' },
];
 */
enum CheckItemEnum {
  all = '全部',
  RED_LINE = 'S项',
  NECESSARY = '必检项',
  YELLOW = 'M项',
  KEY = '关键项',
  PENALTY = '罚款项',
  POSITIVE = '阳性指标项',
}

const SheetCard: React.FC<IProps> = ({ ItemData, rankIndex, type }) => {
  const showRender = useMemo(() => {
    return type === DataViewTypeEnum.自检看板
      ? SlefcountRender
      : PatrolcountRender;
  }, [type]);

  return (
    <div className="py-4 px-3 border border-solid border-[#F0F0F0] rounded-lg mb-2">
      <div className=" text-sm font-medium">TOP{rankIndex + 1}</div>
      <div className="text-sm">{ItemData?.workSheetItemContent}</div>
      <div className="flex justify-between items-center text-xs text-[#858585] mb-2 ">
        <div className="max-w-[200px]  text-ellipsis overflow-hidden whitespace-nowrap">
          不合格率
        </div>
        <div className="">{round(ItemData.noPassRatio * 100, 2)}%</div>
      </div>
      <ProgressBar percent={ItemData.noPassRatio * 100 || 0} />

      <div className="flex justify-between items-center mt-6">
        {showRender?.map((v, index) => {
          return (
            <div className="text-center" key={index}>
              <div className="mb-2 text-[15px] text-[#000000]">
                {ItemData[v.key]}
              </div>
              <div className="text-xs text-[#858585]">{v.lable}</div>
            </div>
          );
        })}
      </div>
      <div className="mt-3 gap-x-2 flex ">
        {(ItemData.worksheetItemTags || []).map((o: any) => {
          return (
            <div className="inline-block border border-solid border-[#F0F0F0] bg-[#FAFAFA] text-[#858585] text-xs p-1">
              {CheckItemEnum[o as keyof typeof CheckItemEnum]}
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default observer(SheetCard);
