import React, { type PropsWithChildren } from 'react';
import { IconFont } from '@src/components';

type IProps = PropsWithChildren<{
  label: string;
  labelColor?: string;
  backgroundColor?: string;
  onJump?: () => void;
  onReload?: () => void;
  otherRender?: React.ReactNode;
  command?: string;
}>;

const OverViewCard = ({
  label,
  onJump,
  onReload,
  otherRender,
  children,
  command = '按检查项的不合格率进行排行',
}: IProps) => {
  return (
    <div className="bg-white p-3 mb-2">
      <div>
        <div className="flex justify-between">
          <div onClick={() => onJump && onJump()}>
            <span className="text-base text-[#141414] font-medium">
              {label}
            </span>
            {onJump && <div className="sh-icon sh-icon-right ml-2" />}
          </div>
          <div className="flex">
            {otherRender && <div className="mr-4">{otherRender}</div>}
            {onReload && (
              <div className="mr-4" onClick={onReload}>
                <IconFont
                  type="icon-xingzhuangjiehe"
                  className="text-xl text-primary"
                />
              </div>
            )}
          </div>
        </div>
        <div className="text-xs text-gray-500 pt-2">{command}</div>
      </div>
      <div>{children}</div>
    </div>
  );
};

export default OverViewCard;
