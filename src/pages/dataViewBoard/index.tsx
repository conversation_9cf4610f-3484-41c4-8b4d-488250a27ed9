import { useEffect, useMemo, useState } from 'react';
import { PageContext } from '@src/common/page-context';
import { report } from '@src/common/report';
import { BackTop, CustomerFilter, DateFilter, IconFont, Loading } from '@src/components';
import { DataBoardFilter, init } from '@src/components/DataBoardFilter';
import WithKeepAlive from '@src/components/WithKeepAlive';
import { useGroupTree } from '@src/hooks';
import { DataBoardStore, userStore } from '@src/store';
import { getDataViewBoardEnum } from '@src/utils/utils';
import { Card, ErrorBlock, SafeArea, Tabs } from 'antd-mobile';
import cn from 'classnames';
import dayjs from 'dayjs';
import { has, keys } from 'lodash';
import { observer } from 'mobx-react';
import { parse, stringify } from 'qs';
import { useLocation, useNavigate, useSearchParams } from 'react-router-dom';
import styles from './index.module.scss';
import SubGroupCondition from './subGroupCondition';

const defaultParams = {
  beginDate: dayjs().format('YYYY-MM-DD'),
  endDate: dayjs().format('YYYY-MM-DD'),
};

export enum DataViewTypeEnum {
  自检看板 = 'selfBoard',
  到店巡检看板 = 'patrolBoard',
  视频云巡检看板 = 'VideoBoard',
  线下稽核看板 = 'offlineAuditBoard',
  视频稽核看板 = 'videoAuditBoard',
  诊断巡检看板 = 'diagnosisBoard',
  '自检看板 (新)' = 'selfBoardTactics',
  巡检不合格情况 = 'patrolUnqualifiedSituation',
}

/* 
NORMAL(0, "到店巡检(权限内)"),
VIDEO(1, "视屏云巡检"),
CROSS(2, "到店巡检(权限外)"),
FOOD_SAFETY_NORMAL(3,"食安线下稽核"),
FOOD_SAFETY_VIDEO(4,"食安视频云巡检");
FOOD_SAFETY_ARRIVE_SHOP(6, "食安稽核到店辅导")
 */
const PatrolType = {
  [DataViewTypeEnum.到店巡检看板]: 'NORMAL',
  [DataViewTypeEnum.视频云巡检看板]: 'VIDEO',
  [DataViewTypeEnum.线下稽核看板]: 'FOOD_SAFETY_NORMAL',
  [DataViewTypeEnum.视频稽核看板]: 'FOOD_SAFETY_VIDEO',
  [DataViewTypeEnum.诊断巡检看板]: 'DIAGNOSTIC',
};

const TabList = [
  {
    key: DataViewTypeEnum.自检看板,
    title: '自检看板',
    permission: getDataViewBoardEnum('自检实时情况_自检看板'),
    onClick: () => {
      report({
        type: 'ability',
        page: '数据看板',
        abilityButton: '营运通数据-自检看板',
      });
    },
  },
  {
    key: DataViewTypeEnum['自检看板 (新)'],
    title: '自检看板 (新)',
    permission: getDataViewBoardEnum('自检实时情况_自检看板'),
    onClick: () => {
      report({
        type: 'ability',
        page: '数据看板',
        abilityButton: '营运通数据-自检看板 (新)',
      });
    },
  },
  {
    key: DataViewTypeEnum.到店巡检看板,
    title: '到店巡检看板',
    permission: getDataViewBoardEnum('自检实时情况_到店巡检看板'),
    onClick: () => {
      report({
        type: 'ability',
        page: '数据看板',
        abilityButton: '营运通数据-到店巡检看板',
      });
    },
  },
  {
    key: DataViewTypeEnum.视频云巡检看板,
    title: '视频云巡检看板',
    permission: getDataViewBoardEnum('自检实时情况_视频云巡检看板'),
    onClick: () => {
      report({
        type: 'ability',
        page: '数据看板',
        abilityButton: '营运通数据-视频云巡检看板',
      });
    },
  },
  {
    key: DataViewTypeEnum.线下稽核看板,
    title: '食安线下稽核看板',
    permission: getDataViewBoardEnum('自检实时情况_食安线下稽核看板'),
    onClick: () => {
      report({
        type: 'ability',
        page: '数据看板',
        abilityButton: '营运通数据-线下稽核看板',
      });
    },
  },
  {
    key: DataViewTypeEnum.视频稽核看板,
    title: '食安线上稽核看板',
    permission: getDataViewBoardEnum('自检实时情况_食安线上稽核看板'),
    onClick: () => {
      report({
        type: 'ability',
        page: '数据看板',
        abilityButton: '营运通数据-视频稽核看板',
      });
    },
  },
  {
    key: DataViewTypeEnum.诊断巡检看板,
    title: '诊断巡检看板',
    permission: getDataViewBoardEnum('自检实时情况_诊断巡检看板'),
    onClick: () => {
      report({
        type: 'ability',
        page: '数据看板',
        abilityButton: '营运通数据-诊断巡检看板',
      });
    },
  },
];

type TProps = {
  /** 是否为策略 */
  isTactics?: boolean;
};

const DataViewBoard = WithKeepAlive<TProps>(
  observer(({ isTactics }) => {
    const [search] = useSearchParams();
    const location = useLocation();
    const { permissionsMap } = userStore;
    const { pid, getGroupTree, tagIdObj } = useGroupTree();
    const [filterVisible, setFilterVisible] = useState(false);

    const tabList = useMemo(() => {
      const tabs = TabList.filter(
        (f) => f.key !== (isTactics ? DataViewTypeEnum.自检看板 : DataViewTypeEnum['自检看板 (新)']),
      );
      return tabs;
    }, [isTactics]);

    const {
      searchObj,
      setSearchObj,
      selfDataChartView,
      patrolChartView,
      SelfOrgsSituation,
      patrolOrgsSituation,
      // selfUnqualifiedItem,
      patrolUnqualifiedItem,
      diagnosisChartView,
      diagnosticOrgsSituation,
    } = DataBoardStore;
    const [searchParmas, setSearchParams] = useState<any>({
      ...defaultParams,
      ...searchObj,
    });

    const activeTab = (search.get('activeTab') as DataViewTypeEnum) || tabList[0].key;
    const [activeKey, setActiveKey] = useState<DataViewTypeEnum>(activeTab);
    const navigate = useNavigate();
    const [loading, setLoading] = useState(false);

    /**
     * ios 二级路由返回时，title有问题，这里强制改一下
     */
    useEffect(() => {
      if (location.pathname === '/dataViewBoard') {
        document.title = '数据看板';
      }
    }, [location]);
    useEffect(() => {
      setSearchObj(searchParmas);
      setLoading(true);

      if ([DataViewTypeEnum.自检看板, DataViewTypeEnum['自检看板 (新)']].includes(activeKey)) {
        Promise.all([
          selfDataChartView.run({
            ...searchParmas,
            isTactics: isTactics && activeKey === DataViewTypeEnum['自检看板 (新)'],
          }),
          SelfOrgsSituation.run({
            ...searchParmas,
            isTactics: isTactics && activeKey === DataViewTypeEnum['自检看板 (新)'],
          }),
          // selfUnqualifiedItem.run(searchParmas),
        ]).finally(() => {
          setLoading(false);
        });
      } else if (activeKey === DataViewTypeEnum.诊断巡检看板) {
        const parmas = {
          ...searchParmas,
          subType: PatrolType[activeKey as keyof typeof PatrolType],
        };
        setSearchObj(parmas);
        Promise.all([
          diagnosisChartView.run(searchParmas),
          diagnosticOrgsSituation.run(searchParmas),
          patrolUnqualifiedItem.run(parmas),
        ]).finally(() => {
          setLoading(false);
        });
      } else {
        const parmas = {
          ...searchParmas,
          subType: PatrolType[activeKey as keyof typeof PatrolType],
        };

        setSearchObj(parmas);
        Promise.all([
          patrolChartView.run(parmas),
          patrolOrgsSituation.run(parmas),
          patrolUnqualifiedItem.run(parmas),
        ]).finally(() => {
          setLoading(false);
        });
      }
    }, [activeKey, searchParmas]);

    useEffect(() => {
      return () => {
        setSearchObj({});
      };
    }, []);

    // if (!permissionsMap.has(AuthorityEnum.营运通数据看板)) {
    if (!permissionsMap.has(getDataViewBoardEnum('自检实时情况') as string)) {
      return <ErrorBlock status="empty" title="您没有该页面权限" description={<span>请与客服人员联系！</span>} />;
    }

    return (
      <Loading spinning={loading}>
        <PageContext.Provider value={{ pid, getGroupTree, tagIdObj }}>
          <div className=" h-full overflow-y-auto" id="boardsPage">
            <div className="bg-white pl-1">
              <Tabs
                className={styles.iAdmTabs}
                style={{ '--title-font-size': '1rem' }}
                activeKey={activeKey}
                onChange={(key) => {
                  const { activeTab: _activeTab, ...rest } = parse(location.search.slice(1));
                  const reportIndex = tabList.findIndex((i) => i.key === key);

                  setActiveKey(() => key as DataViewTypeEnum);
                  setSearchParams(defaultParams);
                  tabList?.[reportIndex]?.onClick();
                  navigate(`${location.pathname}?${stringify({ ...rest, activeTab: key })}`, {
                    replace: true,
                  });
                }}
                stretch={false}
              >
                {tabList
                  .filter((f) =>
                    !!f?.permission && typeof f?.permission === 'string'
                      ? userStore.permissionsMap.has(f.permission)
                      : !!f?.permission,
                  )
                  .map(({ key, title }) => (
                    <Tabs.Tab title={title} key={key} />
                  ))}
              </Tabs>
            </div>
            <Card className="rounded-none border-b border-solid border-line border-px mb-2 " bodyClassName="py-2">
              <div className="flex justify-between items-start">
                <CustomerFilter
                  groupMaxWidth="3.625rem"
                  value={{
                    groupId: searchParmas.groupId,
                    shopCodes: searchParmas.shopIds || [],
                  }}
                  onChange={(e: any) => {
                    setSearchParams((pre: any) => ({
                      ...pre,
                      groupId: e?.shopCodes?.length > 0 ? undefined : e.groupId,
                      shopIds: e.shopCodes,
                    }));
                  }}
                />
                <DateFilter
                  onlyDate
                  value={[dayjs(searchParmas.beginDate).toDate(), dayjs(searchParmas.endDate).toDate()]}
                  customizeDateRange={7}
                  onChange={(e) => {
                    setSearchParams((p: any) => ({
                      ...p,
                      beginDate: e?.[0],
                      endDate: e?.[1],
                    }));
                  }}
                  key="range"
                />
                <button
                  className={cn(
                    `text-sm flex items-center py-[3px] leading-[14px] ${
                      filterVisible ? 'text-primary' : 'text-[#5E5E5E]'
                    } focus:outline-none`,
                    {
                      'text-primary': keys(init).some((o) => !!searchParmas && has(searchParmas, o)),
                    },
                  )}
                  onClick={() => {
                    setFilterVisible(true);
                  }}
                >
                  <div className="w-[1px] h-5 bg-black/[0.03] mr-[18px]" />
                  筛选
                  <IconFont type="icon-a-1111-copy" className="ml-1 text-xs text-[#B8B8B8]" />
                </button>
              </div>
            </Card>
            {/* <OverBoard type={activeKey} /> */}
            <SubGroupCondition type={activeKey} />
            {/* <UnqualifiedItem type={activeKey} /> */}

            <BackTop domId="boardsPage" />
          </div>
          <SafeArea position="bottom" />
          <DataBoardFilter
            value={searchParmas}
            open={filterVisible}
            type={activeKey}
            onClose={() => setFilterVisible(false)}
            onChange={(value: Record<string, any>) => {
              setSearchParams((pre: any) => ({ ...pre, ...value }));
              setFilterVisible(false);
            }}
          />
        </PageContext.Provider>
      </Loading>
    );
  }),
);

export default DataViewBoard;
