import { useMemo, useState } from 'react';
import { IconFont } from '@src/components';
import { formatScore } from '@src/pages/mission/components/core';
import { DataBoardStore } from '@src/store';
import { useRequest } from 'ahooks';
import { Table } from 'antd';
import { Checkbox, List, Popup } from 'antd-mobile';
import _ from 'lodash';
import { observer } from 'mobx-react';
import { nanoid } from 'nanoid';
import './index.module.scss';
import StudyConditionPopup from './studyConditionPopup';
import { DataViewTypeEnum } from '..';
import {
  queryDiagnosticDataIfnfo,
  queryDiagnosticSummary,
  queryPatrolDataIfnfo,
  queryPatrolReportCompleted,
  querySelfReportDetailList,
  querySelfReportListTotal,
} from '../api';
import { queryTacticsSelfReportDetailList, queryTacticsSelfReportListTotal } from '../tactics/api';

type IProps = {
  type: string;
};

const Detail: React.FC<IProps> = ({ type }) => {
  const { searchObj } = DataBoardStore;
  const [visible, setVisible] = useState(false);
  const [studyVisible, setStudyVisible] = useState(false);
  const [curData, setCurData] = useState({ type: '', id: '', name: '' });

  const selfColumns = [
    {
      dataIndex: 'name',
      title: '组织',
      width: 132,
      fixed: true,
      render: (value: any, record: any) => {
        return record?.groupId ? (
          <div
            className=" text-primary"
            onClick={() => {
              run(record?.groupId);
            }}
          >
            {value}
          </div>
        ) : (
          <div>{value}</div>
        );
      },
    },
    {
      dataIndex: 'planReportCount',
      title: '计划报告数',
      width: 150,
      sorter: (a: any, b: any) => a.planReportCount - b.planReportCount,
      tooltip: '所选时间段内原计划应该提交的报告数',
    },
    {
      dataIndex: 'selfReportShopCount',
      title: '自检门店数',
      width: 150,
      sorter: (a: any, b: any) => a.selfReportShopCount - b.selfReportShopCount,

      tooltip: '所选时间段内提交报告的门店数',
    },
    {
      dataIndex: 'submittedReportCount',
      title: '提交报告数',
      width: 150,
      sorter: (a: any, b: any) => a.submittedReportCount - b.submittedReportCount,
      tooltip: '所选时间段内实际提交的报告数',
    },
    {
      dataIndex: 'completedReportRatio',
      title: '报告完成率',
      formatter: '0,0',
      showRadio: true,
      width: 150,
      sorter: (a: any, b: any) => a.completedReportRatio - b.completedReportRatio,
      tooltip: '提交报告数/计划报告数',
      render: (value: number) => {
        return <div>{!_.isNil(value) ? `${(value * 100).toFixed(1)}%` : '-'}</div>;
      },
    },
    {
      dataIndex: 'commentedReportCount',
      title: '点评报告数',
      width: 150,

      sorter: (a: any, b: any) => a.commentedReportCount - b.commentedReportCount,
      tooltip: '所选时间段内实际提交的报告中被巡检人点评的报告数',
    },
    {
      dataIndex: 'commentedReportRatio',
      title: '报告点评率',
      width: 150,
      formatter: '0,0',
      showRadio: true,
      isRate: true,
      sorter: (a: any, b: any) => a.commentedReportRatio - b.commentedReportRatio,
      tooltip: '点评报告数/提交报告数',
      render: (value: number) => {
        return <div>{!_.isNil(value) ? `${(value * 100).toFixed(1)}%` : '-'}</div>;
      },
    },
    {
      dataIndex: 'passReportCount',
      title: '报告通过数',
      width: 150,

      isRate: true,
      sorter: (a: any, b: any) => a.passReportCount - b.passReportCount,
      tooltip: '点评的报告中达到合格分数的报告数',
    },
    {
      dataIndex: 'commentedPassReportRatio',
      title: '自检通过率',
      width: 150,
      formatter: '0,0',
      showRadio: true,
      isRate: true,
      sorter: (a: any, b: any) => a.commentedPassReportRatio - b.commentedPassReportRatio,
      tooltip: '合格的报告数/已点评的报告数',
      render: (value: number) => {
        return <div>{!_.isNil(value) ? `${(value * 100).toFixed(1)}%` : '-'}</div>;
      },
    },
    {
      dataIndex: 'commentedAvgScore',
      title: '报告平均分',
      width: 150,

      sorter: (a: any, b: any) => a.commentedAvgScore - b.commentedAvgScore,
      tooltip: '所有被巡检人点评的报告得分的均值',
    },
    {
      dataIndex: 'uncompletedTaskCount',
      title: '未完成任务数',
      width: 150,

      sorter: (a: any, b: any) => a.uncompletedTaskCount - b.uncompletedTaskCount,
      tooltip: '所选时间段内，剩余应该提交的报告数',
    },
    {
      dataIndex: 'redLineShopRatio',
      title: 'S项不合格门店率',
      width: 200,
      formatter: '0,0',
      showRadio: true,
      isRate: true,
      sorter: (a: any, b: any) => a.redLineShopRatio - b.redLineShopRatio,
      tooltip: '存在S项不合格的门店数/总报告数据',
      render: (value: number) => {
        return <div>{!_.isNil(value) ? `${(value * 100).toFixed(1)}%` : '-'}</div>;
      },
    },
    {
      dataIndex: 'reportRectifyRatio',
      title: '报告整改率',
      width: 150,
      formatter: '0,0',
      showRadio: true,
      isRate: true,
      sorter: (a: any, b: any) => a.reportRectifyRatio - b.reportRectifyRatio,
      tooltip: '全部整改完毕的报告数/所有提交的报告总数',
      render: (value: number) => {
        return <div>{!_.isNil(value) ? `${(value * 100).toFixed(1)}%` : '-'}</div>;
      },
    },
    {
      dataIndex: 'shopRectifyRatio',
      title: '门店整改率',
      width: 150,
      formatter: '0,0',
      showRadio: true,
      isRate: true,
      sorter: (a: any, b: any) => a.shopRectifyRatio - b.shopRectifyRatio,
      tooltip: '不合格项全部整改完毕的门店数/报告中含有不合格项的门店数',
      render: (value: number) => {
        return <div>{!_.isNil(value) ? `${(value * 100).toFixed(1)}%` : '-'}</div>;
      },
    },
    {
      dataIndex: 'checkPassRatio',
      title: '检查项合格率',
      width: 150,
      formatter: '0,0',
      showRadio: true,
      isRate: true,
      sorter: (a: any, b: any) => a.checkPassRatio - b.checkPassRatio,
      tooltip: '所有报告中合格的检查项数/已打分的检查项数',
      render: (value: number) => {
        return <div>{!_.isNil(value) ? `${(value * 100).toFixed(1)}%` : '-'}</div>;
      },
    },
    {
      dataIndex: 'noReformCount',
      title: '剩余待整改数',
      width: 150,

      sorter: (a: any, b: any) => a.noReformCount - b.noReformCount,
      tooltip: '至今‘待整改、已过期’的检查项数',
    },
  ];

  const patrolColumns = [
    {
      dataIndex: 'name',
      title: '组织',
      width: 132,
      fixed: true,

      render: (value: any, record: any) => {
        return record?.groupId ? (
          <a
            className="text-primary"
            onClick={() => {
              run(record?.groupId);
            }}
          >
            {value}
          </a>
        ) : (
          <div>{value}</div>
        );
      },
    },
    {
      dataIndex: 'patrolCount',
      title: '巡检次数',
      width: 100,

      sorter: (a: any, b: any) => a.patrolCount - b.patrolCount,
      tooltip: '该门店共被巡检的总次数',
    },
    {
      dataIndex: 'patrolShopCount',
      title: '巡检门店数',
      width: 150,

      sorter: (a: any, b: any) => a.patrolShopCount - b.patrolShopCount,
      tooltip: '该组织被巡检的门店数',
    },
    {
      dataIndex: 'passReportRatio',
      title: '巡检通过率',
      width: 150,
      formatter: '0,0',
      showRadio: true,
      sorter: (a: any, b: any) => a.passReportRatio - b.passReportRatio,
      tooltip: '合格报告数/总报告数',
      render: (value: number) => {
        return <div>{!_.isNil(value) ? `${(value * 100).toFixed(1)}%` : '-'}</div>;
      },
    },
    {
      dataIndex: 'averageScore',
      title: '报告平均分',
      width: 150,

      sorter: (a: any, b: any) => a.averageScore - b.averageScore,
      tooltip: '报告的报告平均分',
    },
    {
      dataIndex: 'redLineShopRatio',
      title: 'S项不合格门店率',
      width: 170,
      formatter: '0,0',
      showRadio: true,
      isRate: true,
      sorter: (a: any, b: any) => a.redLineShopRatio - b.redLineShopRatio,
      tooltip: '存在S项不合格的门店数/总报告数据',
      render: (value: number) => {
        return <div>{!_.isNil(value) ? `${(value * 100).toFixed(1)}%` : '-'}</div>;
      },
    },
    {
      dataIndex: 'reportRectifyRatio',
      title: '报告整改率',
      width: 150,
      formatter: '0,0',
      showRadio: true,
      isRate: true,
      sorter: (a: any, b: any) => a.reportRectifyRatio - b.reportRectifyRatio,
      tooltip: '全部整改完毕的报告数/所有提交的报告总数',
      render: (value: number) => {
        return <div>{!_.isNil(value) ? `${(value * 100).toFixed(1)}%` : '-'}</div>;
      },
    },
    {
      dataIndex: 'shopRectifyRatio',
      title: '门店整改率',
      width: 150,
      formatter: '0,0',
      showRadio: true,
      isRate: true,
      sorter: (a: any, b: any) => a.shopRectifyRatio - b.shopRectifyRatio,
      tooltip: '不合格项全部整改完毕的门店数/报告中含有不合格项的门店数',
      render: (value: number) => {
        return <div>{!_.isNil(value) ? `${(value * 100).toFixed(1)}%` : '-'}</div>;
      },
    },
    {
      dataIndex: 'passRatio',
      title: '检查项合格率',
      width: 150,
      formatter: '0,0',
      showRadio: true,
      isRate: true,
      sorter: (a: any, b: any) => a.passRatio - b.passRatio,
      tooltip: '所有报告中合格的检查项数/已打分的检查项数',
      render: (value: number) => {
        return <div>{!_.isNil(value) ? `${(value * 100).toFixed(1)}%` : '-'}</div>;
      },
    },
    {
      dataIndex: 'rectifyCount',
      title: '剩余待整改数',
      width: 150,

      sorter: (a: any, b: any) => a.rectifyCount - b.rectifyCount,
      tooltip: '至今‘待整改、已过期’的检查项数',
    },
    {
      dataIndex: 'overdueCount',
      title: '逾期整改项',
      width: 150,
      sorter: (a: any, b: any) => a.overdueCount - b.overdueCount,
    },
    {
      dataIndex: 'opr',
      title: '操作',
      width: 150,
      render: (_value: any, record: any) => {
        return (
          <a
            className="text-primary"
            onClick={() => {
              setStudyVisible(true);
              setCurData({
                type: record?.groupId ? 'ORGANIZATION' : 'SHOP',
                id: record?.groupId || record?.shopId,
                name: record?.name,
              });
            }}
          >
            门店学习情况
          </a>
        );
      },
    },
  ];

  const diagnosisColumns = [
    {
      dataIndex: 'name',
      title: '组织',
      width: 132,
      fixed: true,

      render: (value: any, record: any) => {
        return record?.groupId ? (
          <a
            className="text-primary"
            onClick={() => {
              run(record?.groupId);
            }}
          >
            {value}
          </a>
        ) : (
          <div>{value}</div>
        );
      },
    },
    {
      dataIndex: 'patrolCount',
      title: '巡检次数',
      width: 100,

      sorter: (a: any, b: any) => a.patrolCount - b.patrolCount,
      tooltip: '该门店共被巡检的总次数',
    },
    {
      dataIndex: 'patrolShopCount',
      title: '巡检门店数',
      width: 150,

      sorter: (a: any, b: any) => a.patrolShopCount - b.patrolShopCount,
      tooltip: '该组织被巡检的门店数',
    },
    {
      dataIndex: 'reportRectifyRatio',
      title: '报告整改率',
      width: 150,
      formatter: '0,0',
      showRadio: true,
      isRate: true,
      sorter: (a: any, b: any) => a.reportRectifyRatio - b.reportRectifyRatio,
      tooltip: '全部整改完毕的报告数/所有提交的报告总数',
      render: (value: number) => {
        return <div>{!_.isNil(value) ? `${(value * 100).toFixed(1)}%` : '-'}</div>;
      },
    },
    {
      dataIndex: 'shopRectifyRatio',
      title: '门店整改率',
      width: 150,
      formatter: '0,0',
      showRadio: true,
      isRate: true,
      sorter: (a: any, b: any) => a.shopRectifyRatio - b.shopRectifyRatio,
      tooltip: '不合格项全部整改完毕的门店数/报告中含有不合格项的门店数',
      render: (value: number) => {
        return <div>{!_.isNil(value) ? `${(value * 100).toFixed(1)}%` : '-'}</div>;
      },
    },
    {
      dataIndex: 'passRatio',
      title: '检查项合格率',
      width: 150,
      formatter: '0,0',
      showRadio: true,
      isRate: true,
      sorter: (a: any, b: any) => a.passRatio - b.passRatio,
      tooltip: '所有报告中合格的检查项数/已打分的检查项数',
      render: (value: number) => {
        return <div>{!_.isNil(value) ? `${(value * 100).toFixed(1)}%` : '-'}</div>;
      },
    },
    {
      dataIndex: 'rectifyCount',
      title: '剩余待整改数',
      width: 150,

      sorter: (a: any, b: any) => a.rectifyCount - b.rectifyCount,
      tooltip: '至今‘待整改、已过期’的检查项数',
    },
    {
      dataIndex: 'overdueCount',
      title: '逾期整改项',
      width: 150,
      sorter: (a: any, b: any) => a.overdueCount - b.overdueCount,
    },
    {
      dataIndex: 'opr',
      title: '操作',
      width: 150,
      render: (_value: any, record: any) => {
        return (
          <a
            className="text-primary"
            onClick={() => {
              setStudyVisible(true);
              setCurData({
                type: record?.groupId ? 'ORGANIZATION' : 'SHOP',
                id: record?.groupId || record?.shopId,
                name: record?.name,
              });
            }}
          >
            门店学习情况
          </a>
        );
      },
    },
  ];

  const [columnsFilter, setColumnsFilter] = useState(() => {
    const localStorageColumnsFilter = localStorage.getItem('columnsFilter');

    if (!!!localStorageColumnsFilter) {
      return {
        selfColumnsLimit: selfColumns.map((item) => item.dataIndex),
        patrolColumnsLimit: patrolColumns.map((item) => item.dataIndex),
        diagnosisColumnsLimit: diagnosisColumns.map((item) => item.dataIndex),
      };
    } else {
      if (JSON.parse(localStorageColumnsFilter).hasOwnProperty('diagnosisColumnsLimit')) {
        return JSON.parse(localStorageColumnsFilter);
      } else {
        return {
          ...JSON.parse(localStorageColumnsFilter),
          diagnosisColumnsLimit: diagnosisColumns.map((item) => item.dataIndex),
        };
      }
    }
  });

  const showColumns = useMemo(() => {
    return [DataViewTypeEnum.自检看板, DataViewTypeEnum['自检看板 (新)']].includes(type as DataViewTypeEnum)
      ? selfColumns.filter((o) => columnsFilter.selfColumnsLimit.includes(o.dataIndex))
      : type === DataViewTypeEnum.诊断巡检看板
        ? diagnosisColumns.filter((o) => columnsFilter.diagnosisColumnsLimit.includes(o.dataIndex))
        : patrolColumns.filter((o) => columnsFilter.patrolColumnsLimit.includes(o.dataIndex));
  }, [type, columnsFilter]);

  const { data, loading, run } = useRequest(
    async (groupid?) => {
      const { beginDate: startDate, endDate, ...rest } = searchObj;

      const Params = {
        ...rest,
        startDate: `${searchObj?.beginDate} 00:00:00`,
        endDate: `${searchObj?.endDate} 23:59:59`,
        planTaskIds: searchObj?.planId ? [searchObj?.planId] : null,
        type: searchObj?.shopIds ? 3 : 1, // 默认按组织1;门店3
        groupId: groupid || searchObj?.groupId,
        systemType: 0,
      };

      const Params2 = {
        ...rest,
        startDate: `${searchObj?.beginDate}`,
        endDate: `${searchObj?.endDate}`,
        planTaskIds: searchObj?.planId ? [searchObj?.planId] : null,
        type: searchObj?.shopIds ? 3 : 1, // 默认按组织1;门店3
        groupId: groupid || searchObj?.groupId,
        systemType: 0,
        patrolUserIds: searchObj?.patrolUserIds ? [searchObj?.patrolUserIds] : undefined,
      };

      const listQueryMapFn: { [key: string]: () => Promise<any> } = {
        [DataViewTypeEnum.自检看板]: async () => await querySelfReportDetailList(Params),
        [DataViewTypeEnum['自检看板 (新)']]: async () =>
          await queryTacticsSelfReportDetailList({ ...Params, startDate, endDate }),
        [DataViewTypeEnum.诊断巡检看板]: async () => await queryDiagnosticDataIfnfo(Params2),
      };

      const list = await (Object.keys(listQueryMapFn).includes(type)
        ? listQueryMapFn[type]()
        : queryPatrolDataIfnfo(Params2));

      const summaryQueryMapFn: { [key: string]: () => Promise<any> } = {
        [DataViewTypeEnum.自检看板]: async () => await querySelfReportListTotal(Params),
        [DataViewTypeEnum['自检看板 (新)']]: async () => await queryTacticsSelfReportListTotal(Params),
        [DataViewTypeEnum.诊断巡检看板]: async () => await queryDiagnosticSummary(Params2),
      };

      const itemSummary = await (Object.keys(summaryQueryMapFn).includes(type)
        ? summaryQueryMapFn[type]()
        : queryPatrolReportCompleted(Params2));

      return { list, itemSummary: itemSummary?.summaryData || itemSummary };
    },
    { refreshDeps: [] },
  );

  return (
    <div className="py-2 px-3">
      <div className="flex justify-between items-center ">
        <div className="text-xs">默认按报告平均分排名</div>
        <div className="flex items-center">
          <div className="mr-3" onClick={() => setVisible(true)}>
            <IconFont type="icon-settings" className="ml-1 text-xs" />
          </div>
          <div
            className="mr-4"
            onClick={() => {
              run();
            }}
          >
            <IconFont type="icon-refresh" className="ml-1 text-xs" />
          </div>
        </div>
      </div>
      <div className="mt-3">
        <Table
          rowKey={nanoid()}
          showSorterTooltip={false}
          bordered
          loading={loading}
          sticky
          columns={showColumns}
          dataSource={data?.list || []}
          pagination={false}
          scroll={{ x: 400 }}
          summary={() => {
            return (
              <Table.Summary fixed="top">
                <Table.Summary.Row>
                  {showColumns.map(({ dataIndex, showRadio }, index) => {
                    console.log(dataIndex, '=dataIndex');

                    const itemCount = data?.itemSummary?.[dataIndex];

                    return (
                      <Table.Summary.Cell key={dataIndex} index={index}>
                        {index === 0 ? (
                          '汇总'
                        ) : typeof itemCount === 'number' ? (
                          <> {showRadio ? `${formatScore(itemCount * 100 || 0)}%` : itemCount}</>
                        ) : (
                          '-'
                        )}
                      </Table.Summary.Cell>
                    );
                  })}
                </Table.Summary.Row>
              </Table.Summary>
            );
          }}
        />
      </div>
      <Popup
        bodyStyle={{ width: '60vw' }}
        position="left"
        visible={visible}
        onMaskClick={() => {
          setVisible(false);
          localStorage.setItem('columnsFilter', JSON.stringify(columnsFilter));
        }}
      >
        <Checkbox.Group
          value={
            [DataViewTypeEnum.自检看板, DataViewTypeEnum['自检看板 (新)']].includes(type as DataViewTypeEnum)
              ? columnsFilter.selfColumnsLimit
              : type === DataViewTypeEnum.诊断巡检看板
                ? columnsFilter.diagnosisColumnsLimit
                : columnsFilter.patrolColumnsLimit
          }
          onChange={(e) => {
            const newColumnsFilter = { ...columnsFilter };

            [DataViewTypeEnum.自检看板, DataViewTypeEnum['自检看板 (新)']].includes(type as DataViewTypeEnum)
              ? (newColumnsFilter.selfColumnsLimit = e)
              : type === DataViewTypeEnum.诊断巡检看板
                ? (newColumnsFilter.diagnosisColumnsLimit = e)
                : (newColumnsFilter.patrolColumnsLimit = e);
            setColumnsFilter(newColumnsFilter);
          }}
        >
          <div className="h-screen overflow-scroll">
            <List>
              {([DataViewTypeEnum.自检看板, DataViewTypeEnum['自检看板 (新)']].includes(type as DataViewTypeEnum)
                ? selfColumns
                : type === DataViewTypeEnum.诊断巡检看板
                  ? diagnosisColumns
                  : patrolColumns
              ).map((item) => (
                <List.Item
                  style={{
                    display: item.dataIndex === 'name' ? 'none' : 'block',
                  }}
                  prefix={
                    <div onClick={(e) => e.stopPropagation()}>
                      <Checkbox value={item.dataIndex} />
                    </div>
                  }
                  arrow={false}
                >
                  {item.title}
                </List.Item>
              ))}
            </List>
          </div>
        </Checkbox.Group>
      </Popup>
      <StudyConditionPopup
        curData={curData}
        title={`${curData?.name}学习情况`}
        visible={studyVisible}
        onClose={() => {
          setStudyVisible(false);
        }}
      />
    </div>
  );
};

export default observer(Detail);
