import React, { useEffect, useState } from 'react';
import './index.module.scss';
import { DataBoardStore } from '@src/store';
import { ErrorBlock, SpinLoading } from 'antd-mobile';
import { observer } from 'mobx-react';
import ReportChart from './reportEChart';
import { DataViewTypeEnum } from '..';
import { diagnosticTendencyNames, PatrolTendencyNames, SelfTendencyNames } from '../enum';

type IProps = {
  type: string;
};

const calcPercentage = (num: number) => (num || 0) * 100;
// 数据适配器  将后端数据转成echarts折线图数据
const dataAdapter = (origin: any, controller: any, type: string) => {
  if ([DataViewTypeEnum.自检看板, DataViewTypeEnum['自检看板 (新)']].includes(type as DataViewTypeEnum)) {
    // 自检子组织Eachart
    const xAxis: { data: string[] } = {
      data: [],
    };
    const legend: { data: string[]; selected: any; selectedMode?: boolean } = {
      data: Object.keys(SelfTendencyNames).map(
        (key: string) => SelfTendencyNames[key as keyof typeof SelfTendencyNames],
      ),
      selectedMode: true,
      selected: {
        [SelfTendencyNames.average]: controller.showAvgScore,
        [SelfTendencyNames.complete]: controller.showCompletedRatio,
        [SelfTendencyNames.comment]: controller.showCommentedRatio,
        [SelfTendencyNames.standard]: controller.showPassRatio,
      },
    };
    const series: {
      name: string;
      data: number[];
    }[] = Object.keys(SelfTendencyNames).map((key: string) => ({
      name: SelfTendencyNames[key as keyof typeof SelfTendencyNames],
      data: [],
      type: 'line',
    }));

    (origin || []).forEach((item: any) => {
      const { statisticDate, commentedAvgScore, completedReportRatio, commentedReportRatio, commentedPassReportRatio } =
        item;

      xAxis.data.push(statisticDate);

      [
        commentedAvgScore,
        calcPercentage(completedReportRatio),
        calcPercentage(commentedReportRatio),
        calcPercentage(commentedPassReportRatio),
      ].forEach((item: any, index: number) => {
        series[index].data.push(item || 0);
      });
    });

    return { xAxis, series, legend };
  } else if (type === DataViewTypeEnum.诊断巡检看板) {
    const xAxis: { data: string[] } = {
      data: [],
    };
    const legend: { data: string[]; selected: any; selectedMode?: boolean } = {
      data: Object.keys(diagnosticTendencyNames).map(
        (key: string) => diagnosticTendencyNames[key as keyof typeof diagnosticTendencyNames],
      ),
      selectedMode: true,
      selected: {
        [diagnosticTendencyNames.rectify]: controller.showRectifyRatio,
      },
    };
    const series: {
      name: string;
      data: number[];
    }[] = Object.keys(diagnosticTendencyNames).map((key: string) => ({
      name: diagnosticTendencyNames[key as keyof typeof diagnosticTendencyNames],
      data: [],
      type: 'line',
    }));

    (origin || []).forEach((item: any) => {
      const { statisticDate, shopRectifyRatio } = item;

      xAxis.data.push(statisticDate);

      [calcPercentage(shopRectifyRatio)].forEach((item: any, index: number) => {
        series[index].data.push(item || 0);
      });
    });

    return { xAxis, series, legend };
  } else {
    // 巡检子组织Eachart
    const xAxis: { data: string[] } = {
      data: [],
    };
    const legend: { data: string[]; selected: any; selectedMode?: boolean } = {
      data: Object.keys(PatrolTendencyNames).map(
        (key: string) => PatrolTendencyNames[key as keyof typeof PatrolTendencyNames],
      ),
      selectedMode: true,
      selected: {
        [PatrolTendencyNames.average]: controller.showAvgScore,
        [PatrolTendencyNames.passReport]: controller.showReportRatio,
        [PatrolTendencyNames.redLine]: controller.showRedLineRatio,
        [PatrolTendencyNames.rectify]: controller.showRectifyRatio,
      },
    };
    const series: {
      name: string;
      data: number[];
    }[] = Object.keys(PatrolTendencyNames).map((key: string) => ({
      name: PatrolTendencyNames[key as keyof typeof PatrolTendencyNames],
      data: [],
      type: 'line',
    }));

    (origin || []).forEach((item: any) => {
      const { statisticDate, averageScore, passReportRatio, redLineShopRatio, shopRectifyRatio } = item;

      xAxis.data.push(statisticDate);

      [
        averageScore,
        calcPercentage(passReportRatio),
        calcPercentage(redLineShopRatio),
        calcPercentage(shopRectifyRatio),
      ].forEach((item: any, index: number) => {
        series[index].data.push(item || 0);
      });
    });

    return { xAxis, series, legend };
  }
};

const EchartRow = React.memo(({ data, index, controller, type, onNext }: any) => {
  const { key, groupId, arr } = data;

  return (
    <div className="bg-white mb-2 p-2">
      <div
        className={` ${!!groupId ? 'text-primary' : 'text-black'} mb-2`}
        onClick={() => {
          !!groupId && onNext(groupId);
        }}
      >
        {key}
      </div>
      <ReportChart key={index} canvasId={`report${index}`} option={dataAdapter(arr, controller, type)} />
    </div>
  );
});

const Tendency: React.FC<IProps> = ({ type }) => {
  const { getSelfReportGroupRank, getSelfReportGroupRankLoading, getPatrolGroupRank, getDiagnosisGroupRank } =
    DataBoardStore;
  const [data, setData] = useState<any>([]);
  const [controller] = useState({
    showAvgScore: true,
    showCompletedRatio: true,
    showCommentedRatio: true,
    showPassRatio: true,
    showReportRatio: true,
    showRedLineRatio: true,
    showRectifyRatio: true,
  });

  /* const fetchReportEachartRank = (groupId?: any) => {
    ([DataViewTypeEnum.自检看板, DataViewTypeEnum['自检看板 (新)']].includes(type as any)
      ? getSelfReportGroupRank
      : type === DataViewTypeEnum.诊断巡检看板
        ? getDiagnosisGroupRank
        : getPatrolGroupRank)(groupId).then((value: any) => {
      const virtualData: any[] = value.map((i: any) => {
        const item: any[] = i?.responseList || i?.list;
        const { shopId, groupId } = item[0] as any;

        return {
          key: i.name,
          shopId,
          groupId,
          arr: item,
          onClick:
            groupId != null ? () => fetchReportEachartRank(groupId) : null,
        };
      });

      setData(virtualData?.slice(0, 30) || []);
    });
  }; */

  const fetchReportEachartRank = (groupId?: any) => {
    const actionMap = {
      [DataViewTypeEnum.自检看板]: getSelfReportGroupRank,
      [DataViewTypeEnum['自检看板 (新)']]: getSelfReportGroupRank,
      [DataViewTypeEnum.诊断巡检看板]: getDiagnosisGroupRank,
    };

    const aciton = actionMap[type as keyof typeof actionMap] || getPatrolGroupRank;

    aciton(groupId, { isTactics: type === DataViewTypeEnum['自检看板 (新)'] }).then((value) => {
      const virtualData: any[] = value.map((i: any) => {
        const item: any[] = i?.responseList || i?.list;
        const { shopId, groupId } = item[0] as any;

        return {
          key: i.name,
          shopId,
          groupId,
          arr: item,
          onClick: groupId != null ? () => fetchReportEachartRank(groupId) : null,
        };
      });
      setData(virtualData?.slice(0, 30) || []);
    });
  };

  useEffect(() => {
    fetchReportEachartRank();
  }, []);

  return (
    <div className="py-2 px-3">
      {getSelfReportGroupRankLoading ? (
        <SpinLoading className="mx-auto" />
      ) : (
        <>
          {data?.length > 0 ? (
            <div className="">
              {data?.map((item: any, index: number) => {
                return (
                  <EchartRow
                    key={index}
                    data={item}
                    index={index}
                    controller={controller}
                    type={type}
                    onNext={(gid: number) => {
                      fetchReportEachartRank(gid);
                    }}
                  />
                );
              })}
            </div>
          ) : (
            <ErrorBlock status="empty" description="暂无数据" />
          )}
        </>
      )}
    </div>
  );
};

export default observer(Tendency);
