/*
 * @Author: ZengWu <EMAIL>
 * @Date: 2023-06-30
 * @LastEditors: ZengWu <EMAIL>
 * @LastEditTime: 2023-07-10
 * @Description:
 */

import { useMemo } from 'react';
import ReactECharts, { EChartsOption } from 'echarts-for-react';
import { cloneDeep } from 'lodash';

interface ReportChartProps {
  option: any;
  name?: any;
  canvasId: string;
  disabled?: boolean;
  onClick?: () => void;
}

const defaultOption: EChartsOption = {
  tooltip: {
    trigger: 'axis',
    confine: true,
    textStyle: {
      fontSize: 8,
      color: '#fff',
    },
    backgroundColor: 'rgba(0,0,0,0.7)',
    formatter: (param: any) => {
      const showItemList = param?.map((v: any) => {
        const itemValue =
          v?.seriesName !== '报告平均分'
            ? `${(v?.value).toFixed(1)}%`
            : v?.value;

        return `${v?.marker} ${v?.seriesName}   ${itemValue}`;
      });
      const showTooltip = showItemList?.map((v: any) => {
        return `${v}<br>`;
      });

      return `${param?.[0]?.axisValue}<br>${showTooltip?.join('')}`;
    },
  },
  grid: {
    left: '2%',
    right: '11%',
    bottom: '20%',
    top: '4%',
    containLabel: true,
  },
  legend: {
    bottom: '0',
    width: '100%',
    padding: 5,
    itemWidth: 12,
    itemHeight: 8,
    textStyle: {
      //   fontSize: 4 * pixelRatio + 1,
      fontWeight: 'bold',
    },
    icon: 'rect',
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
  },
  yAxis: {
    type: 'value',
  },
};

const ReportChart = (props: ReportChartProps) => {
  const { canvasId, option } = props;
  const showOptions = useMemo(() => {
    const { option } = props;
    const { tooltip, grid, legend, xAxis, yAxis } = defaultOption;

    return {
      tooltip,
      grid,
      legend: cloneDeep(Object.assign(legend, option.legend)),
      xAxis: cloneDeep(Object.assign(xAxis, option.xAxis)),
      yAxis,
      series: cloneDeep(option.series),
    };
  }, [option]);

  return (
    <ReactECharts
      key={canvasId}
      option={showOptions}
      style={{ height: '100%', minHeight: '300px' }}
    />
  );
};

export default ReportChart;
