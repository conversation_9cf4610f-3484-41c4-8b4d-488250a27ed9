import { Loading } from '@src/components';
import { IPopup, IPopupProps } from '@src/components/IPopup';
import { DataBoardStore } from '@src/store';
import { useRequest } from 'ahooks';
import { toJS } from 'mobx';
import { queryStudyGroupList } from '../api';

interface StudyPopupProps extends IPopupProps {
  curData: {
    type: string;
    id: string | number;
  };
}

const SHOWITEMS = [
  { label: '账号学习完成率', key: 'studyCompletedRate', isRate: true },
  { label: '账号考核通过率', key: 'examPassRate', isRate: true },
  { label: '门店学习完成率', key: 'shopCompletedRate', isRate: true },
  { label: '门店考核通过率', key: 'shopExamPassRate', isRate: true },
  { label: '未完成学习数', key: 'noCompletedCount' },
];

const StudyConditionPopup: React.FC<StudyPopupProps> = ({ curData, onClose, title, visible, ...props }) => {
  const { searchObj } = DataBoardStore;

  const { data, loading } = useRequest(
    async () => {
      const { beginDate, endDate, ...rest } = searchObj;
      console.log(toJS(searchObj), '=searchObj');

      const Params = {
        ...rest,
        startDate: `${searchObj?.beginDate}`,
        endDate: `${searchObj?.endDate}`,
        node: curData,
        taskTypeList: searchObj?.subType ? [searchObj?.subType] : undefined,
      };
      const res = visible && (await queryStudyGroupList(Params));
      return res || [];
    },
    { refreshDeps: [curData, visible] },
  );
  return (
    <IPopup {...props} visible={visible} title={title} onClose={onClose} bodyStyle={{ maxHeight: '80vh' }}>
      {visible && (
        <Loading spinning={loading}>
          <div className="p-4 flex-col overflow-y-scroll">
            {SHOWITEMS.map((item) => {
              const num = data?.[item.key];
              return (
                <div className="text-base mb-4">
                  {item.label} : {item?.isRate ? `${((num || 0) * 100).toFixed(1)}%` : num || '-'}
                </div>
              );
            })}
          </div>
        </Loading>
      )}
    </IPopup>
  );
};

export default StudyConditionPopup;
