import React, { useMemo, useState } from 'react';
import { Tabs } from 'antd-mobile';
import { useSearchParams } from 'react-router-dom';
import Detail from './detail';
import Tendency from './tendency';

export enum DetailTypeEnum {
  数据明细 = 'detail',
  折线趋势图 = 'tendency',
}

const TabList = [
  {
    key: DetailTypeEnum.数据明细,
    title: '数据明细',
  },
  {
    key: DetailTypeEnum.折线趋势图,
    title: '折线趋势图',
  },
];
const SubConditionDetail: React.FC = () => {
  const [routerParams] = useSearchParams();
  const [activeKey, setActiveKey] = useState('detail');
  const { checkType } = useMemo(() => {
    const checkType = routerParams.get('checkType');

    return {
      checkType,
    };
  }, [routerParams]);

  return (
    <React.Fragment>
      <div className="bg-white">
        <Tabs
          style={{ '--title-font-size': '1rem' }}
          activeKey={activeKey}
          onChange={(key) => {
            setActiveKey(key);
          }}
          stretch={false}
        >
          {TabList.map(({ key, title }) => (
            <Tabs.Tab title={title} key={key} />
          ))}
        </Tabs>
      </div>
      {activeKey === DetailTypeEnum.数据明细 && <Detail type={checkType!} />}
      {activeKey === DetailTypeEnum.折线趋势图 && <Tendency type={checkType!} />}
    </React.Fragment>
  );
};

export default SubConditionDetail;
