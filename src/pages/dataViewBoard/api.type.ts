export enum TSubType {
  'toShop' = 0,
  'toVideo' = 1,
}
export type checkDataParams = {
  corpId?: number;
  emphasisType?: number;
  groupId?: number;
  reportEndDate?: string;
  reportStartDate?: string;
  taskEndDate?: string;
  taskBeginDate?: string;
  selfTaskEndDate?: string;
  selfTaskStartDate?: string;
  startDate?: string;
  endDate?: string;
  shopIds?: number[];
  planId?: number;
  shopType?: string;
  userId?: number;
  worksheetCategoryId?: number;
  worksheetItemId?: number;
  workSheetId?: number;
  workSheetIds?: number[];
  reportType?: 'SELF' | 'DISPATCH';
  type?: number;
  systemType?: number;
  isSelectByDay?: number;
  planTaskIds?: number[] | null;
  subType?: TSubType;
  statisticsType?: 'BY_DAY' | 'BY_MONTH' | null;
  lastStartDate?: string;
  lastEndDate?: string;
  patrolUserIds?: number[];
};

export type UnqualifiedBody = {
  commentCount: number;
  emphasisType: number;
  fineFlag: number;
  keyItem: number;
  necessaryFlag: number;
  noPassCount: number;
  noPassRatio: number;
  noPassShopCount: number;
  redLine: number;
  selfCheckCount: number;
  selfShopCount: number;
  workCategoryName: string;
  workSheetCategoryId: number;
  workSheetId: number;
  workSheetItemContent: string;
  workSheetItemId: number;
  workSheetName: string;
  yellowItem: number;
};

export type selfTaskNonconformityBody = {
  count: number;
  reason: string;
  reasonRatio: string;
  total: number;
  workCategoryName: string;
  workSheetCatgoryId: number;
  workSheetId: number;
  workSheetItemContent: string;
  workSheetItemId: number;
  workSheetName: string;
};

export type selfTaskNoPassShop = {
  commentCount: number;
  deductRatio: string;
  emphasisType: number;
  fineFlag: number;
  issuesReportCount: number;
  keyItem: number;
  necessaryFlag: number;
  noPassRatio: string;
  patrolCount: number;
  redLine: number;
  shopId: number;
  shopName: string;
  workCategoryName: string;
  workSheetCategoryId: number;
  workSheetId: number;
  workSheetItemContent: string;
  workSheetItemId: number;
  workSheetName: string;
  yellowItem: number;
};
