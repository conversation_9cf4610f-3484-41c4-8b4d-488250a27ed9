import { useEffect, useMemo, useState } from 'react';
import { DataBoardStore } from '@src/store';
import { ProgressBar } from 'antd-mobile';
import { DownOutline } from 'antd-mobile-icons';
import classNames from 'classnames';
import { round } from 'lodash';
import { observer } from 'mobx-react';
import { createSearchParams, useNavigate } from 'react-router-dom';
import SortControl from './components/sortControl';
import TitleViewCard from './components/titleViewCard';
import {
  diagnosticConditionSort,
  patrolConditionSort,
  patrolUnqualifiedSituationSort,
  selfConditionSort,
} from './enum';
import { Sorter } from '../boards/components';
import { DataViewTypeEnum } from '.';

type IProps = {
  type?: string;
};

enum AscCurrent {
  'Top',
  'Last',
}

const SubGroupCondition = observer(({ type }: IProps) => {
  const navigate = useNavigate();
  const [typeSorter, setTypeSorter] = useState([selfConditionSort[0]?.value]);
  const [ascCurrent, setAscCurrent] = useState(AscCurrent.Top);
  const { SelfOrgsSituation, patrolOrgsSituation, diagnosticOrgsSituation, patrolUnqualifiedSituation, searchObj } =
    DataBoardStore;

  useEffect(() => {
    const sortMap = {
      [DataViewTypeEnum.自检看板]: selfConditionSort,
      [DataViewTypeEnum['自检看板 (新)']]: selfConditionSort,
      [DataViewTypeEnum.诊断巡检看板]: diagnosticConditionSort,
      [DataViewTypeEnum.巡检不合格情况]: patrolUnqualifiedSituationSort,
    };

    const sort = sortMap[type as keyof typeof sortMap] || patrolConditionSort;

    setTypeSorter([sort?.[0]?.value]);
  }, [type]);

  const dataScoure = useMemo(() => {
    const dataMap = {
      [DataViewTypeEnum.自检看板]: SelfOrgsSituation.viewData,
      [DataViewTypeEnum['自检看板 (新)']]: SelfOrgsSituation.viewData,
      [DataViewTypeEnum.诊断巡检看板]: diagnosticOrgsSituation.viewData,
      [DataViewTypeEnum.巡检不合格情况]: patrolUnqualifiedSituation.viewData,
    };

    return dataMap[type as keyof typeof dataMap] || patrolOrgsSituation.viewData;
  }, [
    SelfOrgsSituation.viewData,
    diagnosticOrgsSituation.viewData,
    patrolOrgsSituation.viewData,
    patrolUnqualifiedSituation.viewData,
    type,
  ]);

  const configSort = useMemo(() => {
    const configMap = {
      [DataViewTypeEnum.自检看板]: selfConditionSort,
      [DataViewTypeEnum['自检看板 (新)']]: selfConditionSort,
      [DataViewTypeEnum.诊断巡检看板]: diagnosticConditionSort,
      [DataViewTypeEnum.巡检不合格情况]: patrolUnqualifiedSituationSort,
    };

    return configMap[type as keyof typeof configMap] || patrolConditionSort;
  }, [type]);

  const showData = useMemo(() => {
    let lastData = [];

    const formatData = [...(dataScoure || [])];

    const sortList: any = formatData?.sort((a, b) => {
      return b?.[typeSorter[0]] - a?.[typeSorter[0]];
    });

    if (ascCurrent === AscCurrent.Top) {
      lastData = sortList?.slice(0, 10);
    } else {
      lastData = sortList?.slice(-10)?.reverse();
    }

    return lastData;
  }, [dataScoure, typeSorter, ascCurrent]);

  const onReload = () => {
    const actionMap = {
      [DataViewTypeEnum.自检看板]: SelfOrgsSituation,
      [DataViewTypeEnum['自检看板 (新)']]: SelfOrgsSituation,
      [DataViewTypeEnum.诊断巡检看板]: diagnosticOrgsSituation,
      [DataViewTypeEnum.巡检不合格情况]: patrolUnqualifiedSituation,
    };

    const action = actionMap[type as keyof typeof actionMap] || patrolOrgsSituation;

    action.run(searchObj);
  };

  return (
    <TitleViewCard
      label={
        {
          [DataViewTypeEnum.巡检不合格情况]: '检查项不合格情况',
        }[type!] || '子组织情况 >'
      }
      onJump={() => {
        if (type === DataViewTypeEnum.巡检不合格情况) {
          return;
        }
        navigate({
          pathname: `/dataViewBoard/subGroupCondition`,
          search: `?${createSearchParams({
            checkType: type!,
          })}`,
        });
      }}
      onReload={onReload}
    >
      <div className="flex justify-between items-center mt-3">
        {type !== DataViewTypeEnum.诊断巡检看板 ? (
          <div>
            <Sorter config={configSort} value={typeSorter} onChange={setTypeSorter} />
          </div>
        ) : (
          // 诊断巡检看板没有排序功能，只按门店整改率进行排序
          <div className="flex items-center text-primary">
            门店整改率
            <DownOutline className="ml-1 text-xs" />
          </div>
        )}
        <div>
          <SortControl values={['Top', 'Last']} onClick={(e) => setAscCurrent(e)} current={ascCurrent} />
        </div>
      </div>
      <div className="mt-3" key={type}>
        {showData?.map((item: any) => {
          const isPercent = configSort?.find((v) => v.value === typeSorter[0])?.isRate;
          const isPatrolUnqualified = type === DataViewTypeEnum.巡检不合格情况;

          return (
            <div
              key={item.groupId}
              className="mb-5"
              onClick={
                isPatrolUnqualified
                  ? () => {
                      navigate({
                        pathname: '/dataViewBoard/patrolUnqualifiedDetail',
                        search: `?${createSearchParams({
                          ...(item || {}),
                        })}`,
                      });
                    }
                  : undefined
              }
            >
              <div className="flex justify-between items-center mb-2">
                <span className={classNames('text-sm', isPatrolUnqualified ? 'text-primary' : 'text-[#858585]')}>
                  {item?.name || item?.worksheetItemContent}
                </span>
                <span className="text-sm text-[#5E5E5E]">
                  {isPercent ? `${round(item[typeSorter[0]] * 100 || 0)}%` : item[typeSorter[0]] || 0}
                </span>
              </div>
              <ProgressBar
                percent={isPercent ? round(item[typeSorter[0]] * 100 || 0) : item[typeSorter[0]] || 0}
                style={{
                  '--fill-color': isPatrolUnqualified ? '#C5CAE9' : undefined,
                }}
              />
            </div>
          );
        })}
      </div>
    </TitleViewCard>
  );
});

export default SubGroupCondition;
