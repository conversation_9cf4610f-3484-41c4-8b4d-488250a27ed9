import React, { useMemo, useState } from 'react';
import { DataBoardStore } from '@src/store';
import { formatDateToUTC } from '@src/utils/utils';
import { Tabs } from 'antd-mobile';
import dayjs from 'dayjs';
import { observer } from 'mobx-react';
import { useSearchParams } from 'react-router-dom';
import UnqualifiedReason from './components/UnqualifiedReason';
import UnqualifiedShop from './components/UnqualifiedShop';

export enum DetailTypeEnum {
  不合格原因 = 'unqualifiedReason',
  不合格门店 = 'unqualifiedShop',
}

const CheckItemConditionDetail = observer(() => {
  const [routerParams] = useSearchParams();
  const tabkey = routerParams.get('tabkey') as DetailTypeEnum;
  const { searchObj } = DataBoardStore;

  const [activeKey, setActiveKey] = useState<DetailTypeEnum>(tabkey ?? DetailTypeEnum.不合格原因);

  const params = useMemo(() => {
    const { beginDate: startTime, endDate: endTime, ...rest } = searchObj || {};

    const startDate = formatDateToUTC(dayjs(startTime).startOf('day'));
    const endDate = formatDateToUTC(dayjs(endTime).endOf('day'));
    const worksheetCategoryId = routerParams.get('worksheetCategoryId');
    const worksheetId = routerParams.get('worksheetId');
    const worksheetItemId = routerParams.get('worksheetItemId');

    return {
      ...rest,
      startDate,
      endDate,
      worksheetCategoryId,
      worksheetId,
      worksheetItemId,
    };
  }, [routerParams, searchObj]);

  const TabList = [
    {
      key: DetailTypeEnum.不合格原因,
      title: '不合格原因',
      children: <UnqualifiedReason activeKey={activeKey} params={params} />,
    },
    {
      key: DetailTypeEnum.不合格门店,
      title: '不合格门店',
      children: <UnqualifiedShop activeKey={activeKey} params={params} />,
    },
  ];

  return (
    <React.Fragment>
      <div className="bg-white">
        <Tabs
          style={{ '--title-font-size': '.875rem' }}
          activeKey={activeKey}
          onChange={(key) => {
            setActiveKey(key as DetailTypeEnum);
          }}
          stretch={false}
        >
          {TabList.map(({ key, title, children }) => (
            <Tabs.Tab title={title} key={key}>
              {children}
            </Tabs.Tab>
          ))}
        </Tabs>
      </div>
    </React.Fragment>
  );
});

export default CheckItemConditionDetail;
