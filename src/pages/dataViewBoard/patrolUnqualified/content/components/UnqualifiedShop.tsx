import { useState } from 'react';
import { DownSquareOutlined, UpSquareOutlined } from '@ant-design/icons';
import { IconFont } from '@src/components';
import { getTacticsUnqualifiedShop } from '@src/pages/dataViewBoard/tactics/api';
import { useRequest } from 'ahooks';
import { Table } from 'antd';
import { ColumnType } from 'antd/es/table';
import { Checkbox, List, Popup } from 'antd-mobile';
import { DetailTypeEnum } from '..';

const columns: ColumnType<any>[] = [
  {
    title: '排名',
    dataIndex: 'rank',
    fixed: 'left',
    render: (_value, _record, index) => <div>{index + 1}</div>,
  },
  {
    title: '门店',
    dataIndex: 'shopName',
  },
  {
    title: '巡检次数',
    dataIndex: 'patrolCount',
    sorter: (a, b) => a.patrolCount - b.patrolCount,
  },
  {
    title: '问题发生次数',
    dataIndex: 'issuesReportCount',
    sorter: (a, b) => a.issuesReportCount - b.issuesReportCount,
  },
  {
    title: '需整改次数',
    dataIndex: 'rectifyReportCount',
    sorter: (a, b) => a.rectifyReportCount - b.rectifyReportCount,
  },
  {
    title: '不合格率',
    dataIndex: 'noPassRatio',
    sorter: (a, b) => a.noPassRatio - b.noPassRatio,
  },
  {
    title: '已整改次数',
    dataIndex: 'rectifyCompleteCount',
    sorter: (a, b) => a.rectifyCompleteCount - b.rectifyCompleteCount,
  },
  {
    title: '整改率',
    dataIndex: 'rectifyRatio',
    sorter: (a, b) => a.rectifyRatio - b.rectifyRatio,
  },
];

export default function UnqualifiedShop({
  activeKey,
  params,
}: {
  activeKey: DetailTypeEnum;
  params: Record<string, any>;
}) {
  const [settingColumns, setSettingColumns] = useState<string[]>(columns?.map((m) => m?.dataIndex ?? []));
  const [visible, setVisible] = useState<boolean>(false);

  const {
    data = [],
    loading,
    refresh,
  } = useRequest(
    async () => {
      const res = await getTacticsUnqualifiedShop(params);

      return res || [];
    },
    {
      refreshDeps: [activeKey, params],
      ready: activeKey === DetailTypeEnum.不合格门店 && !!params?.startDate && !!params?.endDate,
    },
  );

  return (
    <>
      <div className="flex items-center justify-between mb-3">
        <div className="text-[13px] leading-[22px] text-[#86909C]">默认按报告平均分排名</div>
        <div className="flex items-center">
          <div className="mr-3" onClick={() => setVisible(true)}>
            <IconFont type="icon-settings" className="ml-1 text-sm" />
          </div>
          <div
            className="mr-4"
            onClick={() => {
              refresh();
            }}
          >
            <IconFont type="icon-refresh" className="ml-1 text-sm" />
          </div>
        </div>
      </div>
      <Table
        dataSource={data}
        loading={loading}
        columns={columns.filter((f) => settingColumns.includes(f.dataIndex))}
        scroll={{ x: 'max-content' }}
        pagination={false}
        rowKey="shopId"
        expandable={{
          expandIcon: ({ expanded, onExpand, record }) => {
            return (
              <div
                className="cursor-pointer"
                onClick={(e) => {
                  onExpand(record, e);
                }}
              >
                {expanded ? <UpSquareOutlined /> : <DownSquareOutlined />}
              </div>
            );
          },
          expandedRowRender: (record) => {
            return (
              <div className="bg-[#4E5969] py-3">
                <div className="text-white px-3">{record?.shopName}</div>
                <div className="bg-[#FFFFFF] min-h-[0.5px] my-3" />
                <div className="flex flex-row px-3">
                  <div className="basis-1/3">
                    <span className="text-white text-xs leading-5">巡检次数</span>
                    <div className="text-sm leading-[1.375rem] text-white">{record?.patrolCount}</div>
                  </div>
                  <div className="basis-1/3">
                    <span className="text-white text-xs leading-5">问题发生次数</span>
                    <div className="text-sm leading-[1.375rem] text-white">{record?.issuesReportCount}</div>
                  </div>
                  <div className="basis-1/3">
                    <span className="text-white text-xs leading-5">不合格率</span>
                    <div className="text-sm leading-[1.375rem] text-white">{record?.noPassRatio}</div>
                  </div>
                </div>
                <div className="flex flex-row px-3 mt-3">
                  <div className="basis-1/3">
                    <span className="text-white text-xs leading-5">已整改次数</span>
                    <div className="text-sm leading-[1.375rem] text-white">{record?.rectifyCompleteCount}</div>
                  </div>
                  <div className="basis-1/3">
                    <span className="text-white text-xs leading-5">需整改次数</span>
                    <div className="text-sm leading-[1.375rem] text-white">{record?.rectifyReportCount}</div>
                  </div>
                  <div className="basis-1/3">
                    <span className="text-white text-xs leading-5">整改率</span>
                    <div className="text-sm leading-[1.375rem] text-white">{record?.rectifyRatio}</div>
                  </div>
                </div>
              </div>
            );
          },
        }}
      />
      <Popup
        bodyStyle={{ width: '60vw' }}
        position="left"
        visible={visible}
        onMaskClick={() => {
          setVisible(false);
        }}
      >
        <Checkbox.Group
          value={settingColumns}
          onChange={(e) => {
            setSettingColumns(e as string[]);
          }}
        >
          <div className="h-screen overflow-scroll">
            <List>
              {columns.map((item) => (
                <List.Item
                  prefix={
                    <div onClick={(e) => e.stopPropagation()}>
                      <Checkbox value={item.dataIndex} />
                    </div>
                  }
                  arrow={false}
                >
                  {item?.title as string}
                </List.Item>
              ))}
            </List>
          </div>
        </Checkbox.Group>
      </Popup>
    </>
  );
}
