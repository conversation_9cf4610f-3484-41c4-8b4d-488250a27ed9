import { useState } from 'react';
import { IconFont } from '@src/components';
import { getTacticsUnqualifiedReason } from '@src/pages/dataViewBoard/tactics/api';
import { useRequest } from 'ahooks';
import { Table } from 'antd';
import { type ColumnsType } from 'antd/es/table';
import { Popup } from 'antd-mobile';
import { CloseOutline } from 'antd-mobile-icons';
import { DetailTypeEnum } from '..';

const columns: ColumnsType = [
  {
    title: '不合格原因',
    dataIndex: 'reason',
    width: 60,
  },
  {
    title: '次数',
    dataIndex: 'count',
    width: 60,
    sorter: (a, b) => a?.count - b?.count,
  },
  {
    title: '占比',
    dataIndex: 'reasonRatio',
    width: 60,
  },
];

export default function UnqualifiedReason({
  activeKey,
  params,
}: {
  activeKey: DetailTypeEnum;
  params: Record<string, any>;
}) {
  const [visible, setVisible] = useState<boolean>(false);

  const { data, loading, refresh } = useRequest(
    async () => {
      const res = await getTacticsUnqualifiedReason(params);

      return res || [];
    },
    {
      refreshDeps: [activeKey, params],
      ready: activeKey === DetailTypeEnum.不合格原因 && !!params?.startDate && !!params?.endDate,
    },
  );

  return (
    <div>
      <div className="flex items-center justify-end mb-3">
        <div
          className="mr-4"
          onClick={() => {
            refresh();
          }}
        >
          <IconFont type="icon-refresh" className="ml-1 text-sm" />
        </div>
      </div>
      <Table
        dataSource={data?.nonconformityReasons || []}
        loading={loading}
        columns={columns}
        scroll={{ x: 'max-content' }}
        pagination={false}
        summary={() => {
          if (activeKey === DetailTypeEnum.不合格原因 && (data?.otherReasons?.length || 0) > 0) {
            return (
              <Table.Summary.Row>
                <Table.Summary.Cell index={0}>
                  <span
                    className="text-primary"
                    onClick={() => {
                      setVisible(true);
                    }}
                  >
                    其他
                  </span>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={1}>{data?.otherReasons?.length}</Table.Summary.Cell>
                <Table.Summary.Cell index={2}>{data?.otherReasonRatio}</Table.Summary.Cell>
              </Table.Summary.Row>
            );
          }
        }}
      />
      <Popup
        visible={visible}
        onMaskClick={() => {
          setVisible(false);
        }}
        onClose={() => {
          setVisible(false);
        }}
        position="bottom"
        bodyStyle={{ height: '40vh', maxHeight: '40vh', borderTopRightRadius: '12px', borderTopLeftRadius: '12px' }}
      >
        <div className="relative">
          <div className="text-lg flex justify-center py-3 font-bold">其他原因</div>
          <CloseOutline className="absolute top-4 right-4" fontSize={16} onClick={() => setVisible(false)} />
        </div>
        <div className="overflow-y-auto px-4 py-2 h-[32vh]">
          {data?.otherReasons?.map((m, idx) => (
            <div key={`${m}_${idx}`} className="text-base">
              {idx + 1}.{m}
            </div>
          ))}
        </div>
      </Popup>
    </div>
  );
}
