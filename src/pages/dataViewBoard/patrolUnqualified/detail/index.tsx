import { useMemo } from 'react';
import classNames from 'classnames';
import { createSearchParams, useNavigate, useSearchParams } from 'react-router-dom';
import { DetailTypeEnum } from '../content';

const columns = [
  {
    title: '巡检次数',
    dataIndex: 'patrolCount',
  },
  {
    title: '问题发生次数',
    dataIndex: 'issuesReportCount',
  },
  {
    title: '需整改次数',
    dataIndex: 'rectifyReportCount',
  },
  {
    title: '不合格率',
    dataIndex: 'noPassRatio',
  },
  {
    title: '已整改次数',
    dataIndex: 'rectifyCompleteCount',
  },
  {
    title: '整改率',
    dataIndex: 'rectifyRatio',
  },
  {
    title: '不合格门店数',
    dataIndex: 'refShopCount',
  },
];

export default function PatrolUnqualifiedDetail() {
  const [routerParams] = useSearchParams();
  const navigate = useNavigate();

  const data = useMemo(() => {
    const patrolCount = routerParams.get('patrolCount');
    const issuesReportCount = routerParams.get('issuesReportCount');
    const rectifyReportCount = routerParams.get('rectifyReportCount');
    const noPassRatio = routerParams.get('noPassRatio');
    const rectifyCompleteCount = routerParams.get('rectifyCompleteCount');
    const rectifyRatio = routerParams.get('rectifyRatio');
    const refShopCount = routerParams.get('refShopCount');

    return {
      patrolCount,
      issuesReportCount,
      rectifyReportCount,
      noPassRatio: `${(Number(noPassRatio) * 100).toFixed(2)}%`,
      rectifyCompleteCount,
      rectifyRatio: `${(Number(rectifyRatio) * 100).toFixed(2)}%`,
      refShopCount,
    };
  }, [routerParams]);

  const queryParams: Record<string, any> = useMemo(() => {
    const worksheetCategoryId = routerParams.get('worksheetCategoryId') ?? '';
    const worksheetId = routerParams.get('worksheetId') ?? '';
    const worksheetItemId = routerParams.get('worksheetItemId') ?? '';

    return {
      worksheetCategoryId,
      worksheetId,
      worksheetItemId,
    };
  }, [routerParams]);

  return (
    <div className="px-3 bg-white flex-1">
      <div className="text-[#86909C] text-[.8125rem] leading-[1.375rem] mb-[13px] mt-4">
        检查项内容：{routerParams?.get('worksheetItemContent') || ''}
      </div>
      <div className="border-[#E5E6EB] border-[0.5px] rounded-lg">
        {columns.map((m) => (
          <div className="flex  border-b-[0.5px]" key={m.dataIndex}>
            <div className="basis-1/2 px-3 py-4 border-r-[0.5px]">
              <span className="text-[13px] leading-[1.375rem] text-[#1D2129]">{m.title}</span>
            </div>
            <div className="basis-1/2 px-3 py-4">
              <span
                className={classNames('text-sm leading-[1.375rem]', {
                  'text-primary': ['issuesReportCount', 'refShopCount'].includes(m.dataIndex),
                })}
                onClick={() => {
                  if (['issuesReportCount', 'refShopCount'].includes(m.dataIndex)) {
                    navigate({
                      pathname: '/dataViewBoard/patrolUnqualifiedContent',
                      search: `?${createSearchParams({
                        ...queryParams,
                        tabkey: {
                          issuesReportCount: DetailTypeEnum.不合格原因,
                          refShopCount: DetailTypeEnum.不合格门店,
                        }[m.dataIndex as 'issuesReportCount' | 'refShopCount']!,
                      })}`,
                    });
                  }
                }}
              >
                {data[m.dataIndex as keyof typeof data]}
              </span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
