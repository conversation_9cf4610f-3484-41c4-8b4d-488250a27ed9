import { IconFont } from '@src/components';
import { IPopupPage } from '@src/components/IPopup';
import PageContainer from '@src/components/PageContainer';
import { Button, Checkbox, Collapse, Form, SideBar, TextArea, Toast } from 'antd-mobile';
import { FC, useEffect, useMemo, useState } from 'react';
import { useLocation, useNavigate, useSearchParams } from 'react-router-dom';
import './index.scss';
import { saveReview, submitReview } from '@src/pages/patrol/api';

const Review: FC = () => {
  const [visible, setVisible] = useState<boolean>(false);
  const [routerParams] = useSearchParams();

  const { state } = useLocation();
  const navigate = useNavigate();
  const [sideBarKey, setSideBarKey] = useState<any>(state?.checklists?.[0]?.patrolWorksheetId);
  const [selectItemIds, setSelectItemIds] = useState<number[]>([]);
  const { taskId } = useMemo(() => {
    const taskId = routerParams.get('taskId');
    return {
      taskId,
    };
  }, [routerParams]);
  const [form] = Form.useForm();
  console.log(state, '=state');

  const { subChecklistMap, checklistItemMap } = useMemo(() => {
    let subChecklistMap: any = {};
    let checklistItemMap: any = {};

    state?.checklists?.forEach(({ children, patrolWorksheetId }: any) => {
      subChecklistMap[patrolWorksheetId] = children;
      children?.forEach(({ children }: any) => {
        children?.forEach((item: any) => {
          checklistItemMap[item.itemId] = item;
        });
      });
    });

    return { subChecklistMap, checklistItemMap };
  }, [state?.checklists]);

  useEffect(() => {
    // queryPatrolReportDetail({ taskId: +taskId! }).then((res: any) => {
    //   const { appPatrolReport, overallReview } = res || {};
    //   let formatMap: any = {};
    //   console.log(appPatrolReport, '=appPatrolReport');
    //   appPatrolReport?.data?.forEach(
    //     ({
    //       patrolWorksheetId,
    //       patrolWorksheetName,
    //       worksheetItemName: itemName,
    //       worksheetCategoryName: checkListTypeName,
    //       itemId,
    //       content: reviewContent,
    //     }: any) => {
    //       if (!formatMap[patrolWorksheetId]) {
    //         formatMap[patrolWorksheetId] = {
    //           patrolWorksheetId,
    //           patrolWorksheetName,
    //           children: [],
    //         };
    //       }
    //       formatMap[patrolWorksheetId].children.push({
    //         patrolWorksheetName,
    //         itemId,
    //         itemName,
    //         checkListTypeName,
    //         reviewContent,
    //         patrolWorksheetId: checklistItemMap[itemId]?.patrolWorksheetId,
    //       });
    //     },
    //   );
    //   console.log();
    //   form?.setFieldsValue({
    //     overallReviewContent: overallReview || '',
    //     checkListReviews: Object.keys(formatMap).map((key) => formatMap[key]),
    //   });
    // });
  }, []);

  const closePopup = () => {
    setSelectItemIds([]);
    setVisible(false);
  };

  const saveAction = () => {
    return form
      ?.validateFields()
      .then(() => {
        const values = form.getFieldsValue();
        const { overallReviewContent, checkListReviews } = values;
        let formatReviews: any[] = [];
        checkListReviews?.forEach(({ children }: any) => {
          children?.forEach(({ checkListId, checkListTypeId, itemId, reviewContent }: any) => {
            formatReviews.push({
              patrolWorksheetId: checkListId,
              patrolWorksheetCategoryId: checkListTypeId,
              patrolWorksheetItemId: itemId,
              reviewContent,
            });
          });
        });
        return saveReview({
          taskReportId: +taskId!,
          checkListReviews: formatReviews,
          overallReviewContent,
        });
      })
      .catch((e: any) => {
        Toast.show('请完成所有必填项');
        throw e;
      });
  };

  return (
    <>
      <PageContainer
        footer={
          <div className="py-2 px-3 bg-[#fff] flex">
            <Button
              className="mr-3 w-1/2"
              onClick={() => {
                saveAction().then(() => {
                  Toast.show('保存成功');
                });
              }}
            >
              保存
            </Button>
            <Button
              color="primary"
              className="w-1/2"
              onClick={() => {
                saveAction().then(() => {
                  return submitReview(+taskId!).then(() => {
                    Toast.show('提交成功');
                    navigate(-1);
                  });
                });
              }}
            >
              提交点评
            </Button>
          </div>
        }
      >
        <div className="bg-[#fff] h-full text-[#141414]">
          <Form form={form}>
            <div className="bg-[#F5F5F5] pb-[10px]">
              <div className="px-4 py-3 bg-[#fff]">
                <span
                  className="text-[#00BBB4] text-sm leading-6 flex items-center"
                  onClick={() => {
                    const checkListReviews = form.getFieldValue('checkListReviews') || [];
                    let selectIds: number[] = [];
                    checkListReviews?.forEach(({ children }: any) => {
                      children?.forEach(({ itemId }: any) => {
                        selectIds?.push(itemId);
                      });
                    });
                    setSelectItemIds(selectIds || []);
                    setVisible(true);
                  }}
                >
                  <IconFont
                    type="icon-plus-circle1"
                    className="text-2xl  leading-6 mr-2 inline-flex"
                  />
                  选择点评检查项
                </span>
              </div>
              <div className="bg-[#fff] patrol-review-form-array">
                <Form.Array name="checkListReviews">
                  {(fields, { remove: wrapRemove }) => {
                    const checkListReviews = form.getFieldValue('checkListReviews');
                    console.log(checkListReviews, '=11111111111111');

                    return fields.map(({ index: wrapIndex }) => {
                      const checklist = checkListReviews?.[wrapIndex];
                      const { checkListId, checkListName, children }: any = checklist;

                      return (
                        <Collapse defaultActiveKey={checkListId?.toString()}>
                          <Collapse.Panel key={checkListId?.toString()} title={checkListName}>
                            <Form.Array name={[wrapIndex, 'children']}>
                              {(fields, { remove }) =>
                                fields.map(({ index }) => {
                                  const { itemName, checkListTypeName } = children?.[index];
                                  return (
                                    <div className="flex items-start">
                                      <div className="flex mr-2">
                                        <IconFont
                                          type="icon-minus-circle1"
                                          className="text-2xl leading-6 text-[#F53F3F]"
                                          onClick={() => {
                                            const checkListReviews =
                                              form.getFieldValue('checkListReviews');

                                            if (
                                              checkListReviews?.[wrapIndex]?.children?.length > 1
                                            ) {
                                              remove(index);
                                            } else {
                                              wrapRemove(wrapIndex);
                                            }
                                          }}
                                        />
                                      </div>
                                      <div className="flex-auto">
                                        <div className="text-[#5E5E5E] text-sm leading-[22px] mb-1">
                                          {checkListTypeName}/{itemName}
                                        </div>
                                        <Form.Item
                                          noStyle
                                          name={[index, 'reviewContent']}
                                          rules={[
                                            {
                                              required: true,
                                              message: '请输入点评内容',
                                            },
                                          ]}
                                        >
                                          <TextArea
                                            maxLength={100}
                                            placeholder="请输入点评内容"
                                            style={{
                                              '--placeholder-color': '#B8B8B8',
                                              '--font-size': '12px',
                                              '--color': '#141414',
                                              fontWeight: 400,
                                            }}
                                            showCount={(length: number, maxLength: number) => {
                                              return (
                                                <div className="flex justify-end text-xs leading-3 text-[#B8B8B8]">
                                                  <span>
                                                    {length}/{maxLength}字
                                                  </span>
                                                </div>
                                              );
                                            }}
                                            rows={2}
                                            className="bg-[#F5F5F5] rounded p-2 text-xs leading-5"
                                          />
                                        </Form.Item>
                                      </div>
                                    </div>
                                  );
                                })
                              }
                            </Form.Array>
                          </Collapse.Panel>
                        </Collapse>
                      );
                    });
                  }}
                </Form.Array>
              </div>
            </div>
            <div className="py-3 px-4">
              <div className="mb-2 text-base font-medium ">
                总点评<span className="text-[#F53F3F] ml-1">*</span>
              </div>
              <Form.Item
                noStyle
                name="overallReviewContent"
                rules={[
                  {
                    required: true,
                    message: '请输入点评内容',
                  },
                ]}
              >
                <TextArea
                  maxLength={300}
                  placeholder="请输入点评内容"
                  style={{
                    '--placeholder-color': '#B8B8B8',
                    '--font-size': '12px',
                    '--color': '#141414',
                    fontWeight: 400,
                  }}
                  showCount={(length: number, maxLength: number) => {
                    return (
                      <div className="flex justify-end text-xs leading-3 text-[#B8B8B8]">
                        <span>
                          {length}/{maxLength}字
                        </span>
                      </div>
                    );
                  }}
                  rows={5}
                  className="bg-[#F5F5F5] rounded p-2 text-xs leading-5"
                />
              </Form.Item>
            </div>
          </Form>
        </div>
      </PageContainer>
      <IPopupPage visible={visible}>
        <PageContainer
          footer={
            <div className="py-2 px-3 bg-[#fff] flex">
              <Button
                className="mr-3 w-1/2"
                onClick={() => {
                  closePopup();
                }}
              >
                返回
              </Button>
              <Button
                color="primary"
                className="w-1/2"
                onClick={() => {
                  console.log(selectItemIds, '=selectItemIds');
                  console.log(checklistItemMap, '=checklistItemMap');

                  const checklistItems = selectItemIds?.map((id: number) => checklistItemMap[id]);

                  console.log(checklistItems, '=checklistItems');

                  let selectChecklistMap: any = {};
                  checklistItems.forEach((item: any) => {
                    const {
                      checkListId,
                      checkListName,
                      checkListTypeId,
                      checkListTypeName,
                      itemId,
                      itemName,
                    } = item;
                    const shortItem = {
                      checkListId,
                      checkListName,
                      checkListTypeId,
                      checkListTypeName,
                      itemId,
                      itemName,
                    };
                    if (!selectChecklistMap[checkListId]) {
                      selectChecklistMap[checkListId] = {
                        checkListId,
                        checkListName,
                        children: [shortItem],
                      };
                    } else {
                      selectChecklistMap[checkListId].children.push(shortItem);
                    }
                  });
                  console.log(selectChecklistMap, '=selectChecklistMap');
                  console.log(
                    Object.keys(selectChecklistMap).map((key) => selectChecklistMap[key]),
                    '=Object.keys(selectChecklistMap).map((key) => selectChecklistMap[key])',
                  );

                  form.setFieldValue(
                    'checkListReviews',
                    Object.keys(selectChecklistMap).map((key) => selectChecklistMap[key]),
                  );
                  closePopup();
                }}
              >
                确定
              </Button>
            </div>
          }
        >
          <div className="flex min-h-full">
            <div>
              <SideBar
                style={{
                  '--width': '108px',
                }}
                className="shrink-0 patrol-review-sidebar"
                activeKey={sideBarKey?.toString()}
                onChange={(key) => {
                  setSideBarKey(key);
                }}
              >
                {state?.checklists?.map(({ patrolWorksheetName, patrolWorksheetId }: any) => {
                  return <SideBar.Item key={patrolWorksheetId} title={patrolWorksheetName} />;
                })}
              </SideBar>
            </div>
            <div className="flex-auto pl-4">
              {subChecklistMap[sideBarKey]
                ?.filter(({ patrolWorksheetCategoryId }: any) => patrolWorksheetCategoryId !== 0)
                .map(
                  ({ patrolWorksheetCategoryName, children, patrolWorksheetCategoryId }: any) => {
                    return (
                      <div className="patrol-review-group" key={patrolWorksheetCategoryId}>
                        <h5 className="font-medium text-sm leading-[22px] text-[#141414] pt-3 pb-2">
                          {patrolWorksheetCategoryName}
                        </h5>
                        {children?.length > 0 && (
                          <ul>
                            {children.map(({ itemName, itemId }: any) => {
                              return (
                                <li className="pr-3" key={itemId}>
                                  <Checkbox
                                    className="patrol-review-checkbox"
                                    checked={selectItemIds?.includes(itemId)}
                                    onChange={(val: boolean) => {
                                      if (val) {
                                        setSelectItemIds(selectItemIds.concat([itemId]));
                                      } else {
                                        setSelectItemIds(
                                          selectItemIds.filter((id: number) => id !== itemId),
                                        );
                                      }
                                    }}
                                  >
                                    {itemName}
                                  </Checkbox>
                                </li>
                              );
                            })}
                          </ul>
                        )}
                      </div>
                    );
                  },
                )}
            </div>
          </div>
        </PageContainer>
      </IPopupPage>
    </>
  );
};

export default Review;
