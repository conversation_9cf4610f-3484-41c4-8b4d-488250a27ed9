.patrol-review {
  &-group {
    padding-bottom: 15px;
    & + & {
      border-top: 1px solid #f0f0f0;
      padding-top: 15px;
    }
  }
  &-form-array {
    .adm-list-card {
      margin: 0;
    }

    .adm-list-default .adm-list-body {
      border: 0;
    }

    .adm-list-item {
      padding-left: 0;
    }
    .adm-list-item-content-main {
      font-size: 16px;
      line-height: 24px;
      color: #141414;
      font-weight: 500;
      padding: 12px 12px 12px 16px;
    }
    .adm-list-item-content-arrow {
      font-size: 16px;
    }

    .adm-collapse-panel-content {
      .adm-list-item-content {
        padding-right: 0;
      }
      .adm-list-card {
        margin-top: 24px;
        &:first-child {
          margin-top: 0;
        }
      }
    }
  }
  &-sidebar {
    .adm-side-bar-item {
      padding: 12px;
    }
    .adm-side-bar-item-highlight {
      display: none;
    }
    .adm-side-bar-item-title {
      font-size: 14px;
      line-height: 22px;
      font-weight: 500;
      letter-spacing: 0;
    }
    .adm-side-bar-item-corner {
      width: 0;
      height: 0;
    }
  }

  &-checkbox {
    display: flex;
    flex-direction: row-reverse;
    justify-content: space-between;
    padding: 9px 0;

    .adm-checkbox-icon {
      width: 20px;
      height: 20px;
    }
    .adm-checkbox-content {
      color: #5e5e5e;
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
      padding-left: 0;
    }
  }
}
