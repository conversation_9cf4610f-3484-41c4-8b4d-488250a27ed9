import { IPopup, IPopupProps } from '@src/components/IPopup';
import OptionsDetail from '@src/components/OptionsDetail';
import { DetailItem } from '@src/pages/patrol/api.type';
interface CheckListPopupProps extends IPopupProps {
  detailItem: DetailItem;
}
const CheckListItemPopup: React.FC<CheckListPopupProps> = ({
  detailItem,
  onClose,
  title,
  visible,
  ...props
}) => {
  if (!detailItem) {
    return null;
  }

  return (
    <IPopup {...props} visible={visible} title={title} onClose={onClose}>
      {visible && <OptionsDetail detailItem={detailItem} />}
    </IPopup>
  );
};

export default CheckListItemPopup;
