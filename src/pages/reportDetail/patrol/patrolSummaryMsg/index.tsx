import React, { useMemo, useState } from 'react';
import { IconFont } from '@src/components';
import { DetailItem, ReportDetailInfo } from '@src/pages/patrol/api.type';
import { patrolTypeText } from '@src/pages/patrol/planDetail/detailCard';
import { Tooltip } from 'antd';
import ReactECharts, { EChartsOption } from 'echarts-for-react';
import CheckListItemPopup from './checkListItemPopup';
import styles from './index.module.scss';
import PatrolKindsMsg from '../kindsMsg';

type ChartOptionsProps = {
  title: string;
  count: number;
  color: string;
  unit: string;
};
interface SUMMARY {
  config: any;
  detail: ReportDetailInfo;
}
const PatrolSummaryMsg: React.FC<SUMMARY> = (props: SUMMARY) => {
  const { config } = props;
  const { appPatrolReport } = props?.detail || {};
  // 根据后台设置判断，检查项合格率（item: 默认）、分类合格率（category）
  const [checkItemVisible, setCheckItemVisible] = useState<boolean>(false);
  const [detailItem, _setDetailItem] = useState<DetailItem>();

  const REPORT_SHOW_PASS_RATE = config?.REPORT_SHOW_PASS_RATE === 'category' ? 'category' : 'pass';

  const valid = REPORT_SHOW_PASS_RATE === 'category' ? appPatrolReport?.validCategoryCount : appPatrolReport?.valid;
  const total = REPORT_SHOW_PASS_RATE === 'category' ? appPatrolReport?.allCategoryCount : appPatrolReport?.total;
  const validText = REPORT_SHOW_PASS_RATE === 'category' ? '合格分类' : '合格项';
  console.log(props?.detail, '=props?.detail');
  console.log(config, '=123config');

  const content = useMemo(() => {
    const reportText =
      (appPatrolReport?.data || [])[0]?.importance === null
        ? '报告得分 = 所有检查表的实际得分/所有检查表的满分 * 100'
        : '报告得分 = 检查表1的百分制得分 * 权重1 + 检查表2的百分制得分 * 权重2 + …\n 其中，检查表的百分制得分 = 所有检查项目得分之和/所有检查项满分 * 100';

    const rateText =
      config?.REPORT_SHOW_PASS_RATE !== 'category'
        ? '合格率 = 合格的检查项数目/总的检查项数目'
        : '合格率 = 满分的分类数目/总的分类数目';

    return (
      <div className="text-left">
        <div>报告得分：</div>
        <div className="mb-4">{reportText}</div>
        <div>合格率：</div>
        <div className="mb-4">{rateText}</div>
        <div className="text-sm">注1：若存在S项，得分按相应逻辑调整，但合格率计算不受影响。</div>
      </div>
    );
  }, [appPatrolReport, config?.REPORT_SHOW_PASS_RATE]);

  const ChartOptions: React.FC<ChartOptionsProps> = ({ title, count, color, unit }) => {
    const option: EChartsOption = {
      color: [color, '#f0f2f5'],
      series: [
        {
          name: title,
          type: 'pie',
          radius: ['85%', '100%'],
          silent: true,
          itemStyle: {
            // borderColor: '#fff',
            // borderWidth: 16,
          },
          label: {
            show: false,
            position: 'center',
          },
          data: [{ value: count }, { value: 100 - count }],
        },
      ],
    };

    return (
      <div className={styles.chart}>
        <ReactECharts option={option} style={{ height: 120 }} />
        <div className={styles.title}>
          <div className="text-xs text-[#5E5E5E] mb-2">{title}</div>
          <div className={styles.content}>{unit === '分' ? count?.toFixed(1) : `${count}${unit}`}</div>
        </div>
      </div>
    );
  };

  return (
    <div className="leading-[14px] ">
      {/* 食安稽核到店辅导-报告不显示汇总，不显示权重 */}
      {props?.detail?.subType !== patrolTypeText.诊断任务 &&
        props?.detail?.subType !== patrolTypeText.食安稽核到店辅导 && (
          <React.Fragment>
            <div className="bg-white  flex justify-between  items-center">
              <div className="text-base p-4 leading-[16px]" style={{ fontWeight: 500 }}>
                汇总&nbsp;
                <Tooltip placement="bottom" title={content}>
                  <IconFont type="icon-changjianwentixiangguanwenti" style={{ color: '#9C9C9C' }} />
                </Tooltip>
              </div>
              <div className="pr-4  text-xs text-gray-400">得分已自动折算为百分制</div>
            </div>
            <div className="p-4 bg-white flex justify-between  items-center">
              <div className="w-full flex flex-col items-center">
                <div className="w-full">
                  <ChartOptions title="报告得分" color="#3491fa" count={appPatrolReport?.score || 0} unit="分" />
                </div>
                <div className="mt-4">
                  实际分数{appPatrolReport?.actualScore}/{appPatrolReport?.actualTotalScore}
                </div>
              </div>
              <div className="w-full flex flex-col  items-center">
                <div className="w-full">
                  <ChartOptions
                    title="合格率"
                    color="#3491fa"
                    count={total ? Math.round((valid / total) * 100) : 0}
                    unit="%"
                  />
                </div>
                <div className="mt-4">
                  {validText} {valid}/{total}
                </div>
              </div>
            </div>

            <div className="bg-white px-4 pb-4 pt-2">
              <PatrolKindsMsg
                detailList={appPatrolReport?.data}
                config={config}
                itemCountMap={props?.detail?.itemCountMap || []}
              />
            </div>
          </React.Fragment>
        )}
      {/* {!onlyOne && ( // 多个检查表
        <div className="bg-white">
          <List>
            {appPatrolReport?.data?.map((sum, ind) => {
              return (
                <List.Item key={ind}>
                  <div
                    className="flex justify-between  items-center"
                    onClick={() => {
                      setCheckItemVisible(true);
                      setDetailItem(sum);
                    }}
                  >
                    <div className="grow flex justify-start items-center">
                      <IconFont type="icon-grid-4" className="w-[16px] h-[16px] block" />
                      <div className="grow ml-2 text-base leading-[16px] ellipsis w-40 ">
                        {sum?.patrolWorksheetName}
                      </div>
                    </div>
                    {props?.detail?.subType !== patrolTypeText.诊断任务 &&
                      props?.detail?.subType !== patrolTypeText.食安稽核到店辅导 && (
                        <div className="w-3/7">
                          <div className="flex justif y-end items-center text-sm">
                            <span
                              className="text-primary bg-primary-1 pt-1 pb-1 pl-4 pr-4"
                              style={{ borderRadius: '16px' }}
                            >
                              权重：{sum?.weight}%
                            </span>
                            <IconFont
                              type="icon-chevron-right"
                              className="text-[#858585] text-base ml-2 inline-block"
                            />
                          </div>
                        </div>
                      )}
                  </div>
                </List.Item>
              );
            })}
          </List>
        </div>
      )} */}
      <CheckListItemPopup
        className={styles.WfullPopup}
        title="报告详情"
        position="right"
        visible={checkItemVisible}
        detailItem={detailItem!}
        onClose={() => setCheckItemVisible(false)}
      />
    </div>
  );
};
export default PatrolSummaryMsg;
