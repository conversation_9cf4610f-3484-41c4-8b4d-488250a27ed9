import React from 'react';
import { Button } from 'antd-mobile';
import { useNavigate } from 'react-router-dom';

interface OperaProps {
  isDudao: boolean; // 是否是督导
  submitTime?: string; // 提交时间
  taskId: number;
  count: number;
  pollingType: 'SelfPolling' | 'NormalPolling' | 'DisinfectionPolling';
  disinfectionType?: string; // 消杀类型
  isDAILY?: boolean; // 是否是日常巡检
  entity?: any; // 实体
}

const OperationBar: React.FC<OperaProps> = ({ submitTime, taskId, isDudao, count, pollingType, isDAILY, entity }) => {
  const navigate = useNavigate();
  return (
    <div className="flex items-center bg-[rgba(0,187,180,0.05)] mt-3 rounded">
      {isDudao ? (
        <React.Fragment>
          <div className="flex-1 text-sm text-primary leading-[22px] px-2 py-[9px]">
            {submitTime}
            &nbsp; 门店提交了整改反馈
          </div>
          <div
            className="flex"
            onClick={() => {
              navigate(`/tasks/pollingAbarbeitung?taskId=${taskId}&pollingType=${pollingType}`);
            }}
          >
            <Button color="primary" className="text-sm leading-[22px] py-[3px] px-2 mr-2">
              去审核
            </Button>
          </div>
        </React.Fragment>
      ) : (
        !!count && (
          <React.Fragment>
            <div className="flex-1 text-sm text-primary leading-[22px] px-2 py-[9px]">
              {count}
              个问题待整改
            </div>
            <div
              className="flex"
              onClick={() => {
                navigate(`/tasks/pollingAbarbeitung?taskId=${taskId}&pollingType=${pollingType}&isDAILY=${isDAILY}`);
              }}
            >
              <Button color="primary" className="text-sm leading-[22px] py-[3px] px-2 mr-2">
                去整改
              </Button>
            </div>
          </React.Fragment>
        )
      )}
    </div>
  );
};
export default OperationBar;
