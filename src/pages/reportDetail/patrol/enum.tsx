// 1:待提交(巡检 & 自检), 2:已提交(巡检), 3:已确认(巡检), 4:待点评(自检), 5: 已点评(自检)

export enum ItemTips {
  'success' = '若此项未检查，则无法提交报告。',
  'line' = '此项不合格， 则整个分类下所有检查项得分0分。',
  'veto' = '此项不合格，则整个检查表得0分。',
  'fine' = '不影响检查得分和统计，仅作为检查项标签。',
}
export const PatrolReportStatus = {
  NOT_STARTED: '待提交',
  SUBMITTED: '已提交',
  CONFIRMED: '已确认',
};
export enum CurReportStatus {
  待提交 = 'NOT_STARTED',
  已提交 = 'SUBMITTED',
  已确认 = 'CONFIRMED',
}
export enum CurReformStatus {
  待整改 = 'REFORM_FEEDBACK',
  待审核 = 'WAIT_AUDIT',
  已过期 = 'EXPIRED',
  已整改 = 'PASS',
}
export enum SignTypeText {
  跟随系统 = 'SYSTEM',
  无需签到签离 = 'SIGN_NEITHER',
  签到签离 = 'SIGN_IN_OUT',
  签到 = 'SIGN_IN',
}
export enum PatrolTagType {
  error = 1,
  process = 2,
  success = 3,
}

export enum SelfTagType {
  error = 1,
  process = 4,
  success = 5,
}
/**
 * @description SelfPolling：自检，NormalPolling：巡检
 */
export type CHECK_TYPE = 'SelfPolling' | 'NormalPolling';
export enum BtnText {
  'SHOP_MANAGER_WAIT_REVIEW' = '撤回报告',
  'REVOKE_REPORT_COMMENT' = '撤回点评',
  'REVOKE_SINGLESHEET_COMMENT' = '撤回点评', // 撤回单个检查表的点评
  'SUBMIT_REPORT_COMMENT' = '提交点评',
  'PREVIEW_SELF_REPORT' = '预览报告',
  'MODIFY_SCORE' = '修改打分',
  'SUBMIT_REPORT' = '提交报告',
  'RECALL_REPORT' = '撤回报告',
  'RECALL_TABLE' = '撤回检查表',
  'ADMIN_RECALL_REPORT' = '撤回报告', // 超管撤回报告
  'REJECT_REPORT' = '申诉',
  'CONFIRM_REPORT' = '确认报告',
  'PROBLEM_DETAIL' = '问题跟进详情',
  'REVIEW' = '点评',
  'NOSAME_COMMENT' = '当前账号不是提交点评者',
  'SUBMIT_PATROL_REVIEW' = '提交点评', // 巡检提交点评
  'NO_AUTHORITY_SUBMIT' = '无点评权限',
  'NO_NEED_REVIEW' = '不需要点评',
  // 点评时效相关
  'REVOKE_REPORT_COMMENT_DISABLE' = '点评已关闭无法撤回',
  'SUBMIT_REPORT_COMMENT_DISABLE' = '未按时完成，点评已关闭',
  // ---
}
export enum BtnType {
  'SHOP_MANAGER_WAIT_REVIEW' = 'default',
  'REVOKE_REPORT_COMMENT' = 'default',
  'SUBMIT_REPORT_COMMENT' = 'primary',
  'PREVIEW_SELF_REPORT' = 'primary',
  'MODIFY_SCORE' = 'default',
  'SUBMIT_REPORT' = 'primary',
  'RECALL_REPORT' = 'default',
  'REJECT_REPORT' = 'default',
  'CONFIRM_REPORT' = 'primary',
  'PROBLEM_DETAIL' = 'primary',
  'REVIEW' = 'primary',
  'ADMIN_RECALL_REPORT' = 'primary',
  'NO_AUTHORITY_SUBMIT' = 'primary',
  'NO_NEED_REVIEW' = 'primary',
}
