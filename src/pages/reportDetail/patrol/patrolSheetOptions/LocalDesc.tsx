import { useState } from 'react';
import Spot from '@src/assets/images/spot.png';
import VideoViewer from '@src/components/VideoViewer';
import { Image, ImageViewer } from 'antd-mobile';
import { PlayOutline } from 'antd-mobile-icons';

interface LocalDescProps {
  data: any;
}

export const LocalDesc = ({ data }: LocalDescProps) => {
  const [videoPreviewUrl, setVideoPreviewUrl] = useState('');
  const [videoPreviewVisible, setVideoPreviewVisible] = useState(false);
  return (
    <div className="mt-2">
      <div className="flex gap-x-2 items-center">
        <img src={Spot} alt="" className="size-3" />
        <span className="text-[#3D3D3D] text-sm">当场整改情况：</span>
      </div>
      <div className="bg-[#eee] p-2">
        {(!!data?.rectifyReason || !!data?.rectifyImages?.length) && (
          <div className="flex flex-col gap-2">
            <span className="break-all">{data?.rectifyReason || '当场整改情况'}</span>
            <div className="flex flex-wrap gap-2">
              {data?.rectifyImageUrls?.map((item: any) => {
                const isImg = item?.contentType?.startsWith('image') || item?.type?.startsWith('IMG');
                if (isImg) {
                  return (
                    <Image
                      onClick={(e) => {
                        e.stopPropagation();
                        ImageViewer.show({ image: item?.url });
                      }}
                      width="70px"
                      height="70px"
                      className="rounded-lg"
                      src={item?.snapshotUrl || item?.url}
                    />
                  );
                }

                return (
                  <div
                    onClick={(e) => {
                      e.stopPropagation();
                      setVideoPreviewVisible(true);
                      setVideoPreviewUrl(item.url);
                    }}
                    className="relative"
                  >
                    <video
                      style={{ objectFit: 'cover' }}
                      poster={item?.snapshotUrl}
                      className="w-[70px] h-[70px] border"
                      src={item.url}
                    />
                    <div className="absolute left-[50%] top-[50%] -translate-x-[50%] -translate-y-[50%]">
                      <PlayOutline fontSize={40} />
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}
      </div>
      <VideoViewer
        visible={videoPreviewVisible}
        url={videoPreviewUrl}
        onClose={() => {
          setVideoPreviewVisible(false);
        }}
      />
    </div>
  );
};
