import React, { useMemo } from 'react';
import { PatrolCheckStore } from '@src/store';
import { roleTypeIsManage } from '@src/utils/tokenUtils';
import { Input } from 'antd-mobile';
import cn from 'classnames';
import dayjs from 'dayjs';
import { observer } from 'mobx-react';
import styles from './index.module.scss';
import { patrolReviewStatusEnum, patrolReviewTypeEnum } from './itemDetailCard';
import { reportStatusEnum } from '..';

interface Props {
  /**
   * 是否为第二点评
   */
  isSecondReview?: boolean;
  data: any;
  updateItem(data: { qualified: boolean | null; content?: string; itemId: number }): void;
}

const CommentBtnGroup: React.FC<Props> = observer((props: Props) => {
  const isDudao = roleTypeIsManage();
  const { updateItem, data, isSecondReview } = props;
  const { reportDetailInfos, curPatrolSheetInfo } = PatrolCheckStore;
  const {
    reportReviewAbnormalButtonName,
    reportReviewNormalButtonName,
    itemId,
    reportReviewMethod,
    hasReviewPermission,
    reportReviewInfo,
    reportSecondReviewInfo,
  } = data;

  const { content, status, qualified } = isSecondReview ? reportSecondReviewInfo || {} : reportReviewInfo || {};

  const isNoOverTime = useMemo(() => {
    if (isSecondReview) {
      return !!reportDetailInfos?.secondReviewDeadline
        ? dayjs() < dayjs(reportDetailInfos?.secondReviewDeadline)
        : true;
    }

    return !!curPatrolSheetInfo?.reportReviewDeadline
      ? dayjs() < dayjs(curPatrolSheetInfo?.reportReviewDeadline)
      : true;
  }, [curPatrolSheetInfo?.reportReviewDeadline, isSecondReview, reportDetailInfos?.secondReviewDeadline]);

  const reviewResultBtn = () => {
    return (
      <React.Fragment>
        <div className={styles.operationArea}>
          <span role="button" className={cn(`${styles.active} ${!!qualified ? styles.pass : styles.fail}`)}>
            {!!qualified
              ? `${reportReviewNormalButtonName || '合格'}`
              : `${reportReviewAbnormalButtonName || '不合格'}`}
          </span>
        </div>
        {!!content && (
          <div className="bg-[#FAFAFA] my-1 p-1">
            <div className="break-all">{content}</div>
          </div>
        )}
      </React.Fragment>
    );
  };

  return (
    <React.Fragment>
      {status === patrolReviewStatusEnum.已提交
        ? reviewResultBtn()
        : isDudao &&
          reportDetailInfos?.reportStatus === reportStatusEnum.已确认 && (
            <React.Fragment>
              {reportReviewMethod === patrolReviewTypeEnum.不需要点评 ? (
                <div className={styles.operationArea}>
                  <span role="button" className={styles.noNeedComment}>
                    不需要点评项
                  </span>
                </div>
              ) : (
                // 可以点评并且有点评权限才能操作
                (isSecondReview
                  ? reportDetailInfos?.hasNeedSecondReview && reportDetailInfos?.hasSecondReviewPermission
                  : reportDetailInfos?.hasNeedReview && hasReviewPermission) &&
                isNoOverTime && (
                  <React.Fragment>
                    <div className={styles.operationArea}>
                      <span
                        role="button"
                        onClick={() => {
                          updateItem({
                            itemId,
                            qualified: false,
                            content,
                          });
                        }}
                        style={{ width: '42%' }}
                        className={cn(
                          `${styles.active} ${typeof qualified === 'boolean' && !qualified ? styles.fail : ''}`,
                        )}
                      >
                        {reportReviewAbnormalButtonName || '不合格'}
                      </span>
                      <span
                        role="button"
                        style={{ width: '42%' }}
                        onClick={() => {
                          updateItem({
                            itemId,
                            qualified: true,
                            content,
                          });
                        }}
                        className={cn(
                          `${styles.active} ${typeof qualified === 'boolean' && qualified ? styles.pass : ''}`,
                        )}
                      >
                        {reportReviewNormalButtonName || '合格'}
                      </span>
                    </div>
                    <div>
                      {typeof qualified === 'boolean' && (
                        <Input
                          className="mt-2"
                          defaultValue={content}
                          style={{ border: '1px solid #eeeeee' }}
                          onBlur={(e) => {
                            updateItem({
                              itemId,
                              qualified,
                              content: e.target.value,
                            });
                          }}
                          placeholder="选填，文字备注"
                          maxLength={100}
                        />
                      )}
                    </div>
                  </React.Fragment>
                )
              )}
            </React.Fragment>
          )}
    </React.Fragment>
  );
});
export default CommentBtnGroup;
