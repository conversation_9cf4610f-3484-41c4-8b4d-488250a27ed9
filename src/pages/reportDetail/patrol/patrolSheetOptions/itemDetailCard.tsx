import React, { useMemo, useState } from 'react';
import { QuestionTags } from '@src/components/OptionsDetail';
import SopPopup from '@src/components/OptionsDetail/SopPopup';
import StandardPopup from '@src/components/OptionsDetail/StandardPopup';
import UnqualifiedPopup from '@src/components/OptionsDetail/UnqualifiedPopup';
import SampleImage from '@src/components/sampleImage';
import VideoViewer from '@src/components/VideoViewer';
import { CheckItem } from '@src/pages/patrol/api.type';
import { RectificationType } from '@src/pages/patrol/checkList/const';
import { AppealStatus } from '@src/pages/selfCheckComment/api.type';
import { PatrolCheckStore } from '@src/store';
import { roleTypeIsManage } from '@src/utils/tokenUtils';
import { message } from 'antd';
import { Dialog, Image, ImageViewer } from 'antd-mobile';
import { PlayOutline } from 'antd-mobile-icons';
import { observer } from 'mobx-react';
import { useSearchParams } from 'react-router-dom';
import CommentBtnGroup from './commentBtnGroup';
import styles from './index.module.scss';
import { LocalDesc } from './LocalDesc';
import { reportStatusEnum } from '..';
import { revokeAppealForPatrol } from '../../self/api';
import { ComplaintsDescPopup } from '../../self/ComplaintsDescPopup';
export enum buttonTypeEnum {
  判断型 = 'JUDGE',
  打分型 = 'SCORE',
}
export enum scoreTypeEnum {
  得分项 = 'ADD_SCORE',
  扣分项 = 'REDUCE_SCORE',
}

export enum patrolReviewTypeEnum {
  判断型 = 'MANUAL_JUDGMENT',
  不需要点评 = 'NO_REVIEWS_NEEDED',
}
export enum patrolReviewStatusEnum {
  待点评提交 = 'WAIT_REVIEW_SUBMIT',
  已提交 = 'SUBMITTED',
  无需点评 = 'NO_NEEDED_REVIEW',
}
const PatrolDetailScoreBtn = (checkItem: CheckItem) => {
  const { type, scoreType, qualified, fullScore, score, reportDisplayScore } = checkItem;
  return (
    <div className={styles.operationArea}>
      <span
        role="button"
        className={`${styles.active} ${
          !!qualified ||
          (type === buttonTypeEnum.打分型 && score === (scoreType === scoreTypeEnum.扣分项 ? 0 : fullScore))
            ? styles.pass
            : styles.fail
        }`}
      >
        {type === buttonTypeEnum.判断型
          ? (!!qualified
              ? `${checkItem?.qualifiedButtonCustomName || '合格'}`
              : `${checkItem?.notQualifiedButtonCustomName || '不合格'}`
            ).concat(
              scoreType === scoreTypeEnum.扣分项
                ? !!qualified
                  ? '(不扣分)'
                  : `(扣${score}分)`
                : fullScore === 0 || !reportDisplayScore
                  ? ''
                  : `(${score}分)`,
            )
          : (scoreType === scoreTypeEnum.扣分项 ? score === 0 : score === fullScore)
            ? `合格(${scoreType === scoreTypeEnum.扣分项 ? '不扣' : score}分)`
            : `${scoreType === scoreTypeEnum.扣分项 ? '扣' : ''}${score}分${
                scoreType === scoreTypeEnum.扣分项 ? '' : `(满分${fullScore}分)`
              }`}
      </span>
    </div>
  );
};

interface Props {
  itemData: CheckItem;
  updateItem(data: { qualified: boolean | null; content?: string; itemId: number; isSecondReview?: boolean }): void;
  reFresh?: () => void;
}

const ItemDetailCard: React.FC<Props> = observer((props: Props) => {
  const { itemData, updateItem, reFresh } = props;
  const [sopPopupVisible, setSopPopupVisible] = useState(false);
  const [standardPopupVisible, setStandardPopupVisible] = useState(false);
  const [videoPreviewUrl, setVideoPreviewUrl] = useState('');
  const [videoPreviewVisible, setVideoPreviewVisible] = useState(false);
  const [unqualifiedVisible, setUnqualifiedVisible] = useState(false);
  const [showSelectReasons, setShowSelectReasons] = useState<string[]>([]);
  const [sopMsg, setSopMsg] = useState<{
    standard?: string;
    sopId?: number;
    title: string;
  }>();
  const { reportDetailInfos } = PatrolCheckStore;

  const {
    otherReason,
    selectReasons,
    itemRemark,
    accentedTermTags,
    imageList,
    itemSOP,
    standard,
    sopId,
    qualified,
    reportReviewStandard,
    sampleImageUrls,
  } = itemData || {};
  const allReasons: any = otherReason ? [...(selectReasons || []), otherReason] : selectReasons;

  const reasonListText = (reasonLis: string[] | null) => {
    const text = reasonLis?.map((v, index) => {
      return <span key={index}>#{v}&nbsp;</span>;
    });
    return text || '';
  };
  const imagesOrVideoRender = (
    images: {
      url: string;
      type: 'IMG' | 'VIDEO';
      id: string;
      snapshotUrl: string;
    }[],
  ) => {
    return (
      !!images?.length && (
        <div className={`flex  flex-wrap gap-1 items-center ${styles.imgOrVideo}`}>
          {images?.map((item) => {
            return (
              <div key={item?.url}>
                {item?.type === 'VIDEO' ? (
                  <div
                    className="relative bg-[#D9D9D9]"
                    onClick={() => {
                      setVideoPreviewVisible(true);
                      setVideoPreviewUrl(item?.url);
                    }}
                  >
                    <video
                      preload="auto"
                      style={{ width: 48, height: 48, objectFit: 'cover' }}
                      poster={item?.snapshotUrl}
                      src={item?.url}
                    />
                    <div className="absolute left-[50%] top-[50%] -translate-x-[50%] -translate-y-[50%]">
                      <PlayOutline color="#000" fontSize={30} />
                    </div>
                  </div>
                ) : (
                  <Image
                    onClick={() => {
                      ImageViewer.show({ image: item?.url });
                    }}
                    style={{ width: 48, height: 48, objectFit: 'scale-down' }}
                    src={item?.url}
                    width={48}
                    height={48}
                  />
                )}
              </div>
            );
          })}
        </div>
      )
    );
  };

  const [routerParams] = useSearchParams();
  const { taskId } = useMemo(() => {
    const taskId = routerParams.get('taskId');
    return {
      taskId,
    };
  }, [routerParams]);

  const [complaintsPopupVisible, setComplaintsPopupVisible] = useState(false);
  const [appealId, setAppealId] = useState<number>();

  const appealDomBuilder = (item: CheckItem) => {
    switch (item?.appealStatus) {
      case AppealStatus.发起:
        return (
          <div className="flex gap-x-1 items-center">
            <span className="text-xs text-red-400">申诉中</span>
            {!roleTypeIsManage() && (
              <button
                className="focus:outline-none bg-red-400 text-white px-[2px] py-[1px] text-xs rounded"
                onClick={(e) => {
                  e.stopPropagation();
                  Dialog.confirm({
                    content: <div className="text-center text-[#141414] text-base">确定撤回申诉？</div>,
                    onConfirm: async () => {
                      try {
                        await revokeAppealForPatrol({
                          taskId: taskId!,
                          taskWorksheetItemId: item?.itemId!,
                        });
                        message.success('撤回成功');
                        reFresh?.();
                      } catch (error) {
                        message.error('撤回失败');
                      }
                    },
                  });
                }}
              >
                撤回申诉
              </button>
            )}
          </div>
        );
      case AppealStatus.通过:
        return (
          <div className="flex gap-x-1 items-center">
            <span className="text-xs text-red-400">申诉成功</span>
            <button
              className="focus:outline-none bg-primary text-white px-[2px] py-[1px] text-xs rounded"
              onClick={(e) => {
                e.stopPropagation();
                setComplaintsPopupVisible(true);
                setAppealId(item?.itemId);
              }}
            >
              点击查看详情
            </button>
          </div>
        );
      case AppealStatus.驳回:
        return (
          <div className="flex gap-x-1 items-center">
            <span className="text-xs text-red-400">已驳回</span>
            <button
              className="focus:outline-none bg-primary text-white px-[2px] py-[1px] text-xs rounded"
              onClick={(e) => {
                e.stopPropagation();
                setComplaintsPopupVisible(true);
                setAppealId(item?.itemId);
              }}
            >
              点击查看详情
            </button>
          </div>
        );
      case AppealStatus.撤回:
        return <div className="text-xs text-red-400">申诉已撤回，不可再次申诉</div>;
      case AppealStatus.已过期:
        return (
          <div className="flex gap-x-1 items-center">
            <span className="text-xs text-red-400">审核已过期</span>
            <button
              className="focus:outline-none bg-primary text-white px-[2px] py-[1px] text-xs rounded"
              onClick={(e) => {
                e.stopPropagation();
                setComplaintsPopupVisible(true);
                setAppealId(item?.itemId);
              }}
            >
              点击查看详情
            </button>
          </div>
        );
      default:
        return null;
    }
  };
  return (
    <React.Fragment>
      <div className="p-3 pb-4 mb-[10px] bg-[#FFF] rounded-[4px]">
        <div className="flex justify-center items-center mt-1 mb-2">
          <div className="flex-1 flex flex-wrap gap-1">
            <QuestionTags accentedTermTags={accentedTermTags || []} />
          </div>
          {!!reportReviewStandard && (
            <div
              className="text-primary mr-2"
              onClick={() => {
                setSopMsg({
                  standard: reportReviewStandard,
                  sopId,
                  title: '点评标准',
                });
                setStandardPopupVisible(true);
              }}
            >
              点评标准
            </div>
          )}
          {!!sopId && (
            <div
              className="text-primary mr-2"
              onClick={() => {
                setSopMsg({
                  standard,
                  sopId,
                  title: '参考标准',
                });
                setSopPopupVisible(true);
              }}
            >
              参考标准
            </div>
          )}
          {!!standard && (
            <div
              className="text-primary"
              onClick={() => {
                setSopMsg({
                  standard,
                  sopId,
                  title: '评分标准',
                });
                setStandardPopupVisible(true);
              }}
            >
              评分标准
            </div>
          )}
        </div>
        <div className="text-sm leading-[22px] break-all">{itemSOP}</div>
        {!!sampleImageUrls?.length && <SampleImage SampleimageList={sampleImageUrls || []} />}
        <div>{PatrolDetailScoreBtn(itemData)}</div>
        <div>
          {(!!itemRemark || allReasons.length || imageList) && (
            <div className="bg-[#FAFAFA] my-2 p-2">
              <div className="break-all mb-2">{itemRemark}</div>
              {!qualified && (
                <div
                  className="break-all  ellipsis-2 text-primary"
                  onClick={() => {
                    setShowSelectReasons(allReasons);
                    setUnqualifiedVisible(true);
                  }}
                >
                  {reasonListText(allReasons)}
                </div>
              )}
              {imageList && imagesOrVideoRender(imageList)}
            </div>
          )}
        </div>
        {!itemData?.qualified &&
          itemData?.hasRectify &&
          (itemData?.actualRectifyType === RectificationType.LOCAL ||
            itemData?.rectifyType === RectificationType.LOCAL) && <LocalDesc data={itemData} />}
        {
          <CommentBtnGroup
            data={itemData}
            updateItem={(callBackVal) => {
              updateItem(callBackVal);
            }}
          />
        }
        {/* 第二点评操作按钮 */}
        {(reportDetailInfos?.reportStatus === reportStatusEnum.已确认 && reportDetailInfos?.reviewStatus === 'REVIEWED'
          ? // 第一点评完后
            true
          : reportDetailInfos?.hasNeedSecondReview) &&
          !!reportDetailInfos?.hasSecondReviewPermission && (
            <CommentBtnGroup
              data={itemData}
              isSecondReview
              updateItem={(callBackVal) => {
                updateItem({
                  ...callBackVal,
                  isSecondReview: true,
                });
              }}
            />
          )}
      </div>
      <div className="my-2">{appealDomBuilder(itemData)}</div>
      <StandardPopup
        {...sopMsg}
        title={sopMsg?.title || ''}
        visible={standardPopupVisible}
        onClose={() => setStandardPopupVisible(false)}
      />
      <SopPopup {...sopMsg} title="参考标准" visible={sopPopupVisible} onClose={() => setSopPopupVisible(false)} />
      <UnqualifiedPopup
        title="不合格原因"
        visible={unqualifiedVisible}
        selectReasons={showSelectReasons}
        onClose={() => setUnqualifiedVisible(false)}
      />
      <VideoViewer visible={videoPreviewVisible} url={videoPreviewUrl} onClose={() => setVideoPreviewVisible(false)} />
      <ComplaintsDescPopup
        visible={complaintsPopupVisible}
        onClose={() => setComplaintsPopupVisible(false)}
        appealId={appealId}
        taskId={taskId!}
        isPatrol={true}
      />
    </React.Fragment>
  );
});
export default ItemDetailCard;
