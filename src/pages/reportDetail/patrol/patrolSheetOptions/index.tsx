import React, { useEffect, useMemo, useState } from 'react';
import Notice from '@src/assets/images/notice2.svg';
import { IconFont, Loading } from '@src/components';
import { IPopup } from '@src/components/IPopup';
import { savePatrolReviewItem, savePatrolSecondReviewItem } from '@src/pages/patrol/api';
import { DetailItem } from '@src/pages/patrol/api.type';
import { PatrolCheckStore } from '@src/store';
import { roleTypeIsManage } from '@src/utils/tokenUtils';
import { useRequest } from 'ahooks';
import { Checkbox, Collapse, Selector, Tabs } from 'antd-mobile';
import cn from 'classnames';
import dayjs from 'dayjs';
import { toJS } from 'mobx';
import { observer } from 'mobx-react';
import styles from './index.module.scss';
import ItemDetailCard, { patrolReviewTypeEnum } from './itemDetailCard';

interface Props {
  workSheetList: DetailItem[];
  refresh?: () => void;
}
const PatrolSheetOptions: React.FC<Props> = observer((props: Props) => {
  const { workSheetList, refresh } = props;
  const [groupId, setGroupId] = useState(-1);
  const [tabPopupVisible, setTabPopupVisible] = useState(false);
  const { updatePatrolReviewSheet, reportDetailInfos, setCurPatrolSheet } = PatrolCheckStore;
  const isDudao = roleTypeIsManage();
  const [curSheetId, setCurSheetId] = useState<number>();
  const [isOPenLeft, setIsOPenLeft] = useState(true);
  const [collapseKeys, setCollapseKeys] = useState([]);
  const [unqualified, setUnqualified] = useState<boolean>(false);
  const [unReviewItems, setUnReviewItems] = useState<boolean>(false);
  useEffect(() => {
    setCurSheetId(workSheetList[0]?.patrolWorksheetId);
    if (workSheetList?.length === 1) {
      setIsOPenLeft(false);
    }
  }, [workSheetList[0]?.patrolWorksheetId]);
  const curWorkSheet = useMemo(() => {
    setGroupId(-1);
    const curSheetData = workSheetList?.find((item) => item?.patrolWorksheetId === curSheetId);
    setCurPatrolSheet(curSheetData!);
    return curSheetData;
  }, [curSheetId, workSheetList]);

  const list = useMemo(() => {
    if (!curWorkSheet) return [];

    let groups = (toJS(curWorkSheet.children) || []).filter((v) =>
      groupId === -1 ? v.patrolWorksheetCategoryId !== 0 : v.patrolWorksheetCategoryId === groupId,
    );
    if (!groups.length) return [];

    console.log(toJS(groups), '=groups');

    if (unqualified) {
      groups = groups.map((v) => ({
        ...v,
        children: v.children.filter((v2) => !v2.qualified),
      }));
    }
    if (unReviewItems) {
      groups = groups.map((v) => ({
        ...v,
        children: v.children.filter(
          (v2) =>
            !(typeof v2?.reportReviewInfo?.qualified === 'boolean') &&
            v2?.reportReviewMethod !== patrolReviewTypeEnum.不需要点评,
        ),
      }));
    }

    return groups;
  }, [curWorkSheet, groupId, unReviewItems, unqualified]);

  console.log(list, '=list');

  useEffect(() => {
    const init = curWorkSheet?.children?.map((v) => {
      return `${v?.patrolWorksheetCategoryId}`;
    });
    setCollapseKeys(init as any);
  }, [curWorkSheet]);

  const { run: updataRun } = useRequest(
    async (payload) => {
      // 是否为第二点评
      const isSecondReview = payload?.isSecondReview;

      const params = {
        taskId: reportDetailInfos?.taskId,
        ...(isSecondReview
          ? {
              ...payload,
              content: undefined,
              qualified: undefined,
              secondContent: payload?.content,
              secondQualified: payload?.qualified,
            }
          : payload),
      };

      (await isSecondReview) ? savePatrolSecondReviewItem(params) : savePatrolReviewItem(params);

      return { ...params, patrolWorksheetId: curSheetId, isSecondReview };
    },
    {
      manual: true,
      onSuccess: (data) => {
        updatePatrolReviewSheet(data);
      },
    },
  );
  return (
    <Loading spinning={false}>
      <React.Fragment>
        <div className="flex bg-white">
          <div className=" bg-[#F8F8F8]">
            {isOPenLeft && (
              <div>
                {workSheetList?.map((v) => {
                  return (
                    <div
                      className={cn('p-4 w-[100px] ', {
                        'bg-white': v.patrolWorksheetId === curSheetId,
                      })}
                      key={v.patrolWorksheetId}
                      onClick={() => {
                        setCurSheetId(v.patrolWorksheetId);
                      }}
                    >
                      {v.patrolWorksheetName}
                    </div>
                  );
                })}
              </div>
            )}

            <div
              className="absolute left-1 bottom-20 z-10"
              onClick={() => {
                setIsOPenLeft(!isOPenLeft);
              }}
            >
              <IconFont type="icon-shouqi" className="text-[#378BFF]" />
            </div>
          </div>
          <div className="flex-1 flex-col overflow-y-scroll ">
            <div className="leading-[14px]">
              <div className="flex p-4 pb-3 justify-between items-center">
                <div className="text-base leading-[16px] font-medium">明细</div>
                <div className="flex items-center">
                  {reportDetailInfos?.hasNeedReview && isDudao && (
                    <div className="flex justify-between items-center mr-4">
                      <Checkbox
                        style={{
                          '--icon-size': '18px',
                          '--font-size': '24px',
                          '--gap': '6px',
                        }}
                        checked={unReviewItems}
                        onChange={(checked) => {
                          setUnReviewItems(checked);
                        }}
                      >
                        <div className="text-sm text-[#5E5E5E]">只看未点评项</div>
                      </Checkbox>
                    </div>
                  )}
                  <div className="flex justify-between items-center">
                    <Checkbox
                      style={{
                        '--icon-size': '18px',
                        '--font-size': '24px',
                        '--gap': '6px',
                      }}
                      checked={unqualified}
                      onChange={(checked) => {
                        setUnqualified(checked);
                      }}
                    >
                      <div className="text-sm text-[#5E5E5E]">仅显示不合格</div>
                    </Checkbox>
                  </div>
                </div>
              </div>
            </div>
            {isDudao &&
              // 第二点评
              (reportDetailInfos.hasNeedSecondReview
                ? !!reportDetailInfos?.secondReviewDeadline && dayjs() < dayjs(reportDetailInfos.secondReviewDeadline)
                : // 第一点评
                  reportDetailInfos.hasNeedReview &&
                  !!curWorkSheet?.reportReviewDeadline &&
                  dayjs() < dayjs(curWorkSheet.reportReviewDeadline)) && (
                /* reportDetailInfos.hasNeedReview &&
              !!curWorkSheet?.reportReviewDeadline &&
              dayjs() < dayjs(curWorkSheet?.reportReviewDeadline) */
                <div className="flex gap-x-1 bg-[#FFF9ED] h-7 px-4 items-center">
                  <img src={Notice} width={16} height={16} alt="" />
                  <span className="text-xs text-[#FF7D00]">
                    须
                    {dayjs(
                      reportDetailInfos.hasNeedSecondReview
                        ? reportDetailInfos.secondReviewDeadline
                        : curWorkSheet?.reportReviewDeadline,
                    ).format('MM月DD号 HH:mm')}
                    前完成点评
                  </span>
                </div>
              )}
            <div className="bg-white flex items-center pl-1">
              <Tabs
                className={styles.Tabs}
                activeKey={groupId as any}
                onChange={(value) => setGroupId(+value as number)}
              >
                <Tabs.Tab
                  title={
                    <div
                      className={cn('flex text-sm items-center leading-[14px] text-[#858585]', {
                        [styles.activeColor]: groupId === -1,
                      })}
                    >
                      全部
                      <span>
                        ({curWorkSheet?.filledCount}/{curWorkSheet?.itemCount})
                      </span>
                    </div>
                  }
                  key={-1}
                />
                {(curWorkSheet?.children || [])?.map((item) => (
                  <Tabs.Tab
                    title={
                      <div
                        className={cn('flex text-sm items-center leading-[14px] text-[#858585]', {
                          [styles.activeColor]: +item?.patrolWorksheetCategoryId === groupId,
                        })}
                      >
                        <div className="max-w-[100px]  text-ellipsis overflow-hidden whitespace-nowrap">
                          {item.patrolWorksheetCategoryName}
                        </div>
                        <div>
                          ({item.filledCount}/{item.itemCount})
                        </div>
                      </div>
                    }
                    key={item.patrolWorksheetCategoryId}
                  />
                ))}
              </Tabs>
              {!!curWorkSheet && curWorkSheet?.children?.length > 2 && (
                <div>
                  <div className="bg-[rgba(0,0,0,0.03)]" />
                  <div
                    className="flex h-full justify-center items-center px-3"
                    onClick={() => setTabPopupVisible(true)}
                  >
                    <IconFont type="icon-menu" className="text-[#858585] text-sm" />
                  </div>
                </div>
              )}
            </div>
            <div>
              {
                <Collapse
                  className={styles.Collapse}
                  onChange={(e: any) => {
                    setCollapseKeys(e);
                  }}
                  activeKey={collapseKeys}
                >
                  {list?.map((data) => {
                    return (
                      <Collapse.Panel
                        key={`${data?.patrolWorksheetCategoryId}`}
                        title={
                          <div className="text-[#141414] text-sm leading-[14px] font-medium  max-w-[200px]  text-ellipsis overflow-hidden whitespace-nowrap ">
                            {data?.patrolWorksheetCategoryName} {data.filledCount}/{data.itemCount}
                          </div>
                        }
                      >
                        {data?.children?.map((item) => {
                          return (
                            <div key={item?.itemId} className="border-b border-solid border-[#F0F0F0]">
                              <ItemDetailCard
                                itemData={toJS(item) as any}
                                updateItem={(callbackValue) => {
                                  updataRun({
                                    ...callbackValue,
                                    patrolWorksheetCategoryId: data?.patrolWorksheetCategoryId,
                                  });
                                }}
                                reFresh={refresh}
                              />
                            </div>
                          );
                        })}
                      </Collapse.Panel>
                    );
                  })}
                </Collapse>
              }
            </div>
          </div>
        </div>
      </React.Fragment>

      <IPopup title="分类" visible={tabPopupVisible} onClose={() => setTabPopupVisible(false)}>
        <div className="pt-5 px-4 pb-10">
          <Selector
            style={{
              '--checked-color': 'var(--color-primary-1)',
              '--border-radius': '4px',
              fontSize: '14px',
            }}
            className={styles.Selector}
            value={[groupId]}
            onChange={(value) => setGroupId(value[0])}
            columns={1}
            showCheckMark={false}
            options={
              curWorkSheet?.children?.map((i) => ({
                label: `${i.patrolWorksheetCategoryName}`,
                value: i.patrolWorksheetCategoryId,
              })) as any
            }
          />
        </div>
      </IPopup>
    </Loading>
  );
});
export default PatrolSheetOptions;
