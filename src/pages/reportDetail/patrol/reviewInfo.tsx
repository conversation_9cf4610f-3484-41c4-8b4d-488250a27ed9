import { IconFont } from '@src/components';
import { getPatrolReviewDetail } from '@src/pages/patrol/api';
import { useRequest } from 'ahooks';
import classNames from 'classnames';
import React, { useState } from 'react';

interface ReviewInfoProps {
  reviewStatus: string;
  taskId: number;
}
//巡检点评状态
export enum InspectionReviewStatus {
  点评 = 'REVIEWED',
  未点评 = 'NOT_REVIEWED',
}
const ReviewInfo: React.FC<ReviewInfoProps> = (props: ReviewInfoProps) => {
  const [showReviewFull, setShowReviewFull] = useState<boolean>(false);

  const { data: reviewDetail }: any = useRequest(
    async () => {
      if (props?.reviewStatus === InspectionReviewStatus.点评) {
        return await getPatrolReviewDetail(props.taskId!);
      }
    },
    { refreshDeps: [props?.reviewStatus, props.taskId] },
  );
  console.log(reviewDetail, '=reviewDetail');

  return (
    <React.Fragment>
      {reviewDetail && (
        <div className="mt-4">
          <div className="mb-2 flex items-center justify-between">
            <span className="text-[#141414] font-medium text-base">巡检点评</span>
            <span
              className="flex items-center text-[#858585] text-sm leading-none"
              onClick={() => {
                setShowReviewFull(!showReviewFull);
              }}
            >
              {showReviewFull ? (
                <>
                  收起
                  <IconFont type="icon-chevron-up" className="ml-1" />
                </>
              ) : (
                <>
                  全部
                  <IconFont type="icon-chevron-down" className="ml-1" />
                </>
              )}
            </span>
          </div>
          <div className="p-2 text-sm leading-[22px] bg-[#FAFAFA] rounded">
            <ul>
              {reviewDetail?.checkListReviews?.map(
                ({ worksheetName, worksheetItemName, content, id }: any) => {
                  return (
                    <li
                      key={id}
                      className={classNames('mt-3 first:mt-0', {
                        'first:block hidden': !showReviewFull,
                      })}
                    >
                      <div className="text-[#5B6A91]">
                        #{worksheetName}/{worksheetItemName}
                      </div>
                      <p
                        className={classNames('text-[#5E5E5E]', {
                          'h-[22px] text-ellipsis overflow-hidden whitespace-nowrap':
                            !showReviewFull,
                        })}
                      >
                        {content}
                      </p>
                    </li>
                  );
                },
              )}
              <li
                className={classNames('mt-3 first:mt-0', {
                  hidden: !showReviewFull,
                  'first:block': !showReviewFull,
                })}
              >
                <div className="text-[#5B6A91]">#总点评</div>
                <p
                  className={classNames('text-[#5E5E5E]', {
                    'h-[22px] text-ellipsis overflow-hidden whitespace-nowrap': !showReviewFull,
                  })}
                >
                  {reviewDetail?.overallReview}
                </p>
              </li>
            </ul>
          </div>
        </div>
      )}
    </React.Fragment>
  );
};
export default ReviewInfo;
