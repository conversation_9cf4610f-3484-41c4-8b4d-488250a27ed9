import { DetailItem, WeekSheet } from '@src/pages/patrol/api.type';
import { Grid } from 'antd-mobile';
import styles from './index.module.scss';

const PatrolKindsMsg: React.FC<{ detailList: DetailItem[]; config: any; itemCountMap: any }> = ({
  detailList,
  config,
}) => {
  let categories: WeekSheet[] = [];
  detailList?.forEach((item) => {
    return (categories = [...categories, ...item.children]);
  });
  console.log(categories, '=categories');
  const flag = config?.REPORT_SHOW_PASS_RATE === 'category';
  return (
    <div>
      <ul className={styles.categorySummary}>
        <Grid className="text-xs px-3 leading-3 text-[#5E5E5E]" columns={2} gap={20}>
          <Grid.Item>得分</Grid.Item>
          <Grid.Item className="flex justify-end">{!flag ? '合格率' : '合格项'}</Grid.Item>
        </Grid>
        {categories?.map(
          ({
            patrolWorksheetCategoryId,
            actualScore,
            actualTotalScore,
            total,
            validCount,
            patrolWorksheetCategoryName,
          }) => {
            return (
              <div key={patrolWorksheetCategoryId} className="mt-3 text-xs text-[#5E5E5E] px-3 leading-[12px]">
                <Grid columns={2} gap={10} className={styles.categoryPercent}>
                  <Grid.Item>
                    <div
                      style={{
                        width: `${(actualScore * 100) / actualTotalScore}%`,
                        color: 'red',
                      }}
                    />
                  </Grid.Item>
                  <Grid.Item>
                    <div
                      style={{
                        width: `${(validCount * 100) / total}%`,
                      }}
                    />
                  </Grid.Item>
                </Grid>
                <Grid columns={3} gap={20} className="mt-2">
                  <Grid.Item>
                    <div>
                      {actualScore}/{actualTotalScore}
                    </div>
                  </Grid.Item>
                  <Grid.Item className="flex justify-center">
                    <div>
                      {patrolWorksheetCategoryName?.length > 10
                        ? `${patrolWorksheetCategoryName.substring(0, 10)}...`
                        : patrolWorksheetCategoryName}
                    </div>
                  </Grid.Item>
                  <Grid.Item className="flex justify-end">
                    <div>
                      {!flag ? `${total ? Math.round((validCount / total) * 100) : 0}%` : `${validCount}/${total}`}
                    </div>
                  </Grid.Item>
                </Grid>
              </div>
            );
          },
        )}
      </ul>
      {/* <ul className={styles.warning}>
        {itemCountMap?.map((item: any) => {
          let showkey = item?.tag === 'RED_LINE' ? item?.configType : item?.tag;
          let showradio = showkey === 'REPORT_SCORE' ? item.rate : undefined;
          return errorsRender.map(({ key, render }) =>
            render(key === showkey, item.quantity, showradio),
          );
        })}
      </ul> */}
    </div>
  );
};
export default PatrolKindsMsg;
