import { Loading } from '@src/components';
import { IPopup, IPopupProps } from '@src/components/IPopup';
import { getPatrolReportLogs, getSelfReportLogs } from '@src/pages/patrol/api';
import { useRequest } from 'ahooks';
import { Steps } from 'antd-mobile';
import dayjs from 'dayjs';

interface OperateLogProps extends IPopupProps {
  checkType: 'SELF' | 'PATROL';
  taskId?: number | undefined;
}
export const StepType = {
  SELF: {
    REVIEWS: '点评',
    SUBMIT: '提交',
    REVOCATION_REPORT: '撤回报告',
    REVOCATION_REVIEWS: '撤回点评',
  },
  PATROL: {
    CONFIRM: '确认',
    REVOCATION_REPORT: '撤回',
    SUBMIT: '提交',
  },
};

const { Step } = Steps;
const OperateLogPopup: React.FC<OperateLogProps> = ({ taskId, onClose, title, visible, checkType, ...props }) => {
  const { data: logData, loading } = useRequest(
    async () => {
      if (!taskId) {
        throw null;
      }

      const res =
        visible && (checkType === 'SELF' ? await getSelfReportLogs(taskId) : await getPatrolReportLogs(taskId));
      return res || [];
    },
    { refreshDeps: [taskId, visible] },
  );
  return (
    <IPopup {...props} visible={visible} title={title} bodyStyle={{ maxHeight: '80vh' }} onClose={onClose}>
      {visible && (
        <Loading spinning={loading}>
          <div className="py-4  flex-col overflow-y-scroll">
            <Steps direction="vertical">
              {logData?.map((item, index) => {
                return (
                  <Step
                    key={item?.id}
                    title={
                      <div className="text-[#858585]">
                        {item?.oprUserName}&nbsp;&nbsp;
                        {dayjs(item?.createTime)?.format('MM-DD HH:mm')}
                        &nbsp;&nbsp;
                        {StepType[checkType][item?.operationType]}
                      </div>
                    }
                    status={index === logData.length - 1 ? 'process' : 'wait'}
                  />
                );
              })}
            </Steps>
          </div>
        </Loading>
      )}
    </IPopup>
  );
};

export default OperateLogPopup;
