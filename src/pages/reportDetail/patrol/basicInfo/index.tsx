import React, { useState } from 'react';
import { IconFont } from '@src/components';
import { ReportDetailInfo } from '@src/pages/patrol/api.type';
import { IagnosticInfoCard } from '@src/pages/patrol/patrolInfo';
import { patrolTypeText } from '@src/pages/patrol/planDetail/detailCard';
import { getParentReportId, getParentReportIdShop } from '@src/pages/tasks/api';
import { roleTypeIsManage } from '@src/utils/tokenUtils';
import { useRequest } from 'ahooks';
import { Toast } from 'antd-mobile';
import dayjs from 'dayjs';
import { useNavigate } from 'react-router-dom';
import OperateLogPopup from './operateLogPopup';
import PhonePopup from './phonePopup';
import { CurReportStatus, PatrolReportStatus } from '../enum';
interface BASICINFO {
  detail: ReportDetailInfo;
}

const BasicInfo: React.FC<BASICINFO> = (props: BASICINFO) => {
  const { detail } = props;
  const isDudao = roleTypeIsManage();
  const navigate = useNavigate();
  const [phonePopupVisible, setPhonePopupVisible] = useState<boolean>(false);

  const [logPopupVisible, setLogPopupVisible] = useState<boolean>(false);

  const { run: runGetReportId } = useRequest(
    () => (isDudao ? getParentReportId(detail?.taskId) : getParentReportIdShop(detail?.taskId)),
    {
      manual: true,
      onSuccess: (res) => {
        if (res?.existReport) {
          navigate(`/patrol/reportdetail?taskId=${res?.parentId}`);
        } else {
          Toast.show('原报告不存在！');
        }
      },
    },
  );

  return (
    <div className="bg-white leading-[14px] mb-2">
      {detail?.subType === patrolTypeText.诊断任务 ? (
        <IagnosticInfoCard patrolDetail={detail} />
      ) : (
        <React.Fragment>
          <div className=" flex justify-between p-4 pb-0 items-center">
            <div className="flex  items-center">
              <div style={{ fontWeight: '500' }} className="text-base ">
                <div className="inline-block w-6 h-6 text-base font-medium text-center text-white bg-orange-500 rounded mr-2">
                  巡
                </div>
                {detail?.shopId} {detail?.shopName}
              </div>
            </div>
            <div className="text-primary text-sm">{PatrolReportStatus[detail?.reportStatus as CurReportStatus]}</div>
          </div>
          <div className="mt-2 mx-2 mb-2 px-2 py-3 bg-[#FAFAFA] text-[#858585] rounded-lg">
            <div className="text-sm">{detail?.taskName}</div>

            {detail?.subType === patrolTypeText.食安稽核到店辅导 && (
              <React.Fragment>
                <div className="mt-2">
                  任务执行时段：{dayjs(detail?.beginTime).format('YYYY/MM/DD HH:mm')}~
                  {dayjs(detail?.expiredTime).format('YYYY/MM/DD HH:mm')}
                </div>
                <div className="mt-2">巡检人:{detail?.treatedUserName}</div>
                <div
                  className="mt-2 text-primary"
                  onClick={() => {
                    runGetReportId();
                  }}
                >
                  原食安稽核报告
                </div>
              </React.Fragment>
            )}
          </div>
        </React.Fragment>
      )}
      <div className=" px-4 pb-2 flex justify-between items-center">
        <div
          className="text-primary flex  items-center"
          onClick={() => {
            setLogPopupVisible(true);
          }}
        >
          操作记录
          <IconFont type="icon-chevron-right" className="text-xs" />
        </div>
        <div
          className="text-primary  flex  items-center"
          onClick={() => {
            setPhonePopupVisible(true);
          }}
        >
          <span className="ml-1">联系店长</span>
          <IconFont type="icon-rongqi" className="text-xs" />
        </div>
      </div>
      <OperateLogPopup
        checkType="PATROL"
        title="操作记录"
        taskId={detail?.taskId}
        visible={logPopupVisible}
        onClose={() => setLogPopupVisible(false)}
      />
      <PhonePopup
        title="联系方式"
        shopId={detail?.shopId}
        // shopId={'T463'}
        visible={phonePopupVisible}
        onClose={() => setPhonePopupVisible(false)}
      />
    </div>
  );
};
export default BasicInfo;
