import { Loading } from '@src/components';
import { IPopup, IPopupProps } from '@src/components/IPopup';
import { getManagePhone } from '@src/pages/patrol/api';
import { useRequest } from 'ahooks';
interface PhoneProps extends IPopupProps {
  shopId?: string | undefined;
}
const PhonePopup: React.FC<PhoneProps> = ({
  shopId,
  onClose,
  title,
  visible,
  ...props
}) => {
  const { data: phoneData, loading } = useRequest(
    async () => {
      if (!shopId) {
        // eslint-disable-next-line no-throw-literal
        throw null;
      }
      const res = await getManagePhone(shopId);
      return res || [];
    },
    { refreshDeps: [shopId, visible] },
  );
  return (
    <IPopup
      {...props}
      visible={visible}
      title={title}
      bodyStyle={{ maxHeight: '80vh' }}
      onClose={onClose}
    >
      {visible && (
        <Loading spinning={loading}>
          <div className="py-4 flex flex-col items-center justify-center">
            {!!phoneData &&
              phoneData?.map((item) => {
                return (
                  <div key={item.userId} className="text-[#858585]">
                    店长：{item?.nickname}，&nbsp;&nbsp;
                    <span
                      className="text-primary text-sm "
                      onClick={() => {
                        window.location.href = `tel:${item?.phone}`;
                      }}
                    >
                      {item?.phone}
                    </span>
                  </div>
                );
              })}
            {phoneData?.length === 0 && <div>该门店暂无店长账号</div>}
          </div>
        </Loading>
      )}
    </IPopup>
  );
};

export default PhonePopup;
