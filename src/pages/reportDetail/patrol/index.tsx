import React, { useEffect, useMemo, useRef, useState } from 'react';
import { ButtonEnums } from '@src/common/api.type';
import AuthorityEnum from '@src/common/authority';
import { Loading } from '@src/components';
import {
  cancelReport,
  confirmReport,
  patrolReportReviewSubmit,
  patrolReportSecondReviewSubmit,
  queryPatrolReportDetail,
  submitPatrolReport,
} from '@src/pages/patrol/api';
import type { WeekSheet } from '@src/pages/patrol/api.type';
import { patrolTypeText } from '@src/pages/patrol/planDetail/detailCard';
import { AppealStatus } from '@src/pages/selfCheckComment/api.type';
import { getExamSituation, getExamSituationShop } from '@src/pages/tasks/api';
import { GlobalStore, PatrolCheckStore, SignStore, userStore } from '@src/store';
import { roleTypeIsManage } from '@src/utils/tokenUtils';
import { encrypt } from '@src/utils/utils';
import { H5Bridge } from '@tastien/rn-bridge';
import { useRequest } from 'ahooks';
import { Button, Dialog, Tabs, TextArea, type TextAreaProps, Toast } from 'antd-mobile';
import { RightOutline } from 'antd-mobile-icons';
import dayjs from 'dayjs';
import { toJS } from 'mobx';
import { observer } from 'mobx-react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import BasicInfo from './basicInfo';
import { BtnText, CurReformStatus, CurReportStatus, SignTypeText } from './enum';
import OperationBar from './OperationBar';
import PatrolSheetOptions from './patrolSheetOptions';
import PatrolSummaryMsg from './patrolSummaryMsg';
import TaskProgress from './taskProgress';
import { ComplaintsPopup } from '../self/ComplaintsPopup';

/**
 * 点评总结
 */
function ReviewSummary({
  title,
  ...textAreaProps
}: {
  title: string;
} & TextAreaProps) {
  return (
    <div className="bg-white p-4 mb-[10px]">
      <div className="text-base leading-[16px] mb-3 font-medium">{title}</div>
      <TextArea
        className="bg-[#FAFAFA] p-1"
        placeholder="请输入总结备注"
        style={{
          '--font-size': '14px',
          '--color': '#B8B8B8',
          height: '50px',
        }}
        maxLength={300}
        {...textAreaProps}
      />
    </div>
  );
}

export enum TabsTypeEnum {
  报告详情 = 'Detail',
  任务进度 = 'Progress',
}

export const TabList = [
  {
    key: TabsTypeEnum.报告详情,
    title: '报告详情',
  },
  {
    key: TabsTypeEnum.任务进度,
    title: '任务进度',
  },
];

export enum reportStatusEnum {
  待提交 = 'NOT_STARTED',
  已提交 = 'SUBMITTED',
  已确认 = 'CONFIRMED',
}

const ReportDetail = observer(() => {
  const { permissionsMap } = userStore;
  const [routerParams] = useSearchParams();
  const { getCorporationConfig } = GlobalStore;
  const { setReportSheetList, setReportDetailInfos, patrolSheetList, curPatrolSheetInfo, reportDetailInfos } =
    PatrolCheckStore;
  const [summaryText, setSummaryText] = useState<string>('');
  const [activeKey, setActiveKey] = useState<string>(TabsTypeEnum.报告详情);
  const [reviewContent, setReviewContent] = useState<string>('');
  const [secondReviewContent, setSecondReviewContent] = useState<string>('');
  const isDudao = roleTypeIsManage();
  const { isSuperManger, userInfo } = userStore;
  const { notFilledItemHandleType, taskId, hasPermission } = useMemo(() => {
    const notFilledItemHandleType = routerParams.get('notFilledItemHandleType');
    const taskId = routerParams.get('taskId');
    const hasPermission = routerParams.get('hasPermission');
    const signOut = routerParams.get('signOut');

    return {
      notFilledItemHandleType,
      taskId,
      hasPermission,
      signOut: !!signOut,
    };
  }, [routerParams]);
  console.log(toJS(curPatrolSheetInfo), '=curPatrolSheetInfo');

  // 是否可以撤回报告
  const canRevokeWorksheetRef = useRef(true);
  // 是否可以确认报告
  const canDetermineReportRef = useRef(true);

  const [complaintsPopupVisible, setComplaintsPopupVisible] = useState(false);

  // 可申诉项-相对于单个表
  const complaintsItems = useMemo(() => {
    const _complaintsItems: WeekSheet['children'][] = [];

    curPatrolSheetInfo?.children?.forEach((c: any) => {
      c?.children?.forEach((i: any) => {
        if (i?.hasApply && !i?.qualified && i?.appealStatus === AppealStatus.未申诉) {
          _complaintsItems.push(i);
        }
      });
    });
    return _complaintsItems;
  }, [curPatrolSheetInfo]);

  useEffect(() => {
    const hasInvalidAppealStatus = curPatrolSheetInfo?.children?.some((c: any) =>
      c?.children?.some((i: any) => i?.appealStatus === AppealStatus.发起),
    );
    canDetermineReportRef.current = !hasInvalidAppealStatus;

    const _hasInvalidAppealStatus = curPatrolSheetInfo?.children?.some((c: any) =>
      c?.children?.some((i: any) =>
        [AppealStatus.发起, AppealStatus.通过, AppealStatus.驳回].includes(i?.appealStatus),
      ),
    );
    canRevokeWorksheetRef.current = !_hasInvalidAppealStatus;
  }, [curPatrolSheetInfo]);

  const navigate = useNavigate();
  // // 食安稽核到店辅导-点评按钮
  // const { permissionsMap } = userStore;
  // const enableReportButton = permissionsMap.has(AuthorityEnum.食安稽核到店辅导点评);

  const {
    data: detailData,
    loading: detailLoading,
    run: detailRun,
  } = useRequest(
    async () => {
      const res = await queryPatrolReportDetail({
        taskId: +taskId! as number,
        notFilledItemHandleType: notFilledItemHandleType || undefined,
        filterHasApply: true,
      });
      if (res?.subType === patrolTypeText.食安稽核到店辅导) {
        runExamData();
      }
      setReportDetailInfos(res);
      setReportSheetList(res?.appPatrolReport?.data || []);
      setReviewContent(res?.reportOverallReview || '');
      setSecondReviewContent(res?.reportOverallSecondReview || '');

      return res;
    },
    { refreshDeps: [taskId] },
  );

  const { data: config, loading: configLoading } = useRequest(
    async () => {
      return await getCorporationConfig();
    },
    { refreshDeps: [] },
  );

  const { data: ExamData, run: runExamData } = useRequest(
    async () => {
      return isDudao ? await getExamSituation(taskId!) : await getExamSituationShop(taskId!);
    },
    { manual: true },
  );

  useEffect(() => {
    detailData?.summaryText && setSummaryText(detailData.summaryText);
  }, [detailData]);

  const dealReport = (
    opera: ButtonEnums,
    /**
     * 是否为第二点评
     */
    isSecondReview = false,
  ) => {
    const goOpera: any = {
      MODIFY_SCORE: () => {
        // 修改打分
        navigate(`/patrol/shopcheck?taskId=${detailData?.taskId}`);
      },
      SUBMIT_REPORT: () => {
        if (!summaryText) {
          Toast.show({
            content: '输入巡店总结后再提交哦!',
          });
          return;
        }
        // 提交报告
        const hasSigned =
          detailData?.signType === SignTypeText.签到签离 ||
          (detailData?.signType === SignTypeText.跟随系统 && config.MUST_CHECK_IN === '2') ||
          !!detailData?.cross;
        if (hasSigned && !detailData?.leaveTime && detailData?.subType !== patrolTypeText.视频云巡检) {
          // 权限外任务必须签离
          Dialog.confirm({
            content: <div className="text-center text-[#141414] text-base">需签离才能提交报告,是否继续？</div>,
            onConfirm: async () => {
              const allInfo: any = {
                ...SignStore.queryTaskShopInfo,
                isSubmitSignOutReport: true,
                taskId: +taskId!,
                summaryText,
                signed: 1, // 签离
                notFilledItemHandleType: notFilledItemHandleType || 'SET_FULL_SCORE',
              };
              await SignStore.setTaskShopInfo(allInfo);
              // navigate(`/patrol/plan/sign`);
              const info = {
                path: 'SupervisorCheckout',
                isSubmitSignOutReport: true,
                shopId: allInfo.shopId,
                taskId: allInfo.taskId,
                summaryContext: summaryText || '',
                notFilledItemHandleType: notFilledItemHandleType || 'SET_FULL_SCORE',
                callBackUrl: `/patrol/reportdetail?taskId=${allInfo.taskId}&signOut=true`,
              };
              await H5Bridge.customPostMessage.customPostMessage({
                module: 'face-module',
                method: 'navigateTo',
                params: {
                  param: encrypt(info),
                },
              });
            },
          });
        } else {
          reportOpera('SUBMIT_REPORT');
        }
      },
      CONFIRM_REPORT: async () => {
        if (!canDetermineReportRef.current) {
          Toast.show({
            content: '报告申诉处理中，无法确认报告',
          });
          return;
        }
        Dialog.confirm({
          content: (
            <div className="text-center text-[#141414] text-base">
              确认报告表示你对报告中各项检查结果完全同意。报告被确认后将无法进行修改。
            </div>
          ),
          onConfirm: async () => {
            await confirmReport(+taskId!);
            Toast.show({
              icon: 'success',
              content: '已确认报告',
            });
            await detailRun();
          },
        });
      },
      REJECT_REPORT: async () => {
        Dialog.confirm({
          content: (
            <div className="text-center text-[#141414] text-base">
              如果你对报告内容有疑义，可联系报告提交人。让报告提交人进行撤回
            </div>
          ),
          onConfirm: async () => {},
        });
      },
      RECALL_REPORT: () => {
        if (!canRevokeWorksheetRef.current) {
          Toast.show({
            content: '该报告已发起过申诉，无法进行撤回操作',
          });
          return;
        }
        // 撤回报告
        Dialog.confirm({
          content: (
            <div className="text-center text-[#141414] font-medium text-base">
              确认撤回报告吗？撤回的报告可在修改后重新提交。
            </div>
          ),
          onConfirm: () => {
            cancelReport({ id: +taskId! }).then(() => {
              Toast.show({
                icon: 'success',
                content: '撤回成功',
              });
              detailRun();
            });
          },
        });
      },
      ADMIN_RECALL_REPORT: () => {
        // 超管撤回报告
        Dialog.confirm({
          content: (
            <div className="text-center text-[#141414] font-medium text-base">撤回后门店将会需要重新提交整改反馈</div>
          ),
          onConfirm: () => {
            cancelReport({ id: +taskId! }).then(() => {
              Toast.show({
                icon: 'success',
                content: '撤回成功',
              });
              detailRun();
            });
          },
        });
      },
      REVIEW: () => {
        navigate(`/patrol/reportdetail/review?taskId=${taskId}`, {
          state: { checklists: patrolSheetList || [] },
        });
      },
      SUBMIT_PATROL_REVIEW: () => {
        let unReviewCount = 0;

        patrolSheetList?.forEach((v) => {
          // 是否没有超时效
          const isNoOverTime = isSecondReview
            ? !!reportDetailInfos?.secondReviewDeadline
              ? dayjs() < dayjs(reportDetailInfos.secondReviewDeadline)
              : true
            : !!v?.reportReviewDeadline
              ? dayjs() < dayjs(v.reportReviewDeadline)
              : true;

          const groups = v?.children || [];

          for (let j = 0, len = groups.length; j < len; j++) {
            const items = groups[j].children || [];

            for (const item of items) {
              // 未点评项
              const notReviewItem =
                // 有点评权限
                item?.hasReviewPermission &&
                // 未点评过
                typeof item?.reportReviewInfo?.qualified !== 'boolean' &&
                // 过滤无需点评项
                item?.reportReviewInfo?.status !== 'NO_NEEDED_REVIEW';

              // 第二点评未点评项
              const secondNotReviewItem =
                // 有点评权限
                reportDetailInfos?.hasSecondReviewPermission &&
                // 未点评过
                typeof item?.reportSecondReviewInfo?.qualified !== 'boolean' &&
                // 过滤无需点评项
                item?.reportSecondReviewInfo?.status !== 'NO_NEEDED_REVIEW';

              if (
                /* isNoOverTime &&
                item?.hasReviewPermission &&
                !(typeof item?.reportReviewInfo?.qualified === 'boolean') */
                isNoOverTime &&
                (isSecondReview ? secondNotReviewItem : notReviewItem)
              ) {
                unReviewCount++;
              }
            }
          }
        });

        if (unReviewCount > 0) {
          Toast.show({
            content: `有${unReviewCount}个项未点评，请点评完整后再提交。`,
          });
          return;
        }

        // 点评项提交
        const reviewItemSubmit = isSecondReview ? patrolReportSecondReviewSubmit : patrolReportReviewSubmit;

        reviewItemSubmit({
          taskId: +taskId!,
          overallReviewContent: isSecondReview ? undefined : reviewContent,
          secondOverallReviewContent: isSecondReview ? secondReviewContent : undefined,
        }).then(() => {
          Toast.show({
            icon: 'success',
            content: '点评成功',
          });
          detailRun();
        });
      },
    };
    goOpera[opera]();
  };

  // 提交报告
  const reportOpera = async (type: ButtonEnums) => {
    const operaRequest: any = {
      SUBMIT_REPORT: () => {
        submitPatrolReport({
          id: +taskId!,
          summaryContext: summaryText,
          notFilledItemHandleType: notFilledItemHandleType || 'SET_FULL_SCORE',
        }).then(() => {
          Toast.show({
            icon: 'success',
            content: '提交成功',
          });
          detailRun();
        });
      },
    };
    await operaRequest[type]();
  };

  const isShowOperationBar = useMemo(() => {
    return (
      (!isDudao && detailData?.reformStatus === CurReformStatus.待整改) ||
      (isDudao && detailData?.reformStatus === CurReformStatus.待审核)
    );
  }, [detailData?.reformStatus, isDudao]);

  const showOprBtns = useMemo(() => {
    let buttonEnums: ButtonEnums[] = [];

    const { reportStatus, hasNeedReview, reviewStatus, submitUserId } = detailData || {};

    if (isDudao) {
      // 督导
      if (reportStatus === reportStatusEnum.待提交) {
        buttonEnums = ['MODIFY_SCORE', 'SUBMIT_REPORT'];
      }
      if (
        (reportStatus === reportStatusEnum.已提交 && (submitUserId === userInfo?.userId || isSuperManger)) ||
        (isSuperManger && reportStatus === reportStatusEnum.已确认)
      ) {
        buttonEnums = ['RECALL_REPORT'];
      }
      if (reportStatus === reportStatusEnum.已确认 && reviewStatus !== 'REVIEWED') {
        !curPatrolSheetInfo?.existNeedReviewItem
          ? buttonEnums.push('NO_NEED_REVIEW') // 无需点评;
          : curPatrolSheetInfo?.hasReviewPermission // 有点评权限
            ? !!curPatrolSheetInfo?.reportReviewDeadline && dayjs() > dayjs(curPatrolSheetInfo?.reportReviewDeadline)
              ? buttonEnums.push('SUBMIT_REPORT_COMMENT_DISABLE') // 未按时完成，点评已关闭
              : hasNeedReview
                ? buttonEnums.push('SUBMIT_PATROL_REVIEW') // 提交点评
                : buttonEnums.push()
            : buttonEnums.push('NO_AUTHORITY_SUBMIT'); // 无点评权限;
      }

      // 第二点评相关
      if (
        // 第一点评完后
        reportStatus === reportStatusEnum.已确认 &&
        reviewStatus === 'REVIEWED'
      ) {
        // 无需点评
        if (!reportDetailInfos?.hasNeedSecondReview) {
          buttonEnums.push('NO_NEED_REVIEW');
          // 第二点评权限
        } else if (reportDetailInfos?.hasSecondReviewPermission) {
          // 未按时完成 点评已关闭
          if (!!reportDetailInfos?.secondReviewDeadline && dayjs() > dayjs(reportDetailInfos?.secondReviewDeadline)) {
            buttonEnums.push('SUBMIT_REPORT_COMMENT_DISABLE');
          }
          // 需要第二点评 提交点评
          if (reportDetailInfos?.hasNeedSecondReview) {
            buttonEnums.push('SUBMIT_PATROL_REVIEW');
          }
        } else {
          // 无点评权限
          buttonEnums.push('NO_AUTHORITY_SUBMIT');
        }
      }
    } else {
      // 店长
      if (reportStatus === reportStatusEnum.已提交) {
        buttonEnums = ['REJECT_REPORT', 'CONFIRM_REPORT'];
      }
    }
    return buttonEnums;
  }, [detailData, isDudao, isSuperManger, curPatrolSheetInfo, reportDetailInfos]);

  return (
    <Loading spinning={detailLoading || configLoading}>
      <div className="flex flex-col fixed inset-x-0 inset-y-0">
        {detailData?.subType === patrolTypeText.食安稽核到店辅导 && (
          <div className="bg-white">
            <Tabs
              style={{ '--title-font-size': '1rem' }}
              activeKey={activeKey}
              onChange={(key) => {
                setActiveKey(key);
              }}
            >
              {TabList.map(({ key, title }) => (
                <Tabs.Tab title={title} key={key} />
              ))}
            </Tabs>
          </div>
        )}
        {activeKey === TabsTypeEnum.任务进度 && <TaskProgress taskId={+taskId!} />}
        {activeKey === TabsTypeEnum.报告详情 && (
          <React.Fragment>
            <div className="grow overflow-y-scroll">
              <BasicInfo detail={detailData!} />
              {detailData?.reportStatus === CurReportStatus.已确认 && !!detailData?.issuesCount && (
                <div className="bg-white p-4 mt-[10px]">
                  <div
                    className="flex"
                    onClick={() => {
                      navigate(
                        `/tasks/pollingAbarbeitung?taskId=${detailData?.taskId}&pollingType=NormalPolling&cross=${hasPermission}`,
                      );
                    }}
                  >
                    <div className="flex-1 text-base">共{detailData?.noReformCount}个整改项</div>

                    <RightOutline className="text-[#c5c5c5] mt-1 text-sm " />
                  </div>
                  {!detailData?.cross && isShowOperationBar && !!detailData?.noReformCount && (
                    <div>
                      <OperationBar
                        pollingType="NormalPolling"
                        submitTime={
                          detailData?.operateList?.find((v) => v.operateType === 'REFORM_SUBMIT')?.operateTime
                        }
                        count={detailData?.waitRectifiedCount}
                        isDudao={isDudao}
                        taskId={detailData?.taskId}
                      />
                    </div>
                  )}
                </div>
              )}
              <div className="my-[10px]">
                <PatrolSummaryMsg config={config} detail={detailData!} />
              </div>
              <div className="bg-white p-4 mb-[10px]">
                <div className="text-base leading-[16px] mb-3 font-medium">总结</div>
                <TextArea
                  className="bg-[#FAFAFA] p-1"
                  disabled={detailData?.reportStatus !== reportStatusEnum.待提交}
                  value={summaryText}
                  onChange={(v) => {
                    setSummaryText(v);
                  }}
                  placeholder="请输入"
                  style={{
                    '--font-size': '14px',
                    '--color': '#B8B8B8',
                    height: '50px',
                  }}
                  maxLength={300}
                />
                {detailData?.subType === patrolTypeText.食安稽核到店辅导 && (
                  <React.Fragment>
                    <div className="text-base leading-[16px] my-3 font-medium">考试情况确认</div>
                    <div className="text-sm my-1 flex items-center">
                      <span>门店是否都已完成并通过考试？</span>
                      <div className="text-sm text-primary">{ExamData?.confirm ? '已确认' : '未确认'}</div>
                    </div>
                    {ExamData?.remark && (
                      <div>
                        <div className="mb-1">备注说明：</div>
                        <div className="bg-[#FAFAFA] my-1 p-1">
                          <div className="break-all text-[#B8B8B8]">{ExamData?.remark || ''}</div>
                        </div>
                      </div>
                    )}
                  </React.Fragment>
                )}
              </div>
              {((detailData?.hasNeedReview && isDudao) || !!reviewContent) && (
                <ReviewSummary
                  title="点评总结"
                  value={reviewContent}
                  disabled={!detailData?.hasNeedReview || !isDudao}
                  onChange={(v) => {
                    setReviewContent(v);
                  }}
                />
              )}
              {((detailData?.hasNeedSecondReview && isDudao) || !!secondReviewContent) && (
                <ReviewSummary
                  title="第二点评总结"
                  value={secondReviewContent}
                  disabled={!detailData?.hasNeedSecondReview || !isDudao}
                  onChange={(v) => {
                    setSecondReviewContent(v);
                  }}
                />
              )}
              <div>
                {!!patrolSheetList.length && <PatrolSheetOptions workSheetList={patrolSheetList} refresh={detailRun} />}
              </div>
            </div>
            {showOprBtns?.length !== 0 && (
              <div className="shrink-0">
                <div className="py-2 px-2 bg-[#fff]">
                  <div className="flex justify-between gap-2">
                    {permissionsMap.has(AuthorityEnum.申诉) &&
                      !roleTypeIsManage() &&
                      curPatrolSheetInfo?.hasCanAppeal &&
                      !!complaintsItems?.length && (
                        <Button
                          block
                          color="primary"
                          onClick={() => {
                            setComplaintsPopupVisible(true);
                          }}
                        >
                          申诉
                        </Button>
                      )}
                    {showOprBtns
                      // 原逻辑复杂，先过滤掉申诉按钮自行维护
                      ?.filter((v) => v !== 'REJECT_REPORT')
                      ?.map((item, index) => {
                        return (
                          <Button
                            block
                            key={index}
                            disabled={[
                              'SUBMIT_REPORT_COMMENT_DISABLE',
                              'NO_AUTHORITY_SUBMIT',
                              'NO_NEED_REVIEW',
                              'DISABLE_CONFIRM_REPORT',
                            ]?.includes(item)}
                            color="primary"
                            onClick={() => {
                              dealReport(
                                item as ButtonEnums,
                                // 是否为第二点评
                                reportDetailInfos?.reportStatus === reportStatusEnum.已确认 &&
                                  reportDetailInfos?.reviewStatus === 'REVIEWED' &&
                                  reportDetailInfos?.hasNeedSecondReview &&
                                  showOprBtns.includes('SUBMIT_PATROL_REVIEW'),
                              );
                            }}
                          >
                            {BtnText[item as ButtonEnums]}
                          </Button>
                        );
                      })}
                  </div>
                </div>
              </div>
            )}
          </React.Fragment>
        )}
      </div>

      <ComplaintsPopup
        visible={complaintsPopupVisible}
        onClose={() => setComplaintsPopupVisible(false)}
        complaintsItems={complaintsItems}
        taskId={taskId!}
        reFresh={detailRun}
        isPatrol={true}
      />
    </Loading>
  );
});
export default ReportDetail;
