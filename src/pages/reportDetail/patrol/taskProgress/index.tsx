import React, { ReactNode } from 'react';
import { Loading } from '@src/components';
import { getCompanyWorkProgress, getShopWorkProgress } from '@src/pages/patrol/api';
import { roleTypeIsManage } from '@src/utils/tokenUtils';
import { useRequest } from 'ahooks';
import { Steps, Tag } from 'antd-mobile';
import { Step } from 'antd-mobile/es/components/steps/step';
import dayjs from 'dayjs';
interface PROGRESS {
  taskId: number;
}

// 食安稽核到店任务进度类型
export enum TutorProcessShopType {
  CREATED = 'CREATED',
  FOOD_SAFETY_ARRIVE_SHOP_CONFIRM = 'FOOD_SAFETY_ARRIVE_SHOP_CONFIRM',
  FOOD_SAFETY_ARRIVE_SHOP_EXE_PERIOD_AUDIT = 'FOOD_SAFETY_ARRIVE_SHOP_EXE_PERIOD_AUDIT',
  TASK_SIGN_IN = 'TASK_SIGN_IN',
  FOOD_SAFETY_ARRIVE_SHOP_CONFIRM_EXAM_PASS = 'FOOD_SAFETY_ARRIVE_SHOP_CONFIRM_EXAM_PASS',
  SUBMIT = 'SUBMIT',
  FOOD_SAFETY_ARRIVE_SHOP_OVERDUE_NOT_EXE_TASK_TRANSFER = 'FOOD_SAFETY_ARRIVE_SHOP_OVERDUE_NOT_EXE_TASK_TRANSFER',
  TASK_EXPIRED = 'TASK_EXPIRED',
  FOOD_SAFETY_ARRIVE_SHOP_CANCEL_CONFIRM_EXAM_PASS = 'FOOD_SAFETY_ARRIVE_SHOP_CANCEL_CONFIRM_EXAM_PASS',
}

// 食安稽核到店任务进度类型
export enum TutorArrivalTimeType {
  _24_HOURS_ARRIVE_SHOP = '_24_HOURS_ARRIVE_SHOP',
  _48_HOURS_ARRIVE_SHOP = '_48_HOURS_ARRIVE_SHOP',
  OVER_48_HOURS_ARRIVE_SHOP = 'OVER_48_HOURS_ARRIVE_SHOP',
  _72_HOURS_ARRIVE_SHOP = '_72_HOURS_ARRIVE_SHOP',
  OVER_72_HOURS_ARRIVE_SHOP = 'OVER_72_HOURS_ARRIVE_SHOP',
}

// 食安稽核到店任务进度审核状态
export enum TutorAuditStatus {
  AGREE = 'AGREE',
  REJECTED = 'REJECTED',
}
const TutorProcessShopTypeTitleCN: Record<string, string> = {
  [TutorProcessShopType.CREATED]: '创建任务',
  [TutorProcessShopType.FOOD_SAFETY_ARRIVE_SHOP_CONFIRM]: '确定到店时间',
  [TutorArrivalTimeType._48_HOURS_ARRIVE_SHOP]: '任务执行时段修改申请审核',
  [TutorArrivalTimeType.OVER_48_HOURS_ARRIVE_SHOP]: '修改任务执行人',
  [TutorArrivalTimeType.OVER_72_HOURS_ARRIVE_SHOP]: '修改任务执行人',
  [TutorProcessShopType.TASK_SIGN_IN]: '开始执行任务',
  [TutorProcessShopType.FOOD_SAFETY_ARRIVE_SHOP_CONFIRM_EXAM_PASS]: '确认考试通过',
  [TutorProcessShopType.SUBMIT]: '提交报告',
  [TutorProcessShopType.FOOD_SAFETY_ARRIVE_SHOP_OVERDUE_NOT_EXE_TASK_TRANSFER]: '超时未执行任务转交',
  [TutorProcessShopType.TASK_EXPIRED]: '超时未完成任务过期',
  [TutorProcessShopType.FOOD_SAFETY_ARRIVE_SHOP_CANCEL_CONFIRM_EXAM_PASS]: '取消确认考试通过',
};

const TutorArrivalTimeContext: Record<TutorArrivalTimeType, string> = {
  [TutorArrivalTimeType._24_HOURS_ARRIVE_SHOP]: '确认24H能到店',
  [TutorArrivalTimeType._48_HOURS_ARRIVE_SHOP]: '确认48H能到店',
  [TutorArrivalTimeType.OVER_48_HOURS_ARRIVE_SHOP]: '确认超出48H能到店',
  [TutorArrivalTimeType._72_HOURS_ARRIVE_SHOP]: '确认72H能到店',
  [TutorArrivalTimeType.OVER_72_HOURS_ARRIVE_SHOP]: '确认超出72H能到店',
};

const TutorAuditStatusCN: Record<TutorAuditStatus, string> = {
  [TutorAuditStatus.AGREE]: '同意',
  [TutorAuditStatus.REJECTED]: '驳回',
};

const renderTitle = (title: string, createTime: string) => {
  return (
    <div className="flex justify-between items-start">
      <span>{title}</span>
      <span className="whitespace-nowrap">{dayjs(createTime).format('YYYY/MM/DD HH:mm')}</span>
    </div>
  );
};
const TutorProcessShopTypeFn: Record<string, (data: any) => ReactNode> = {
  [TutorProcessShopType.CREATED]: (data) => {
    const { operatorUserName, remark, operatorUserId } = data;
    const json = remark ? JSON.parse(remark) : {};

    return (
      <p className="text-[#999999]">
        {!operatorUserId ? '系统 自动' : operatorUserName}创建了任务
        <br />
        巡检人：{json?.taskTreatedUserName}
      </p>
    );
  },
  [TutorProcessShopType.FOOD_SAFETY_ARRIVE_SHOP_CONFIRM]: (data) => {
    const { operatorUserName, remark, operatorUserId } = data;
    const json = remark ? JSON.parse(remark) : {};

    return (
      <p className="text-[#999999]">
        {!operatorUserId ? '系统 自动' : operatorUserName}
        {TutorArrivalTimeContext[json?.exePeriodConfirmType as TutorArrivalTimeType]}
      </p>
    );
  },
  [TutorProcessShopType.FOOD_SAFETY_ARRIVE_SHOP_EXE_PERIOD_AUDIT]: (data) => {
    const { operatorUserName, remark, operatorUserId, createTime } = data;
    console.log(data, '=data');

    const json = remark ? JSON.parse(remark) : {};

    return (
      <div>
        {renderTitle(TutorProcessShopTypeTitleCN[json?.applyType], createTime)}
        {[TutorArrivalTimeType.OVER_48_HOURS_ARRIVE_SHOP, TutorArrivalTimeType.OVER_72_HOURS_ARRIVE_SHOP].includes(
          json?.applyType,
        ) ? (
          <p className="text-[#999999]">
            {!operatorUserId ? '系统 自动' : operatorUserName}修改了任务执行人：由{json?.beforeUserName}调整为
            {json?.afterUserName}
          </p>
        ) : (
          <p className="text-[#999999] text-xs">
            {!operatorUserId ? '系统 自动' : operatorUserName}
            {TutorAuditStatusCN[json?.auditStatus as TutorAuditStatus]}任务执行时段调整为48H的申请
            {json?.overdue && <Tag color="danger">超时审核</Tag>}
          </p>
        )}
      </div>
    );
  },
  [TutorProcessShopType.TASK_SIGN_IN]: (data) => {
    const { operatorUserName, operatorUserId } = data;

    return <p className="text-[#999999]">{!operatorUserId ? '系统 自动' : operatorUserName}开始了任务（签到时间）</p>;
  },
  [TutorProcessShopType.FOOD_SAFETY_ARRIVE_SHOP_CONFIRM_EXAM_PASS]: (data) => {
    const { operatorUserName, operatorUserId } = data;

    return <p className="text-[#999999]">{!operatorUserId ? '系统 自动' : operatorUserName}确认了门店都已完成考试</p>;
  },
  [TutorProcessShopType.SUBMIT]: (data) => {
    const { operatorUserName, operatorUserId } = data;

    return <p className="text-[#999999]">{!operatorUserId ? '系统 自动' : operatorUserName}提交了报告</p>;
  },
  [TutorProcessShopType.FOOD_SAFETY_ARRIVE_SHOP_OVERDUE_NOT_EXE_TASK_TRANSFER]: (data) => {
    const { remark } = data;
    const json = remark ? JSON.parse(remark) : {};

    return (
      <p className="text-[#999999]">
        {json?.beforeUserName}超时未完成任务，系统自动转给{json?.afterUserName}
      </p>
    );
  },
  [TutorProcessShopType.TASK_EXPIRED]: (data) => {
    const { operatorUserName } = data;

    return <p className="text-[#999999]">{operatorUserName}超时未完成，任务已过期</p>;
  },
  [TutorProcessShopType.FOOD_SAFETY_ARRIVE_SHOP_CANCEL_CONFIRM_EXAM_PASS]: (data) => {
    const { operatorUserName } = data;

    return <p className="text-[#999999]">{operatorUserName}取消确认门店都已完成考试</p>;
  },
};

const TaskProgress: React.FC<PROGRESS> = (props: PROGRESS) => {
  const isDudao = roleTypeIsManage();
  const { taskId } = props || {};
  const { data, loading } = useRequest(
    async () => {
      if (taskId) {
        const res = isDudao ? await getCompanyWorkProgress(taskId) : await getShopWorkProgress(taskId);
        return res;
      }

      return [];
    },
    { refreshDeps: [taskId, isDudao] },
  );

  return (
    <Loading spinning={loading}>
      <div className="bg-white text-sm font-medium m-2 p-4 overflow-auto">
        <Steps direction="vertical">
          {data?.map((item) => {
            const { createTime, type } = item;
            const title =
              type === TutorProcessShopType.FOOD_SAFETY_ARRIVE_SHOP_EXE_PERIOD_AUDIT
                ? TutorProcessShopTypeFn[type]?.(item)
                : renderTitle(TutorProcessShopTypeTitleCN[type], createTime);
            const description =
              type !== TutorProcessShopType.FOOD_SAFETY_ARRIVE_SHOP_EXE_PERIOD_AUDIT
                ? TutorProcessShopTypeFn[type]?.(item)
                : undefined;
            return <Step title={title} description={description} status="finish" />;
          })}
        </Steps>
      </div>
    </Loading>
  );
};
export default TaskProgress;
