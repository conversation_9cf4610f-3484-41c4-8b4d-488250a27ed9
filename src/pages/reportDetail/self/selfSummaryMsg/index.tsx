import { SelfReportDetail } from '@src/pages/selfCheckComment/api.type';
import ReactECharts, { EChartsOption } from 'echarts-for-react';
// import _ from 'lodash';
import { toJS } from 'mobx';
import styles from './index.module.scss';
import KindsMsg from '../kindsMsg';

type ChartOptionsProps = {
  title: string;
  count: number | string;
  color: string;
  unit: string;
};

interface SUMMARY {
  config: any;
  detail: SelfReportDetail;
}

const SelfSummaryMsg: React.FC<SUMMARY> = (props: SUMMARY) => {
  const { detail, config } = props;
  const { statistics } = detail || {};

  console.log(toJS(detail), '=detail');

  console.log(toJS(statistics), '=statistics');
  console.log(toJS(config), '=config');

  // 根据后台设置判断，检查项合格率（item: 默认）、分类合格率（category）
  const REPORT_SHOW_PASS_RATE =
    config?.REPORT_SHOW_PASS_RATE === 'category' ? 'category' : 'pass';

  const valid =
    REPORT_SHOW_PASS_RATE === 'category'
      ? detail?.qualifiedCategoryCount
      : detail?.qualifiedItemCount;
  const total =
    REPORT_SHOW_PASS_RATE === 'category'
      ? detail?.categoryCount
      : detail?.checkItemCount;
  const validText =
    REPORT_SHOW_PASS_RATE === 'category' ? '合格分类' : '合格项';

  // const content = useMemo(() => {
  //   const reportText =
  //     (detail?.data || [])[0]?.importance === null
  //       ? '报告得分 = 所有检查表的实际得分/所有检查表的满分 * 100'
  //       : '报告得分 = 检查表1的百分制得分 * 权重1 + 检查表2的百分制得分 * 权重2 + …\n 其中，检查表的百分制得分 = 所有检查项目得分之和/所有检查项满分 * 100';

  //   const rateText =
  //     config?.REPORT_SHOW_PASS_RATE !== 'category'
  //       ? '合格率 = 合格的检查项数目/总的检查项数目'
  //       : '合格率 = 满分的分类数目/总的分类数目';

  //   return (
  //     <div className="text-left">
  //       <div>报告得分：</div>
  //       <div className="mb-4">{reportText}</div>
  //       <div>合格率：</div>
  //       <div className="mb-4">{rateText}</div>
  //       <div className="text-sm">注1：若存在S项，得分按相应逻辑调整，但合格率计算不受影响。</div>
  //     </div>
  //   );
  // }, [detail, config?.REPORT_SHOW_PASS_RATE]);

  const ChartOptions: React.FC<ChartOptionsProps> = ({
    title,
    count,
    color,
    unit,
  }) => {
    const option: EChartsOption = {
      color: [color, '#f0f2f5'],
      series: [
        {
          name: title,
          type: 'pie',
          radius: ['85%', '100%'],
          silent: true,
          itemStyle: {
            // borderColor: '#fff',
            // borderWidth: 16,
          },
          label: {
            show: false,
            position: 'center',
          },
          data: [{ value: count }, { value: 100 - count }],
        },
      ],
    };

    return (
      <div className={styles.chart}>
        <ReactECharts option={option} style={{ height: 120 }} />
        <div className={styles.title}>
          <div className="text-xs text-[#5E5E5E] mb-2">{title}</div>
          <div className={styles.content}>
            {unit === '分' ? count?.toFixed(2) : `${count}${unit}`}
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="leading-[14px] ">
      <div className="bg-white  flex justify-between  items-center">
        <div
          className="text-base p-4 leading-[16px]"
          style={{ fontWeight: 500 }}
        >
          汇总&nbsp;
          {/* <Tooltip 
          placement="bottom" 
          title={content}
          >
            <IconFont type="icon-changjianwentixiangguanwenti" style={{ color: '#9C9C9C' }} />
          </Tooltip> */}
        </div>
        <div className="pr-4  text-xs text-gray-400">
          得分已自动折算为百分制
        </div>
      </div>
      <div className="pl-4 pr-4 bg-white flex justify-between  items-center">
        <div className="w-full flex flex-col items-center">
          <div className="w-full">
            <ChartOptions
              title="报告得分"
              color="#378BFF"
              count={detail?.score || 0}
              unit="分"
            />
          </div>

          <div className="mt-4">
            实际分数
            {detail?.actualScore}/{detail?.fullScore}
          </div>
        </div>
        <div className="w-full flex flex-col  items-center">
          <div className="w-full">
            <ChartOptions
              title="合格率"
              color="#378BFF"
              count={total ? ((valid / total) * 100).toFixed(0) : 100}
              unit="%"
            />
          </div>
          <div className="mt-4">
            {validText}
            {valid}/{total}
          </div>
        </div>
      </div>
      <div className="bg-white px-4 pb-4 pt-2">
        <KindsMsg statistics={statistics} config={config} />
      </div>
    </div>
  );
};

export default SelfSummaryMsg;
