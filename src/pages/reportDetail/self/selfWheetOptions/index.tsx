import React, { useEffect, useMemo, useState } from 'react';
import AuthorityEnum from '@src/common/authority';
import { IconFont, Loading } from '@src/components';
import { IPopup } from '@src/components/IPopup';
import SopPopup from '@src/components/OptionsDetail/SopPopup';
import StandardPopup from '@src/components/OptionsDetail/StandardPopup';
import { AppealStatus, CategoryItem, CheckItem, SelfReportDetail } from '@src/pages/selfCheckComment/api.type';
import { Report_STATUS_TEXT } from '@src/pages/selfCheckComment/enum';
import SelfCheckItemCard from '@src/pages/selfCheckComment/selfCheckList/selfCheckItemCard';
import { hasAppealOperate } from '@src/pages/selfCheckComment/selfCheckList/utils';
import ValidityCheckItemCard from '@src/pages/selfCheckComment/selfCheckList/validityCheckItemCard';
import { SelfCheckStore, userStore } from '@src/store';
import { roleTypeIsManage } from '@src/utils/tokenUtils';
import { message } from 'antd';
import { Badge, Checkbox, Collapse, Dialog, Selector, SideBar, Tabs } from 'antd-mobile';
import cn from 'classnames';
import dayjs from 'dayjs';
import { toJS } from 'mobx';
import { observer } from 'mobx-react';
import { useSearchParams } from 'react-router-dom';
import styles from './index.module.scss';
import { revokeAppeal, revokeAppealManage } from '../api';
import { ComplaintsDescPopup } from '../ComplaintsDescPopup';

interface Props {
  selfWorkSheet: SelfReportDetail;
  setactiveWorksheetId: any;
  reFresh?: () => void;
}

const SelfWheetOptions: React.FC<Props> = observer((props: Props) => {
  const { worksheets } = props?.selfWorkSheet || {};
  const { setactiveWorksheetId, reFresh } = props;
  const { setCurCheckSheet, hasAppeal, setHasAppeal } = SelfCheckStore;
  const [groupId, setGroupId] = useState(-1);
  const [tabPopupVisible, setTabPopupVisible] = useState(false);
  const [sopPopup, setSopPopup] = useState<{
    visible: boolean;
    sopId: number | undefined;
  }>({
    visible: false,
    sopId: undefined,
  });
  const [standardPopup, setStandardPopup] = useState<{
    visible: boolean;
    standard: any;
  }>({
    visible: false,
    standard: undefined,
  });
  const [curSheetId, setCurSheetId] = useState<number>();
  const [complainable, setComplainable] = useState<boolean>(false);
  const curWorkSheet = useMemo(() => {
    return worksheets?.find((item) => +item?.taskWorksheetId === +curSheetId!);
  }, [curSheetId, worksheets]);
  useEffect(() => {
    setactiveWorksheetId(curWorkSheet?.worksheetId);
  }, [curWorkSheet?.worksheetId]);

  const { permissionsMap } = userStore;

  const [routerParams] = useSearchParams();
  const { taskId } = useMemo(() => {
    const taskId = routerParams.get('taskId');
    return {
      taskId,
    };
  }, [routerParams]);

  const [complaintsPopupVisible, setComplaintsPopupVisible] = useState(false);
  const [appealId, setAppealId] = useState<number>();

  const [isOPenLeft, setIsOPenLeft] = useState(true);
  const [collapseKeys, setCollapseKeys] = useState<string[]>([]);
  const isDudao = roleTypeIsManage();

  useEffect(() => {
    // 第一张检查表没权限的话 切到其他有权限的表
    const perMission = worksheets?.find((i) => {
      return i?.reviewPermission && i?.status !== Report_STATUS_TEXT.已点评;
    });
    perMission ? setCurSheetId(perMission?.taskWorksheetId) : setCurSheetId(worksheets?.[0]?.taskWorksheetId);
  }, [worksheets?.[0]?.taskWorksheetId]);

  const list = useMemo(() => {
    if (!curWorkSheet) return [];
    setCurCheckSheet(curWorkSheet);
    const groups = (toJS(curWorkSheet.categories) || []).filter((v) =>
      groupId === -1 ? v.categoryId !== 0 : v.categoryId === groupId,
    );
    const data: Array<(CheckItem & { type: 'item' }) | (CategoryItem & { type: 'group' })> = [];
    for (let i = 0, len = groups.length; i < len; i++) {
      data.push({ ...groups[i], type: 'group' });
    }
    return data;
  }, [curWorkSheet, groupId]);

  useEffect(() => {
    const init: any = curWorkSheet?.categories?.map((v) => {
      return `${v?.categoryId}`;
    });
    setCollapseKeys(init);
  }, [curWorkSheet]);

  useEffect(() => {
    return () => {
      setHasAppeal(false);
    };
  }, []);

  const BadgeColor = (status: string, reviewPrescription?: string, submitted?: boolean) => {
    let color = '';
    if (isDudao) {
      if (status === Report_STATUS_TEXT.已点评) {
        color = 'green';
      } else {
        if (!!reviewPrescription && dayjs() > dayjs(reviewPrescription)) {
          color = 'red';
        } else {
          color = 'orange';
        }
      }
    } else {
      !!submitted
        ? (color = '#3DB86D')
        : status === ('REVIEWED' as unknown as any)
          ? (color = '#EA0000')
          : (color = '#FBA238');
    }

    return color;
  };

  const appealDomBuilder = (item: CheckItem, isSupervisor?: boolean) => {
    const condition = isSupervisor ? item.secondReviewAppealStatus : item.appealStatus;

    const withdrawNode = (
      <div className="flex gap-x-1 items-center">
        <span className="text-xs text-red-400">申诉中</span>
        {(!roleTypeIsManage() || isSupervisor) && (
          <button
            className="focus:outline-none bg-red-400 text-white px-[2px] py-[1px] text-xs rounded"
            onClick={(e) => {
              e.stopPropagation();
              Dialog.confirm({
                content: <div className="text-center text-[#141414] text-base">确定撤回申诉？</div>,
                onConfirm: async () => {
                  try {
                    const action = isSupervisor ? revokeAppealManage : revokeAppeal;
                    await action({
                      taskId: taskId!,
                      taskWorksheetItemId: item?.taskWorksheetItemId!,
                    });
                    message.success('撤回成功');
                    reFresh?.();
                  } catch (error) {
                    message.error('撤回失败');
                  }
                },
              });
            }}
          >
            撤回申诉
          </button>
        )}
      </div>
    );

    switch (condition) {
      case AppealStatus.发起:
        return withdrawNode;
      case AppealStatus.审核超时转派:
        return withdrawNode;
      case AppealStatus.通过:
        return (
          <div className="flex gap-x-1 items-center">
            <span className="text-xs text-red-400">申诉成功</span>
            <button
              className="focus:outline-none bg-primary text-white px-[2px] py-[1px] text-xs rounded"
              onClick={(e) => {
                e.stopPropagation();
                setComplaintsPopupVisible(true);
                setAppealId(item?.taskWorksheetItemId);
              }}
            >
              点击查看详情
            </button>
          </div>
        );
      case AppealStatus.驳回:
        return (
          <div className="flex gap-x-1 items-center">
            <span className="text-xs text-red-400">已驳回</span>
            <button
              className="focus:outline-none bg-primary text-white px-[2px] py-[1px] text-xs rounded"
              onClick={(e) => {
                e.stopPropagation();
                setComplaintsPopupVisible(true);
                setAppealId(item?.taskWorksheetItemId);
              }}
            >
              点击查看详情
            </button>
          </div>
        );
      case AppealStatus.撤回:
        return <div className="text-xs text-red-400">申诉已撤回，不可再次申诉</div>;
      case AppealStatus.已过期:
        return (
          <div className="flex gap-x-1 items-center">
            <span className="text-xs text-red-400">审核已过期</span>
            <button
              className="focus:outline-none bg-primary text-white px-[2px] py-[1px] text-xs rounded"
              onClick={(e) => {
                e.stopPropagation();
                setComplaintsPopupVisible(true);
                setAppealId(item?.taskWorksheetItemId);
              }}
            >
              点击查看详情
            </button>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <Loading spinning={false}>
      <React.Fragment>
        <div className="flex justify-between bg-white p-4 items-center">
          <div className="text-base leading-[16px] font-medium">检查明细</div>
          {isDudao && permissionsMap.has(AuthorityEnum.申诉_督导端) && (
            <Checkbox
              checked={hasAppeal}
              style={{
                '--font-size': '14px',
                '--icon-size': '16px',
                '--gap': '4px',
              }}
              onChange={setHasAppeal}
            >
              可申诉项
            </Checkbox>
          )}
        </div>
        <div className="flex bg-white">
          <div className=" bg-[#F8F8F8]">
            {isOPenLeft && (
              <SideBar
                onChange={(e: any) => {
                  setCurSheetId(e);
                  setactiveWorksheetId(curWorkSheet?.worksheetId);
                  setGroupId(-1);
                }}
                activeKey={curSheetId?.toString()}
              >
                {worksheets?.map((item: any) => (
                  <SideBar.Item
                    key={item.taskWorksheetId}
                    title={
                      <Badge
                        color={BadgeColor(item?.status, item?.reviewPrescription, !!item?.submitted)}
                        content={Badge.dot}
                      >
                        {item?.supportOverdue && (
                          <div className="text-xs text-[#378BFF] border border-solid border-[#D6E3F4] rounded w-fit p-[2px]">
                            可逾期
                          </div>
                        )}
                        <div>{item.name}</div>
                      </Badge>
                    }
                  />
                ))}
              </SideBar>
              // <div>
              //   {worksheets?.map((v) => {
              //     return (
              //       <div
              //         className={cn('p-4 w-[80px]', {
              //           'bg-white': v.taskWorksheetId === curSheetId,
              //         })}
              //         key={v.taskWorksheetId}
              //         onClick={() => {
              //           setCurSheetId(v.taskWorksheetId);
              //           setGroupId(-1);
              //         }}
              //       >
              //         {v?.name}
              //       </div>
              //     );
              //   })}
              // </div>
            )}

            <div
              className="absolute left-1 bottom-20 z-10"
              onClick={() => {
                setIsOPenLeft(!isOPenLeft);
              }}
            >
              <IconFont type="icon-shouqi" className="text-[#378BFF]" />
            </div>
          </div>
          <div className="flex-1 flex-col overflow-y-scroll ">
            {/* <div className="leading-[14px]">
              <div className="flex p-4 pb-3 justify-between items-center">
                <div className="flex items-center">
                  <div className="flex justify-between items-center">
                    <Checkbox
                      style={{
                        '--icon-size': '18px',
                        '--font-size': '24px',
                        '--gap': '6px',
                      }}
                      checked={isShowImgItem}
                      onChange={(checked) => {
                        setIsShowImgItem(checked);
                      }}
                    >
                      <div className="text-sm text-[#5E5E5E]">仅显示有图项</div>
                    </Checkbox>
                  </div>
                </div>
              </div>
            </div> */}
            <div className="bg-white flex items-center pl-1">
              <Tabs
                className={styles.Tabs}
                activeKey={groupId as any}
                onChange={(value) => setGroupId(+value as number)}
              >
                <Tabs.Tab
                  title={
                    <div
                      className={cn('flex text-sm items-center leading-[14px] text-[#858585]', {
                        [styles.activeColor]: +groupId === -1,
                      })}
                    >
                      全部
                      <span>
                        ({curWorkSheet?.filledCount}/{curWorkSheet?.itemCount})
                      </span>
                    </div>
                  }
                  key={-1}
                />
                {(curWorkSheet?.categories || [])?.map((item) => (
                  <Tabs.Tab
                    title={
                      <div
                        className={cn('flex text-sm items-center leading-[14px] text-[#858585]', {
                          [styles.activeColor]: +item?.categoryId === +groupId,
                        })}
                      >
                        <div className="max-w-[100px]  text-ellipsis overflow-hidden whitespace-nowrap">
                          {item.name}
                        </div>
                        <div>
                          ({item.filledCount}/{item.itemCount})
                        </div>
                      </div>
                    }
                    key={item.categoryId}
                  />
                ))}
              </Tabs>
              {(curWorkSheet?.categories?.length ?? 0) > 2 && (
                <>
                  <div className="bg-[rgba(0,0,0,0.03)]" />
                  <div
                    className="flex h-full justify-center items-center px-3"
                    onClick={() => setTabPopupVisible(true)}
                  >
                    <IconFont type="icon-menu" className="text-[#858585] text-sm" />
                  </div>
                </>
              )}
            </div>
            <div>
              {
                <Collapse
                  className={styles.Collapse}
                  onChange={(e) => {
                    setCollapseKeys(e);
                  }}
                  activeKey={collapseKeys}
                >
                  {list?.map((data) => {
                    const itemKey = curWorkSheet?.worksheetType === 'VALIDITY' ? 'validityDtos' : 'items';
                    return (
                      data.type === 'group' && (
                        <Collapse.Panel
                          key={`${data?.categoryId}`}
                          title={
                            <div className="text-[#141414] text-sm leading-[14px] font-medium  max-w-[200px]  text-ellipsis overflow-hidden whitespace-nowrap ">
                              {data?.name} {data?.filledCount}/{data?.itemCount}
                            </div>
                          }
                        >
                          {data[itemKey]
                            ?.filter((f) => !hasAppeal || hasAppealOperate(f))
                            ?.map((item: CheckItem) => {
                              return (
                                <div className="border-b border-solid border-[#F0F0F0]" key={item?.taskItemId}>
                                  {curWorkSheet?.worksheetType === 'VALIDITY' ? (
                                    <ValidityCheckItemCard data={toJS(item)} />
                                  ) : (
                                    <div>
                                      {
                                        <SelfCheckItemCard
                                          data={toJS(item)}
                                          isDetail={true}
                                          status={curWorkSheet?.status!}
                                          reviewUserId={curWorkSheet?.reviewUserId!}
                                          reviewPermission={curWorkSheet?.reviewPermission!}
                                          showStandard={(value) => {
                                            setStandardPopup({
                                              visible: true,
                                              standard: value,
                                            });
                                          }}
                                          showSop={(value: any) => {
                                            setSopPopup({
                                              visible: true,
                                              sopId: value,
                                            });
                                          }}
                                          reFresh={reFresh}
                                        />
                                      }
                                    </div>
                                  )}
                                  {/* <div className="my-2">{appealDomBuilder(item)}</div> */}
                                  <div className="my-2">{appealDomBuilder(item, isDudao)}</div>
                                </div>
                              );
                            })}
                        </Collapse.Panel>
                      )
                    );
                  })}
                </Collapse>
              }
            </div>
          </div>
        </div>
      </React.Fragment>

      <IPopup title="分类" visible={tabPopupVisible} onClose={() => setTabPopupVisible(false)}>
        <div className="pt-5 px-4 pb-10">
          <Selector
            style={{
              '--checked-color': 'var(--color-primary-1)',
              '--border-radius': '4px',
              fontSize: '14px',
            }}
            className={styles.Selector}
            value={[groupId]}
            onChange={(value) => setGroupId(value[0])}
            columns={1}
            showCheckMark={false}
            options={
              curWorkSheet?.categories?.map((i) => ({
                label: `${i?.name}`,
                value: i?.categoryId,
              })) ?? []
            }
          />
        </div>
      </IPopup>
      <StandardPopup
        title="评分标准"
        visible={standardPopup.visible}
        standard={standardPopup.standard}
        onClose={() => {
          setStandardPopup({
            visible: false,
            standard: undefined,
          });
        }}
      />
      <SopPopup
        sopId={sopPopup.sopId}
        title="参考标准"
        visible={sopPopup.visible}
        onClose={() => {
          setSopPopup({
            visible: false,
            sopId: undefined,
          });
        }}
      />
      <ComplaintsDescPopup
        visible={complaintsPopupVisible}
        onClose={() => setComplaintsPopupVisible(false)}
        appealId={appealId}
        taskId={taskId!}
      />
    </Loading>
  );
});
export default SelfWheetOptions;
