import { useMemo, useState } from 'react';
import { CloseOutlined } from '@ant-design/icons';
import { IconFont } from '@src/components';
import { IPopup } from '@src/components/IPopup';
import type { WeekSheet } from '@src/pages/patrol/api.type';
import SystemImageUpload from '@src/pages/patrol/checkList/components/systemImageUpload';
import type { CheckItem } from '@src/pages/selfCheckComment/api.type';
import { useRequest } from 'ahooks';
import { Button, Card, Form, Input, message, Select } from 'antd';
import { batchAppealForPatrol, batchAppealForSelf } from './api';

const { TextArea } = Input;

interface ComplaintsPopupProps {
  visible: boolean;
  onClose: () => void;
  complaintsItems: CheckItem[] | WeekSheet['children'][];
  taskId: string;
  reFresh: () => void;
  isPatrol?: boolean;
}

export const ComplaintsPopup = ({
  visible,
  onClose,
  complaintsItems,
  taskId,
  reFresh,
  isPatrol,
}: ComplaintsPopupProps) => {
  const [form] = Form.useForm();
  const items = Form.useWatch('items', form);
  const selectedItems = useMemo(() => {
    const _selectedItems: number[] = [];
    items?.forEach((item: any) => {
      if (item?.unqualifiedItem) {
        _selectedItems.push(item?.unqualifiedItem?.value);
      }
    });
    return _selectedItems || [];
  }, [items]);

  const options = useMemo(() => {
    const _options = complaintsItems
      // 过滤已选中的项
      ?.filter((item) => {
        if ('taskWorksheetItemId' in item) {
          return !selectedItems.includes(item.taskWorksheetItemId);
        }

        if ('itemId' in item) {
          return !selectedItems.includes(item.itemId as number);
        }

        return false;
      })
      ?.map((item) => {
        if ('taskWorksheetItemId' in item) {
          return {
            label: item.content,
            value: item.taskWorksheetItemId,
          };
        } else {
          return {
            label: (item as any).itemName,
            value: (item as any).itemId,
          };
        }
      });
    return _options || [];
  }, [complaintsItems, selectedItems]);
  const [upLoading, setUpLoading] = useState<boolean>(false);

  const { run, loading: batchAppealLoading } = useRequest(isPatrol ? batchAppealForPatrol : batchAppealForSelf, {
    manual: true,
    onSuccess: () => {
      message.success('申诉成功');
      reFresh?.();
      form.resetFields();
      onClose?.();
    },
  });

  return (
    <IPopup
      title={`请选择要申诉的检查项 ${selectedItems?.length} / ${complaintsItems?.length}`}
      visible={visible}
      onClose={() => {
        form.resetFields();
        onClose?.();
      }}
      footer={
        <div className="bg-white flex gap-x-3 p-3">
          <Button
            block
            className="h-[45px] text-base"
            onClick={() => {
              onClose();
            }}
          >
            取消
          </Button>
          <Button
            type="primary"
            block
            className="h-[45px] text-base"
            onClick={() => {
              form.submit();
            }}
            loading={upLoading || batchAppealLoading}
          >
            确认提交
          </Button>
        </div>
      }
      bodyStyle={{ height: '85vh' }}
      destroyOnClose
    >
      <div className="px-2 py-1 h-full overflow-auto" id="complaintsContainer">
        <Form
          form={form}
          initialValues={{ items: [{}] }}
          onFinish={(values) => {
            const payload = {
              taskId,
              applyInfos: values?.items?.map((i: any) => {
                return {
                  taskWorksheetItemId: i?.unqualifiedItem?.value,
                  reason: i?.complaintsReason,
                  attachmentIds: i?.images?.map((img: any) => img?.id),
                };
              }),
            };
            run(payload);
          }}
        >
          <Form.List name="items">
            {(fields, { add, remove }) => (
              <div style={{ display: 'flex', rowGap: 16, flexDirection: 'column' }}>
                {fields.map((field) => {
                  return (
                    <Card
                      size="small"
                      title={
                        <div className="flex justify-center items-center text-white font-medium  bg-primary size-7 rounded-full">
                          {field.name + 1}
                        </div>
                      }
                      key={field.key}
                      extra={
                        items?.length > 1 && (
                          <CloseOutlined
                            onClick={() => {
                              remove(field.name);
                            }}
                          />
                        )
                      }
                    >
                      <Form.Item
                        label="不合格项"
                        name={[field.name, 'unqualifiedItem']}
                        rules={[{ required: true, message: '请选择不合格项' }]}
                      >
                        <Select
                          placeholder="请选择不合格项"
                          options={options}
                          style={{ width: 250 }}
                          allowClear
                          labelInValue
                        />
                      </Form.Item>
                      <Form.Item
                        label="申诉原因"
                        name={[field.name, 'complaintsReason']}
                        rules={[{ required: true, message: '请输入申诉原因' }]}
                      >
                        <TextArea
                          placeholder="请输入申诉原因"
                          maxLength={50}
                          rows={2}
                          style={{
                            width: 250,
                            resize: 'none',
                          }}
                          autoSize={false}
                        />
                      </Form.Item>
                      <Form.Item label="辅助材料" name={[field.name, 'images']}>
                        <SystemImageUpload isAlbumUpImg={false} setUpLoading={setUpLoading} FileUploadType="SELF" />
                      </Form.Item>
                    </Card>
                  );
                })}

                {selectedItems?.length < complaintsItems?.length && (
                  <div
                    className="fixed right-[15px] top-[100px] flex flex-col justify-center items-center bg-white w-[50px] h-[50px] rounded-full shadow-[0_2px_5px_0_rgba(0,0,0,0.1)] z-[999] text-[#747578] text-13 leading-[13px]"
                    onClick={() => {
                      const isAllFill = items.every((field: any) => {
                        return field?.unqualifiedItem?.value && field?.complaintsReason;
                      });
                      if (!isAllFill) {
                        message.error('请填写完整之前的项');
                        return;
                      }
                      add();
                      setTimeout(() => {
                        const complaintsContainer = document.getElementById('complaintsContainer');
                        if (complaintsContainer) {
                          complaintsContainer.scrollTo({
                            top: complaintsContainer.scrollHeight,
                            behavior: 'smooth',
                          });
                        }
                      }, 0);
                    }}
                  >
                    <IconFont type="icon-tianjia1" className="text-[22px]  text-[#5E5E5E]" />
                  </div>
                )}
              </div>
            )}
          </Form.List>
        </Form>
      </div>
    </IPopup>
  );
};
