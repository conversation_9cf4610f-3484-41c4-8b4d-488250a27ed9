import type { AppealStatus } from '@src/pages/selfCheckComment/api.type';
import type { CheckItemAttribute } from '@src/pages/tasks/circularRectification/enum';
import { NodeTypeEnum, TaskTypeEnum } from './enum';

export interface BatchAppealPayload {
  /**
   * 申诉信息集
   */
  applyInfos: ShopTaskReportItemAppealApplyDTO[] | null;
  /**
   * 任务id
   */
  taskId: string | null;
  [property: string]: any;
}

interface ShopTaskReportItemAppealApplyDTO {
  /**
   * 申诉附件id集
   */
  attachmentIds?: string[] | null;
  /**
   * 申诉理由
   */
  reason: null | string;
  /**
   * 任务检查项id
   */
  taskWorksheetItemId: number | null;
  [property: string]: any;
}

export interface ShopTaskReportAppealDetailDTO {
  /**
   * 申诉id
   */
  appealId?: number | null;
  /**
   * 申诉附件信息集
   */
  attachmentFiles?: FileDTO[] | null;
  /**
   * 申诉进度
   */
  process?: AppealProcessDTO[] | null;
  /**
   * 申诉理由
   */
  reason?: null | string;
  /**
   * 任务id
   */
  taskId?: number | null;
  /**
   * 任务类型
   */
  taskType?: null | string;
  /**
   * 任务检查项id
   */
  taskWorksheetItemId?: number | null;
  [property: string]: any;
}

/**
 * FileDTO
 */
export interface FileDTO {
  contentType?: string;
  fileType?: FileType;
  id: string;
  name?: string;
  snapshotUrl: string;
  url: string;
  [property: string]: any;
}

enum FileType {
  Excel = 'EXCEL',
  Img = 'IMG',
  PDF = 'PDF',
  Video = 'VIDEO',
}

interface AppealProcessDTO {
  /**
   * 附件
   */
  attachmentFiles?: FileDTO[] | null;
  /**
   * 操作时间
   */
  operatorTime?: string;
  /**
   * 操作人用户名
   */
  operatorUserName?: null | string;
  /**
   * 备注
   */
  remark?: null | string;
  /**
   * 类型
   */
  type?: null | string;
  [property: string]: any;
}

export interface CorpAppTaskReportAppealInfoDTO {
  /**
   * 检查项重点项标签
   */
  accentedTermTags?: CheckItemAttribute[];
  /**
   * 截止时间
   */
  deadlineTime?: string;
  /**
   * 申诉进度
   */
  process?: TaskReportAppealProcessDTO[] | null;
  status?: AppealStatus;
  taskName?: string;
  /**
   * 任务ID
   */
  taskId?: number | null;
  /**
   * 任务类型
   */
  taskType?: TaskTypeEnum;
  /**
   * 任务项ID
   */
  taskWorksheetItemId?: number;
  /**
   * 检查表分类名称
   */
  worksheetCategoryName?: string;
  /**
   * 检查项内容
   */
  worksheetItemContent?: string;
  /**
   * 检查项项名称
   */
  worksheetItemName?: string;
  /**
   * 检查项提交附件集
   */
  worksheetItemSubmitAttachments?: FileDTO[];
  /**
   * 检查表名称
   */
  worksheetName?: string;
  hasPermissionAudit?: boolean; // 是否有权限审核
  /**
   * 节点类型
   */
  nodeType?: NodeTypeEnum;
  [property: string]: any;
}

export interface TaskReportAppealProcessDTO {
  /**
   * 附件
   */
  attachmentFiles?: FileDTO[] | null;
  /**
   * 创建时间
   */
  createTime?: string;
  id?: number;
  /**
   * 类型
   */
  operationType?: OperationType;
  /**
   * 操作人用户名
   */
  operatorUserName?: string;
  /**
   * 备注
   */
  remark?: string;
  [property: string]: any;
}

export enum OperationType {
  发起 = 'CREATE',
  通过 = 'PASSED',
  撤回 = 'RECALL',
  驳回 = 'REJECTED',
  提交点评 = 'REVIEW',
  二次审核通过 = 'SECOND_AUDIT_PASSED',
  二次审核驳回 = 'SECOND_AUDIT_REJECTED',
  超时未审核 = 'AUDIT_TIMEOUT_TRANSFER',
  第二审核超时未审核 = 'SECOND_AUDIT_TIMEOUT_TRANSFER',
  审核超时 = 'AUDIT_TIMEOUT',
  第二审核超时 = 'SECOND_AUDIT_TIMEOUT',
  作废 = 'INVALID',
}

export const OperationTypeText: Record<OperationType, string> = {
  [OperationType.发起]: '发起申诉，申诉原因如下',
  [OperationType.通过]: '审核通过，审核结果为判断为合格',
  [OperationType.撤回]: '撤回申诉',
  [OperationType.驳回]: '审核驳回，审核结果为不合格',
  [OperationType.提交点评]: '提交点评',
  [OperationType.二次审核通过]: '复审通过，复审结果为判断为合格',
  [OperationType.二次审核驳回]: '复审驳回，复审结果为判断为不合格',
  [OperationType.超时未审核]: '任务超时未审核，自动转至超时处理人',
  [OperationType.第二审核超时未审核]: '任务超时未审核，自动转至超时处理人',
  [OperationType.审核超时]: '任务超时未审核，系统自动驳回',
  [OperationType.第二审核超时]: '任务超时未审核，系统自动驳回',
  [OperationType.作废]: '因后台已作废任务，故申诉跟随任务作废',
};
