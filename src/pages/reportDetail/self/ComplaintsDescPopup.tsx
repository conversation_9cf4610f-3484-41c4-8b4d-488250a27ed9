import { useMemo } from 'react';
import { IPopup } from '@src/components/IPopup';
import Loading from '@src/components/Loading';
import { OperationRecord } from '@src/pages/tasks/complaintWaitForDeal/components/OperationRecord';
import { roleTypeIsManage } from '@src/utils/tokenUtils';
import { useRequest } from 'ahooks';
import dayjs from 'dayjs';
import { getAppealDetail, getAppealDetailForPatrol } from './api';
import { OperationType, OperationTypeText } from './api.type';
import { TaskTypeEnum } from './enum';

interface ComplaintsDescPopupProps {
  visible: boolean;
  onClose: () => void;
  appealId?: number;
  taskId?: string;
  isPatrol?: boolean;
}

export const ComplaintsDescPopup = ({ visible, onClose, appealId, taskId, isPatrol }: ComplaintsDescPopupProps) => {
  const { data, loading } = useRequest(
    () =>
      isPatrol
        ? getAppealDetailForPatrol({ taskWorksheetItemId: appealId, taskId, roleTypeIsManage: roleTypeIsManage() })
        : getAppealDetail({
            taskWorksheetItemId: appealId,
            taskId,
            roleTypeIsManage: roleTypeIsManage(),
            appealTaskType: TaskTypeEnum.自检第二点评,
          }),
    {
      refreshDeps: [appealId],
      ready: !!appealId,
    },
  );

  // 描述数据来源
  const descDataSource = data?.taskType === 'PATROL' ? data?.patrolTaskWorksheetItem : data?.selfTaskWorksheetItem;

  const renderCommentDescBuilder = (noReason?: boolean) => {
    return (
      <>
        {data?.taskType === 'PATROL' && !noReason && <span className="empty:hidden">{descDataSource?.itemRemark}</span>}

        {!noReason && (
          <div className="flex gap-x-2 flex-wrap empty:hidden">
            {(descDataSource?.otherReason
              ? [
                  ...(descDataSource?.reportReviewAbnormalReasons ||
                    descDataSource?.selectedNonconformityReasons ||
                    []),
                  descDataSource?.otherReason,
                ]
              : descDataSource?.reportReviewAbnormalReasons || descDataSource?.selectedNonconformityReasons
            )?.map((r: any) => {
              return (
                <span key={r} className="text-blue-400">
                  #{r}
                </span>
              );
            })}
          </div>
        )}
      </>
    );
  };

  const operationRecord = useMemo(() => {
    if (!Array.isArray(data?.process) || !data?.process?.length) {
      return [];
    }

    return (
      data.process
        ?.filter((v, idx) => !(data?.taskType === 'PATROL' && v?.operationType === 'REVIEW' && idx === 0))
        ?.map((v) => ({
          operationRecordUser: v?.operatorUserName!,
          operationRecordText: `${
            [
              OperationType.超时未审核,
              OperationType.第二审核超时未审核,
              OperationType.审核超时,
              OperationType.第二审核超时,
            ].includes(v?.operationType!)
              ? ''
              : v?.operatorUserName
          } ${OperationTypeText[v?.operationType! as keyof typeof OperationTypeText] ?? ''}`,
          operationRecordTime: dayjs(v?.createTime).format('MM-DD HH:mm'),
          descAndFiles: {
            desc: [OperationType.超时未审核, OperationType.第二审核超时未审核].includes(v?.operationType!) ? null : (
              <>
                <span className="text-sm empty:hidden break-all">{v?.remark}</span>
                {renderCommentDescBuilder(v?.operationType !== 'REVIEW')}
              </>
            ),
            files: v?.attachmentFiles?.map((f) => ({
              fileType: f?.fileType,
              id: f?.id,
              snapshotUrl: f?.snapshotUrl,
              url: f?.url,
            })),
          },
        })) || []
    );
  }, [data?.process, data?.taskType]);

  return (
    <IPopup title="申诉详情" visible={visible} onClose={onClose} bodyStyle={{ height: '65vh' }} destroyOnClose>
      <Loading spinning={loading}>
        <div className="p-4">
          <OperationRecord record={operationRecord} isAlwaysShowMore />
        </div>
      </Loading>
    </IPopup>
  );
};
