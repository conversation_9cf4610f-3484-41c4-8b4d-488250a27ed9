import { get, post, put } from '@src/api';
import type { AppealStatus } from '@src/pages/selfCheckComment/api.type';
import {
  BatchAppealPayload,
  type CorpAppTaskReportAppealInfoDTO,
  type ShopTaskReportAppealDetailDTO,
} from './api.type';

// 自检-申诉发起
export const batchAppealForSelf = async (data: BatchAppealPayload) =>
  post('/om-api/shop/self/report/appeal/batch', { data });
// 巡检-申诉发起
export const batchAppealForPatrol = async (data: BatchAppealPayload) =>
  post('/om-api/shop/patrol/report/appeal/batch', { data });

// 自检-申诉撤回
export const revokeAppeal = async (data: { taskId: string; taskWorksheetItemId: number }) =>
  put(`/om-api/shop/self/report/appeal/revocation`, { data });

// 督导-自检-申诉撤回
export function revokeAppealManage(data: { taskId: string; taskWorksheetItemId: number }) {
  return put(`/om-api/corp/self/report/appeal/revocation`, { data });
}
// 巡检-申诉撤回
export const revokeAppealForPatrol = async (data: { taskId: string; taskWorksheetItemId: number }) =>
  put(`/om-api/shop/patrol/report/appeal/revocation`, { data });

// 自检-申诉详情
export const getAppealDetail = async (params: {
  taskWorksheetItemId?: number;
  taskId?: string;
  roleTypeIsManage: boolean;
  appealTaskType?: string;
}) =>
  get<ShopTaskReportAppealDetailDTO>(`/om-api/${params.roleTypeIsManage ? 'corp' : 'shop'}/self/report/appeal/detail`, {
    params,
  });
// 巡检-申诉详情
export const getAppealDetailForPatrol = async (params: {
  taskWorksheetItemId?: number;
  taskId?: string;
  roleTypeIsManage: boolean;
}) =>
  get<ShopTaskReportAppealDetailDTO>(
    `/om-api/${params.roleTypeIsManage ? 'corp' : 'shop'}/patrol/report/appeal/detail`,
    {
      params,
    },
  );

// 审核申诉列表
export const getAppealList = async (
  data: {
    pageNo: number;
    pageSize: number;
    beginTime?: string;
    endTime?: string;
    shopIds?: string[];
    groupId?: number;
    status?: AppealStatus[];
    /** 只看待我处理 */
    onlyMePending?: boolean;
  },
  /** 是否为策略 */
  isTactics?: boolean,
) =>
  post<PageRes<CorpAppTaskReportAppealInfoDTO>>(
    `/${isTactics ? 'tm' : 'om'}-api/corp/task/report/appeal/list-page-app`,
    {
      data,
    },
  );

// 督导-自检-维持原结果
export const maintainAppeal = async (data: {
  taskId: number;
  taskWorksheetItemId: number;
  remark: string;
  appealType?: string;
}) => put('/om-api/corp/self/report/appeal/reject', { data });

// 策略 督导-自检-维持原结果
export const maintainAppealTactics = async (data: {
  taskId: number;
  baseTaskId: number;
  taskItemId: number;
  remark: string;
}) => put('/tm-api/corp/self/report/appeal/reject', { data });

// 督导-自检-判断为合格
export const passAppeal = async (data: {
  taskId: number;
  taskWorksheetItemId: number;
  remark: string;
  appealType?: string;
}) => put('/om-api/corp/self/report/appeal/pass', { data });

// 策略 督导-自检-判断为合格
export const passAppealTactics = async (data: {
  taskId: number;
  baseTaskId: number;
  taskItemId: string;
  remark: string;
}) => put('/tm-api/corp/self/report/appeal/pass', { data });

// 督导-巡检-维持原结果
export const maintainAppealForPatrol = async (data: { taskId: number; taskWorksheetItemId: number; remark: string }) =>
  put('/om-api/corp/patrol/report/appeal/reject', { data });

// 督导-巡检-判断为合格
export const passAppealForPatrol = async (data: { taskId: number; taskWorksheetItemId: number; remark: string }) =>
  put('/om-api/corp/patrol/report/appeal/pass', { data });

/**
 * @description 提交申诉
 * @param data
 * @returns
 */
export function submitAppeal(data: {
  taskId: number;
  applyInfos: {
    taskWorksheetItemId: number;
    reason: string;
    attachmentIds: string[];
  }[];
}) {
  return post('/om-api/corp/self/report/appeal/batch', { data });
}
