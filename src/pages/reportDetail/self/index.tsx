import React, { FC, useEffect, useMemo, useRef, useState } from 'react';
import { ButtonEnums } from '@src/common/api.type';
import AuthorityEnum from '@src/common/authority';
import { Loading } from '@src/components';
import { useCanReviewFlag } from '@src/hooks/useCanReviewFlag';
import {
  getSelfReportReview,
  revocationSubmit,
  revokeWorkSheet,
  revoleSelfReport,
  revoleSingleSheet,
  secondSubmitComment,
  submitSelfComment,
} from '@src/pages/selfCheckComment/api';
import { AppealStatus, type CheckItem } from '@src/pages/selfCheckComment/api.type';
import { Report_STATUS_TEXT, Work_Sheet_TEXT } from '@src/pages/selfCheckComment/enum';
import ShopInfo from '@src/pages/selfCheckComment/shopInfo';
import { GlobalStore, SelfCheckStore, userStore } from '@src/store';
import { roleTypeIsManage } from '@src/utils/tokenUtils';
import { useRequest } from 'ahooks';
import { Button, NoticeBar, Toast } from 'antd-mobile';
import { RightOutline } from 'antd-mobile-icons';
import dayjs from 'dayjs';
import { toJS } from 'mobx';
import { observer } from 'mobx-react';
// import { useAliveController } from 'react-activation';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { ComplaintsPopup } from './ComplaintsPopup';
import SelfSummaryMsg from './selfSummaryMsg';
import SelfWheetOptions from './selfWheetOptions';
import { BtnText, CurReformStatus } from '../patrol/enum';
import OperationBar from '../patrol/OperationBar';

// 报告状态枚举
export enum reportReformEnum {
  waitToReform = 1,
  waitToCheck = 2,
  success = 4,
}
const ReportDetail: FC = () => {
  const { permissionsMap } = userStore;

  const navigate = useNavigate();
  const { getCorporationConfig } = GlobalStore;
  const { setPreviewInfos, curCheckSheetInfo } = SelfCheckStore;
  const { isSuperManger } = userStore;
  const { userInfo } = userStore;

  const [complaintsPopupVisible, setComplaintsPopupVisible] = useState(false);
  const [submitLoading, setSubmitLoading] = useState<boolean>(false);

  const [routerParams] = useSearchParams();
  const isDudao = roleTypeIsManage();
  // const { refresh } = useAliveController();
  console.log(toJS(curCheckSheetInfo), '=curCheckSheetInfo');
  console.log(toJS(isSuperManger), '=isSuperManger');

  const { taskId } = useMemo(() => {
    const taskId = routerParams.get('taskId');
    return {
      taskId,
    };
  }, [routerParams]);

  const {
    data: detailData,
    loading: detailLoading,
    run: reFresh,
  } = useRequest(
    async () => {
      const res = await getSelfReportReview(taskId!, undefined, true);
      setPreviewInfos(res);
      return res;
    },
    { refreshDeps: [taskId] },
  );

  const canRevokeWorksheetRef = useRef(true);

  // 可申诉项-相对于单个表
  const complaintsItems = useMemo(() => {
    const _complaintsItems: CheckItem[] = [];

    curCheckSheetInfo?.categories?.forEach((c: any) => {
      c?.items?.forEach((i: any) => {
        if (i?.hasApply && !i?.qualified && i?.appealStatus === AppealStatus.未申诉) {
          _complaintsItems.push(i);
        }
      });
    });
    return _complaintsItems;
  }, [curCheckSheetInfo]);

  useEffect(() => {
    const hasInvalidAppealStatus = detailData?.worksheets?.some((w) =>
      w?.categories?.some((c: any) =>
        c?.items?.some((i: any) => [AppealStatus.发起, AppealStatus.通过, AppealStatus.驳回].includes(i?.appealStatus)),
      ),
    );
    canRevokeWorksheetRef.current = !hasInvalidAppealStatus;
  }, [detailData]);

  // 是否为第二点评人
  const { hasSecondReview } = detailData || {};
  const { data: config, loading: configLoading } = useRequest(
    async () => {
      return await getCorporationConfig();
    },
    { refreshDeps: [] },
  );

  const [activeWorksheetId, setactiveWorksheetId] = useState<any>();
  const dealReport = (opera: ButtonEnums) => {
    const goOpera = {
      SUBMIT_REPORT_COMMENT: () => {
        // 提交点评
        setSubmitLoading(true);
        curCheckSheetInfo?.hasSecondReview
          ? secondSubmitComment({ taskId: +taskId! })
              .then(() => {
                Toast.show({
                  icon: 'success',
                  content: '提交成功',
                });
                reFresh();
              })
              .finally(() => setSubmitLoading(false))
          : submitSelfComment({
              notFilledItemHandleType: 'SET_FULL_SCORE',
              taskId: +taskId!,
            })
              .then(() => {
                Toast.show({
                  icon: 'success',
                  content: '提交成功',
                });
                if (curCheckSheetInfo?.hasSecondReviewPermission) {
                  navigate(`/self/review?taskId=${taskId}`);
                } else {
                  reFresh();
                }
              })
              .finally(() => setSubmitLoading(false));

        /** 手动触发一次 keepAlive 的更新 */
        // refresh('/mission');
        // navigate(`/mission?roletype=${isDudao ? 'supervision' : 'manager'}`);
      },
      REVOKE_REPORT_COMMENT: () => {
        if (!canRevokeWorksheetRef.current) {
          Toast.show({
            content: '当前检查表存在申诉状态，无法撤回',
          });
          return;
        }
        // 撤回点评
        revoleSelfReport({
          data: +taskId!,
        }).then(() => {
          navigate(`/self/review?taskId=${taskId}`);
        });
      },
      REVOKE_SINGLESHEET_COMMENT: () => {
        // 撤回单个检查表点评
        revoleSingleSheet({
          taskId: +taskId!,
          worksheetId: curCheckSheetInfo?.worksheetId!,
        }).then(() => {
          reFresh();
        });
      },
      RECALL_REPORT: () => {
        // 店长撤回报告
        revocationSubmit({
          data: +taskId!,
        }).then(() => {
          reFresh();
        });
      },
      RECALL_TABLE: () => {
        // 店长撤回检查表
        revokeWorkSheet({
          taskId: +taskId!,
          worksheetId: activeWorksheetId,
        }).then(() => {
          reFresh();
        });
      },
    };
    goOpera[opera as keyof typeof goOpera]();
  };
  const isShowOperationBar = useMemo(() => {
    return (
      (!isDudao && detailData?.reformStatus === CurReformStatus.待整改) || (isDudao && !!detailData?.waitAuditCount)
    );
  }, [detailData, isDudao]);

  const canReviewFlag = useCanReviewFlag(curCheckSheetInfo, curCheckSheetInfo?.hasSecondReview);
  const isShowSummary: boolean = useMemo(() => {
    if (isDudao) {
      return true;
    } else {
      // 店长待点评状态不显示汇总
      return detailData?.status === Report_STATUS_TEXT.已点评;
    }
  }, [isDudao, detailData]);
  console.log(detailData, '=detailData');

  const isAllComment = useMemo(() => {
    // 判断全部检查表是否已点评
    return detailData?.worksheets
      ?.filter((i) => {
        return i?.worksheetType === Work_Sheet_TEXT.自检表;
      })
      ?.every((v) => {
        return v?.status === Report_STATUS_TEXT.已点评;
      });
  }, [detailData]);

  const showOprBtns = useMemo(() => {
    // const { hasSecondReview, secondReview } = detailData || {};
    const {
      status,
      hasRectificationOperation,
      reviewTime,
      noNeedReview,
      hasSecondReview,
      secondReviewed,
      reviewUserId,
      reviewPermission,
    } = curCheckSheetInfo || {};
    const isShowRevokeComment = (() => {
      console.log(dayjs()?.diff(dayjs(reviewTime), 'hour') < 3, '=dayjs(reviewTime)<3');
      // 有问题项，门店还未操作时

      if (status === Report_STATUS_TEXT.已点评 && dayjs()?.diff(dayjs(reviewTime), 'hour') < 3) {
        // 有问题项，门店还未操作时
        if (!hasRectificationOperation) {
          return true;
        } else {
          return !!isSuperManger;
        }
      }
    })();
    let btns: ButtonEnums[] = [];
    if (status === Report_STATUS_TEXT.已提交 || (hasSecondReview && !secondReviewed)) {
      if (!canReviewFlag) {
        btns = ['SUBMIT_REPORT_COMMENT_DISABLE'];
      } else {
        btns = ['SUBMIT_REPORT_COMMENT'];
      }
    } else if (!noNeedReview && isShowRevokeComment && !hasSecondReview) {
      // 满足撤回点评的条件
      const isPermission = reviewUserId === userInfo?.userId || !!isSuperManger;
      if (!canReviewFlag && isPermission) {
        btns = ['REVOKE_REPORT_COMMENT_DISABLE'];
      } else {
        btns = isPermission ? ['REVOKE_REPORT_COMMENT'] : !reviewPermission ? ['NOSAME_COMMENT'] : [];
      }
    }
    return btns;
  }, [canReviewFlag, curCheckSheetInfo, isSuperManger]);
  console.log(showOprBtns, '=showOprBtns');
  const commentList = useMemo(() => {
    const list = detailData?.worksheets
      ?.filter((i) => {
        return !!i?.corpSummary && i?.status === Report_STATUS_TEXT.已点评;
      })
      ?.map((v) => {
        return {
          corpSummary: v?.corpSummary,
          reviewUserName: v?.reviewUserName,
          reviewerRoles: v?.reviewerRoles,
        };
      });
    return list;
  }, [detailData]);
  console.log(commentList, '=commentList');
  const isShowSecondReview = useMemo(() => {
    return curCheckSheetInfo?.hasSecondReview && !curCheckSheetInfo?.secondReviewed;
  }, [curCheckSheetInfo]);
  return (
    <Loading spinning={detailLoading || configLoading}>
      <div className="flex flex-col fixed inset-x-0 inset-y-0">
        <div className="grow overflow-y-scroll ">
          <div className="p-4 bg-white">
            <ShopInfo detail={detailData!} />
          </div>
          {!!detailData?.issuesCount &&
            detailData.status === Report_STATUS_TEXT.已点评 &&
            !!detailData?.reformStatus && (
              <div className="bg-white p-4 mt-[10px]">
                <div
                  className="flex"
                  onClick={() => {
                    navigate(`/tasks/pollingAbarbeitung?taskId=${detailData?.id}&pollingType=SelfPolling`);
                  }}
                >
                  <div className="flex-1 text-base">共{detailData?.noReformCount}个不合格项</div>
                  <RightOutline className="text-[#c5c5c5] mt-1 text-sm " />
                </div>
                {isShowOperationBar && (
                  <div>
                    <OperationBar
                      pollingType="SelfPolling"
                      submitTime={detailData?.latestRectificationSubmitTime}
                      count={detailData?.waitRectifiedCount}
                      isDudao={isDudao}
                      taskId={detailData?.id}
                    />
                  </div>
                )}
              </div>
            )}
          {!isAllComment && curCheckSheetInfo?.status === Report_STATUS_TEXT.已点评 && (
            <div className="my-1">
              <NoticeBar content="该报告存在点评人还未提交,报告得分还未生成" color="alert" />
            </div>
          )}
          {isShowSummary && isAllComment && (
            <div className="my-[10px]">
              <SelfSummaryMsg config={config} detail={detailData!} />
            </div>
          )}

          {detailData?.shopSummary && (
            <div className="bg-white p-4 mb-[10px]">
              <div className="text-base leading-[16px] font-medium">门店总结</div>
              <div className={`bg-[#fafafa] rounded-[4px] leading-4 p-4 py-3 mt-2`}>{detailData?.shopSummary}</div>
            </div>
          )}
          {!!detailData?.summaryDtos?.length && (
            <div className="bg-white p-4 mb-[10px]">
              <div className="text-base leading-[16px] mb-3 font-medium">点评总结</div>
              <div className={`bg-[#fafafa] rounded-[4px]  leading-4 px-2 py-3 mt-2`}>
                {detailData?.summaryDtos?.map((item, index) => (
                  <div key={index}>
                    {`${item?.username} (${item?.roleNames}): `}
                    {item?.summary}
                  </div>
                ))}
              </div>
            </div>
          )}
          {detailData?.secondSummary && isDudao && (
            <div className="bg-white p-4 mb-[10px]">
              <div className="text-base leading-[16px] mb-3 font-medium">第二点评总结</div>
              <div className={`bg-[#fafafa] rounded-[4px]  leading-4 px-2 py-3 mt-2`}>{detailData?.secondSummary}</div>
            </div>
          )}

          {!detailLoading && (
            <SelfWheetOptions
              selfWorkSheet={detailData!}
              setactiveWorksheetId={setactiveWorksheetId}
              reFresh={reFresh}
            />
          )}
        </div>

        {roleTypeIsManage() && (
          <div className="shrink-0">
            <div className="py-2 px-2 bg-[#fff]">
              <div className="flex justify-between gap-2">
                {isShowSecondReview && (
                  <Button
                    block
                    color="primary"
                    loading={submitLoading}
                    onClick={() => {
                      dealReport('SUBMIT_REPORT_COMMENT' as ButtonEnums);
                    }}
                  >
                    提交点评
                  </Button>
                )}
                {!isShowSecondReview && (
                  <React.Fragment>
                    {curCheckSheetInfo?.reviewPermission || isSuperManger ? (
                      curCheckSheetInfo.noNeedReview ? (
                        <Button block disabled color="primary">
                          不需要点评
                        </Button>
                      ) : (
                        showOprBtns?.map((item, index) => {
                          return (
                            <Button
                              block
                              key={index}
                              loading={submitLoading}
                              color={showOprBtns?.length === 1 || index !== 0 ? 'primary' : 'default'}
                              onClick={() => {
                                dealReport(item as ButtonEnums);
                              }}
                              disabled={
                                item === 'SUBMIT_REPORT_COMMENT_DISABLE' || item === 'REVOKE_REPORT_COMMENT_DISABLE'
                              }
                            >
                              {BtnText[item as ButtonEnums]}
                            </Button>
                          );
                        })
                      )
                    ) : curCheckSheetInfo?.status === Report_STATUS_TEXT.已点评 ? (
                      showOprBtns?.includes('NOSAME_COMMENT') && (
                        <Button block disabled color="primary">
                          无点评权限
                        </Button>
                      )
                    ) : (
                      <Button block disabled color="primary">
                        {curCheckSheetInfo?.hasSecondReviewPermission ? '第一点评未完成,暂不可点评' : '无点评权限'}
                      </Button>
                    )}
                  </React.Fragment>
                )}
              </div>
            </div>
          </div>
        )}
        {!roleTypeIsManage() && (
          <div className="flex gap-x-4 justify-between">
            {permissionsMap.has(AuthorityEnum.申诉) && curCheckSheetInfo?.hasCanAppeal && !!complaintsItems?.length && (
              <div className="grow py-2 px-2">
                <Button
                  block
                  color={'primary'}
                  onClick={() => {
                    setComplaintsPopupVisible(true);
                  }}
                >
                  申诉
                </Button>
              </div>
            )}
            {!!activeWorksheetId &&
              detailData?.status !== Report_STATUS_TEXT.已点评 &&
              curCheckSheetInfo?.categories?.[0]?.items?.length &&
              curCheckSheetInfo?.status !== Report_STATUS_TEXT.已点评 &&
              curCheckSheetInfo?.submitted &&
              dayjs() < dayjs(curCheckSheetInfo?.taskDeadline) && (
                <div className="grow py-2 px-2">
                  <Button
                    block
                    color={'primary'}
                    onClick={() => {
                      dealReport('RECALL_TABLE' as any);
                    }}
                  >
                    {'撤回检查表'}
                  </Button>
                </div>
              )}
          </div>
        )}
      </div>
      <ComplaintsPopup
        visible={complaintsPopupVisible}
        onClose={() => setComplaintsPopupVisible(false)}
        complaintsItems={complaintsItems}
        taskId={taskId!}
        reFresh={reFresh}
      />
    </Loading>
  );
};
export default observer(ReportDetail);
