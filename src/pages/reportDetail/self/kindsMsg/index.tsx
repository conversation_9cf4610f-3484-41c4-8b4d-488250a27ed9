import ItemTag from '@src/components/OptionsDetail/itemTag';
import { statistics } from '@src/pages/selfCheckComment/api.type';
import { Grid } from 'antd-mobile';
import styles from './index.module.scss';

export const errorsRender = [
  {
    key: 'ALL_CATEGORY_SCORE',
    render: (isShow: boolean, count: number) =>
      isShow && (
        <li key="ALL_CATEGORY_SCORE" className="flex  items-center">
          <ItemTag color="#F53F3F" bg="rgba(245, 63, 63, 0.05)" borderColor="#FBACA3" text="S项" />
          <div className="flex-1 ml-2 text-xs ">有 {count} 个S项不合格，其所在的整个分类得0分。</div>
        </li>
      ),
  },
  {
    key: 'WORKSHEET_SCORE',
    render: (isShow: boolean, count: number) =>
      isShow && (
        <li key="WORKSHEET_SCORE" className="flex  items-center">
          <ItemTag color="#F53F3F" bg="rgba(245, 63, 63, 0.05)" borderColor="#FBACA3" text="S项" />
          <div className="flex-1 ml-2  text-xs ">有 {count} 个S项不合格，整个检查表得 0 分。</div>
        </li>
      ),
  },
  {
    key: 'REPORT_SCORE',
    render: (isShow: boolean, count: number, radio?: number) =>
      isShow && (
        <li key="REPORT_SCORE" className="flex  items-center">
          <ItemTag color="#F53F3F" bg="rgba(245, 63, 63, 0.05)" borderColor="#FBACA3" text="S项" />
          <div className="flex-1 ml-2  text-xs ">
            有 {count} 个S项不合格，整个报告得分扣除{radio}%。
          </div>
        </li>
      ),
  },
  {
    key: 'PENALTY',
    render: (isShow: boolean, count: number) =>
      isShow && (
        <li key="PENALTY" className="flex  items-center">
          <ItemTag color="#4E5969" bg="rgba(78, 89, 105, 0.05)" borderColor="#C9CDD4" text="罚款项" />
          <div className="flex-1 ml-2  text-xs ">有 {count} 个罚款项不合格，请留意。</div>
        </li>
      ),
  },
  {
    key: 'KEY',
    render: (isShow: boolean, count: number) =>
      isShow && (
        <li key="KEY" className="flex  items-center">
          <ItemTag color="#4E5969" bg="rgba(78, 89, 105, 0.05)" borderColor="#C9CDD4" text="关键项" />
          <div className="flex-1 ml-2  text-xs ">有 {count} 个关键项不合格，其所在的整个分类得0分。</div>
        </li>
      ),
  },
  {
    key: 'YELLOW',
    render: (isShow: boolean, count: number) =>
      isShow && (
        <li key="YELLOW" className="flex  items-center">
          <ItemTag color="#4E5969" bg="rgba(78, 89, 105, 0.05)" borderColor="#C9CDD4" text="M项" />
          <div className="flex-1 ml-2  text-xs ">有 {count} 个M项不合格，其所在的整个分类得0分。</div>
        </li>
      ),
  },
];

const KindsMsg: React.FC<{ statistics: statistics; config: any }> = ({ statistics, config }) => {
  const { categoryStatistics, tagStatistics } = statistics || {};
  console.log(statistics, '=statistics');
  const flag = config?.REPORT_SHOW_PASS_RATE === 'category';

  return (
    <div>
      <ul className={styles.categorySummary}>
        <Grid className="text-xs px-3 leading-3 text-[#5E5E5E]" columns={2} gap={20}>
          <Grid.Item>得分</Grid.Item>
          <Grid.Item className="flex justify-end">{!flag ? '合格率' : '合格项'}</Grid.Item>
        </Grid>
        {categoryStatistics?.map(({ fullScore, id, itemCount, qualifiedItemCount, reviewCount, name, score }) => {
          return (
            <div key={id} className="mt-3 text-xs text-[#5E5E5E] px-3 leading-[12px]">
              <Grid columns={2} gap={10} className={styles.categoryPercent}>
                <Grid.Item>
                  <div
                    style={{
                      width: `${(score * 100) / fullScore}%`,
                      color: 'red',
                    }}
                  />
                </Grid.Item>
                <Grid.Item>
                  <div
                    style={{
                      width: `${(qualifiedItemCount * 100) / itemCount}%`,
                    }}
                  />
                </Grid.Item>
              </Grid>
              <Grid columns={3} gap={20} className="mt-2">
                <Grid.Item>
                  <div>
                    {score}/{fullScore}
                  </div>
                </Grid.Item>
                <Grid.Item className="flex justify-center">
                  <div>{name.length > 7 ? `${name.substring(0, 7)}...` : name}</div>
                </Grid.Item>
                <Grid.Item className="flex justify-end">
                  <div>
                    {!flag
                      ? `${itemCount ? Math.round((qualifiedItemCount / itemCount) * 100) : 0}%`
                      : `${qualifiedItemCount}/${itemCount}`}
                  </div>
                </Grid.Item>
              </Grid>
            </div>
          );
        })}
      </ul>

      {/* <ul className={styles.warning}>
        {tagStatistics?.map((item) => {
          let showkey = item?.tag === 'RED_LINE' ? item?.configType : item?.tag;
          let showradio = showkey === 'REPORT_SCORE' ? item.rate : undefined;
          return errorsRender.map(({ key, render }) => render(key === showkey, item.quantity, showradio));
        })}
      </ul> */}
    </div>
  );
};
export default KindsMsg;
