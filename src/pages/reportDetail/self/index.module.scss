.iAdmForm {
  :global {
    .adm-list-item {
      padding-left: 0;

      &:not(:first-child) {
        border-top: 1px solid #f6f6f6;
      }
    }

    .adm-list-item-content {
      padding-left: 16px;
      padding-right: 16px;
      border-top: 0;
    }

    .adm-list-item-content-main {
      padding: 16px 0;
    }

    .adm-form-item-horizontal .adm-list-item-content-prefix {
      padding-top: 16px;
      padding-bottom: 16px;
    }
  }
}

.iAdmSlider {
  :global {
    .adm-slider-thumb-container {
      width: 24px;
      height: 24px;
    }

    .adm-slider-thumb {
      width: 20px;
      height: 20px;
    }

    .adm-slider-mark {
      display: none;
    }

    .adm-slider-track,
    .adm-slider-fill {
      height: 8px;
      border-radius: 100px;
    }

    .adm-slider-track {
      background-color: #e8f4f4;
    }
  }
}

.ISteps2 {
  :global {
    .adm-step-status-finish .adm-step-icon-dot {
      outline-width: 4px;
      outline-color: #b3ebe9;
      outline-style: solid;
    }

    .adm-step-status-finish {
      --line-to-next-color: var(--adm-color-border);
    }

    .adm-step-status-wait .adm-step-icon-dot {
      width: 10px;
      height: 10px;
    }

    .adm-step-content {
      padding-bottom: 0.8125rem !important;

      .adm-step-title {
        line-height: 1.25rem !important;
      }
    }

    .adm-steps-vertical .adm-step .adm-step-content {
      padding-bottom: 20px;
    }
  }
}
