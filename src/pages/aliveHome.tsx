import WithKeepAlive from '@src/components/WithKeepAlive';
import { observer } from 'mobx-react';
import { useState } from 'react';
import { Link } from 'react-router-dom';

function PagesHome() {
  const [first, setFirst] = useState(0);
  return (
    <div className="h-full overflow-scroll">
      {first}
      <div>
        <button
          className="px-2 py-1"
          onClick={() => {
            setFirst((pre) => ++pre);
          }}
        >
          +
        </button>
        {Array(20)
          .fill(0)
          .map((_, idx) => (
            <Link to="/welcome" className="px-20 py-10 flex flex-col">
              {idx}
            </Link>
          ))}
      </div>
    </div>
  );
}

export default WithKeepAlive(PagesHome);
