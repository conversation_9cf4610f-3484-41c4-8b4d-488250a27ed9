import { SecondReviewAppealStatusEnum } from './enum';
import { SelfscoreTypeEnum } from './selfCheckList/selfButtonGroup';

export type CheckItem = {
  abnormalButtonName: Nullable<string>;
  abnormalMustSelectReason: Nullable<string>;
  abnormalReasons: Nullable<string>;
  accentedTermTags: ['PENALTY', 'RED_LINE', 'NECESSARY', 'YELLOW', 'KEY'];
  actionType: string;
  afterActionInputBoxRemainderCopy: Nullable<string>;
  afterActionPhotoType: Nullable<string>;
  afterActionRelationSopId: Nullable<string>;
  afterActionType: 'NOT_ACTION';
  allowUploadSystemFile: 'NOT_ALLOW';
  content: string;
  status: any;
  hasApply: boolean;
  normalButtonName: Nullable<string>;
  qualified: boolean;
  hasFillItem: boolean;
  reformLimit: Nullable<string>;
  reportDisplayScore: boolean;
  reportReviewAbnormalButtonName: string;
  reportReviewAbnormalMustSelectReason: boolean;
  selectedSecondReasons: string[];
  secondOtherReason: string;
  reportReviewAbnormalReasons: string[];
  reportReviewMethod: string;
  itemRemark: string;
  reportReviewNormalButtonName: string;
  reportReviewScoreMax: number;
  reportReviewScoreMin: 0;
  reportReviewScoreType: SelfscoreTypeEnum; // 得分项|扣分项
  reportReviewStandard: string;
  score: number;
  selectedChooseNonconformityReasons: string[];
  otherReason: string;
  shopCheckContent: {
    abnormalReasons: string[];
    images: Images[];
    normalFlag: boolean;
    text: string;
  };
  sopId: Nullable<number>;
  sort: number;
  stermDeductionConfigRate: 100;
  stermDeductionConfigType: 'ALL_CATEGORY_SCORE';
  submitTime: Nullable<string>;
  taskWorksheetItemId: number;
  taskItemId: number;
  taskWorksheetCategoryId: number;
  worksheetItemId: number;
  explain: string;
  expirationTimes: {
    instant: string;
    status: 'VALID' | 'TODAY_PAST_DUE' | 'PAST_DUE';
    imageId?: string;
    imageUrl?: string;
  }[];
  secondScore?: number;
  secondReasons?: string[];
  secondHasApply: boolean;
  secondHasFillItem: boolean;
  secondItemRemark: string;
  secondQualified: boolean;
  sampleImageUrls: { snapshotUrl: string; url: string }[];
  differentItemLabel: string;
  shopHasApply: boolean;
  appealStatus: AppealStatus;
  secondReviewAppealStatus?: SecondReviewAppealStatusEnum;
};

export enum AppealStatus {
  未申诉 = 'NO_APPEAL',
  发起 = 'CREATE',
  通过 = 'PASSED',
  驳回 = 'REJECTED',
  撤回 = 'RECALL',
  已过期 = 'AUDIT_TIMEOUT',
  审核超时转派 = 'AUDIT_TIMEOUT_TRANSFER',
  已作废 = 'INVALID',
}
export type Images = {
  contentType: string;
  fileType: string;
  id: string;
  name: string;
  url: string;
};
export type CategoryItem = {
  items: CheckItem[];
  categoryId: number;
  name: string;
  taskCategoryId: number;
  validityDtos: CheckItem[];
  itemCount: number;
  filledCount: number;
};

export type sheetsItem = {
  categories: CategoryItem[];
  name: string;
  filledCount: number;
  itemCount: number;
  status: 'UN_SUBMITTED' | 'SUBMITTED' | 'REVIEWED';
  taskWorksheetId: number;
  reviewPermission: boolean;
  reviewRole: number;
  reviewUserId: number;
  worksheetId: number;
  worksheetType: string;
  generateRectification: boolean; // 是否发起整改
  noNeedReview: boolean; // 是否无需点评
  hasSecondReview: boolean; // 检查表是否可以开始二次点评
  hasSecondReviewPermission: boolean; // 是否是第二点评人
  secondReviewed: boolean;
  reviewUserName?: string | null;
  reviewerRoles?: string | null;
  corpSummary?: string;
  hasRectificationOperation: boolean;
  reviewTime: string;
  autoReview: boolean;
  submitted?: boolean;
  taskDeadline?: string;
  reviewTimeExpired?: boolean; // 第一点评时效已过期标识
  reviewPrescription?: string; // 点评时效时间
  hasCanAppeal?: boolean; // 是否可以申诉
};
export type statistics = {
  tagStatistics: {
    configType: string;
    quantity: number;
    rate: number;
    tag: string;
  }[];
  categoryStatistics: {
    fullScore: number;
    id: number;
    itemCount: number;
    name: string;
    qualifiedItemCount: number;
    reviewCount: Nullable<number>;
    score: number;
  }[];
};
export type SelfReportDetail = {
  end: string;
  end2: string;
  id: number;
  score: number;
  latestSubmitTime: string;
  latestReviewTime: string;
  issuesCount: number;
  reformStatus: 'REFORM_FEEDBACK' | 'WAIT_AUDIT' | 'EXPIRED' | 'PASS';
  waitAuditCount: number;
  shopId: string;
  start: string;
  waitRectifiedCount: number;
  latestRectificationSubmitTime: string;
  taskStatus: 'IN_PROGRESS' | 'COMPLETED' | 'OVERDUE';
  hasRectificationOperation: boolean;
  shopName: string;
  status: 'UN_SUBMITTED' | 'SUBMITTED' | 'REVIEWED'; // UN_SUBMITTED待提交  //SUBMITTED已提交 //REVIEWED已点评
  unreviewed: boolean;
  taskDate: string;
  taskName: string;
  statistics: statistics;
  worksheets: sheetsItem[];
  corpSummary: string;
  secondSummary: string;
  hasSecondReview: boolean; // 是否有第二点评人权限
  secondReview: boolean; // 第二点评人是否已点评
  /** 是否可以发起第二点评申诉 */
  hasSecondReviewCanAppeal?: boolean;
  summaryDtos: {
    roleNames: string[];
    summary: string;
    userId: number;
    username: string;
  }[];
  autoReview: boolean;
  shopSummary: string;
  actualScore: number;
  fullScore: number;
  noReformCount: number;
  qualifiedItemCount: number;
  qualifiedCategoryCount: number;
  checkItemCount: number;
  categoryCount: number;
};
export type UpdateSelfCheckItemParams = {
  itemId: number;
  qualified: Nullable<boolean>;
  reasons?: any;
  score?: number | null | undefined;
  itemRemark?: string;
  reformLimit?: number;
  copyUserIds?: number[];
};
export type secondUpdateItemParams = {
  itemId: number;
  secondQualified: boolean | null;
  secodnReasons?: any;
  taskCategoryId: number | undefined;
  secondHasApply?: boolean | null;
  secondScore?: number | null | undefined;
  secondItemRemark?: any;
};
