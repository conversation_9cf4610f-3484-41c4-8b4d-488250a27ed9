export enum TASK_STATUS {
  IN_PROGRESS = '进行中',
  COMPLETED = '完成',
  OVERDUE = '逾期',
}
export enum TASK_STATUS_Text {
  进行中 = 'IN_PROGRESS',
  完成 = 'COMPLETED',
  逾期 = 'OVERDUE',
}
export enum Report_STATUS {
  UN_SUBMITTED = '待提交',
  SUBMITTED = '待点评',
  REVIEWED = '已点评',
}

export enum Report_STATUS_TEXT {
  待提交 = 'UN_SUBMITTED',
  已提交 = 'SUBMITTED',
  已点评 = 'REVIEWED',
}
export enum Work_Sheet_TEXT {
  自检表 = 'SELF',
  效期表 = 'VALIDITY',
}

export enum SecondReviewAppealStatusEnum {
  未申诉 = 'NO_APPEAL',
  发起 = 'CREATE',
  通过 = 'PASSED',
  驳回 = 'REJECTED',
  撤回 = 'RECALL',
  审核超时 = 'AUDIT_TIMEOUT',
}
