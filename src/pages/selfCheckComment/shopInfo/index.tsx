import { useState } from 'react';
import { IconFont } from '@src/components';
import OperateLogPopup from '@src/pages/reportDetail/patrol/basicInfo/operateLogPopup';
import PhonePopup from '@src/pages/reportDetail/patrol/basicInfo/phonePopup';
import { roleTypeIsManage } from '@src/utils/tokenUtils';
import dayjs from 'dayjs';
import { SelfReportDetail } from '../api.type';
import { Report_STATUS, TASK_STATUS_Text } from '../enum';

interface BASICINFO {
  detail: SelfReportDetail;
}

const ShopInfo: React.FC<BASICINFO> = (props: BASICINFO) => {
  const { detail } = props;
  // const isDudao = roleTypeIsManage();
  const [logPopupVisible, setLogPopupVisible] = useState<boolean>(false);
  const [phonePopupVisible, setPhonePopupVisible] = useState<boolean>(false);

  const taskStatusShow = (taskStatus: 'IN_PROGRESS' | 'COMPLETED' | 'OVERDUE') => {
    let text = '';
    if (taskStatus === TASK_STATUS_Text.进行中 && dayjs(detail?.end) > dayjs()) {
      return (text = '进行中');
    }
    if (taskStatus === TASK_STATUS_Text.进行中 && dayjs(detail?.end2) > dayjs()) {
      return (text = '逾期进行中');
    }
    if (taskStatus === TASK_STATUS_Text.完成) {
      return (text = '已提交');
    }
    if (taskStatus === TASK_STATUS_Text.进行中 && dayjs() > dayjs(detail?.end2)) {
      return (text = '已关闭');
    }
  };
  return (
    <div className="bg-white">
      <div className="flex text-base items-center mb-3">
        <div className="text-primary mr-2">{Report_STATUS[detail?.status]}</div>
        <div className=" font-[500] ">
          {detail?.shopId} {detail?.shopName}
        </div>
      </div>
      <div className="text-[#858585] text-sm py-3 px-2 mt-3 bg-[#FAFAFA] rounded-lg">
        <div>任务单号:{detail?.id}</div>
        <div>任务名称:{detail?.taskName}</div>
        <div>
          任务状态:
          {detail?.taskStatus === 'OVERDUE'
            ? '已关闭'
            : detail?.taskStatus === 'EXPIRED'
              ? '逾期进行中'
              : taskStatusShow(detail?.taskStatus)}
        </div>
        <div className="text-nowrap">
          执行时段:{dayjs(detail?.start).format('YYYY/MM/DD HH:mm:ss')}至
          {dayjs(detail?.end).format('YYYY/MM/DD HH:mm:ss')}
        </div>
      </div>
      <div className="justify-between flex mt-2">
        <div
          className="text-primary text-sm flex  items-center"
          onClick={() => {
            setLogPopupVisible(true);
          }}
        >
          操作记录
          <IconFont type="icon-chevron-right" className="text-xs" />
        </div>
        <div
          className="text-primary text-sm flex  items-center"
          onClick={() => {
            setPhonePopupVisible(true);
          }}
        >
          <span className="ml-1">联系店长</span>
          <IconFont type="icon-rongqi" />
        </div>
      </div>

      <OperateLogPopup
        checkType="SELF"
        title="操作记录"
        taskId={detail?.id}
        visible={logPopupVisible}
        onClose={() => setLogPopupVisible(false)}
      />

      <PhonePopup
        title="联系方式"
        shopId={detail?.shopId}
        // shopId={'T463'}
        visible={phonePopupVisible}
        onClose={() => setPhonePopupVisible(false)}
      />
    </div>
  );
};
export default ShopInfo;
