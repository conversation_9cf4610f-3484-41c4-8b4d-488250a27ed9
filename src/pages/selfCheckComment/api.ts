import { get, post } from '@src/api';
import { userStore } from '@src/store';
import { isNull } from 'lodash';
import { toJS } from 'mobx';
import { secondUpdateItemParams, SelfReportDetail, sheetsItem, UpdateSelfCheckItemParams } from './api.type';

// const getSelfReportDetailButtonEnums = (
//   detail: SelfReportDetail,
//   isSuper: boolean,
// ) => {
//   const {
//     status,
//     unreviewed,
//     reformStatus,
//     end,
//     end2,
//     issuesCount,
//     noReformCount,
//     latestReviewTime,
//     taskStatus,
//     hasRectificationOperation,
//     hasSecondReview,
//     secondReview,
//     autoReview,
//   } = detail;
//   let buttonEnums: string[] = [];
//   console.log(
//     dayjs()?.diff(dayjs(latestReviewTime), 'hour') < 3,
//     '=  dayjs(latestReviewTime)?.diff(day',
//   );

//   /** 是否展示撤回点评按钮 */
//   const showRevokeReportComment = (() => {
//     if (
//       status === Report_STATUS_TEXT.已点评 &&
//       reformStatus !== ERreformStatus.已整改 &&
//       dayjs()?.diff(dayjs(latestReviewTime), 'hour') < 3
//     ) {
//       // 没有问题项
//       if (!issuesCount) {
//         return true;
//       } else {
//         // 有问题项，门店还未操作时
//         if (!hasRectificationOperation) {
//           return true;
//         } else if (!noReformCount) {
//           // 有问题项，已全部整改完毕
//           return false;
//         } else {
//           // 有问题项，门店已有操作
//           return !!isSuper;
//         }
//       }
//     }
//   })();
//   if (roleTypeIsManage()) {
//     // 督导
//     if (
//       (status === Report_STATUS_TEXT.已提交 && unreviewed) ||
//       (hasSecondReview && !secondReview)
//     ) {
//       buttonEnums = ['SUBMIT_REPORT_COMMENT'];
//     }
//     if (!autoReview && showRevokeReportComment && !hasSecondReview) {
//       // 自动提交的报告没有撤回按钮and第二点评人没有撤回按钮
//       buttonEnums = ['REVOKE_REPORT_COMMENT'];
//     }
//   } else {
//     // 店长
//     if (
//       status === Report_STATUS_TEXT.已提交 &&
//       taskStatus === yytTaskPlanStatusEnum.已结束 &&
//       (dayjs(end) > dayjs() || dayjs(end2) > dayjs())
//     ) {
//       buttonEnums = ['RECALL_REPORT'];
//     }
//   }
//   return buttonEnums;
// };

// 获取自检检查表点评数据
export const formatData = (firstResult: any, secondResult: any) => {
  return !isNull(secondResult) ? secondResult : firstResult;
};
export const formatWheets = (Worksheets: sheetsItem[]) => {
  const { userInfo } = userStore;
  console.log(toJS(userInfo), '=123userInfo');

  const initWorksheets = Worksheets || [];
  for (let i = 0; i < initWorksheets?.length; i++) {
    const curWorkSheet = initWorksheets[i];
    console.log(curWorkSheet, '=123curWorkSheet');
    const isSame = curWorkSheet?.reviewUserId === userInfo?.userId;
    const { categories } = curWorkSheet;
    for (let j = 0; j < categories.length; j++) {
      const cateitems = categories[j].items;
      for (let y = 0; y < cateitems.length; y++) {
        let initItem = categories?.[j]?.items[y];
        if (isSame) {
          // 第二点评人与第一点评人同一个人时 第二点评默认为第一点评结果
          initItem = {
            ...initItem,
            secondItemRemark: formatData(initItem?.itemRemark, initItem?.secondItemRemark),
            secondScore: formatData(initItem?.score, initItem?.secondScore),
            secondHasApply: formatData(initItem?.hasApply, initItem?.secondHasApply),
            secondHasFillItem: true,
            secondQualified: formatData(initItem?.qualified, initItem?.secondQualified),
          };
        }
        curWorkSheet.categories[j].items[y] = initItem;
      }
    }
    initWorksheets[i] = { ...curWorkSheet };
  }
  return initWorksheets;
};

export const getSelfReportReview = async (
  taskId: string,
  notFilledItemHandleType?: Nullable<string>,
  isDetail?: boolean,
) => {
  const detail = await get<SelfReportDetail>(
    `/om-api/common/self-task/detail?taskId=${taskId}${notFilledItemHandleType ? `&notFilledItemHandleType=${notFilledItemHandleType}` : ''}`,
    {},
  );
  return {
    ...detail,
    worksheets: isDetail ? detail?.worksheets : formatWheets(detail?.worksheets),
  };
};

export const getCycleJobCount = async (taskId: string) =>
  get<any>(`/om-api/common/patrol/issue/cycle-job-count?id=${taskId}`);

// common/patrol/issue/cycle-job-list
export const getCycleJobList = async (taskId: string) =>
  get<any>(`/om-api/common/patrol/issue/cycle-job-list?id=${taskId}`);

export const suspendCycleJob = async (data: { id: number; reason: string }) =>
  post<any>(`/om-api/corp/patrol/issue/cycle-job/suspend`, { data });

export const getIsSuper = async () => get<any>(`/om-api/corp/user/sa`);

// 第一点评人项
export const updateSelfCheckItem = (data: UpdateSelfCheckItemParams) => {
  return post<{
    taskId?: number;
    filledItemCount?: number;
    relatedFilledItemCount?: number; // 必检项
    allFilledItemCount?: number;
    copyUserIds?: number[];
    status?: number;
  }>('/om-api/corp/self-task/item/review', { data });
};
// 第二点评人项
export const secondUpdateCheckItem = (data: secondUpdateItemParams) => {
  return post('/om-api/corp/self-task/second/item/save', { data });
};
// 第二点评人提交点评
export const secondSubmitComment = (data: { taskId: number; notFilledItemHandleType?: string }) => {
  return post('/om-api/corp/self-task/second/report-review', { data });
};

// 第二点评人点评总结
export const secondCommentReport = (data: { taskId: number; summary: string }) => {
  return post('/om-api/corp/self-task/second/report-summary', { data });
};

// 点评不适用
export const updateNotApplyItem = (data: { data: number }) => {
  return post<{
    taskId?: number;
    checkTypeId?: number;
    filledItemCount?: number;
    relatedFilledItemCount?: number; // 必检项
    allFilledItemCount?: number;
    copyUserIds?: number[];
    status?: number;
  }>('/om-api/corp/self-task/not-apply', { data });
};

// 自检点评总结
export const commentReport = (data: { taskId: number; summary: string }) => {
  return post('/om-api/corp/self-task/corp-summary', { data });
};
// 单个自检检查表点评总结
export const commentSingleSheet = (data: { taskId: number; worksheetId: number; summary: string }) => {
  return post('/om-api/corp/self-task/worksheet/corp-summary', { data });
};
// 提交点评
export const submitSelfComment = (data: { taskId: number; notFilledItemHandleType: string }) => {
  return post('/om-api/corp/self-task/review-submit', { data });
};
// 撤回点评
export const revoleSelfReport = (data: { data: number }) => {
  return post('/om-api/corp/self-task/revocation-review', { data });
};
// 撤回单个检查表点评
export const revoleSingleSheet = (data: { taskId: number; worksheetId: number }) => {
  return post('/om-api/corp/self-task/worksheet/revocation-review', { data });
};
// 店长撤回报告
export const revocationSubmit = (data: { data: number }) => {
  return post('/om-api/shop/self-task/revocation-submit', { data });
};

// 店长撤回检查表
export const revokeWorkSheet = (data: { taskId?: number; worksheetId?: number }) => {
  return post('/om-api/shop/self-task/worksheet/revocation-submit', { data });
};
