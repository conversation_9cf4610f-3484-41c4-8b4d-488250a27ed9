import { useMemo, useState } from 'react';
import { Loading } from '@src/components';
import { useCanReviewFlag } from '@src/hooks/useCanReviewFlag';
import { SelfCheckStore } from '@src/store';
import { getUserId } from '@src/utils/tokenUtils';
import { useRequest } from 'ahooks';
import { Button as AntdButton } from 'antd';
import { ActionSheet, Button, Toast } from 'antd-mobile';
import { Action } from 'antd-mobile/es/components/action-sheet';
import { toJS } from 'mobx';
import { observer } from 'mobx-react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Report_STATUS_TEXT } from './enum';
import SelfCheckList from './selfCheckList';
import { SelfButtonTypeEnum } from './selfCheckList/selfButtonGroup';
import ShopInfo from './shopInfo';
const actions: Action[] = [
  { text: '设为“合格”(得满分)', key: 'SET_FULL_SCORE' },
  { text: '设为“不适用”(不计分)', key: 'SET_NOT_APPLY' },
];
const SelfCheckComment = observer(() => {
  const navigate = useNavigate();
  const [routerParams] = useSearchParams();
  const userId = getUserId();
  console.log(userId, '=userId');
  const { getPreviewInfos, previewInfos, selfWorkSheetList, curCheckSheetInfo } = SelfCheckStore;
  const [actionVisible, setActionVisible] = useState(false);
  const [isNoApplyAll, setNoApplyAll] = useState(false);
  const { taskId } = useMemo(() => {
    const taskId = routerParams.get('taskId');
    return {
      taskId,
    };
  }, [routerParams]);
  const { loading } = useRequest(
    async () => {
      if (!taskId) {
        // eslint-disable-next-line no-throw-literal
        throw null;
      }
      await getPreviewInfos({
        taskId,
      });
    },
    { refreshDeps: [taskId] },
  );

  // const isAuthComment: boolean = useMemo(() => {
  //   return !!curCheckSheetInfo?.reviewPermission && curCheckSheetInfo?.status !== Report_STATUS_TEXT.已点评;
  // }, [curCheckSheetInfo]);

  const isNoNeedCommentSheet: boolean = useMemo(() => {
    const isNoNeed = (curCheckSheetInfo?.categories || [])?.every((v) => {
      return (v?.items || []).every((v2) => v2?.reportReviewMethod === SelfButtonTypeEnum.不需要点评);
    });
    return isNoNeed;
  }, [curCheckSheetInfo]);

  console.log(toJS(isNoNeedCommentSheet), '=isNoNeedCommentSheet');
  console.log(toJS(curCheckSheetInfo), '=curCheckSheetInfo');

  const preview = () => {
    if (curCheckSheetInfo?.hasSecondReview) {
      // 第二点评预览报告
      let secondUnfillCount = 0;
      const secondCommentSheetList =
        toJS(selfWorkSheetList)?.filter(
          ({ worksheetType, status }) => worksheetType !== 'VALIDITY' && status === Report_STATUS_TEXT.已点评,
        ) || [];
      secondCommentSheetList.forEach((v) => {
        const groups = v.categories || [];
        for (let j = 0, len = groups.length; j < len; j++) {
          const items = groups[j].items || [];
          for (const item of items) {
            if (!item.secondHasFillItem && item?.reportReviewMethod !== SelfButtonTypeEnum.不需要点评) {
              secondUnfillCount++; // 未填项
            }
          }
        }
      });
      if (secondUnfillCount > 0) {
        return Toast.show({
          content: `有${secondUnfillCount}个项未填写，请补充完整后再预览报告。`,
          afterClose: () => {},
        });
      }

      return navigate(`/self/reportdetail?taskId=${taskId}`, {
        replace: true,
      });
    }
    // 第一点评人预览报告
    let qualifiedCount = 0; // 非必检项未填 let noApplyAll = false;
    let unfilledCount = 0; // 必检项未填
    let noApplyAll = false;
    const needCommentSheetList =
      toJS(selfWorkSheetList)?.filter(
        ({ worksheetType, status, reviewPermission, reviewTimeExpired }) =>
          worksheetType !== 'VALIDITY' && status !== 'UN_SUBMITTED' && reviewPermission && !reviewTimeExpired,
      ) || [];
    for (let i = 0, len = needCommentSheetList.length; i < len; i++) {
      noApplyAll = (needCommentSheetList[i].categories || []).every((v) => {
        return (v.items || []).every((v2) => !v2.hasApply);
      });
    }
    needCommentSheetList.forEach((v) => {
      const groups = v.categories || [];

      for (let j = 0, len = groups.length; j < len; j++) {
        const items = groups[j].items || [];

        for (const item of items) {
          if (!item.hasFillItem && item?.reportReviewMethod !== SelfButtonTypeEnum.不需要点评) {
            qualifiedCount++; // 未填项
            if (item?.accentedTermTags?.includes('NECESSARY')) {
              unfilledCount++;
            }
          }
        }
      }
    });
    if (unfilledCount > 0) {
      return Toast.show({
        content: `有${unfilledCount}个必检项未填写，请补充完整后再预览报告。`,
        afterClose: () => {},
      });
    }
    if (qualifiedCount > 0) {
      setNoApplyAll(noApplyAll);
      setActionVisible(true);
      return;
    }
    navigate(`/self/reportdetail?taskId=${taskId}&notFilledItemHandleType=SET_FULL_SCORE`, {
      replace: true,
    });
  };

  const { isNoShowBtn, isFirstUnReviewed } = useMemo(() => {
    const isNoShowBtn =
      curCheckSheetInfo?.reviewPermission &&
      curCheckSheetInfo?.status === Report_STATUS_TEXT.已点评 &&
      !curCheckSheetInfo?.noNeedReview;
    const isFirstUnReviewed =
      curCheckSheetInfo?.hasSecondReviewPermission &&
      curCheckSheetInfo?.status !== Report_STATUS_TEXT.已点评 &&
      !curCheckSheetInfo.reviewPermission;
    return {
      isNoShowBtn,
      isFirstUnReviewed,
    };
  }, [curCheckSheetInfo]);

  const canReviewFlag = useCanReviewFlag(curCheckSheetInfo, curCheckSheetInfo?.hasSecondReview);
  return (
    <Loading spinning={loading}>
      <div className="flex flex-col fixed inset-x-0 inset-y-0">
        <div className="grow overflow-y-scroll">
          <div className="bg-white leading-[14px] mb-2">
            <div className="text-sm font-medium p-4">
              <ShopInfo detail={previewInfos!} />
            </div>
          </div>
          <div className="mb-2">
            <SelfCheckList canReviewFlag={canReviewFlag} />
          </div>
        </div>
        {curCheckSheetInfo?.hasSecondReview ? ( // 第二点评人默认有全部权限
          !curCheckSheetInfo.secondReviewed && (
            <Button
              onClick={preview as any}
              disabled={curCheckSheetInfo?.noNeedReview}
              color="primary"
              fill="solid"
              block
              className="text-base"
            >
              {curCheckSheetInfo?.noNeedReview ? '不需要点评' : '预览报告'}
            </Button>
          )
        ) : !isNoShowBtn &&
          (canReviewFlag || !curCheckSheetInfo?.reviewPermission || curCheckSheetInfo?.noNeedReview) ? (
          <div className="shrink-0 py-2 px-2 bg-[#fff]">
            <Button
              disabled={
                (curCheckSheetInfo?.hasSecondReview ? false : !curCheckSheetInfo?.reviewPermission) ||
                curCheckSheetInfo?.noNeedReview ||
                !curCheckSheetInfo?.reviewPermission
              }
              onClick={preview as any}
              color="primary"
              fill="solid"
              block
              className="text-base"
            >
              {isFirstUnReviewed
                ? '第一点评未完成,暂不可点评'
                : !curCheckSheetInfo?.reviewPermission
                  ? '无点评权限'
                  : curCheckSheetInfo?.noNeedReview
                    ? '不需要点评'
                    : '预览报告'}
            </Button>
          </div>
        ) : (
          !isNoShowBtn &&
          !canReviewFlag && (
            <div className="shrink-0 py-2 px-2 bg-[#fff]">
              <AntdButton disabled={true} block className="text-base" size="large">
                未按时完成，点评已关闭
              </AntdButton>
            </div>
          )
        )}
      </div>
      <ActionSheet
        extra={
          <div className="text-center">
            <div>预览报告失败</div>
            <div>有检查项未填写，请选择处理方案</div>
          </div>
        }
        cancelText="返回继续填写"
        visible={actionVisible}
        actions={actions}
        onAction={(res) => {
          if (res.key === 'SET_NOT_APPLY' && isNoApplyAll) {
            return Toast.show({
              content: '不能所有项都是“不适用”',
            });
          }
          navigate(`/self/reportdetail?taskId=${taskId}&notFilledItemHandleType=${res.key}`, {
            replace: true,
          });
        }}
        onClose={() => setActionVisible(false)}
      />
    </Loading>
  );
});
export default SelfCheckComment;
