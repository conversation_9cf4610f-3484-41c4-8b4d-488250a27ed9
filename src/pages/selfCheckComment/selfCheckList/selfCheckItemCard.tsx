import React, { useMemo, useState } from 'react';
import { QuestionTags } from '@src/components/OptionsDetail';
import UnqualifiedPopup from '@src/components/OptionsDetail/UnqualifiedPopup';
import SampleImage from '@src/components/sampleImage';
import VideoViewer from '@src/components/VideoViewer';
import { SelfCheckStore } from '@src/store';
import { roleTypeIsManage } from '@src/utils/tokenUtils';
import { Badge, Image, ImageViewer, Tag } from 'antd-mobile';
import { observer } from 'mobx-react';
import styles from './index.module.scss';
import SecondCommentBtnGroup from './secondCommentBtnGroup';
import SelfButtonGroup, { SelfButtonTypeEnum, SelfscoreTypeEnum } from './selfButtonGroup';
import { CheckItem, secondUpdateItemParams, UpdateSelfCheckItemParams } from '../api.type';
import { Report_STATUS_TEXT } from '../enum';

interface Props {
  config?: any;
  reviewPermission: boolean;
  data: CheckItem;
  isDetail?: boolean;
  taskCategoryId?: number;
  reviewUserId: number;
  generateRectification?: boolean;
  updateItem?(payload: UpdateSelfCheckItemParams): void;
  secondUpdateItem?(payload: secondUpdateItemParams): void;
  showTips?(data: { title: string; desc: string }): void;
  showStandard?(standard: string): void;
  showSop?(sopId: number): void;
  onClick?(): void;
  reFresh?(): void;
  status: 'UN_SUBMITTED' | 'SUBMITTED' | 'REVIEWED';
  shopManagerName?: string;
  canReviewFlag?: boolean;
}
interface SelfScoreDetailProps {
  data: CheckItem;
}
export const reasonListText = (reasonLis: string[]) => {
  const text = reasonLis?.map((v, index) => {
    return <span key={index}>#{v}&nbsp;</span>;
  });
  return text || '';
};
export const FirstSelfScoreDetail: React.FC<SelfScoreDetailProps> = ({ data }) => {
  const [unqualifiedVisible, setUnqualifiedVisible] = useState(false);
  const [selectReasons, setSelectReasons] = useState<string[]>([]);
  const {
    reportReviewAbnormalButtonName,
    reportReviewNormalButtonName,
    reportReviewScoreMax,
    reportReviewScoreType,
    reportReviewMethod,
    score,
    qualified,
    itemRemark,
    selectedChooseNonconformityReasons,
    reportDisplayScore,
    hasApply,
    otherReason,
  } = data;
  const {
    curCheckSheetInfo: { hasSecondReview, secondReviewed },
  } = SelfCheckStore;
  const allReasons = useMemo(() => {
    return !!otherReason
      ? [...(selectedChooseNonconformityReasons || []), otherReason]
      : selectedChooseNonconformityReasons;
  }, [selectedChooseNonconformityReasons, otherReason]);

  return (
    <React.Fragment>
      {(!!itemRemark || !!allReasons) && (
        <div className="bg-[#FAFAFA] my-2 p-2">
          {itemRemark && <div className="break-all mb-2">{itemRemark}</div>}
          {!qualified && (
            <div
              className="break-all  ellipsis-2 text-primary"
              onClick={() => {
                setSelectReasons(allReasons);
                setUnqualifiedVisible(true);
              }}
            >
              {reasonListText(allReasons)}
            </div>
          )}
        </div>
      )}
      <div className={styles.DetailOperationArea}>
        <div className={styles.commentTip}>
          <Badge color="#378BFF" content={hasSecondReview || secondReviewed ? '1' : null} style={{ '--top': '100%' }}>
            <span>点评</span>
          </Badge>
        </div>
        <span
          role="button"
          className={`${styles.active} ${
            !!qualified ||
            (reportReviewMethod === SelfButtonTypeEnum.打分型 &&
              score === (reportReviewScoreType === SelfscoreTypeEnum.扣分项 ? 0 : reportReviewScoreMax))
              ? styles.pass
              : styles.fail
          }`}
        >
          {!hasApply
            ? '不适用'
            : reportReviewMethod === SelfButtonTypeEnum.判断型
              ? (!!qualified
                  ? `${reportReviewNormalButtonName || '合格'}`
                  : `${reportReviewAbnormalButtonName || '不合格'}`
                ).concat(
                  reportReviewScoreType === SelfscoreTypeEnum.扣分项
                    ? !!qualified
                      ? '(不扣分)'
                      : `(扣${score}分)`
                    : reportReviewScoreMax === 0 || !reportDisplayScore
                      ? ''
                      : `(${score}分)`,
                )
              : (reportReviewScoreType === SelfscoreTypeEnum.扣分项 ? score === 0 : score === reportReviewScoreMax)
                ? `合格(${reportReviewScoreType === SelfscoreTypeEnum.扣分项 ? '不扣' : score}分)`
                : `${reportReviewScoreType === SelfscoreTypeEnum.扣分项 ? '扣' : ''}${score}分${
                    reportReviewScoreType === SelfscoreTypeEnum.扣分项 ? '' : `(满分${reportReviewScoreMax}分)`
                  }`}
        </span>
      </div>

      <UnqualifiedPopup
        title="不合格原因"
        visible={unqualifiedVisible}
        selectReasons={selectReasons}
        onClose={() => setUnqualifiedVisible(false)}
      />
    </React.Fragment>
  );
};
const SelfCheckItemCard: React.FC<Props> = observer((props: Props) => {
  const {
    updateItem,
    secondUpdateItem,
    data,
    status, // 当前检查表状态
    showStandard,
    showSop,
    isDetail,
    taskCategoryId,
    config,
    reviewUserId,
    reviewPermission,
    generateRectification,
    canReviewFlag,
    reFresh,
  } = props;

  const {
    curCheckSheetInfo: { hasSecondReview, secondReviewed },
  } = SelfCheckStore;

  const isDudao = roleTypeIsManage();
  const {
    taskWorksheetItemId,
    content,
    reportReviewStandard,
    sopId,
    shopCheckContent,
    accentedTermTags,
    reportReviewMethod,
    status: itemStatus,
    normalButtonName,
    abnormalButtonName,
    sampleImageUrls,
    differentItemLabel,
    shopHasApply,
  } = data;
  const [videoPreviewUrl, setVideoPreviewUrl] = useState('');
  const [videoPreviewVisible, setVideoPreviewVisible] = useState(false);

  const imagesOrVideoRender = (images: { url: string; fileType: string; id: string; snapshotUrl: string }[]) => {
    return (
      !!images?.length && (
        <div className={`flex flex-wrap gap-1 items-center`}>
          {images.map((item) => {
            return (
              <div key={item.url}>
                {item.fileType === 'VIDEO' ? (
                  <video
                    width={48}
                    onClick={() => {
                      setVideoPreviewVisible(true);
                      setVideoPreviewUrl(item.url);
                    }}
                    height={48}
                    src={item.url}
                  />
                ) : (
                  <Image
                    onClick={() => {
                      ImageViewer.show({ image: item.url });
                    }}
                    src={item?.snapshotUrl || item.url}
                    width={48}
                    height={48}
                  />
                )}
              </div>
            );
          })}
        </div>
      )
    );
  };

  return (
    <React.Fragment>
      <div className="p-3 pb-4 mb-[10px] bg-[#FFF] rounded-[4px]">
        <div className="flex justify-center items-start mt-1 mb-2">
          <div className="flex-1 flex flex-wrap gap-1">
            <QuestionTags accentedTermTags={accentedTermTags || []} />
            <span className="text-sm leading-[22px] break-all">{content}</span>
          </div>
          {!!sopId && (
            <div
              className="text-primary mr-2"
              onClick={() => {
                showSop?.(sopId);
              }}
            >
              参考标准
            </div>
          )}
          {!!reportReviewStandard && (
            <div
              className="text-primary"
              onClick={() => {
                showStandard?.(reportReviewStandard);
              }}
            >
              评分标准
            </div>
          )}
        </div>
        {!!sampleImageUrls?.length && <SampleImage SampleimageList={sampleImageUrls || []} />}
        {!!shopCheckContent?.images?.length && imagesOrVideoRender(shopCheckContent?.images as any)}
        {!!shopCheckContent?.abnormalReasons?.length && (
          <div className="text-sm leading-[22px] break-all">{shopCheckContent?.abnormalReasons?.toString()}</div>
        )}
        {typeof shopCheckContent?.normalFlag === 'boolean' && (
          <div className="text-sm leading-[22px] break-all">
            {shopCheckContent?.normalFlag ? (
              <Tag color="success">{normalButtonName || '合格'}</Tag>
            ) : (
              <Tag color="danger">{abnormalButtonName || '不合格'}</Tag>
            )}
          </div>
        )}
        {!!shopCheckContent?.text && <div className="text-sm leading-[22px] break-all">{shopCheckContent?.text}</div>}
        {differentItemLabel === 'DIFFERENT' && !shopHasApply && <Tag color="danger">不适用</Tag>}
        {reportReviewMethod === SelfButtonTypeEnum.不需要点评 ? (
          isDudao && (
            <div className={styles.DetailOperationArea}>
              <span role="button" className={styles.noNeedComment}>
                不需要点评项
              </span>
            </div>
          )
        ) : (
          <React.Fragment>
            {hasSecondReview ? (
              // 第二点评人
              <React.Fragment>
                <FirstSelfScoreDetail data={data} />
                {isDudao && (
                  <SecondCommentBtnGroup
                    isDetail={isDetail}
                    reviewUserId={reviewUserId}
                    secondUpdateItem={(item) => {
                      secondUpdateItem?.({
                        ...item,
                        taskCategoryId,
                        itemId: taskWorksheetItemId,
                      });
                    }}
                    data={props.data}
                    config={config}
                    reFresh={reFresh}
                  />
                )}
              </React.Fragment>
            ) : (
              // 第一点评人
              <React.Fragment>
                {isDetail || status === Report_STATUS_TEXT.已点评 ? (
                  <React.Fragment>
                    {itemStatus === Report_STATUS_TEXT.已点评 && <FirstSelfScoreDetail data={data} />}
                    {secondReviewed && isDudao && (
                      <SecondCommentBtnGroup
                        reviewUserId={reviewUserId}
                        generateRectification={generateRectification}
                        isDetail={isDetail}
                        data={props.data}
                        reFresh={reFresh}
                      />
                    )}
                  </React.Fragment>
                ) : (
                  <div>
                    {status === Report_STATUS_TEXT.已提交 && reviewPermission && canReviewFlag && (
                      <SelfButtonGroup
                        data={props.data}
                        config={config}
                        generateRectification={generateRectification}
                        updateItem={(item) => {
                          updateItem?.({
                            ...item,
                            itemId: taskWorksheetItemId,
                          });
                        }}
                      />
                    )}
                  </div>
                )}
              </React.Fragment>
            )}
          </React.Fragment>
        )}
      </div>
      <VideoViewer visible={videoPreviewVisible} url={videoPreviewUrl} onClose={() => setVideoPreviewVisible(false)} />
    </React.Fragment>
  );
});
export default SelfCheckItemCard;
