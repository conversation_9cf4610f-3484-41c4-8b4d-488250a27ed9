import React, { useMemo, useState } from 'react';
import AuthorityEnum from '@src/common/authority';
import { IconFont } from '@src/components';
import UnqualifiedPopup from '@src/components/OptionsDetail/UnqualifiedPopup';
import { SelfCheckStore, userStore } from '@src/store';
import { <PERSON><PERSON>, Picker } from 'antd-mobile';
import _ from 'lodash';
import { observer } from 'mobx-react';
import AppealPopup from './components/AppealPopup';
import Explain from './explain';
import styles from './index.module.scss';
import { selfScoreRange } from './selfButtonGroup';
import { reasonListText } from './selfCheckItemCard';
import { hasAppealOperate } from './utils';
import { CheckItem } from '../api.type';

const scoreTypeText = {
  REDUCE_SCORE: '扣',
  ADD_SCORE: '得',
};

export enum SelfButtonTypeEnum {
  判断型 = 'MANUAL_JUDGMENT',
  打分型 = 'MANUAL_SCORING',
  不需要点评 = 'NO_REVIEWS_NEEDED',
}

export enum SelfscoreTypeEnum {
  得分项 = 'ADD_SCORE',
  扣分项 = 'REDUCE_SCORE',
}

export const LimitRange = Array.from({ length: 30 }, (_, ind) => {
  return { label: ind + 1, value: ind + 1 };
});
interface Props {
  data: CheckItem;
  isDetail?: boolean;
  reviewUserId: number;
  config?: any;
  generateRectification?: boolean;
  reFresh?: () => void;
  secondUpdateItem?(data: {
    secondScore?: number | null;
    secondQualified: boolean | null;
    secondHasApply: boolean;
    taskWorksheetItemId?: number;
    curSecondReasons?: string[];
    secondOtherReason?: string;
    secondItemRemark?: string;
  }): void;
}

export type EXPLAIN = {
  visible: boolean;
  isPassBtn: boolean;
  isSubmitScore: boolean;
};

const SecondCommentBtnGroup: React.FC<Props> = observer((props: Props) => {
  const {
    curCheckSheetInfo: { hasSecondReview, secondReviewed },
  } = SelfCheckStore;
  const [sedUnqualifiedVisible, setSedUnqualifiedVisible] = useState(false);
  const [selectReasons, setSelectReasons] = useState<string[]>([]);
  const [explain, setExplain] = useState<EXPLAIN>({
    visible: false,
    isPassBtn: false,
    isSubmitScore: false,
  });
  const [scoreVisible, setScoreVisible] = useState(false);
  const [submitScore, setSubmitScore] = useState();
  const { permissionsMap } = userStore;

  const { secondUpdateItem, data, config, isDetail, generateRectification, reFresh } = props;

  const {
    reportReviewAbnormalButtonName,
    reportReviewNormalButtonName,
    reportReviewScoreMax,
    reportReviewScoreMin,
    reportReviewScoreType,
    taskWorksheetItemId,
    reportReviewMethod,
    secondItemRemark,
    secondScore,
    secondHasApply,
    secondHasFillItem,
    secondQualified,
    reportDisplayScore, // 是否显示分数
    selectedSecondReasons,
    secondOtherReason,
    qualified,
    secondReviewAppealStatus,
  } = data;

  const [applyShow, setApplyShow] = useState(secondHasApply === false);
  const [appealPopupVisible, setAppealPopupVisible] = useState<boolean>(false);

  const notQualified =
    _.isNumber(secondScore) &&
    ((reportReviewScoreType === SelfscoreTypeEnum.得分项 && secondScore < reportReviewScoreMax) ||
      (reportReviewScoreType === SelfscoreTypeEnum.扣分项 && secondScore < 0));

  const scoreText = notQualified
    ? `${reportReviewScoreType === SelfscoreTypeEnum.扣分项 ? '扣' : ''}${secondScore}分`
    : '打分';

  const allReasons = useMemo(() => {
    return secondOtherReason ? [...(selectedSecondReasons || []), secondOtherReason] : selectedSecondReasons;
  }, [selectedSecondReasons, secondOtherReason]);

  // 是否有申诉操作
  const isAppeal = useMemo(() => {
    return hasAppealOperate({ secondReviewAppealStatus, qualified, secondQualified });
  }, [qualified, secondQualified, secondReviewAppealStatus]);

  const secondScoreDetail = () => {
    return (
      <React.Fragment>
        {(!!allReasons?.length || !!secondItemRemark) && (
          <div className="bg-[#FAFAFA] my-2 p-2">
            {!!secondItemRemark && <div className="break-all mb-2">{secondItemRemark}</div>}
            <div
              className="break-all  ellipsis-2 text-primary"
              onClick={() => {
                setSelectReasons(allReasons);
                setSedUnqualifiedVisible(true);
              }}
            >
              {reasonListText(allReasons)}
            </div>
          </div>
        )}
        <div className={styles.DetailOperationArea}>
          <div className={styles.commentTip}>
            <Badge color="#378BFF" content="2" style={{ '--top': '100%' }}>
              <span>点评</span>
            </Badge>
          </div>
          <span
            role="button"
            className={`${styles.active} ${
              !!secondQualified ||
              (reportReviewMethod === SelfButtonTypeEnum.打分型 &&
                secondScore === (reportReviewScoreType === SelfscoreTypeEnum.扣分项 ? 0 : reportReviewScoreMax))
                ? styles.pass
                : styles.fail
            }`}
          >
            {reportReviewMethod === SelfButtonTypeEnum.判断型
              ? (!!secondQualified
                  ? `${reportReviewNormalButtonName || '合格'}`
                  : `${reportReviewAbnormalButtonName || '不合格'}`
                ).concat(
                  reportReviewScoreType === SelfscoreTypeEnum.扣分项
                    ? !!secondQualified
                      ? '(不扣分)'
                      : `(扣${secondScore}分)`
                    : reportReviewScoreMax === 0 || !reportDisplayScore
                      ? ''
                      : `(${secondScore}分)`,
                )
              : (
                    reportReviewScoreType === SelfscoreTypeEnum.扣分项
                      ? secondScore === 0
                      : secondScore === reportReviewScoreMax
                  )
                ? `合格(${reportReviewScoreType === SelfscoreTypeEnum.扣分项 ? '不扣' : secondScore}分)`
                : `${reportReviewScoreType === SelfscoreTypeEnum.扣分项 ? '扣' : ''}${secondScore}分${
                    reportReviewScoreType === SelfscoreTypeEnum.扣分项 ? '' : `(满分${reportReviewScoreMax}分)`
                  }`}
          </span>
          {permissionsMap.has(AuthorityEnum.申诉_督导端) && isAppeal && (
            <span
              role="button"
              className="!w-14 ml-auto bg-primary text-white border-primary"
              onClick={() => {
                setAppealPopupVisible(true);
              }}
            >
              申诉
            </span>
          )}
        </div>
        <AppealPopup
          visible={appealPopupVisible}
          onClose={() => setAppealPopupVisible(false)}
          taskWorksheetItemId={taskWorksheetItemId}
          reFresh={reFresh}
        />
      </React.Fragment>
    );
  };

  return isDetail || secondReviewed ? (
    <div>
      {secondScoreDetail()}
      <UnqualifiedPopup
        title="不合格原因"
        visible={sedUnqualifiedVisible}
        selectReasons={selectReasons}
        onClose={() => setSedUnqualifiedVisible(false)}
      />
    </div>
  ) : (
    hasSecondReview && (
      <React.Fragment>
        {reportReviewMethod === SelfButtonTypeEnum.判断型 ? ( // 判断型
          <div className={styles.operationArea}>
            <span
              role="button"
              onClick={() => {
                setExplain({
                  visible: true,
                  isPassBtn: false,
                  isSubmitScore: false,
                });
              }}
              style={{ width: applyShow ? '32%' : '42%' }}
              className={`${styles.active} ${!secondHasApply || !!secondQualified ? '' : styles.fail}`}
            >
              {reportReviewAbnormalButtonName || '不合格'}
              {reportReviewScoreMax === 0 || !reportDisplayScore
                ? ''
                : reportReviewScoreType === SelfscoreTypeEnum.扣分项
                  ? `（扣${reportReviewScoreMax}分）`
                  : '（0分）'}
            </span>
            <span
              role="button"
              style={{ width: applyShow ? '32%' : '42%' }}
              onClick={() => {
                setExplain({
                  visible: false,
                  isPassBtn: true,
                  isSubmitScore: false,
                });
                secondUpdateItem?.({
                  secondScore: reportReviewScoreType === SelfscoreTypeEnum.扣分项 ? 0 : reportReviewScoreMax,
                  secondQualified: true,
                  secondHasApply: true, // 改为适用
                });
                setApplyShow(false);
              }}
              className={`${styles.active} ${!secondHasApply || !secondQualified ? '' : styles.pass}`}
            >
              {reportReviewNormalButtonName || '合格'}
              {reportReviewScoreMax === 0 || !reportDisplayScore
                ? ''
                : reportReviewScoreType === SelfscoreTypeEnum.扣分项
                  ? '（不扣分）'
                  : `（${reportReviewScoreMax}分）`}
            </span>
            {applyShow ? (
              <span
                style={{ width: '32%' }}
                className={`${styles.active} ${
                  typeof secondHasApply === 'boolean' && !secondHasApply ? styles.pass : ''
                }`}
                onClick={() => {
                  secondUpdateItem?.({
                    secondHasApply: false,
                    secondQualified: null, // 是否合格恢复为不选
                    secondScore: null, // 分数恢复为不选
                  });
                }}
              >
                不适用
              </span>
            ) : (
              <div
                onClick={() => {
                  setApplyShow(true);
                }}
              >
                <IconFont type="icon-menu-horizontal" />
              </div>
            )}
          </div>
        ) : (
          // 打分型
          <div className={styles.operationArea}>
            <span
              role="button"
              style={{ width: applyShow ? '32%' : '42%' }}
              className={`${styles.active} ${!secondHasApply || !notQualified ? '' : styles.fail}`}
              onClick={() => {
                setExplain({
                  visible: false,
                  isPassBtn: false,
                  isSubmitScore: true,
                });
                setScoreVisible(true);
              }}
            >
              {scoreText}
            </span>
            <span
              role="button"
              style={{ width: applyShow ? '32%' : '42%' }}
              className={`${styles.active} ${!secondHasApply || !secondQualified ? '' : styles.pass}`}
              onClick={() => {
                setExplain({
                  visible: false,
                  isPassBtn: true,
                  isSubmitScore: false,
                });
                secondUpdateItem?.({
                  secondScore: reportReviewScoreType === SelfscoreTypeEnum.扣分项 ? 0 : reportReviewScoreMax,
                  secondQualified: true,
                  secondHasApply: true, // 改为适用
                });
                setApplyShow(false);
              }}
            >
              {reportReviewScoreType === SelfscoreTypeEnum.扣分项
                ? '合格（不扣分）'
                : `合格（${reportReviewScoreMax}分）`}
            </span>
            {applyShow ? (
              <span
                style={{ width: '32%' }}
                className={`${styles.active} ${
                  typeof secondHasApply === 'boolean' && !secondHasApply ? styles.pass : ''
                }`}
                onClick={() => {
                  secondUpdateItem?.({
                    secondHasApply: false,
                    secondScore: null, // 打分恢复默认
                    secondQualified: null, // 是否合格恢复为不选
                  });
                }}
              >
                不适用
              </span>
            ) : (
              <div
                onClick={() => {
                  setApplyShow(true);
                }}
              >
                <IconFont type="icon-menu-horizontal" />
              </div>
            )}
          </div>
        )}
        <div>
          {!!secondHasFillItem && secondHasApply && secondItemRemark && (
            <div
              className="bg-[#FAFAFA] my-1 p-1"
              onClick={() => {
                setExplain({
                  visible: true,
                  isPassBtn: !notQualified,
                  isSubmitScore: explain.isSubmitScore,
                });
              }}
            >
              <div className="break-all mb-2">{secondItemRemark || '情况说明'}</div>
            </div>
          )}
        </div>

        <Picker
          columns={[
            [{ label: scoreTypeText[reportReviewScoreType], value: 0 }],
            selfScoreRange(
              reportReviewScoreType,
              reportReviewScoreType === SelfscoreTypeEnum.得分项
                ? reportReviewScoreMax
                : Math.abs(reportReviewScoreMin),
            ),
            [{ label: '分', value: 0 }],
          ]}
          visible={scoreVisible}
          onClose={() => {
            setScoreVisible(false);
          }}
          onConfirm={(v) => {
            setExplain({
              visible: true,
              isPassBtn: false,
              isSubmitScore: true,
            });
            setSubmitScore(v[1] as any);
          }}
        />
        <Explain
          explain={explain}
          isSecondComment={true}
          generateRectification={generateRectification}
          config={config}
          setExplain={(e: boolean, isPass: boolean, isSubmit: boolean) => {
            setExplain({
              visible: e,
              isPassBtn: isPass,
              isSubmitScore: isSubmit,
            });
          }}
          submit={(callBackData: any) => {
            console.log(callBackData, '=回调参数');
            let score;
            let qualified: boolean;
            if (explain.isSubmitScore) {
              score = reportReviewScoreType === SelfscoreTypeEnum.扣分项 ? +`-${submitScore}` : submitScore;
            } else if (reportReviewMethod === SelfButtonTypeEnum.打分型 && !explain.isPassBtn) {
              score = reportReviewScoreMin;
            } else if (!explain.isPassBtn) {
              score = reportReviewScoreMin;
            } else {
              score = data?.secondScore;
            }
            if (explain.isPassBtn) {
              qualified = true;
            } else if (!explain.isPassBtn) {
              qualified = false;
            } else {
              qualified = data.secondQualified;
            }
            secondUpdateItem?.({
              taskWorksheetItemId,
              secondHasApply: true,
              secondQualified: qualified,
              secondItemRemark: callBackData?.itemRemark,
              curSecondReasons: callBackData?.reasons,
              secondOtherReason: callBackData?.otherReason,
              secondScore: score,
            });
            setApplyShow(false);
          }}
          initData={data}
        />
      </React.Fragment>
    )
  );
});
export default SecondCommentBtnGroup;
