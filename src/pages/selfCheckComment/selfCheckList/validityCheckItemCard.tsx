import React from 'react';
import { Image, ImageViewer, Tag } from 'antd-mobile';
import { CheckCircleOutline, CloseCircleOutline, ExclamationCircleOutline } from 'antd-mobile-icons';
import cn from 'classnames';
import dayjs from 'dayjs';
import { observer } from 'mobx-react';
import { CheckItem } from '../api.type';

interface Props {
  data: CheckItem;
  showStandard?(standard: string): void;
  showSop?(sopId: number): void;
}

enum expirationEnum {
  有效 = 'VALID',
  今日过期 = 'TODAY_PAST_DUE',
  过期 = 'PAST_DUE',
}
const expirationText = {
  [expirationEnum.有效]: (
    <div className="flex items-center text-[#3DB86D]">
      <CheckCircleOutline className="mr-2" />
      有效
    </div>
  ),
  [expirationEnum.今日过期]: (
    <div className="flex items-center text-[#FBA238]">
      <ExclamationCircleOutline className="mr-2" />
      今日过期
    </div>
  ),
  [expirationEnum.过期]: (
    <div className="flex items-center text-[#EA0000] ">
      <CloseCircleOutline className="mr-2" />
      过期
    </div>
  ),
};

const statusText = {
  UNSOLD: '未售',
  STOCKOUT: '缺货',
};
const ValidityCheckItemCard: React.FC<Props> = observer((props: Props) => {
  const { data } = props;
  const { content, expirationTimes, explain, status } = data;
  const statusShow = statusText?.[status as 'UNSOLD' | 'STOCKOUT'];
  return (
    <React.Fragment>
      <div className="px-2 pt-2 mb-[10px] bg-[#FFF] rounded-[4px]">
        <div className="text-sm text-[#000000] mb-2">{content}</div>
        <div className="text-xs text-[#858585] mb-2">{explain}</div>
        {statusShow && (
          <div className="mb-2">
            <Tag color="warning">{statusShow}</Tag>
          </div>
        )}
        <div>
          {expirationTimes?.map((item, index) => {
            const { instant, status, imageUrl, imageId } = item || {};
            return (
              <>
                <div
                  key={index}
                  className={cn('flex justify-between mb-2 p-1 text-[13px]', {
                    'bg-[#F5F5F5]': true,
                  })}
                >
                  <span className="">
                    {imageId ? (
                      <div className="flex items-center text-[#3DB86D]">
                        <CheckCircleOutline className="mr-2" />
                        已过期且处理
                      </div>
                    ) : (
                      expirationText[status]
                    )}
                  </span>
                  <span>{dayjs(instant)?.format('YYYY/MM/DD HH:mm')}</span>
                </div>
                {imageId ? (
                  <div className="flex items-center pb-2">
                    <Image
                      onClick={() => {
                        ImageViewer.show({ image: imageUrl });
                      }}
                      width={200}
                      src={imageUrl}
                    />
                  </div>
                ) : null}
              </>
            );
          })}
        </div>
      </div>
    </React.Fragment>
  );
});
export default ValidityCheckItemCard;
