import React, { RefObject, useEffect, useMemo } from 'react';
import { IconFont } from '@src/components';
import { IPopupPage } from '@src/components/IPopup';
import { CopyUserSelect } from '@src/pages/patrol/checkList/components/copyUserSelect';
import { Button, Checkbox, Form, Picker, PickerRef, Space, TextArea } from 'antd-mobile';
import { observer } from 'mobx-react';
import styles from './index.module.scss';
import { EXPLAIN } from './selfButtonGroup';

export const scoreRange = Array.from({ length: 100 }, (_, ind) => {
  return { label: ind + 1, value: ind + 1 };
});
export const LimitRange = Array.from({ length: 30 }, (_, ind) => {
  return { label: ind + 1, value: ind + 1 };
});
interface Props {
  explain: EXPLAIN;
  config: any;
  generateRectification?: boolean;
  setExplain: (visible: boolean, isPassBtn: boolean, isSubmitScore: boolean) => void;
  initData: any;
  submit: (data: any) => void;
  shopManagerName?: string;
  isSecondComment?: boolean;
}

const Explain: React.FC<Props> = observer((props: Props) => {
  const { initData, explain, setExplain, submit, config, isSecondComment } = props;
  const { reportReviewAbnormalMustSelectReason, curSecondReasons, selectedSecondReasons, secondOtherReason } =
    initData || {};

  const [form] = Form.useForm();
  console.log(initData, '=initData');
  const isOther = Form.useWatch('reasons', form);
  useEffect(() => {
    const initSelectResons = initData?.otherReason
      ? [...(initData?.selectedChooseNonconformityReasons || []), -1]
      : initData?.selectedChooseNonconformityReasons;

    const initSedResons = secondOtherReason ? [...(selectedSecondReasons || []), -1] : selectedSecondReasons;

    form.setFieldsValue({
      reasons: isSecondComment ? curSecondReasons || initSedResons || [] : initData?.reasons || initSelectResons || [],
      otherReason: isSecondComment ? initData?.secondOtherReason : initData?.otherReason,
      itemRemark: initData?.itemRemark,
      reformLimit: initData?.reformLimit ? [initData?.reformLimit] : [+config?.ISSUE_FIX_DEFAULT_DAYS],
      watchId: initData?.watchId,
    });
  }, [config, initData]);
  const isFromPassBtn: boolean = useMemo(() => {
    // 是否合格
    return explain.isPassBtn;
  }, [explain]);
  const unqualifiedReasonList = useMemo(() => {
    return isSecondComment ? initData?.secondReviewReasons : initData?.reportReviewAbnormalReasons;
  }, [isSecondComment, initData]);
  const isShowOther = useMemo(() => {
    return isOther && isOther?.includes(-1);
  }, [isOther]);
  return (
    <React.Fragment>
      <IPopupPage
        DocumentTitle="情况说明"
        visible={explain.visible}
        onClose={() => {
          setExplain(false, explain.isPassBtn, explain.isSubmitScore);
        }}
        footer={
          <div className="p-3 bg-white">
            <Button
              onClick={async () => {
                const fields = await form.validateFields();
                submit(fields);
                setExplain(false, explain.isPassBtn, explain.isSubmitScore);
              }}
              color="primary"
              fill="solid"
              block
              className="h-[45px] text-base"
            >
              确定
            </Button>
          </div>
        }
      >
        {explain.visible && (
          <Form layout="vertical" form={form} className={styles.IForm}>
            <div>
              {!!unqualifiedReasonList?.length && !isFromPassBtn && (
                <Form.Item
                  label={<div className="text-base text-[#141414] font-medium">不合格原因</div>}
                  name="reasons"
                  rules={[
                    {
                      required: reportReviewAbnormalMustSelectReason,
                      message: '请选择不合格原因',
                    },
                  ]}
                >
                  <Checkbox.Group>
                    <Space direction="vertical">
                      {unqualifiedReasonList?.map((v: string) => {
                        return (
                          <Checkbox
                            style={{
                              '--font-size': '14px',
                              '--icon-size': '16px ',
                            }}
                            value={v}
                          >
                            {v}
                          </Checkbox>
                        );
                      })}
                      <Checkbox
                        style={{
                          '--font-size': '14px',
                          '--icon-size': '16px ',
                        }}
                        value={-1}
                      >
                        其他
                      </Checkbox>
                    </Space>
                  </Checkbox.Group>
                </Form.Item>
              )}
              {isShowOther && (
                <Form.Item
                  name="otherReason"
                  label={<div className="text-base text-[#141414] font-medium">其他原因</div>}
                  rules={[
                    {
                      required: true,
                      message: '请输入其他原因',
                    },
                  ]}
                >
                  <TextArea
                    className="bg-[#FAFAFA] p-1"
                    placeholder="请输入其他不合格原因"
                    style={{ '--font-size': '14px', '--color': '#B8B8B8', height: '40px' }}
                    maxLength={50}
                  />
                </Form.Item>
              )}
              <Form.Item name="itemRemark" label={<div className="text-base text-[#141414] font-medium">详细说明</div>}>
                <TextArea
                  className="bg-[#FAFAFA] p-1"
                  placeholder="输入详细的情况说明，最多140字"
                  style={{ '--font-size': '14px', '--color': '#B8B8B8', height: '80px' }}
                  maxLength={140}
                />
              </Form.Item>
            </div>
            <div className="h-[10px] bg-[#F5F5F5]" />
            {!explain.isPassBtn && !isSecondComment && (
              <React.Fragment>
                <Form.Item
                  layout="horizontal"
                  childElementPosition="right"
                  name="reformLimit"
                  trigger="onConfirm"
                  label={
                    <div className="flex items-center">
                      <IconFont type="icon-hourglass" className="mr-2 text-[20px] text-[#00BBB4]" />
                      <span className="text-base text-[#141414]">整改期限</span>
                    </div>
                  }
                  onClick={(_, PickerRef: RefObject<PickerRef>) => {
                    PickerRef.current?.open();
                  }}
                >
                  <Picker columns={[LimitRange]}>
                    {(value) => {
                      return !!value?.length ? (
                        <span className="text-[#CCCCCC]">{value[0]?.value}天</span>
                      ) : (
                        <span className="text-[#CCCCCC]">未选择</span>
                      );
                    }}
                  </Picker>
                </Form.Item>
                <Form.Item
                  layout="horizontal"
                  childElementPosition="right"
                  name="watchId"
                  label={
                    <div className="flex items-center">
                      <IconFont type="icon-calendar-user" className="mr-2 text-[20px] text-[#00BBB4]" />
                      <span className="text-base text-[#141414]">处理人</span>
                    </div>
                  }
                >
                  门店所有员工
                </Form.Item>
                <Form.Item
                  layout="horizontal"
                  childElementPosition="right"
                  name="copyUserIds"
                  label={
                    <div className="flex items-center">
                      <IconFont type="icon-calendar-edit" className="mr-2 text-[20px] text-[#00BBB4]" />
                      <span className="text-base text-[#141414]">抄送人</span>
                    </div>
                  }
                >
                  <CopyUserSelect />
                </Form.Item>
              </React.Fragment>
            )}
          </Form>
        )}
      </IPopupPage>
    </React.Fragment>
  );
});
export default Explain;
