import React, { useEffect, useMemo, useState } from 'react';
import Notice from '@src/assets/images/notice2.svg';
import { IconFont, Loading } from '@src/components';
import { IPopup } from '@src/components/IPopup';
import SopPopup from '@src/components/OptionsDetail/SopPopup';
import StandardPopup from '@src/components/OptionsDetail/StandardPopup';
import TextareaPopup from '@src/pages/tasks/components/TextareaPopup';
import { GlobalStore, SelfCheckStore, userStore } from '@src/store';
import { roleTypeIsManage } from '@src/utils/tokenUtils';
import { useRequest } from 'ahooks';
import { Badge, Checkbox, Collapse, FloatingBubble, Selector, SideBar, Tabs, Toast } from 'antd-mobile';
import cn from 'classnames';
import dayjs from 'dayjs';
import { toJS } from 'mobx';
import { observer } from 'mobx-react';
import styles from './index.module.scss';
import { SelfButtonTypeEnum } from './selfButtonGroup';
import SelfCheckItemCard from './selfCheckItemCard';
import ValidityCheckItemCard from './validityCheckItemCard';
import {
  commentReport,
  secondCommentReport,
  secondUpdateCheckItem,
  updateNotApplyItem,
  updateSelfCheckItem,
} from '../api';
import { CategoryItem, CheckItem } from '../api.type';
import { Report_STATUS_TEXT } from '../enum';
interface Props {
  canReviewFlag?: boolean;
}

const SelfCheckList: React.FC<Props> = observer((props) => {
  const { canReviewFlag } = props;
  const [groupId, setGroupId] = useState(-1);
  const [tabPopupVisible, setTabPopupVisible] = useState(false);
  const [sopPopup, setSopPopup] = useState<{
    visible: boolean;
    sopId: number | undefined;
  }>({
    visible: false,
    sopId: undefined,
  });
  const [standardPopup, setStandardPopup] = useState<{
    visible: boolean;
    standard: any;
  }>({
    visible: false,
    standard: undefined,
  });
  const {
    updateSelfWorkSheet,
    selfWorkSheetList,
    secondUpdateSelfWorkSheet,
    setCurCheckSheet,
    previewInfos: { id: taskId, secondSummary, hasSecondReview, summaryDtos, status },
    curCheckSheetInfo,
  } = SelfCheckStore;
  const { userInfo } = userStore;
  const [curSheetId, setCurSheetId] = useState<number>();
  const [isOPenLeft, setIsOPenLeft] = useState<boolean>(true);
  const [summary, setSummary] = useState<string>('');
  const [isShowUnItem, setIsShowUnItem] = useState<boolean>(false);
  const [secondCommentsummary, setSecondSummary] = useState<string>('');
  const [commentPopupVisible, setCommentPopupVisible] = useState<boolean>(false);
  const [collapseKeys, setCollapseKeys] = useState([]);
  const isDudao = roleTypeIsManage();
  console.log(toJS(selfWorkSheetList), '=123selfWorkSheetList');
  console.log(toJS(userInfo), '=userInfo');
  useEffect(() => {
    setCurSheetId(selfWorkSheetList?.[0]?.taskWorksheetId);
  }, [selfWorkSheetList?.[0]?.taskWorksheetId]);
  const curWorkSheet = useMemo(() => {
    const curData: any = selfWorkSheetList?.find((item) => +item?.taskWorksheetId === +curSheetId!);
    setCurCheckSheet(curData);
    return curData;
  }, [curSheetId, selfWorkSheetList]);

  console.log(toJS(curWorkSheet), '=curWorkSheet');

  const { run } = useRequest(
    async (payload) => {
      // 第一点评人
      const res = payload.hasApply
        ? await updateSelfCheckItem({
            taskId,
            ...payload,
            // eslint-disable-next-line eqeqeq
            reasons: payload?.reasons?.filter((v: number | string) => v != -1),
          })
        : await updateNotApplyItem({
            data: payload?.itemId,
          });
      return { ...res, taskWorksheetId: curSheetId, payload };
    },
    {
      manual: true,
      onSuccess: (data) => {
        updateSelfWorkSheet(data);
      },
    },
  );
  const { run: secondRun } = useRequest(
    async (payload) => {
      // 第二点评人
      const res = await secondUpdateCheckItem({
        taskId,
        ...payload,
        // eslint-disable-next-line eqeqeq
        reasons: payload?.curSecondReasons?.filter((v: number | string) => v != -1),
      });
      return {
        ...res,
        taskWorksheetId: curSheetId,
        taskCategoryId: payload?.taskCategoryId,
        payload,
      };
    },
    {
      manual: true,
      onSuccess: (data) => {
        console.log(data, '=data显示更新后数据');
        secondUpdateSelfWorkSheet(data);
      },
    },
  );

  useEffect(() => {
    const curComment = summaryDtos?.find((v) => v?.userId === userInfo?.userId)?.summary;
    setSummary(curComment || '');
    setSecondSummary(secondSummary || '');
  }, [summaryDtos, secondSummary, userInfo]);
  const list = useMemo(() => {
    if (!curWorkSheet) return [];

    let groups = (toJS(curWorkSheet.categories) || []).filter((v: any) =>
      groupId === -1 ? v.categoryId !== 0 : v.categoryId === groupId,
    );
    console.log('🚀 ~ list ~ groups:', groups);

    // 过滤未填写项（未打分）&&以及超出点评时效的检查项目（仅第一点评人。第二点评人不过滤）
    if (isShowUnItem) {
      groups = groups.map((v: { items: any[] }) => ({
        ...v,
        items:
          // 第一点评人，如果时效已过期，则不可点评所有项
          !hasSecondReview && !canReviewFlag
            ? []
            : v?.items.filter((y: CheckItem) => {
                if (curWorkSheet?.hasSecondReview) {
                  return !y?.secondHasFillItem && y?.reportReviewMethod !== SelfButtonTypeEnum.不需要点评;
                } else {
                  return (
                    !y?.hasFillItem &&
                    curWorkSheet?.reviewPermission &&
                    y?.reportReviewMethod !== SelfButtonTypeEnum.不需要点评
                  );
                }
              }),
      }));
    }

    const data: Array<(CheckItem & { type: 'item' }) | (CategoryItem & { type: 'group' })> = [];
    for (let i = 0, len = groups.length; i < len; i++) {
      data.push({ ...groups[i], type: 'group' });
    }
    return data;
  }, [curWorkSheet, groupId, isShowUnItem]);
  // const { run: summaryRun } = useRequest(
  //   () => {
  //     return commentReport({ taskId: taskId!, summary: summary.trim() });
  //   },
  //   {
  //     manual: true,
  //     onSuccess: () => {},
  //   },
  // );

  const handComment = async (remark: string) => {
    if (status === Report_STATUS_TEXT.已点评) {
      await secondCommentReport({
        taskId: taskId!,
        summary: remark.trim(),
      }).then(() => {
        Toast.show({
          icon: 'success',
          content: '点评成功',
        });
        setSecondSummary(remark);
        setCommentPopupVisible(false);
      });
    } else {
      await commentReport({
        taskId: taskId!,
        summary: remark.trim(),
      }).then(() => {
        Toast.show({
          icon: 'success',
          content: '点评成功',
        });
        setSummary(remark);
        setCommentPopupVisible(false);
      });
    }
  };

  // const { run: secondSummaryRun } = useRequest(
  //   () => {
  //     return secondCommentReport({ taskId: taskId!, summary: secondCommentsummary.trim() });
  //   },
  //   {
  //     manual: true,
  //     onSuccess: () => {},
  //   },
  // );
  useEffect(() => {
    const init = curWorkSheet?.categories?.map((v: any) => {
      return `${v?.categoryId}`;
    });
    setCollapseKeys(init);
  }, [curWorkSheet]);
  const { data: config } = useRequest(
    async () => {
      const res = await GlobalStore.getCorporationConfig();
      return res;
    },
    { refreshDeps: [] },
  );
  console.log(list, '=list');
  const BadgeColor = (status: string, reviewPrescription?: string, submitted?: boolean) => {
    let color = '';
    if (isDudao) {
      if (status === Report_STATUS_TEXT.已点评) {
        color = 'green';
      } else {
        if (!!reviewPrescription && dayjs() > dayjs(reviewPrescription)) {
          color = 'red';
        } else {
          color = 'orange';
        }
      }
    } else {
      !!submitted
        ? (color = '#3DB86D')
        : status === ('REVIEWED' as unknown as any)
          ? (color = '#EA0000')
          : (color = '#FBA238');
    }

    return color;
  };
  return (
    <Loading spinning={false}>
      <React.Fragment>
        <div className="leading-[14px] bg-white">
          <div className="flex p-4 pb-0 justify-between items-center">
            <div className="text-base leading-[16px] font-medium">检查明细</div>
            <div className="flex items-center">
              <div className="flex justify-between items-center">
                <Checkbox
                  style={{
                    '--icon-size': '18px',
                    '--font-size': '24px',
                    '--gap': '6px',
                  }}
                  checked={isShowUnItem}
                  onChange={(checked) => {
                    setIsShowUnItem(checked);
                  }}
                >
                  <div className="text-sm text-[#5E5E5E]">只看未点评项</div>
                </Checkbox>
              </div>
            </div>
          </div>
        </div>
        <div className="flex bg-white">
          <div className=" bg-[#F8F8F8]">
            {isOPenLeft && (
              <SideBar
                onChange={(e: any) => {
                  console.log(e, '=e');

                  setCurSheetId(e);
                  setGroupId(-1);
                }}
                activeKey={curSheetId?.toString()}
              >
                {selfWorkSheetList.map((item) => {
                  return (
                    <SideBar.Item
                      key={item.taskWorksheetId}
                      title={
                        <Badge
                          color={BadgeColor(item?.status, item.reviewPrescription, !!item?.submitted)}
                          content={Badge.dot}
                        >
                          {item?.supportOverdue && (
                            <div className="text-xs text-[#378BFF] border border-solid border-[#D6E3F4] rounded w-fit p-[2px]">
                              可逾期
                            </div>
                          )}
                          <div>{item.name}</div>
                        </Badge>
                      }
                    />
                  );
                })}
              </SideBar>
            )}

            <div
              className="absolute left-1 bottom-20 z-10"
              onClick={() => {
                setIsOPenLeft(!isOPenLeft);
              }}
            >
              <IconFont type="icon-shouqi" className="text-[#378BFF]" />
            </div>
          </div>
          <div className="flex-1 flex-col overflow-y-scroll ">
            {curCheckSheetInfo?.reviewPrescription &&
              dayjs() < dayjs(curCheckSheetInfo?.reviewPrescription) &&
              !hasSecondReview && (
                <div className="flex gap-x-1 bg-[#FFF9ED] h-7 px-4 items-center">
                  <img src={Notice} width={16} height={16} alt="" />
                  <span className="text-xs text-[#FF7D00]">
                    须{dayjs(curCheckSheetInfo?.reviewPrescription).format('MM月DD号 HH:mm')}前完成点评
                  </span>
                </div>
              )}
            <div className="bg-white flex items-center pl-1">
              <Tabs
                className={styles.Tabs}
                activeKey={groupId as any}
                onChange={(value) => setGroupId(+value as number)}
              >
                <Tabs.Tab
                  title={
                    <div
                      className={cn('flex text-sm items-center leading-[14px] text-[#858585]', {
                        [styles.activeColor]: groupId === -1,
                      })}
                    >
                      全部
                      <span>
                        ({curWorkSheet?.filledCount}/{curWorkSheet?.itemCount})
                      </span>
                    </div>
                  }
                  key={-1}
                />
                {(curWorkSheet?.categories || [])?.map((item: any) => (
                  <Tabs.Tab
                    title={
                      <div
                        className={cn('flex text-sm items-center leading-[14px] text-[#858585]', {
                          [styles.activeColor]: +item?.categoryId === +groupId,
                        })}
                      >
                        <div className="max-w-[100px]  text-ellipsis overflow-hidden whitespace-nowrap">
                          {item.name}
                        </div>
                        <div>
                          ({item.filledCount}/{item.itemCount})
                        </div>
                      </div>
                    }
                    key={item.categoryId}
                  />
                ))}
              </Tabs>
              {curWorkSheet?.categories?.length > 2 && (
                <>
                  <div className="bg-[rgba(0,0,0,0.03)]" />
                  <div
                    className="flex h-full justify-center items-center px-3"
                    onClick={() => setTabPopupVisible(true)}
                  >
                    <IconFont type="icon-menu" className="text-[#858585] text-sm" />
                  </div>
                </>
              )}
            </div>
            <div>
              {
                <Collapse
                  className={styles.Collapse}
                  onChange={(e: any) => {
                    setCollapseKeys(e);
                  }}
                  activeKey={collapseKeys}
                >
                  {list?.map((data) => {
                    const itemKey = curWorkSheet?.worksheetType === 'VALIDITY' ? 'validityDtos' : 'items';
                    return (
                      data.type === 'group' && (
                        <Collapse.Panel
                          key={`${data?.categoryId}`}
                          title={
                            <div className="text-[#141414] text-sm leading-[14px] font-medium  max-w-[200px]  text-ellipsis overflow-hidden whitespace-nowrap ">
                              {data?.name} {data?.filledCount}/{data?.itemCount}
                            </div>
                          }
                        >
                          {data[itemKey]?.map((item: CheckItem) => {
                            return (
                              <div className="border-b border-solid border-[#F0F0F0]">
                                {curWorkSheet?.worksheetType === 'VALIDITY' ? (
                                  <ValidityCheckItemCard // 效期检查表(不需要点评，仅查看)
                                    showStandard={(value) => {
                                      setStandardPopup({
                                        visible: true,
                                        standard: value,
                                      });
                                    }}
                                    showSop={(value: any) => {
                                      setSopPopup({
                                        visible: true,
                                        sopId: value,
                                      });
                                    }}
                                    key={item?.taskItemId}
                                    data={toJS(item)}
                                  />
                                ) : (
                                  <SelfCheckItemCard
                                    canReviewFlag={canReviewFlag}
                                    config={config}
                                    key={item?.taskWorksheetItemId}
                                    taskCategoryId={data?.taskCategoryId}
                                    data={toJS(item)}
                                    status={curWorkSheet?.status!}
                                    reviewUserId={curWorkSheet?.reviewUserId!}
                                    reviewPermission={curWorkSheet?.reviewPermission!}
                                    generateRectification={curWorkSheet?.generateRectification}
                                    updateItem={(values) => {
                                      console.log(values, '=values');
                                      run({ ...values, hasFillItem: true });
                                    }}
                                    secondUpdateItem={(values) => {
                                      secondRun({
                                        ...values,
                                        secondHasFillItem: true,
                                      });
                                    }}
                                    showStandard={(value) => {
                                      setStandardPopup({
                                        visible: true,
                                        standard: value,
                                      });
                                    }}
                                    showSop={(value: any) => {
                                      setSopPopup({
                                        visible: true,
                                        sopId: value,
                                      });
                                    }}
                                  />
                                )}
                              </div>
                            );
                          })}
                        </Collapse.Panel>
                      )
                    );
                  })}
                </Collapse>
              }
            </div>
            {((curWorkSheet?.reviewPermission &&
              !curWorkSheet?.noNeedReview &&
              curWorkSheet?.status !== Report_STATUS_TEXT.已点评) ||
              hasSecondReview) && (
              <FloatingBubble
                style={{
                  '--initial-position-bottom': '60px',
                  '--initial-position-right': '8px',
                  '--size': '60px',
                  '--background': '#378BFF',
                }}
                onClick={() => {
                  setCommentPopupVisible(true);
                }}
              >
                <div className="flex flex-col items-center">
                  <IconFont type="icon-bianji" className="text-[26px] text-[fff]" />
                  <span className="text-[10px] text-[fff]">点评总结</span>
                </div>
              </FloatingBubble>
            )}
          </div>
        </div>
      </React.Fragment>

      <IPopup title="分类" visible={tabPopupVisible} onClose={() => setTabPopupVisible(false)}>
        <div className="pt-5 px-4 pb-10">
          <Selector
            style={{
              '--checked-color': 'var(--color-primary-1)',
              '--border-radius': '4px',
              fontSize: '14px',
            }}
            className={styles.Selector}
            value={[groupId]}
            onChange={(value) => setGroupId(value[0])}
            columns={1}
            showCheckMark={false}
            options={curWorkSheet?.categories?.map((i: any) => ({
              label: `${i?.name}`,
              value: i?.categoryId,
            }))}
          />
        </div>
      </IPopup>
      <StandardPopup
        title="评分标准"
        visible={standardPopup.visible}
        standard={standardPopup.standard}
        onClose={() => {
          setStandardPopup({
            visible: false,
            standard: undefined,
          });
        }}
      />
      <SopPopup
        sopId={sopPopup.sopId}
        title="参考标准"
        visible={sopPopup.visible}
        onClose={() => {
          setSopPopup({
            visible: false,
            sopId: undefined,
          });
        }}
      />
      <TextareaPopup
        title={status === Report_STATUS_TEXT.已点评 ? '第二点评总结' : '点评总结'}
        okText="确定"
        defaultValue={status === Report_STATUS_TEXT.已点评 ? secondCommentsummary : summary}
        onOk={handComment}
        visible={commentPopupVisible}
        onClose={() => {
          setCommentPopupVisible(false);
        }}
      />
    </Loading>
  );
});
export default SelfCheckList;
