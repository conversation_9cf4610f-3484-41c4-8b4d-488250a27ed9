import { IPopup } from '@src/components/IPopup';
import useQuerySearchParams from '@src/hooks/useQuerySearchParams';
import { submitAppeal } from '@src/pages/reportDetail/self/api';
import { useRequest } from 'ahooks';
import { Button, Card, Form, Input, message } from 'antd';

const { TextArea } = Input;

interface IAppealPopupProps {
  visible: boolean;
  taskWorksheetItemId: number;
  onClose: () => void;
  reFresh?: () => void;
}

export default function AppealPopup({ visible, taskWorksheetItemId, onClose, reFresh }: IAppealPopupProps) {
  const [form] = Form.useForm();
  const [params] = useQuerySearchParams();

  const { runAsync: submitAppealRun, loading: submitAppealLoading } = useRequest(
    async (values) => {
      if (!params?.taskId || !taskWorksheetItemId) {
        message.error('参数异常');
        return;
      }

      const res = await submitAppeal({
        taskId: +params.taskId,
        applyInfos: [{ taskWorksheetItemId, ...values }],
      });

      return res;
    },
    {
      manual: true,
      onSuccess: () => {
        message.success('操作成功');

        setTimeout(() => {
          reFresh?.();
          onClose();
        }, 300);
      },
    },
  );

  return (
    <IPopup
      title="申诉详情"
      visible={visible}
      onClose={() => {
        form.resetFields();
        onClose?.();
      }}
      footer={
        <div className="bg-white flex gap-x-3 p-3">
          <Button
            block
            className="h-[45px] text-base"
            onClick={() => {
              onClose();
            }}
          >
            取消
          </Button>
          <Button
            type="primary"
            block
            className="h-[45px] text-base"
            onClick={() => {
              form.submit();
            }}
            loading={submitAppealLoading}
          >
            确认提交
          </Button>
        </div>
      }
      bodyStyle={{ height: '50vh' }}
      destroyOnClose
    >
      <div className="px-2 py-1 h-full overflow-auto">
        <Form
          form={form}
          onFinish={(values) => {
            submitAppealRun({ ...values, attachmentIds: values?.attachmentIds?.map((m: { id: string }) => m?.id) });
          }}
        >
          <Card size="small">
            <Form.Item label="申诉原因" name="reason" rules={[{ required: true, message: '请输入申诉原因' }]}>
              <TextArea
                placeholder="请输入申诉原因"
                maxLength={50}
                rows={3}
                style={{
                  width: '100%',
                  resize: 'none',
                }}
                autoSize={false}
              />
            </Form.Item>
            {/* <Form.Item label="辅助材料" name="attachmentIds">
              <SystemImageUpload setUpLoading={setUpLoading} />
            </Form.Item> */}
          </Card>
        </Form>
      </div>
    </IPopup>
  );
}
