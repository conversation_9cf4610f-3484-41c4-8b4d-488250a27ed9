import React, { useState } from 'react';
import { IconFont } from '@src/components';
import { Picker } from 'antd-mobile';
import _ from 'lodash';
import { observer } from 'mobx-react';
import Explain from './explain';
import styles from './index.module.scss';
import { CheckItem } from '../api.type';
const scoreTypeText = {
  REDUCE_SCORE: '扣',
  ADD_SCORE: '得',
};

export const LimitRange = Array.from({ length: 30 }, (_, ind) => {
  return { label: ind + 1, value: ind + 1 };
});
interface Props {
  data: CheckItem;
  config: any;
  generateRectification?: boolean;
  updateItem(data: {
    score?: number | null;
    qualified: boolean | null;
    hasApply: boolean;
    taskWorksheetItemId?: number;
    reasons?: string;
    images?: any;
    itemRemark?: string;
    reformLimit?: number;
    otherReason?: string;
    copyUserIds?: number[];
    watchId?: number;
  }): void;
}
export type EXPLAIN = {
  visible: boolean;
  isPassBtn: boolean;
  isSubmitScore: boolean;
};
export enum SelfButtonTypeEnum {
  判断型 = 'MANUAL_JUDGMENT',
  打分型 = 'MANUAL_SCORING',
  不需要点评 = 'NO_REVIEWS_NEEDED',
}
export enum SelfscoreTypeEnum {
  得分项 = 'ADD_SCORE',
  扣分项 = 'REDUCE_SCORE',
}
export const selfScoreRange = (reportReviewScoreType: SelfscoreTypeEnum, scoreLimit: number) => {
  return Array.from({ length: scoreLimit }, (_, ind) => {
    return reportReviewScoreType === SelfscoreTypeEnum.得分项
      ? { label: ind, value: ind }
      : { label: ind + 1, value: ind + 1 };
  });
};

const SelfButtonGroup: React.FC<Props> = observer((props: Props) => {
  const [explain, setExplain] = useState<EXPLAIN>({
    visible: false,
    isPassBtn: false,
    isSubmitScore: false,
  });
  const [scoreVisible, setScoreVisible] = useState(false);
  // const [isQualifiedItem, setIsQualifiedItem] = useState(false);
  const [submitScore, setSubmitScore] = useState();
  const { updateItem, data, config, generateRectification } = props;
  const {
    reportReviewAbnormalButtonName,
    reportReviewNormalButtonName,
    reportReviewScoreMax,
    reportReviewScoreMin,
    reportReviewScoreType,
    taskWorksheetItemId,
    reportReviewMethod,
    itemRemark,
    score,
    hasApply,
    hasFillItem,
    qualified,
    reportDisplayScore, // 是否显示分数
  } = data;
  const [applyShow, setApplyShow] = useState(hasApply === false);
  console.log(_.isNumber(score), '=_.isNumber(score)');

  const notQualified =
    _.isNumber(score) &&
    ((reportReviewScoreType === SelfscoreTypeEnum.得分项 && score < reportReviewScoreMax) ||
      (reportReviewScoreType === SelfscoreTypeEnum.扣分项 && score < 0));
  // const isInteger = Math.floor(reportReviewScoreMax) === reportReviewScoreMax;
  const scoreText = notQualified
    ? `${reportReviewScoreType === SelfscoreTypeEnum.扣分项 ? '扣' : ''}${score}分`
    : '打分';
  console.log(explain, '=explain');

  return (
    <React.Fragment>
      {reportReviewMethod === SelfButtonTypeEnum.判断型 ? ( // 判断型
        <div className={styles.operationArea}>
          <span
            role="button"
            onClick={() => {
              setExplain({
                visible: true,
                isPassBtn: false,
                isSubmitScore: false,
              });
            }}
            style={{ width: applyShow ? '32%' : '42%' }}
            className={`${styles.active} ${!hasApply || !!qualified ? '' : styles.fail}`}
          >
            {reportReviewAbnormalButtonName || '不合格'}
            {reportReviewScoreMax === 0 || !reportDisplayScore
              ? ''
              : reportReviewScoreType === SelfscoreTypeEnum.扣分项
                ? `（扣${reportReviewScoreMax}分）`
                : '（0分）'}
          </span>
          <span
            role="button"
            style={{ width: applyShow ? '32%' : '42%' }}
            onClick={() => {
              setExplain({
                visible: false,
                isPassBtn: true,
                isSubmitScore: false,
              });
              updateItem({
                score: reportReviewScoreType === SelfscoreTypeEnum.扣分项 ? 0 : reportReviewScoreMax,
                qualified: true,
                itemRemark: '',
                hasApply: true, // 改为适用
              });
              setApplyShow(false);
            }}
            className={`${styles.active} ${!hasApply || !qualified ? '' : styles.pass}`}
          >
            {reportReviewNormalButtonName || '合格'}
            {reportReviewScoreMax === 0 || !reportDisplayScore
              ? ''
              : reportReviewScoreType === SelfscoreTypeEnum.扣分项
                ? '（不扣分）'
                : `（${reportReviewScoreMax}分）`}
          </span>
          {applyShow ? (
            <span
              style={{ width: '32%' }}
              className={`${styles.active} ${typeof hasApply === 'boolean' && !hasApply ? styles.pass : ''}`}
              onClick={() => {
                updateItem({
                  hasApply: false,
                  qualified: null, // 是否合格恢复为不选
                  score: null, // 分数恢复为不选
                });
              }}
            >
              不适用
            </span>
          ) : (
            <div
              onClick={() => {
                setApplyShow(true);
              }}
            >
              <IconFont type="icon-menu-horizontal" />
            </div>
          )}
        </div>
      ) : (
        // 打分型
        <div className={styles.operationArea}>
          <span
            role="button"
            style={{ width: applyShow ? '32%' : '42%' }}
            className={`${styles.active} ${!hasApply || !notQualified ? '' : styles.fail}`}
            onClick={() => {
              setExplain({
                visible: false,
                isPassBtn: false,
                isSubmitScore: true,
              });
              setScoreVisible(true);
            }}
          >
            {scoreText}
          </span>
          <span
            role="button"
            style={{ width: applyShow ? '32%' : '42%' }}
            className={`${styles.active} ${!hasApply || !qualified ? '' : styles.pass}`}
            onClick={() => {
              setExplain({
                visible: false,
                isPassBtn: true,
                isSubmitScore: false,
              });
              updateItem({
                score: reportReviewScoreType === SelfscoreTypeEnum.扣分项 ? 0 : reportReviewScoreMax,
                qualified: true,
                itemRemark: '',
                hasApply: true, // 改为适用
              });
              setApplyShow(false);
            }}
          >
            {reportReviewScoreType === SelfscoreTypeEnum.扣分项
              ? '合格（不扣分）'
              : `合格（${reportReviewScoreMax}分）`}
          </span>
          {applyShow ? (
            <span
              style={{ width: '32%' }}
              className={`${styles.active} ${typeof hasApply === 'boolean' && !hasApply ? styles.pass : ''}`}
              onClick={() => {
                updateItem({
                  hasApply: false,
                  score: null, // 打分恢复默认
                  qualified: null, // 是否合格恢复为不选
                });
              }}
            >
              不适用
            </span>
          ) : (
            <div
              onClick={() => {
                setApplyShow(true);
              }}
            >
              <IconFont type="icon-menu-horizontal" />
            </div>
          )}
        </div>
      )}
      <div>
        {!!hasFillItem && hasApply && itemRemark && (
          <div
            className="bg-[#FAFAFA] my-1 p-1"
            onClick={() => {
              setExplain({
                visible: true,
                isPassBtn: !notQualified,
                isSubmitScore: explain.isSubmitScore,
              });
            }}
          >
            <div className="break-all mb-2">{itemRemark || '情况说明'}</div>
          </div>
        )}
      </div>

      <Picker
        columns={[
          [{ label: scoreTypeText[reportReviewScoreType], value: 0 }],
          selfScoreRange(
            reportReviewScoreType,
            reportReviewScoreType === SelfscoreTypeEnum.得分项 ? reportReviewScoreMax : Math.abs(reportReviewScoreMin),
          ),
          [{ label: '分', value: 0 }],
        ]}
        visible={scoreVisible}
        onClose={() => {
          setScoreVisible(false);
        }}
        onConfirm={(v: any) => {
          setExplain({
            visible: true,
            isPassBtn: false,
            isSubmitScore: true,
          });
          setSubmitScore(v?.[1]);
        }}
      />
      <Explain
        explain={explain}
        generateRectification={generateRectification}
        config={config}
        setExplain={(e: boolean, isPass: boolean, isSubmit: boolean) => {
          setExplain({
            visible: e,
            isPassBtn: isPass,
            isSubmitScore: isSubmit,
          });
        }}
        submit={(callBackData: any) => {
          console.log(callBackData, '=回调参数');

          let score;
          let qualified: boolean;
          if (explain.isSubmitScore) {
            score = reportReviewScoreType === SelfscoreTypeEnum.扣分项 ? +`-${submitScore}` : submitScore;
          } else if (reportReviewMethod === SelfButtonTypeEnum.打分型 && !explain.isPassBtn) {
            score = reportReviewScoreMin;
          } else if (!explain.isPassBtn) {
            score = reportReviewScoreMin;
          } else {
            score = data?.score;
          }
          if (explain.isPassBtn) {
            qualified = true;
          } else if (!explain.isPassBtn) {
            qualified = false;
          } else {
            // eslint-disable-next-line prefer-destructuring
            qualified = data.qualified;
          }
          updateItem({
            taskWorksheetItemId,
            reformLimit: callBackData?.reformLimit?.[0],
            otherReason: callBackData?.otherReason,
            hasApply: true,
            qualified,
            itemRemark: callBackData?.itemRemark,
            reasons: callBackData?.reasons,
            score,
            copyUserIds: callBackData?.copyUserIds,
          });
          setApplyShow(false);
        }}
        initData={data}
      />
    </React.Fragment>
  );
});
export default SelfButtonGroup;
