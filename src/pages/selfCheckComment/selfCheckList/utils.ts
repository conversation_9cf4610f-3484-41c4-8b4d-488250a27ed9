import { SelfCheckStore, userStore } from '@src/store';
import { SecondReviewAppealStatusEnum } from '../enum';

/**
 * @description 处理是否有申诉操作（督导端）
 * @param {object} data
 * @param {SecondReviewAppealStatusEnum} data.secondReviewAppealStatus 申诉状态
 * @param {boolean} data.qualified 第一点评是否合格
 * @param {boolean} data.secondQualified 第二点评是否合格
 * @returns 是否有申诉操作
 */
export function hasAppealOperate({
  secondReviewAppealStatus,
  qualified,
  secondQualified,
}: {
  secondReviewAppealStatus?: SecondReviewAppealStatusEnum;
  qualified: boolean;
  secondQualified: boolean;
}) {
  const { previewInfos, selfWorkSheetList } = SelfCheckStore;
  const { userInfo } = userStore;

  // 是否为当前操作人
  const isCurrentOperatePeople = selfWorkSheetList?.some((s) => s?.reviewUserId === userInfo?.userId);

  if (!isCurrentOperatePeople) {
    return false;
  }

  // 当前操作人 + 未申诉状态 + 可发起第二点评申诉 + 第一点评合格 + 第二点评不合格
  return (
    secondReviewAppealStatus === SecondReviewAppealStatusEnum.未申诉 &&
    !!previewInfos?.hasSecondReviewCanAppeal &&
    qualified &&
    !secondQualified
  );
}
