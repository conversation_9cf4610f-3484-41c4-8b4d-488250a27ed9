import { useEffect, useState } from 'react';
import { Button, Form, Input, Radio, Space, Toast } from 'antd-mobile'
import MobileDetect from 'mobile-detect'
import { cloneDeep } from 'lodash';
import { jsonToSheetXlsx } from '@src/utils/excel/Export2Excel';
import { submitFetch, getData } from './api';
const exportColumns = [
  {
    dataIndex: 'os',
    title: '手机系统',
    width: 200,
  },
  {
    title: '手机型号',
    dataIndex: 'phoneType',
    width: 200,
  },
  {
    title: '系统版本号',
    dataIndex: 'osVersion',
    width: 200,
  },
  {
    title: '手机所属',
    dataIndex: 'memberType',
    width: 300,
  },
  {
    title: '门店名称',
    dataIndex: 'storeName',
    width: 400,
  },
];
export default function statistics() {
  const [form] = Form.useForm();
  const [phoneInfo, setPhoneInfo] = useState({});
  const [showExportBtn, setShowExportBtn] = useState(false);
  useEffect(() => {
    // 获取userAgent设备信息
    const userAgent = navigator.userAgent;
    const md = new MobileDetect(userAgent);
    const os = md.os();  
    let model: any = "";
    let version: any = ""; 
    if (os == "iOS") {  
        version = md.version("iPhone");
        model = md.mobile();  
    } else if (os == "AndroidOS") {  
        version = md.version("Android");
        const array = userAgent.split(";"); 
        array.forEach((m) => {
          if(m.indexOf("Build/") > -1) {
            model = m.split('Build/')[0].replace(/\s+/g, '')
          }
        }) 
    }
    const mdObj = {'os': os, 'osVersion': version, 'phoneModel': model, 'userAgent': userAgent}
    setPhoneInfo(mdObj)
    if(window.location.href.indexOf('?viewData=true') > -1){
      setShowExportBtn(true)
    }
    console.log(mdObj);
  }, []);

  const submit = () => {
    form?.validateFields()
    .then(() => {
      const values = form.getFieldsValue();
      const phoneObj: any = cloneDeep(phoneInfo)
      const { memberType, storeName, phoneType } = values;
      phoneObj.phoneType = phoneType
      if(!memberType || !storeName || !phoneType) {
        return
      }
      submitFetch({
        memberType,
        storeName,
        phoneName: JSON.stringify(phoneObj)
      }).then(() => {
        Toast.show('提交成功, 感谢您的配合！');
      });

    })
  };

  const exportData = async () => {
    const res = await getData();
    const filterData = res.filter((m: any) => {
      const json = JSON.parse(m.phoneName)
      return json.phoneModel || json.phoneType
    })
    const list = filterData.map((item: any) => {
      const json = JSON.parse(item.phoneName)
      const obj: any = {};
      exportColumns.forEach((column: any) => {
        if(column.dataIndex === 'phoneType') {
          obj[column.title] = json.phoneType
        }
        if(column.dataIndex === 'osVersion') {
          obj[column.title] = json.osVersion
        }
        if(column.dataIndex === 'os') {
          obj[column.title] = json.os
        }
        if(column.dataIndex === 'memberType') {
          obj[column.title] = item.memberType === 1 ? '店长' : item.memberType === 2 ?  '店助' : '门店手机'
        }
        if(column.dataIndex === 'storeName') {
          obj[column.title] = item.storeName
        }
      });
      return obj;
    });
    jsonToSheetXlsx({
      data: list || [],
      filename: `设备信息采集.xlsx`,
    });

  };

  return (
    <div>
      <Form 
        form={form}
        footer={
          <Button 
            block 
            type='submit' 
            color='primary' 
            size='middle'
            onClick={() => {
              submit();
            }}
          >
            提交
          </Button>
        }
      >
        <Form.Header>塔塔运营通设备信息采集</Form.Header>
        <Form.Item name='memberType' label='手机所属' rules={[{ required: true, message: '请选择手机所属' }]}>
          <Radio.Group>
            <Space direction='vertical'>
              <Radio value='1'>店长</Radio>
              <Radio value='2'>店助</Radio>
              <Radio value='3'>门店手机</Radio>
            </Space>
          </Radio.Group>
        </Form.Item>
        <Form.Item
          name='phoneType'
          label='手机型号（例如iphone13或者小米14）'
          rules={[{ required: true, message: '手机型号不能为空' }]}
        >
          <Input placeholder='请填写手机型号' />
        </Form.Item>
        <Form.Item
          name='storeName'
          label='门店名称'
          rules={[{ required: true, message: '门店名称不能为空' }]}
        >
          <Input placeholder='请填写门店名称' />
        </Form.Item>
      </Form>
      {showExportBtn && (<div style={{margin: '0 0.75rem'}}>
        <Button 
          block 
          type='submit' 
          color='primary' 
          size='middle'
          onClick={() => {
            exportData();
          }}
        >
          导出数据
        </Button>
      </div>)}
    </div>
  )
}
