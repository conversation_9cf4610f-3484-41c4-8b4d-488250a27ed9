/*
 * @Author: chen<PERSON><PERSON> <EMAIL>
 * @Date: 2023-10-20
 * @LastEditors: chenweibin <EMAIL>
 * @LastEditTime: 2023-12-08
 */
import { IconFont } from '@src/components';
import { Popup, SafeArea, Selector } from 'antd-mobile';
import { useState, useEffect } from 'react';
import { CssProps } from '@src/common/api.type';
import { CloseOutline } from 'antd-mobile-icons';

type IProps = CssProps & {
  defaultValue?: string;
  options: any[];
  onChange: (e: string) => void;
};
const ShopFilter: React.FC<IProps> = ({ options, onChange, defaultValue = '' }) => {
  const [visible, setVisible] = useState(false);
  const [value, setValue] = useState<string>(defaultValue);

  useEffect(() => {
    setValue(() => defaultValue);
  }, [defaultValue]);

  const handleConfirm = (val: string) => {
    onChange && onChange(val);
    setVisible(false);
  };

  return (
    <>
      <IconFont
        type="icon-menu"
        className="ml-1"
        style={{ paddingBottom: '4px', color: '#9C9C9C' }}
        onClick={() => setVisible(true)}
      />
      <Popup
        position="bottom"
        visible={visible}
        bodyClassName="!pb-10 box-border rounded-t-lg"
        onMaskClick={() => {
          setValue(() => defaultValue); //数据还原
          setVisible(false);
        }}
      >
        {/* <div className="flex justify-between">
          <Button
            fill="none"
            size="small"
            onClick={() => {
              setValue(() => defaultValue); //数据还原
              setVisible(false);
            }}
          >
            取消
          </Button>
          <Button size="small" color="primary" fill="none" onClick={handleConfirm}>
            确定
          </Button>
        </div> */}
        <div
          className="flex px-4 justify-between items-center border-b border-solid border-line"
          style={{ height: '3.375rem' }}
        >
          <div className="text-base font-semibold">分类</div>
          <CloseOutline
            onClick={() => {
              setValue(() => defaultValue); //数据还原
              setVisible(false);
            }}
            className="text-grey text-13"
          />
        </div>
        <div className="pt-5 px-4">
          <Selector
            showCheckMark={false}
            style={{
              '--border-radius': '4px',
              '--color': 'rgba(249,250,251)',
              '--text-color': '#58595B',
              '--checked-color': '#e1faf5',
              '--padding': '0.5rem 0.5rem',
              fontSize: '0.8125rem',
            }}
            columns={3}
            options={options}
            value={[value]}
            onChange={(v) => {
              if (v.length) {
                setValue(v[0]);
                handleConfirm(v[0]);
              }
            }}
          />
        </div>
        <SafeArea position="bottom" />
      </Popup>
    </>
  );
};

export default ShopFilter;
