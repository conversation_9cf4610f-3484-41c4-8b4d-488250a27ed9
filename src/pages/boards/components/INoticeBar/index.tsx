import { CheckCircleOutline, InformationCircleOutline } from 'antd-mobile-icons';
import clsx from 'clsx';

type IProps = {
  className?: string;
  status?: string; //展示不同图标和颜色
  content: string;
  isShowIcon?: boolean;
};

const INoticeBar: React.FC<IProps> = ({
  content,
  className,
  status = 'danger',
  isShowIcon = true,
}) => {
  return (
    <div
      className={clsx(
        'flex items-center px-4 py-2 leading-5',
        status === 'danger' ? 'bg-red-50 text-red-400' : 'bg-primary-1 text-primary',
        className,
      )}
    >
      {isShowIcon && (
        <div className="w-8">
          {status === 'success' ? (
            <CheckCircleOutline className="font-semibold text-lg" />
          ) : (
            <InformationCircleOutline fontSize={18} className="font-semibold" />
          )}
        </div>
      )}
      <div className="flex-1">{content}</div>
    </div>
  );
};

export default INoticeBar;
