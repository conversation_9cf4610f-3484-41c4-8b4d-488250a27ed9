import { IconFont } from '@src/components';
import { ReactNode, useState } from 'react';
import PopupTip from './PopupTip';
import { CssProps } from '@src/common/api.type';
import { Skeleton } from 'antd-mobile';
import styles from '../../index.module.scss';
import { abs } from './utils';
import clsx from 'clsx';

type IProps = CssProps & {
  type?: 'full' | 'half';
  title: string; //指标名称
  indicator: string; //指标
  tooltip?: string[]; //指标提示语
  percent?: {
    same: string; //月同比
    circle: string; //月环比
  };
  percentDateType?: {
    same: 'day' | 'month' | 'default'; //同/环比类型
    circle: 'day' | 'month' | 'default'; //同/环比类型
  };
  columns?: {
    label: string;
    value: number | string | ReactNode;
  }[];
  isWarning?: boolean; //是否异常
  loading?: boolean;
};

const IndicatorCardSub: React.FC<IProps> = ({
  percentDateType = { circle: 'default', same: 'default' },
  title,
  tooltip,
  indicator,
  percent,
  columns,
  isWarning,
  className,
  loading,
}) => {
  const percentDateTypeMap = {
    day: '周',
    month: '月',
    default: '',
  };

  const [visible, setVisible] = useState<boolean>(false);

  const isUp = (val?: string) => {
    if (!val) return null;
    const valList = val.split('%');
    if (valList[0] === '-') return null;
    if (Number(valList[0]) === 0) return null;
    if (Number(valList[0]) > 0)
      return <IconFont className="ml-1" type="icon-shangjiantou" style={{ color: '#DF5850' }} />;
    if (Number(valList[0]) < 0)
      return <IconFont className="ml-1" type="icon-xiajiantou" style={{ color: '#00BBB4' }} />;
  };
  if (loading) {
    return (
      <div className="flex justify-between bg-white p-3 border-l border-b border-r border-solid border-line">
        <div>
          <Skeleton animated className="w-[200px] h-6 rounded-[12px]" />
          <Skeleton animated className="w-[80px] h-6 mt-3 rounded-[12px]" />
        </div>
        <div>
          <Skeleton animated className="w-20 h-6 rounded-[12px]" />
          <Skeleton animated className="w-20 h-6 mt-3 rounded-[12px]" />
        </div>
      </div>
    );
  }
  return (
    <div
      className={clsx(
        className,
        styles.IndicatorCardSub,
        visible ? '' : 'border-b',
        'border-l border-r border-solid border-line bg-white',
      )}
    >
      <div className={`pt-3 pb-[10px] px-3 flex justify-between`}>
        <div className="text-dark">
          <div className="flex items-center text-[13px] leading-[21px]">
            {title}
            {tooltip && <PopupTip title={title} list={tooltip || []} />}
            {isWarning && (
              <div
                className="leading-[15px] text-[13px] px-1 rounded-[2px] ml-1"
                style={{ color: '#E75454', border: '1px solid #E75454' }}
              >
                异常
              </div>
            )}
          </div>
          <div className="text-[16px] leading-[24px] mt-1 font-semibold">{indicator}</div>
        </div>
        <div className="text-xs mt-1 leading-none">
          <div className="flex items-center text-85 justify-end">
            {percentDateTypeMap[percentDateType.same]}同比:
            <span className="text-5E" style={{ marginLeft: '0.125rem' }}>
              {abs(percent?.same)}
            </span>
            {isUp(percent?.same)}
          </div>
          <div className="flex items-center text-85 mt-2 justify-end">
            {percentDateTypeMap[percentDateType.circle]}环比:
            <span className="text-5E" style={{ marginLeft: '0.125rem' }}>
              {abs(percent?.circle)}
            </span>
            {isUp(percent?.circle)}
          </div>
          <div className="pt-1 pl-3 flex justify-end" onClick={() => setVisible((p) => !p)}>
            <IconFont
              type={visible ? 'icon-chevron-up' : 'icon-chevron-down'}
              className="text-85"
            />
          </div>
        </div>
      </div>
      {visible && (
        <div className={clsx(styles.IndicatorSubContent, 'pb-3 px-3 bg-FA')}>
          {columns?.map((count, index) => (
            <div key={index} className="flex text-[12px] pt-3 text-5E leading-none justify-between">
              <div>{count?.label}</div>
              <div>{count?.value ?? '-'}</div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default IndicatorCardSub;
