import { IconFont } from '@src/components';
import { Popup, SafeArea } from 'antd-mobile';
import { useState } from 'react';
import { CssProps } from '@src/common/api.type';
import { CloseOutline } from 'antd-mobile-icons';

type IProps = CssProps & {
  title?: string;
  list: string[];
  fontSize?: string;
};
const PopupTip: React.FC<IProps> = ({ title, list = [], fontSize = 'text-[18px]' }) => {
  const [visible, setVisible] = useState(false);

  return (
    <>
      <IconFont
        type="icon-question-circle"
        className={`ml-1 text-B8 ${fontSize}`}
        onClick={(e) => {
          e.stopPropagation();
          setVisible(true);
        }}
      />
      <Popup
        position="bottom"
        visible={visible}
        bodyClassName="!pb-10 box-border rounded-t-lg"
        onMaskClick={() => {
          setVisible(false);
        }}
      >
        {/* <div className="flex justify-between">
          <Button
            fill="none"
            size="small"
            onClick={() => {
              setValue(() => defaultValue); //数据还原
              setVisible(false);
            }}
          >
            取消
          </Button>
          <Button size="small" color="primary" fill="none" onClick={handleConfirm}>
            确定
          </Button>
        </div> */}
        <div
          className="flex px-4 justify-between items-center border-b border-solid border-line"
          style={{ height: '3.375rem' }}
        >
          <div className="text-[18px] text-dark font-semibold">说明</div>
          <CloseOutline
            onClick={() => {
              setVisible(false);
            }}
            className="text-grey text-13"
          />
        </div>
        <div className="pt-4 px-4">
          {title && (
            <div className="text-[14px] leading-[22px] text-dark font-semibold">{title}</div>
          )}
          <div className="text-5E leading-[22px] max-h-96 overflow-y-auto">
            {list.map((v, index) => (
              <div key={index}>{v}</div>
            ))}
          </div>
        </div>
        <SafeArea position="bottom" />
      </Popup>
    </>
  );
};

export default PopupTip;
