import { ReactNode } from 'react';
import { CssProps } from '@src/common/api.type';
import { IconFont } from '@src/components';
import clsx from 'clsx';
import PopupTip from './PopupTip';
import { abs } from './utils';

type IProps = CssProps & {
  type?: 'full' | 'half';
  title: string; // 指标名称
  indicator: string; // 指标
  tooltip?: string[]; // 指标提示语
  percent?: {
    same: string; // 月同比
    circle: string; // 月环比
  };
  percentDateType?: {
    same: 'day' | 'month' | 'default'; // 同/环比类型
    circle: 'day' | 'month' | 'default'; // 同/环比类型
  };
  count?: {
    label: string;
    value: number | string | ReactNode;
  };
  columns?: { label: string; value: number | string | ReactNode }[];
  isWarning?: boolean; // 是否异常
  isWarningText?: string; // 异常文本
  detailContent?: ReactNode;
  extra?: ReactNode;
  content?: ReactNode;
  lineClassName?: string;
};

export const isUp = (val?: string) => {
  if (!val) return null;
  const valList = val.split('%');
  if (valList[0] === '-') return null;
  if (Number(valList[0]) === 0) return null;
  if (Number(valList[0]) > 0)
    return (
      <IconFont
        className="ml-1"
        type="icon-shangjiantou"
        style={{ color: '#DF5850' }}
      />
    );
  if (Number(valList[0]) < 0)
    return (
      <IconFont
        className="ml-1"
        type="icon-xiajiantou"
        style={{ color: '#00BBB4' }}
      />
    );
};

const IndicatorCardV2: React.FC<IProps> = ({
  percentDateType = { circle: 'default', same: 'default' },
  title,
  tooltip,
  indicator,
  percent,
  count,
  columns,
  isWarning,
  isWarningText = '异常',
  content,
  className,
  lineClassName,
}) => {
  const percentDateTypeMap = {
    day: '周',
    month: '月',
    default: '',
  };

  return (
    <>
      <div
        className={clsx(
          'flex border border-solid border-line bg-white rounded-t-lg',
          className,
        )}
      >
        <div className={clsx('w-1 rounded-tl-base bg-primary', lineClassName)}>
          &nbsp;
        </div>
        <div className="flex-1">
          <div
            className={`pt-3 pb-[10px] px-3 flex justify-between`}
            style={{
              background:
                'linear-gradient(90deg, rgba(55, 139, 255, 0.11) 0%, rgba(55, 139, 255, 0) 86%)',
            }}
          >
            <div className="text-dark">
              <div className="flex items-center text-sm leading-[22px]">
                {title}
                {tooltip && <PopupTip title={title} list={tooltip || []} />}
                {isWarning && (
                  <div
                    className="leading-[15px] text-[13px] px-1 rounded-[2px] ml-1"
                    style={{ color: '#E75454', border: '1px solid #E75454' }}
                  >
                    {isWarningText}
                  </div>
                )}
              </div>
              <div className="text-[20px] leading-[28px] mt-1 font-semibold">
                {indicator}
              </div>
            </div>
            <div className="text-xs mt-2 leading-none">
              <div className="flex items-center text-85 justify-end">
                {percentDateTypeMap[percentDateType.same]}同比:
                <span className="text-5E" style={{ marginLeft: '0.125rem' }}>
                  {abs(percent?.same)}
                </span>
                {isUp(percent?.same)}
              </div>
              <div className="flex items-center text-85 mt-[10px] justify-end">
                {percentDateTypeMap[percentDateType.circle]}环比:
                <span className="text-5E" style={{ marginLeft: '0.125rem' }}>
                  {abs(percent?.circle)}
                </span>
                {isUp(percent?.circle)}
              </div>
            </div>
          </div>
          {count?.label && (
            <div className="pb-3 px-3">
              <div className="flex text-[12px] pt-3 text-5E leading-none justify-between">
                <div>{count?.label}</div>
                <div>{count?.value}</div>
              </div>
            </div>
          )}
          {columns && columns.length && (
            <div className="pb-3 px-3">
              {columns.map((v, i) => (
                <div
                  key={i}
                  className="flex text-[12px] pt-3 text-5E leading-none justify-between"
                >
                  <div>{v?.label}</div>
                  <div>{v?.value ?? '-'}</div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
      {content}
    </>
  );
};

export default IndicatorCardV2;
