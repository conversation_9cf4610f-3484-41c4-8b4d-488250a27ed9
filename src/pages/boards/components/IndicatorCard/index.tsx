import { ReactElement, ReactNode } from 'react';
import { CssProps } from '@src/common/api.type';
import { IconFont } from '@src/components';
import { Card, CardProps, Rate } from 'antd-mobile';
import { RightOutline } from 'antd-mobile-icons';
import clsx from 'clsx';
import { parse, stringify } from 'qs';
import { useLocation, useNavigate } from 'react-router-dom';
import PopupTip from './PopupTip';
import { abs } from './utils';
import GaugeChart from '../GaugeChart';

type IProps = CardProps &
  CssProps & {
    type?: 'full' | 'half';
    title: string; // 指标名称
    indicator: string | ReactNode; // 指标
    tooltip?: string[]; // 指标提示语
    percent?: {
      same: string; // 月同比
      circle: string; // 月环比
    };
    content?: ReactElement;
    percentDateType?: {
      same: 'day' | 'month' | 'default'; // 同/环比类型
      circle: 'day' | 'month' | 'default'; // 同/环比类型
    };
    count?: {
      label: string;
      value: number;
    };
    isWarning?: boolean; // 是否异常
    isWarningText?: string; // 异常文本
    detailContent?: ReactNode;
    extra?: ReactNode;
    hasReport?: boolean;
    isChart?: boolean;
    isRate?: boolean;
    rateVal?: number;
    indicatorElement?: ReactNode;
    handleClick?: () => void;
  };

const IndicatorCard: React.FC<IProps> = ({
  type = 'full',
  percentDateType = { circle: 'default', same: 'default' },
  title,
  tooltip,
  indicator,
  percent,
  count,
  className,
  style,
  isWarning,
  content,
  isChart = true,
  hasReport,
  indicatorElement,
  handleClick,
  isWarningText = '异常',
  isRate = false,
  rateVal = 0,
  ...rest
}) => {
  const percentDateTypeMap = {
    day: '周',
    month: '月',
    default: '',
  };

  const location = useLocation();
  const navigate = useNavigate();

  const isUp = (val?: string) => {
    if (!val) return null;
    const valList = val.split('%');
    if (valList[0] === '-') return null;
    if (Number(valList[0]) === 0) return null;
    if (Number(valList[0]) > 0)
      return (
        <IconFont
          className="ml-1"
          type="icon-shangjiantou"
          style={{ color: '#DF5850' }}
        />
      );
    if (Number(valList[0]) < 0)
      return (
        <IconFont
          className="ml-1"
          type="icon-xiajiantou"
          style={{ color: '#00BBB4' }}
        />
      );
  };

  // const imgUrl = new URL('./count_bg.png', import.meta.url).href;

  const IndicatorFull = () => {
    return (
      <Card
        className={clsx(className, 'rounded-none px-4 leading-none')}
        bodyClassName="!py-4"
        onClick={() => {
          const { pageType, ...rest } = parse(location.search.slice(1));
          !pageType &&
            navigate(
              `${location.pathname}?${stringify({ ...rest, pageType: 'detail' })}`,
            );
          handleClick && handleClick();
        }}
        {...rest}
      >
        <div className="flex items-center">
          <div className="flex items-center flex-1">
            <div className="flex items-center text-sm leading-[22px]">
              {title}
              {tooltip && (
                // <Popover content={tooltip} trigger="click" placement="top" mode="dark">
                //   <IconFont
                //     type="icon-changjianwentixiangguanwenti"
                //     className="ml-1"
                //     style={{ color: '#9C9C9C' }}
                //   />
                // </Popover>
                <PopupTip title={title} list={tooltip || []} />
              )}
            </div>
            {isWarning && (
              <div
                className="leading-[15px] text-[13px] px-1 rounded-[2px] ml-1"
                style={{ color: '#E75454', border: '1px solid #E75454' }}
              >
                {isWarningText}
              </div>
            )}
          </div>
          <div className="flex items-center">
            {hasReport && (
              <>
                <IconFont
                  type="icon-list-right"
                  className="mr-[2px] text-[16px] text-primary"
                />
                <span className="mr-1 text-13 text-primary">报告已生成</span>
              </>
            )}

            <RightOutline className="text-B8 text-xs leading-none" />
          </div>
        </div>
        <div
          className={`flex justify-between ${!isChart ? 'items-center' : 'mt-3'}`}
        >
          {isChart ? (
            <div style={{ height: '3.625rem', overflow: 'hidden' }}>
              <GaugeChart
                value={indicator as string}
                style={{ height: '7.1875rem', width: '7.1875rem' }}
              />
            </div>
          ) : (
            <div className="flex flex-col gap-2">
              <div className="text-dark text-[20px] mt-3">{indicator}</div>
              {isRate && (
                <Rate
                  allowHalf
                  readOnly
                  value={rateVal}
                  style={{ '--star-size': '19px' }}
                />
              )}
            </div>
          )}
          {indicatorElement}
          {!indicatorElement && (
            <div className="flex flex-col justify-end items-end text-xs leading-none">
              {count?.label && (
                <div className="flex text-13">
                  <div
                    className="text-grey leading-none"
                    style={{ marginRight: '0.125rem' }}
                  >
                    {count?.label}:
                  </div>
                  <div>{count?.value}</div>
                </div>
              )}
              <div className="flex items-center mt-2 text-light">
                {percentDateTypeMap[percentDateType.same]}同比:
                <span className="text-grey" style={{ marginLeft: '0.125rem' }}>
                  {abs(percent?.same)}
                </span>
                {isUp(percent?.same)}
              </div>
              <div className="flex items-center text-light mt-[10px] leading-none">
                {percentDateTypeMap[percentDateType.circle]}环比:
                <span className="text-grey" style={{ marginLeft: '0.125rem' }}>
                  {abs(percent?.circle)}
                </span>
                {isUp(percent?.circle)}
              </div>
            </div>
          )}
        </div>
      </Card>
    );
  };

  const IndicatorHalf = () => {
    return (
      <Card
        className={clsx('rounded-none pl-4 pr-3 leading-none', className)}
        style={style}
        bodyClassName="!py-4"
        onClick={() => {
          const { pageType, ...rest } = parse(location.search.slice(1));
          !pageType &&
            navigate(
              `${location.pathname}?${stringify({ ...rest, pageType: 'detail' })}`,
            );
          handleClick && handleClick();
        }}
        {...rest}
      >
        <div className="mb-4">
          <div className="flex items-center text-sm text-grey">
            <div className="flex items-center flex-1 text-sm text-dark leading-[22px]">
              {title}
              {tooltip && (
                // <Popover content={tooltip} trigger="click" placement="top" mode="dark">
                //   <IconFont
                //     type="icon-changjianwentixiangguanwenti"
                //     className="ml-1"
                //     style={{ color: '#9C9C9C' }}
                //   />
                // </Popover>
                <PopupTip title={title} list={tooltip || []} />
              )}
            </div>
            <div className="flex items-center">
              <RightOutline className="text-B8 text-xs leading-none" />
            </div>
          </div>
          <div className={`flex mt-3`}>
            <div style={{ height: '3.625rem', overflow: 'hidden' }}>
              <GaugeChart
                value={indicator as string}
                style={{ height: '7.1875rem', width: '7.1875rem' }}
              />
            </div>
            {isWarning && (
              <div
                className="leading-[17px] text-[13px] px-1 rounded-[2px] ml-1 h-[17px]"
                style={{ color: '#E75454', border: '1px solid #E75454' }}
              >
                {isWarningText}
              </div>
            )}
          </div>
        </div>
        {content ? (
          content
        ) : (
          <>
            {count?.label && (
              <div
                className="flex text-13 leading-[21px]"
                style={{ marginTop: '0.625rem' }}
              >
                <div className="text-grey">{count?.label}:</div>
                <div style={{ marginLeft: '0.125rem' }}>{count?.value}</div>
              </div>
            )}
            <div className="flex items-center text-xs leading-none text-light mt-2">
              {percentDateTypeMap[percentDateType.same]}同比:
              <span className="text-grey" style={{ marginLeft: '0.125rem' }}>
                {abs(percent?.same)}
              </span>
              {isUp(percent?.same)}
              {/* <IconFont className="ml-1" type="icon-caret-up" /> */}
            </div>
            <div
              className="flex items-center text-xs text-light"
              leading-none
              style={{ marginTop: '0.625rem' }}
            >
              {percentDateTypeMap[percentDateType.circle]}环比:
              <span className="text-grey" style={{ marginLeft: '0.125rem' }}>
                {abs(percent?.circle)}
              </span>
              {isUp(percent?.circle)}
              {/* <IconFont className="ml-1" type="icon-caret-down" /> */}
            </div>
          </>
        )}
      </Card>
    );
  };

  const Component = type === 'full' ? IndicatorFull : IndicatorHalf;

  return <Component />;
};

export const IndicatorCardShimmer = () => {
  return (
    <div className="h-[133px] p-4 mb-2 bg-white flex flex-col gap-y-5">
      <div className="shimmer w-full h-[22px] rounded-lg" />
      <div className="flex justify-between">
        <div className="shimmer w-16 h-4 rounded-lg" />
        <div className="flex flex-col justify-between items-end h-[57.5px] rounded-tl-[57.5px]">
          <div className="shimmer h-4 w-32 rounded-lg" />
          <div className="shimmer h-3 w-16 rounded-lg" />
          <div className="shimmer h-3 w-8 rounded-lg" />
        </div>
      </div>
    </div>
  );
};

export default IndicatorCard;
