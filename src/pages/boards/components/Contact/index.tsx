import { useRequest } from 'ahooks';
import { Button, Dialog, Space, Toast } from 'antd-mobile';
import { sendMsg } from '../../api';
import { IconFont } from '@src/components';

type IProps = {
  manager: {
    name?: string;
    phone?: string;
    id?: number | string;
  };
  percent: {
    label: string;
    value: string;
  };
  title: string;
  type: 'card' | 'page';
};

const Contact: React.FC<IProps> = ({ title, manager, percent, type = 'page' }) => {
  const { run, loading } = useRequest((e) => sendMsg({ ...e }), {
    manual: true,
    onSuccess: () => {
      Toast.show({
        icon: 'success',
        content: '发送成功',
      });
    },
  });
  const CardStyle = () => {
    return (
      <Space>
        <Button
          size="mini"
          color="primary"
          disabled={!manager.id}
          className="text-xs py-0"
          style={{
            '--border-radius': '8px',
            fontSize: '.8125rem',
            paddingTop: 0,
            paddingBottom: 0,
          }}
          loading={loading}
          fill="none"
          onClick={() => {
            // if (!manager.id) {
            //   Toast.show({
            //     content: `未设置${type === 'group' ? '负责人' : '店长'}，请联系上级负责人确认处理`,
            //   });
            //   return;
            // }
            Dialog.confirm({
              content: `您将通过“飞书”消息，通知相关负责人关注异常问题`,
              onConfirm: () =>
                run({
                  userIds: [manager.id],
                  module: '1',
                  msgContent: {
                    title: `【${percent.label}】预警通知`,
                    content: `您负责的${title}【${percent.label}】为${percent.value}，存在风险，请及时处理。`,
                  },
                }),
            });
          }}
        >
          <div className="flex items-center text-xs leading-none">
            <IconFont type="icon-rongqi-2" className="mr-1 text-sm text-primary" />
            飞书通知
          </div>
        </Button>
        <Button
          size="mini"
          color="primary"
          className="text-xs"
          disabled={!manager.phone}
          style={{
            '--border-radius': '8px',
            fontSize: '.8125rem',
            paddingTop: 0,
            paddingBottom: 0,
          }}
          fill="none"
          onClick={() => {
            window.location.href = `tel:${manager.phone}`;
          }}
        >
          <div className="flex items-center text-xs leading-none">
            <IconFont type="icon-rongqi" className="mr-1 text-sm text-primary" />
            拨打电话
          </div>
        </Button>
      </Space>
    );
  };
  const PageStyle = () => {
    return (
      <div className="flex px-4 py-3 items-center">
        <div className="flex-1">
          <span className="pr-1 text-grey">店长:</span>
          <span>{manager.name}</span>
        </div>
        <div className="flex">
          <Button
            color="primary"
            disabled={!manager.id}
            // shape="rounded"
            className="h-[30px] text-sm leading-none"
            fill="outline"
            loading={loading}
            onClick={() => {
              Dialog.confirm({
                content: `您将通过“飞书”消息，通知相关负责人关注异常问题`,
                onConfirm: () =>
                  run({
                    userIds: [manager.id],
                    module: '1',
                    msgContent: {
                      title: `【${percent.label}】预警通知`,
                      content: `您负责的${title}【${percent.label}】为${percent.value}，存在风险，请及时处理。`,
                    },
                  }),
              });
            }}
          >
            飞书通知
          </Button>
          <Button
            color="primary"
            className="ml-3 h-[30px] text-sm leading-none"
            // shape="rounded"
            fill="outline"
            disabled={!manager.phone}
            onClick={() => {
              window.location.href = `tel:${manager.phone}`;
            }}
          >
            拨打电话
          </Button>
        </div>
      </div>
    );
  };

  return type === 'card' ? <CardStyle /> : <PageStyle />;
};

export default Contact;
