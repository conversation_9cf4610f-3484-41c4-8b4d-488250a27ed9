import { useEffect, useState } from 'react';

type IProps = {
  value?: string;
  onChange?: (key: string) => void;
  disabledShop?: boolean;
  disabledGroup?: boolean;
};
const CustomerType: React.FC<IProps> = ({ value, onChange, disabledShop, disabledGroup }) => {
  const [activeKey, setActiveKey] = useState<string>(value || 'shop');

  useEffect(() => {
    onChange && onChange(activeKey);
  }, [activeKey]);

  if (disabledGroup && disabledShop) {
    return <></>;
  }

  if (disabledGroup && !disabledShop) {
    return (
      <div className="flex text-xs leading-7 text-center">
        <div
          className={`w-14 rounded-base bg-primary-1 text-primary border-primary border border-solid`}
        >
          门店
        </div>
      </div>
    );
  }

  if (!disabledGroup && disabledShop) {
    return (
      <div className="flex text-xs leading-7 text-center">
        <div
          className={`w-14 rounded-base bg-primary-1 text-primary border-primary border border-solid`}
        >
          组织
        </div>
      </div>
    );
  }

  return (
    <div className="flex text-xs leading-7 text-center text-grey">
      <div
        className={`w-14 rounded-l-base border border-solid ${
          activeKey === 'shop'
            ? 'bg-primary-1 text-primary border-primary'
            : 'border-line border-r-0'
        }`}
        onClick={() => {
          setActiveKey('shop');
        }}
      >
        门店
      </div>
      <div
        className={`w-14 rounded-r-base border border-solid ${
          activeKey === 'group'
            ? 'bg-primary-1 text-primary border-primary'
            : 'border-line border-l-0'
        }`}
        onClick={() => setActiveKey('group')}
      >
        组织
      </div>
    </div>
  );
};

export default CustomerType;
