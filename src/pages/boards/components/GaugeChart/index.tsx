import React, { useMemo } from 'react';
import ReactECharts, { EChartsOption } from 'echarts-for-react';

interface Props {
  value: string;
  style?: any;
}

const Line: React.FC<Props> = ({ value, ...rest }: Props) => {
  const data = value && String(value)?.includes('%') ? value.replace('%', '') : value;
  const options: EChartsOption = {
    series: [
      {
        type: 'gauge',
        radius: '100%',
        startAngle: 180,
        endAngle: 0,
        //进度条
        progress: {
          show: true,
          width: 11,
          itemStyle: {
            color: '#378BFF',
          },
        },
        //指针
        pointer: {
          show: false,
        },
        //轴线
        axisLine: {
          lineStyle: {
            width: 11,
            color: [[1, 'rgba(55, 139, 255, 0.1)']],
          },
        },
        //刻度
        axisTick: {
          show: false,
        },
        //分割线
        splitLine: {
          show: false,
        },
        //刻度值
        axisLabel: {
          show: false,
        },
        title: {
          show: false,
        },
        detail: {
          color: '#222',
          fontSize: 18,
          offsetCenter: [0, -7],
          valueAnimation: true,
          formatter: function (value: number) {
            return isNaN(value) ? '-' : value + '%';
          },
        },
        data: [
          {
            value: data,
          },
        ],
      },
    ],
  };

  const LineCharts = useMemo(() => {
    // if (!data) return <div></div>;
    return <ReactECharts option={options} {...rest} />;
  }, [value]);

  return <div>{LineCharts}</div>;
};

export default Line;
