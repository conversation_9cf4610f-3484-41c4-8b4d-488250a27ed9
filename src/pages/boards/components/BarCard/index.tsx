import { Bar } from '@src/components/IEcharts/Bar/Bar';
import PopupTip from '../IndicatorCard/PopupTip';
import { color } from '@src/components/IEcharts/Bar/BarOptionColor';
import { IconFont } from '@src/components';
import clsx from 'clsx';
import { formatPer } from '@src/utils/helper';

interface BarCardProps {
  id: string;
  data: { name: string; value: number; label?: string }[];
  title?: string;
  tooltip?: string[];
  titleClassName?: string;
  layout?: 'half' | 'full';
  yType?: '￥' | '%';
  mt?: string;
}

export const BarCard = ({
  id,
  data,
  title,
  tooltip,
  titleClassName,
  layout = 'half',
  yType = '￥',
  mt = 'mt-6',
}: BarCardProps) => {
  const colorList = color.slice(0, data.length);

  return (
    <div
      className={clsx('p-4', {
        'col-span-2': layout === 'full',
      })}
    >
      <div
        className={`pb-5 text-[14px] leading-none font-medium flex items-center ${titleClassName}}`}
      >
        {title}
        {tooltip && <PopupTip title={title} list={tooltip || []} />}
      </div>
      {data.length !== 0 && !data.every((o) => !!!o?.value) ? (
        <>
          <div className="flex flex-col">
            <Bar
              id={id}
              data={data}
              style={{
                height: '100px',
              }}
              barWidth={layout === 'half' ? 17 : data.length >= 10 ? 17 : 24}
              yType={yType}
            />
          </div>
          <div
            className={`grid ${
              layout === 'half' ? 'grid-cols-1 gap-2' : 'grid-cols-2 gap-x-5 gap-y-2'
            }  w-full ${mt}`}
          >
            {colorList.map((item, index) => (
              <div
                className={clsx('flex items-center justify-between text-xs leading-[20px]', {})}
                key={index}
              >
                <div className="flex items-center shrink-0">
                  <div
                    className="rounded-full h-[5px] w-[5px] mr-1"
                    style={{ backgroundColor: item }}
                  ></div>
                  <div className="text-5E">{data[index].name}</div>
                </div>
                <div className="ml-2 text-dark-14">
                  {data[index]?.label ? data[index].label : formatPer(data[index].value, '%', 1)}
                </div>
              </div>
            ))}
          </div>
        </>
      ) : (
        <div className="flex flex-col items-center h-full gap-4">
          <IconFont type="icon-zanwushuju" className="text-[90px]  text-[#DCDCDC]" />
          <span className="text-[#858585] text-xs">暂无数据</span>
        </div>
      )}
    </div>
  );
};
