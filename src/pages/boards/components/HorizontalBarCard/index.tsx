import PopupTip from '../IndicatorCard/PopupTip';
import { HorizontalBar } from '@src/components/IEcharts/Bar/HorizontalBar';
import { horizontalColor } from '@src/components/IEcharts/Bar/BarOptionColor';
import { IconFont } from '@src/components';
import clsx from 'clsx';
import { formatPer } from '@src/utils/helper';

interface HorizontalBarCardProps {
  id: string;
  data: { name: string; value: number; label?: string; yoy?: number; qoq?: number }[];
  title: string;
  tooltip?: any[];
  titleClassName?: string;
  // 从某个下标开始截取bar的颜色数组
  horizontalColorIndex?: number;
}

export const HorizontalBarCard = ({
  id,
  data,
  title,
  tooltip,
  titleClassName,
  horizontalColorIndex = 0,
}: HorizontalBarCardProps) => {
  const colorList = horizontalColor.slice(horizontalColorIndex, horizontalColorIndex + data.length);

  return (
    <div className={clsx('p-4')}>
      <div
        className={`pb-5 text-[14px] leading-none font-medium flex items-center ${titleClassName}}`}
      >
        {title}
        {tooltip && <PopupTip title={title} list={tooltip || []} />}
      </div>
      {data.length !== 0 && !data.every((o) => !!!o?.value) ? (
        <>
          <div className="flex flex-col">
            <HorizontalBar
              id={id}
              data={data}
              style={{
                height: '100px',
              }}
              barWidth={18}
              horizontalColorIndex={horizontalColorIndex}
            />
          </div>
          <div className={`w-full mt-6 flex flex-wrap justify-between gap-2`}>
            {colorList.map((item, index) => (
              <div
                className={clsx('flex items-center text-xs leading-[20px] gap-x-1', {})}
                key={index}
              >
                <div className="flex items-center">
                  <div
                    className="rounded-full h-[5px] w-[5px] mr-1"
                    style={{ backgroundColor: item }}
                  ></div>
                  <div className="text-5E">{data[index].name}:</div>
                </div>
                <div className="text-dark-14">
                  {data[index]?.label ? data[index].label : formatPer(data[index].value, '%', 1)}
                </div>
              </div>
            ))}
          </div>
        </>
      ) : (
        <div className="flex flex-col items-center h-full gap-4">
          <IconFont type="icon-zanwushuju" className="text-[90px]  text-[#DCDCDC]" />
          <span className="text-[#858585] text-xs">暂无数据</span>
        </div>
      )}
    </div>
  );
};
