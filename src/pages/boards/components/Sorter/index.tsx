import { useState } from 'react';
import { useControllableValue } from 'ahooks';
import { Picker } from 'antd-mobile';
import { DownOutline } from 'antd-mobile-icons';

type IProps = {
  config: { label: string; value: string }[];
  value?: string[];
  onChange?: (v: string[]) => void;
};
const Sorter: React.FC<IProps> = ({ config, ...props }) => {
  const [visible, setVisible] = useState(false);
  const [value, setValue] = useControllableValue(props);
  return (
    <>
      <div
        className="text-primary text-xs flex items-center"
        onClick={() => setVisible(true)}
      >
        {/* <Ellipsis
          className="mr-1 flex-1 text-left"
          direction="end"
          content={
            value && value[0]
              ? config.find((v) => v.value === value[0])?.label || '默认排序'
              : '默认排序'
          }
        /> */}
        <div className="flex-1 text-left ellipsis" style={{ maxWidth: '6rem' }}>
          {value && value[0]
            ? config.find((v) => v.value === value[0])?.label || '默认排序'
            : '默认排序'}
        </div>
        <DownOutline className="ml-1 text-xs" />
      </div>
      <Picker
        columns={[config]}
        visible={visible}
        onClose={() => {
          setVisible(false);
        }}
        value={value}
        onConfirm={(v) => {
          setValue(v);
        }}
      />
    </>
  );
};

export default Sorter;
