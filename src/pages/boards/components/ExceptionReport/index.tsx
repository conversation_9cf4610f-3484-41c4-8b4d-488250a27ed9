import { CssProps } from '@src/common/api.type';
import { IconFont } from '@src/components';
import { Ellipsis } from 'antd-mobile';
import { RightOutline } from 'antd-mobile-icons';
import clsx from 'clsx';
import dayjs from 'dayjs';

type IProps = CssProps & {
  shopNum: number | null;
  title: string;
  content: string | string[];
  time: string;
  type?: 'list' | 'page'; //在列表展示 在页面展示
  headerClick?: () => void; //头部
  footerClick?: () => void; //底部
  onClick?: () => void; //整个卡片
  onDetailClick?: () => void; //详情点击事件
  hiddenDetialEntry?: boolean; //是否隐藏详情入口
  hasReport?: boolean; //是否生成报告
};
const ExceptionReport: React.FC<IProps> = ({
  shopNum,
  content,
  title,
  time,
  type = 'list',
  className,
  headerClick,
  footerClick,
  onClick,
  onDetailClick,
  hiddenDetialEntry = false,
  hasReport,
}) => {
  return hasReport ? (
    <div
      className={clsx(className, 'bg-white py-4 px-3 rounded-lg leading-none')}
      onClick={() => onClick && onClick()}
    >
      <div className="flex items-center text-dark" onClick={() => headerClick && headerClick()}>
        {shopNum !== null ? (
          <>
            <div className="text-sm leading-[22px] font-semibold">{shopNum ?? 0}</div>
            <div className="flex-1 pl-1 text-sm leading-[22px] font-semibold">家门店{title}</div>
          </>
        ) : (
          <div className="flex-1 text-sm leading-[22px] font-semibold">{title}</div>
        )}
        {!hiddenDetialEntry && (
          <div className="flex items-center">
            {type === 'page' && (
              <div
                onClick={() => {
                  onDetailClick?.();
                }}
                className="text-[14px] text-grey"
              >
                详情
              </div>
            )}
            <RightOutline className="text-85 text-xs leading-none ml-1" />
          </div>
        )}
      </div>
      <div
        className={`py-2 text-sm leading-[22px] border-b word-break ${
          type === 'page' ? 'border-solid' : 'border-dashed'
        }  border-line`}
      >
        {type === 'page' ? (
          typeof content === 'string' && (
            <Ellipsis direction="end" content={content} expandText="展开" collapseText="收起" />
          )
        ) : typeof content === 'string' ? (
          content
        ) : (
          <>
            <div>{content[0]}</div>
            <div>{content[1]}</div>
          </>
        )}
      </div>
      <div
        className="flex items-center leading-none mt-4"
        onClick={() => footerClick && footerClick()}
      >
        {type === 'page' && (
          <div>
            <IconFont type="icon-xingzhuangjiehe" className="text-primary text-base mr-1" />
          </div>
        )}
        <div className="flex-1 text-[14px]">
          {type === 'page' ? '最新报告' : '报告时间'}:
          <span className="ml-4">{time ? dayjs(time).format('YYYY/MM/DD') : '-'}</span>
        </div>
        {type === 'page' && <div className="text-[14px] text-85">历史报告</div>}
        {type === 'page' && <RightOutline className="text-85 text-xs leading-none ml-1" />}
      </div>
    </div>
  ) : (
    <div
      onClick={() => footerClick && footerClick()}
      className="px-3 py-4 rounded-lg bg-white flex items-center"
    >
      <IconFont type="icon-xingzhuangjiehe" className="text-primary text-base mr-1" />
      <div className="flex-1 leading-none">报告暂未生成</div>
      <IconFont type="icon-chevron-right" className=" text-sm text-85" />
    </div>
  );
};

export default ExceptionReport;
