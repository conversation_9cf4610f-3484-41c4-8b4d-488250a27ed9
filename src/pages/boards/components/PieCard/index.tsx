import { Pie } from '@src/components/IEcharts';
import PopupTip from '../IndicatorCard/PopupTip';
import { color } from '@src/components/IEcharts/Bar/BarOptionColor';
import { IconFont } from '@src/components';
import clsx from 'clsx';
import { formatPer } from '@src/utils/helper';

type props = {
  title: string;
  data: { name: string; value: number; label?: string }[];
  tooltip?: string[];
  titleClassName?: string;
  layout?: 'horizontal' | 'vertical';
  colSpan?: 'half' | 'full';
};

const PieCard = ({
  title,
  data,
  tooltip,
  titleClassName,
  layout = 'horizontal',
  colSpan = 'half',
}: props) => {
  const colorList = color.slice(0, data.length);
  return (
    <div
      className={clsx('p-4', {
        'col-span-2': colSpan === 'full',
      })}
    >
      <div
        className={`pb-5 text-[14px] leading-none font-medium flex items-center ${titleClassName}}`}
      >
        {title}
        {tooltip && <PopupTip title={title} list={tooltip || []} />}
      </div>
      {data.length !== 0 && !data.every((o) => !!!o?.value) ? (
        <div
          className={clsx('flex items-center justify-around', {
            'flex-col': layout === 'vertical',
          })}
        >
          <div className="flex justify-center">
            <Pie
              id="sale_platform"
              data={data}
              style={{ height: '100px', width: '100px' }}
              showTip={true}
            />
          </div>
          <div
            className={clsx(
              `grid ${colSpan === 'half' ? 'grid-cols-1 gap-2' : 'grid-cols-2 gap-x-10 gap-y-2'}`,
              {
                '': layout === 'horizontal',
                'w-full mt-6': layout === 'vertical',
              },
            )}
          >
            {colorList.map((item, index) => (
              <div
                className={clsx('flex items-center text-xs leading-[20px]', {
                  'justify-between': layout === 'vertical',
                })}
                key={index}
              >
                <div className="flex items-center">
                  <div
                    className="rounded-full h-[5px] w-[5px] mr-1"
                    style={{ backgroundColor: item }}
                  ></div>
                  <div className="text-5E">{data[index].name}</div>
                </div>
                <div className="ml-2 text-dark-14">
                  {data[index]?.label ? data[index].label : formatPer(data[index].value, '%', 1)}
                </div>
              </div>
            ))}
          </div>
        </div>
      ) : (
        <div className="flex flex-col items-center h-full gap-4">
          <IconFont type="icon-zanwushuju" className="text-[90px]  text-[#DCDCDC]" />
          <span className="text-[#858585] text-xs">暂无数据</span>
        </div>
      )}
    </div>
  );
};

export default PieCard;
