import { CssProps } from '@src/common/api.type';
import Contact from '../Contact';
import React, { ReactNode, useState } from 'react';
import { Rate } from 'antd-mobile';
import { DownOutline, UpOutline } from 'antd-mobile-icons';
import clsx from 'clsx';

type IProps = CssProps & {
  type: 'shop' | 'group';
  title: string | ReactNode;
  indexArea: ReactNode;
  header: string | ReactNode;
  titleText?: string; // 当 title 为 ReactNode 时，消息通知取这个字段
  percent: {
    label: string;
    value: string;
  };
  star?: number;
  columns?: {
    label: string;
    value: string;
  }[];
  manager: {
    name: string;
    phone?: string;
    id?: number;
  };
  navNext?: () => void;
  navDetail?: () => void;
  innerNode?: React.ReactNode;
  noStar?: boolean;
  hasDetail?: boolean;
};

const StarCustomerInfo: React.FC<IProps> = ({
  className,
  title,
  header,
  titleText,
  star,
  indexArea,
  columns,
  manager,
  type,
  navNext,
  navDetail,
  innerNode,
  noStar,
  percent,
  hasDetail = true,
}) => {
  const [show, setShow] = useState<boolean>(false);

  const handleClick = () => {
    if (type === 'shop') {
      navDetail && navDetail();
    } else {
      navNext && navNext();
    }
  };

  return (
    <div className={clsx('bg-white mb-2 leading-none', className)}>
      <div onClick={handleClick}>
        <div className="flex justify-between px-4 py-4">
          <div>
            <div className="text-sm leading-[14px] mb-3">{title}</div>
            <div className="flex items-end gap-1">
              {indexArea}
              <div className="text-xs leading-none text-[#B8B8B8]">{header}</div>
            </div>
          </div>
          <div className="text-right flex flex-col justify-between">
            <div></div>
            {!noStar && <Rate allowHalf readOnly value={star} style={{ '--star-size': '19px' }} />}
            {hasDetail && (
              <div
                className="flex items-center justify-end text-xs mt-3 text-grey"
                onClick={(e) => {
                  e.stopPropagation();
                  setShow((flag) => !flag);
                }}
              >
                <div>{show ? '收起' : '展开'}</div>
                {show ? (
                  <UpOutline className="text-xs ml-1" />
                ) : (
                  <DownOutline className="text-xs ml-1" />
                )}
              </div>
            )}
          </div>
        </div>
        {show && (
          <>
            <div className="flex flex-wrap">
              {(columns || []).map((item, index) => (
                <div
                  className={`w-1/2 pt-4 pb-2 pl-4 text-xs border-t border-solid border-line ${
                    index % 2 ? 'border-l' : ''
                  }`}
                >
                  <div className="mb-2 text-light">{item.label}</div>
                  <div className="text-grey">{item.value}</div>
                </div>
              ))}
            </div>
            {innerNode}
          </>
        )}
      </div>

      <div className="flex justify-between items-center px-4 py-3 text-xs border-t border-solid border-line">
        <div>
          <span className="text-light">{type === 'group' ? '负责人' : '店长'}：</span>
          <span className="text-grey">{manager.name}</span>
        </div>
        <Contact
          type="card"
          manager={manager}
          percent={percent}
          title={typeof title === 'string' ? title : titleText || ''}
        />
      </div>
    </div>
  );
};

export default StarCustomerInfo;
