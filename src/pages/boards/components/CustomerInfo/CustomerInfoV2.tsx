import { RightOutline } from 'antd-mobile-icons';
import { CssProps } from '@src/common/api.type';
import Contact from '../Contact';
import { ReactNode } from 'react';
import styles from './index.module.scss';
import clsx from 'clsx';

type IProps = CssProps & {
  type: 'shop' | 'group';
  title: string | ReactNode;
  titleText?: string; // 当 title 为 ReactNode 时，消息通知取这个字段
  percent: {
    label: string;
    value: string;
  };
  columns: {
    label: string;
    value: string;
  }[];
  manager?: {
    name: string;
    phone?: string;
    id?: number;
  };
  navNext?: () => void;
  navDetail?: () => void;
  hiddenFooter?: boolean; //详情页可以用
  isWarning?: boolean;
};

const CustomerInfoV2: React.FC<IProps> = ({
  className,
  title,
  titleText,
  percent,
  columns,
  manager,
  type,
  hiddenFooter,
  navNext,
  navDetail,
  isWarning,
}) => {
  const handleClick = () => {
    if (type === 'shop') {
      navDetail && navDetail();
    } else {
      navNext && navNext();
    }
  };
  const hasArrow = (type === 'shop' && navDetail) || (type === 'group' && navNext);

  return (
    <div className={clsx(styles.customerInfoV2, className, 'leading-none')}>
      <div className="flex justify-between items-center py-3" onClick={handleClick}>
        <div className="text-sm flex items-center">
          {title}
          {isWarning && (
            <div
              className="leading-[15px] text-[13px] px-1 rounded-[2px] ml-1"
              style={{ color: '#E75454', border: '1px solid #E75454' }}
            >
              异常
            </div>
          )}
        </div>
        {hasArrow && (
          <div className="flex items-center text-light text-xs">
            <span className="mr-1">查看详情</span>
            <RightOutline />
          </div>
        )}
      </div>
      <div
        className={`flex items-stretch rounded-base text-center mb-3 ${styles.content}`}
        onClick={handleClick}
      >
        <div className={`${styles.percent} text-white rounded-l-base py-3`}>
          <div className="text-xs mb-2">{percent?.label}</div>
          <div className="text-sm font-semibold">{percent.value ?? '-'}</div>
        </div>
        {columns.map((item, index) => (
          <div
            key={index}
            className={`text-light text-xs flex-1 py-3 flex flex-col grow justify-between`}
          >
            <div>{item.label}</div>
            <div className="text-grey mt-2">{item.value ?? '-'}</div>
          </div>
        ))}
      </div>
      {!hiddenFooter && (
        <div className="flex justify-between items-center pb-3 text-xs">
          <div>
            <span className="text-light">{type === 'group' ? '负责人' : '店长'}：</span>
            <span className="text-grey">{manager?.name}</span>
          </div>
          <Contact
            type="card"
            manager={manager!}
            percent={percent}
            title={typeof title === 'string' ? title : titleText || ''}
          />
        </div>
      )}
    </div>
  );
};

export default CustomerInfoV2;
