import { CssProps } from '@src/common/api.type';
import Contact from '../Contact';
import { ReactNode } from 'react';
import { RightOutline } from 'antd-mobile-icons';
import clsx from 'clsx';
import { formatPer } from '@src/utils/helper';

type IProps = CssProps & {
  type: 'shop' | 'group';
  title: string | ReactNode;
  titleText?: string; // 当 title 为 ReactNode 时，消息通知取这个字段
  badReplyNum: number;
  manager: {
    name: string;
    phone?: string;
    id?: number;
  };
  navNext?: () => void;
  navDetail?: () => void;
};

const CustomerInfo: React.FC<IProps> = ({
  className,
  title,
  titleText,
  badReplyNum,
  manager,
  type,
  navNext,
  navDetail,
}) => {
  const handleClick = () => {
    if (type === 'shop') {
      navDetail && navDetail();
    } else {
      navNext && navNext();
    }
  };
  const hasArrow = (type === 'shop' && navDetail) || (type === 'group' && navNext);

  return (
    <div className={clsx('leading-none', className)}>
      <div className="flex justify-between items-center py-3" onClick={handleClick}>
        <div className="text-sm">{title}</div>
        {hasArrow && (
          <div className="flex items-center text-light text-xs">
            <span className="mr-1">查看详情</span>
            <RightOutline />
          </div>
        )}
      </div>
      <div
        className="flex items-center justify-between rounded-base text-light text-xs mb-3 p-4"
        style={{ backgroundColor: '#fafafa' }}
      >
        <div>小程序差评数</div>
        <div className="text-base text-grey">{badReplyNum}</div>
      </div>

      <div className="flex justify-between items-center pb-3 text-xs">
        <div>
          <span className="text-grey">{type === 'group' ? '负责人' : '店长'}：</span>
          <span className="font-semibold">{manager.name}</span>
        </div>
        <Contact
          type="card"
          manager={manager}
          percent={{ label: '小程序差评数', value: formatPer(badReplyNum, '') }}
          title={typeof title === 'string' ? title : titleText || ''}
        />
      </div>
    </div>
  );
};

export default CustomerInfo;
