import { RightOutline } from 'antd-mobile-icons';
import { CssProps } from '@src/common/api.type';
import Contact from '../Contact';
import { ReactNode } from 'react';
import clsx from 'clsx';

type IProps = CssProps & {
  type: 'shop' | 'group';
  title: string | ReactNode;
  titleText?: string; // 当 title 为 ReactNode 时，消息通知取这个字段
  percent: {
    label: string;
    value: string;
  };
  columns: {
    label: string;
    value: string;
  }[];
  manager: {
    name: string;
    phone?: string;
    id?: number;
  };
  navNext?: () => void;
  navDetail?: () => void;
  hiddenFooter?: boolean; //详情页可以用
};

const CustomerInfo: React.FC<IProps> = ({
  className,
  title,
  titleText,
  percent,
  columns,
  manager,
  type,
  hiddenFooter,
  navNext,
  navDetail,
}) => {
  const handleClick = () => {
    if (type === 'shop') {
      navDetail && navDetail();
    } else {
      navNext && navNext();
    }
  };
  const hasArrow = (type === 'shop' && navDetail) || (type === 'group' && navNext);

  return (
    <div
      className={clsx('py-3 px-1', className)}
      style={hiddenFooter ? {} : { borderBottom: '1px solid #eeee' }}
    >
      <div className="flex justify-between items-center" onClick={handleClick}>
        <div className="font-semibold">{title}</div>
        {hasArrow && <RightOutline />}
      </div>
      <div className="flex justify-between my-3 items-center" onClick={handleClick}>
        <div>
          <div className="text-grey text-xs mb-1">{percent?.label}</div>
          <div className="text-base font-semibold">{percent.value}</div>
        </div>
        <div className="text-right">
          {columns.map((item, index) => (
            <div key={index} className="mb-1 text-xs">
              <span className="text-grey">{item.label}：</span>
              <span>{item.value}</span>
            </div>
          ))}
        </div>
      </div>
      {!hiddenFooter && (
        <div className="flex justify-between items-center">
          <div>
            <span className="text-grey">{type === 'group' ? '负责人' : '店长'}：</span>
            <span className="font-semibold">{manager.name}</span>
          </div>
          <Contact
            type="card"
            manager={manager}
            percent={percent}
            title={typeof title === 'string' ? title : titleText || ''}
          />
        </div>
      )}
    </div>
  );
};

export default CustomerInfo;
