interface VerticalProps {}

export const VerticalShimmer = ({}: VerticalProps) => {
  return (
    <div className="h-[207px] shrink-0 p-4 mb-2 bg-white flex flex-col gap-y-5">
      <div className="shimmer w-full h-[22px] rounded-lg"></div>
      <div className="flex flex-col justify-between grow">
        <div className="shimmer-chart shrink-0 flex justify-center items-end w-[115px] h-[57.5px] rounded-tl-[57.5px] rounded-tr-[57.5px] border-[12px] border-b-0 ">
          <div className="shimmer w-1/2 h-4 rounded-lg"></div>
        </div>
        <div className="h-full flex flex-col justify-between mt-[10px]">
          <div className="shimmer h-4 w-32 rounded-lg"></div>
          <div className="shimmer h-3 w-8 rounded-lg"></div>
          <div className="shimmer h-3 w-8 rounded-lg"></div>
        </div>
      </div>
    </div>
  );
};
