import { post } from '@src/api';
import { OverviewParams } from './api.type';

//总览(废弃)
export const getOverView = (data: OverviewParams) =>
  post('/om-api/common/inspect/dashboard/overall/indicators', { data });

// S项指标数据
export const getRedLine = (data: OverviewParams) =>
  post('/om-api/common/inspect/dashboard/query/redLine', { data });
// 门店稽核通过率
export const getPass = (data: OverviewParams) =>
  post('/om-api/common/inspect/dashboard/query/pass', { data });
// 门店稽核通过率二级
export const getPassSub = (data: OverviewParams) =>
  post('/om-api/common/inspect/dashboard/query/secondary/pass', { data });
// 门店稽核整改完成率
export const getRectify = (data: OverviewParams) =>
  post('/om-api/common/inspect/dashboard/query/rectify', { data });
// 门店稽核整改完成率二级
export const getRectifySub = (data: OverviewParams) =>
  post('/om-api/common/inspect/dashboard/query/secondary/rectify', { data });
// 自检任务合格率
export const getSelfPass = (data: OverviewParams) =>
  post('/om-api/common/inspect/dashboard/query/selfPass', { data });
// 自检任务完成率
export const getUnComplete = (data: OverviewParams) =>
  post('/om-api/common/inspect/dashboard/query/unComplete', { data });

//督导完成率
export const getSupervision = (data: OverviewParams) =>
  post('/om-api/common/inspect/dashboard/query/supervision', { data });

//获取列表数据
export const getList = (data: OverviewParams, type: string, Dimension: string) =>
  post(`/om-api/common/inspect/dashboard/${type}/listBy${Dimension}`, { data });
//获取详情数据
export const getDetail = (data: OverviewParams, type: string) =>
  post(`/om-api/common/inspect/dashboard/${type}/report/list`, { data });
//获取诊断状态数据
export const getAlarm = (data: OverviewParams) =>
  post('/om-api/common/inspect/dashboard/alarm/status', { data });
//督导明细列表(废弃)
export const getSupervisionList = (data: OverviewParams) =>
  post('/om-api/common/inspect/dashboard/query/supervision/list', { data });
