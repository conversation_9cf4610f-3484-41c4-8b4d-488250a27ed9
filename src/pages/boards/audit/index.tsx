import { useMemo, useState } from 'react';
import AuthorityEnum from '@src/common/authority';
import { usePageContext } from '@src/common/page-context';
import CustomerFilter from '@src/components/CustomerFilter';
import DateFilter from '@src/components/DateFilter';
import { IconFont } from '@src/components/IconFont';
import Loading from '@src/components/Loading';
import { userStore } from '@src/store';
import { commonSorter, formatPer, getDatePercentType } from '@src/utils/helper';
import { roleTypeIsManage } from '@src/utils/tokenUtils';
import { useRequest } from 'ahooks';
import { Card, SearchBar, Tabs } from 'antd-mobile';
import dayjs from 'dayjs';
import { cloneDeep } from 'lodash';
import { observer } from 'mobx-react';
import qs from 'qs';
import { useNavigate, useSearchParams } from 'react-router-dom';
import {
  getPass,
  getRectify,
  getRedLine,
  getSelfPass,
  getUnComplete,
} from './api';
import { OverviewParams } from './api.type';
import DetailIndicator from './components/DetailIndicator';
import DetailList from './components/DetailList';
import {
  AuditTypeEnum,
  getTypeOptions,
  RoleClassificationEnum,
  SelfWorkSheetEnum,
  SupervisionEnum,
  ToolTipMap,
  typeObj,
} from './enum';
import { SShimmer } from './Shimmer/SShimmer';
import { checkReport, getReport, updateReadFlag } from '../api';
import {
  CustomerType,
  ExceptionReport,
  IndicatorCard,
  INoticeBar,
  Sorter,
} from '../components';
import styles from '../index.module.scss';

const defaultParams = {
  beginDate: dayjs().format('YYYY-MM-DD'),
  endDate: dayjs().format('YYYY-MM-DD'),
  groupId: '',
  shopCodes: [],
};

const AuditBoard = observer(() => {
  const { pid, getGroupTree } = usePageContext();
  const { permissionsMap } = userStore;
  const disabledShop = !permissionsMap.has(AuthorityEnum.AuditShopBtn);
  const disabledGroup = !permissionsMap.has(AuthorityEnum.AuditGroupBtn);
  const [params, setParams] = useState<OverviewParams>({
    ...defaultParams,
    groupId: pid,
  });
  const [month, setMonth] = useState<string>(dayjs().format('YYYY-MM'));
  const [dataType, setDataType] = useState<'shop' | 'group'>(
    disabledShop ? 'group' : 'shop',
  );
  const [searchValue, setSearchValue] = useState<string>('');
  const [sorter, setSorter] = useState<string[]>([]);

  const [search] = useSearchParams();
  const hiddenDetail = search.get('pageType') !== 'detail'; // 隐藏详情
  const hiddenHome = search.get('pageType') === 'detail'; // 隐藏统计页

  const navigator = useNavigate();

  const { data: showReport, run: showReportRun } = useRequest(
    async () => {
      const res = await checkReport({ reportType: 1 });
      return res;
    },
    {
      ready: roleTypeIsManage(),
    },
  );
  const { data: reportData, run } = useRequest(
    async () => {
      const res = await getReport({ reportType: 1 });

      return res || {};
    },
    {
      ready: roleTypeIsManage(),
    },
  );

  const { run: updateReadRun } = useRequest(
    async (reportId: number) => {
      if (!reportId) throw null;
      const res = await updateReadFlag(reportId);
      return res;
    },
    {
      manual: false,
      onSuccess: () => {
        showReportRun();
      },
    },
  );

  const [alarmData, setAlarmData] = useState<Record<string, any>>({});
  // const { data: alarmData } = useRequest(async () => getAlarm(params), { refreshDeps: [params] });

  const showAlarmData = useMemo(() => {
    const alarmConfig = [
      { text: '“S项”门店占比存在异常', key: 'redLineShopRatioAlarm' },
      { text: '门店整改完成率存在异常', key: 'shopRectifyRatioAlarm' },
      { text: '门店通过率存在异常', key: 'patrolShopPassRatioAlarm' },
      { text: '自检完成率存在异常', key: 'selfShopCompleteRatioAlarm' },
      { text: '自检合格率存在异常', key: 'selfShopPassRatioAlarm' },
    ];

    const alarmList = alarmConfig.filter((v) => alarmData?.[v.key]);
    return {
      status: alarmList.length ? 'danger' : 'success',
      text: alarmList.map((v) => v.text).join('，'),
    };
  }, [alarmData]);

  // const { data } = useRequest(async () => getOverView(params), { refreshDeps: [params] });

  const { data: redLine, loading: redLineLoading } = useRequest(
    async () => {
      if (!permissionsMap.has(AuthorityEnum.红线门店占比)) throw null;
      const res = await getRedLine(params);
      return res;
    },
    {
      refreshDeps: [params],
      onSuccess: ({ statusAlarm }) => {
        setAlarmData((pre) => ({ ...pre, redLineShopRatioAlarm: statusAlarm }));
      },
    },
  );

  const { data: pass, loading: passLoading } = useRequest(
    async () => {
      if (!permissionsMap.has(AuthorityEnum.门店通过率)) throw null;
      const res = await getPass(params);
      return res;
    },
    {
      refreshDeps: [params],
      onSuccess: ({ statusAlarm }) => {
        setAlarmData((pre) => ({
          ...pre,
          patrolShopPassRatioAlarm: statusAlarm,
        }));
      },
    },
  );
  // const { data: passSub } = useRequest(
  //   async () => {
  //     if (hiddenDetail) throw null;
  //     const res = await getPassSub(params);
  //     return res;
  //   },
  //   {
  //     refreshDeps: [params, hiddenDetail],
  //   },
  // );
  const { data: rectify, loading: rectifyLoading } = useRequest(
    async () => {
      if (!permissionsMap.has(AuthorityEnum.门店整改完成率)) throw null;
      const res = await getRectify(params);
      return res;
    },
    {
      refreshDeps: [params],
      onSuccess: ({ statusAlarm }) => {
        setAlarmData((pre) => ({ ...pre, shopRectifyRatioAlarm: statusAlarm }));
      },
    },
  );
  // const { data: rectifySub } = useRequest(
  //   async () => {
  //     if (hiddenDetail) throw null;
  //     const res = await getRectifySub(params);
  //     return res;
  //   },
  //   {
  //     refreshDeps: [params, hiddenDetail],
  //   },
  // );
  const { data: selfPass, loading: selfPassLoading } = useRequest(
    async () => {
      if (!permissionsMap.has(AuthorityEnum.自检合格率)) throw null;
      const res = await getSelfPass(params);
      return res;
    },
    {
      refreshDeps: [params],
      onSuccess: ({ statusAlarm }) => {
        setAlarmData((pre) => ({
          ...pre,
          selfShopPassRatioAlarm: statusAlarm,
        }));
      },
    },
  );
  const { data: unComplete, loading: unCompleteLoading } = useRequest(
    async () => {
      if (!permissionsMap.has(AuthorityEnum.自检完成率)) throw null;
      const res = await getUnComplete(params);
      return res;
    },
    {
      refreshDeps: [params],
      onSuccess: ({ statusAlarm }) => {
        setAlarmData((pre) => ({
          ...pre,
          selfShopCompleteRatioAlarm: statusAlarm,
        }));
      },
    },
  );
  // const { data: supervision, loading: supervisionLoading } = useRequest(
  //   async () => {
  //     if (!permissionsMap.has(AuthorityEnum.督导巡检完成率)) throw null;
  //     const res = await getSupervision({
  //       ...params,
  //       beginDate: dayjs(month).startOf('M').format('YYYY-MM-DD'),
  //       endDate:
  //         dayjs(month).format('YYYY-MM') === dayjs().format('YYYY-MM')
  //           ? dayjs().format('YYYY-MM-DD')
  //           : dayjs(month).endOf('M').format('YYYY-MM-DD'),
  //     });
  //     return res;
  //   },
  //   {
  //     refreshDeps: [params, month],
  //   },
  // );

  // const data = {
  //   redLineShopCount: redLine?.redLineShopCount,
  //   unPatrolPassShopCount: pass?.unPatrolPassShopCount,
  //   unRectifyShopCount: rectify?.unRectifyShopCount,
  //   unSelfShopPassCount: selfPass?.unSelfShopPassCount,
  //   unSelfCompleteShopCount: unComplete?.unSelfCompleteShopCount,
  // };

  const typeOptions = getTypeOptions().filter((v) =>
    permissionsMap.has(v.auth),
  );
  const [detailType, setDetailType] = useState<AuditTypeEnum[]>(
    typeOptions.length ? [typeOptions[0].value] : [AuditTypeEnum.触碰红线],
  );
  const [detailSubType, setDetailSubType] = useState<
    RoleClassificationEnum | 'all' | SelfWorkSheetEnum | SupervisionEnum
  >('all');

  const { data: list, loading } = useRequest(
    async () => {
      if (!typeOptions.length) throw null;
      if (hiddenDetail) throw null;
      // 有选门店，督导巡检完成率列表不请求接口，直接置为空
      if (
        AuditTypeEnum.督导巡检完成率 === detailType[0] &&
        params.shopCodes &&
        params.shopCodes.length
      ) {
        return [];
      }
      const subParams = [
        AuditTypeEnum.触碰红线,
        AuditTypeEnum.未通过巡检,
        AuditTypeEnum.未完成整改,
      ].includes(detailType[0])
        ? {
            roleClassificationEnum:
              detailSubType === 'all'
                ? undefined
                : (detailSubType as RoleClassificationEnum),
          }
        : [AuditTypeEnum.自检不合格, AuditTypeEnum.未完成自检].includes(
              detailType[0],
            )
          ? {
              selfWorkSheet:
                detailSubType === 'all'
                  ? undefined
                  : (detailSubType as SelfWorkSheetEnum),
            }
          : {
              supervisionEnum:
                detailSubType === 'all'
                  ? undefined
                  : (detailSubType as SupervisionEnum),
            };
      let res = [];
      res = await typeObj[detailType[0]].getList[dataType](
        {
          ...params,
          ...subParams,
        },
        { month },
      ).catch(() => {
        res = [];
      });
      return res;
    },
    {
      refreshDeps: [
        params,
        dataType,
        detailType,
        detailSubType,
        hiddenDetail,
        month,
      ],
    },
  );

  const ListData = useMemo(() => {
    let arr = list;
    if (searchValue) {
      arr = list?.filter(
        (v: any) =>
          v?.name?.includes(searchValue) ||
          v?.shopName?.includes(searchValue) ||
          v?.userName?.includes(searchValue),
      );
    }
    if (sorter && sorter[0]) {
      const [key, order] = sorter[0].split('-');
      arr = cloneDeep(arr).sort((a: any, b: any) =>
        commonSorter(a[key], b[key], order),
      );
    }
    return arr;
  }, [list, searchValue, sorter]);

  const showGroupNames = useMemo(() => {
    if (!params.groupId) return dataType === 'group' ? '全部组织' : '全部门店';
    const { list } = getGroupTree(+params.groupId);
    if (!list || !list.length) return <></>;
    const curNode = list[list.length - 1];
    const breadcrumbList = list.slice(0, list.length - 1);
    return (
      <>
        {breadcrumbList.map((v: { name: string; id: number }) => (
          <>
            <span
              key={v.id}
              className="text-primary"
              onClick={() => {
                setParams((p) => ({ ...p, groupId: String(v.id) }));
              }}
            >
              {v.name}
            </span>
            <IconFont type="icon-chevron-right" className="mx-1" />
          </>
        ))}
        <span>{curNode.name}</span>
      </>
    );
  }, [params.groupId, dataType]);

  const showPercentDateType = useMemo(() => {
    return {
      same:
        getDatePercentType(params.beginDate, params.endDate) === 'day'
          ? 'day'
          : 'default',
      circle: 'default',
    } as {
      same: 'day' | 'month' | 'default';
      circle: 'day' | 'month' | 'default';
    };
  }, [params.beginDate, params.endDate]);

  const handleTabChange = (key: AuditTypeEnum) => {
    setSorter([]);
    setDetailType([key]);
    setDetailSubType('all');
  };

  const redLineIndicatorCard = useMemo(
    () => (
      <>
        {!redLineLoading ? (
          <IndicatorCard
            className="mb-2"
            title="稽核“S项”占比"
            indicator={formatPer(redLine?.touchRedLineRatio)}
            tooltip={ToolTipMap[AuditTypeEnum.触碰红线]}
            percent={{
              same: formatPer(redLine?.touchRedLineOverRatio),
              circle: formatPer(redLine?.touchRedLineRatioRatio),
            }}
            type="full"
            percentDateType={showPercentDateType}
            isWarning={!!redLine?.statusAlarm}
            count={{
              label: '触碰“S项”条数',
              value: redLine?.touchRedLineItemCount,
            }}
            handleClick={() => {
              handleTabChange(AuditTypeEnum.触碰红线);
              !!showReport && updateReadRun(showReport);
            }}
            hasReport={!!showReport}
          />
        ) : (
          <SShimmer />
        )}
      </>
    ),
    [redLineLoading],
  );
  const passIndicatorCard = useMemo(
    () => (
      <>
        {!passLoading ? (
          <IndicatorCard
            className="mb-2"
            title="门店稽核通过率"
            indicator={formatPer(pass?.shopPassRatio)}
            tooltip={ToolTipMap[AuditTypeEnum.未通过巡检]}
            count={{
              label: '稽核通过报告数',
              value: pass?.patrolPassCount,
            }}
            isWarning={!!pass?.statusAlarm}
            percent={{
              same: formatPer(pass?.shopPassOverRatio),
              circle: formatPer(pass?.shopPassRatioRatio),
            }}
            type="full"
            percentDateType={showPercentDateType}
            handleClick={() => handleTabChange(AuditTypeEnum.未通过巡检)}
          />
        ) : (
          <SShimmer />
        )}
      </>
    ),
    [passLoading],
  );
  const rectifyIndicatorCard = useMemo(
    () => (
      <>
        {!rectifyLoading ? (
          <IndicatorCard
            className="mb-2"
            title="门店稽核整改完成率"
            indicator={formatPer(rectify?.rectifyRatio)}
            tooltip={ToolTipMap[AuditTypeEnum.未完成整改]}
            count={{
              label: '已完成整改的报告数',
              value: rectify?.rectifyCount,
            }}
            isWarning={!!rectify?.statusAlarm}
            percent={{
              same: formatPer(rectify?.rectifyOverRatio),
              circle: formatPer(rectify?.rectifyRatioRatio),
            }}
            type="full"
            percentDateType={showPercentDateType}
            handleClick={() => handleTabChange(AuditTypeEnum.未完成整改)}
          />
        ) : (
          <SShimmer />
        )}
      </>
    ),
    [rectifyLoading],
  );
  const selfPassIndicatorCard = useMemo(
    () => (
      <>
        {!selfPassLoading ? (
          <IndicatorCard
            className="mb-2"
            title="自检任务合格率"
            indicator={formatPer(selfPass?.selfPassRatio)}
            percent={{
              same: formatPer(selfPass?.selfPassOverRatio),
              circle: formatPer(selfPass?.selfPassRatioRatio),
            }}
            type="full"
            percentDateType={showPercentDateType}
            tooltip={ToolTipMap[AuditTypeEnum.自检不合格]}
            isWarning={!!selfPass?.statusAlarm}
            count={{
              label: '自检任务合格报告数',
              value: selfPass?.selfPassCount,
            }}
            handleClick={() => handleTabChange(AuditTypeEnum.自检不合格)}
          />
        ) : (
          <SShimmer />
        )}
      </>
    ),
    [selfPassLoading],
  );
  const unCompleteIndicatorCard = useMemo(
    () => (
      <>
        {!unCompleteLoading ? (
          <IndicatorCard
            className="mb-2"
            title="自检任务完成率"
            indicator={formatPer(unComplete?.selfCompleteRatio)}
            percent={{
              same: formatPer(unComplete?.selfCompleteOverRatio),
              circle: formatPer(unComplete?.selfCompleteRatioRatio),
            }}
            type="full"
            percentDateType={showPercentDateType}
            tooltip={ToolTipMap[AuditTypeEnum.未完成自检]}
            isWarning={!!unComplete?.statusAlarm}
            count={{
              label: '已提交自检任务报告数',
              value: unComplete?.selfCompleteCount,
            }}
            handleClick={() => handleTabChange(AuditTypeEnum.未完成自检)}
          />
        ) : (
          <SShimmer />
        )}
      </>
    ),
    [unCompleteLoading],
  );
  // const supervisionIndicatorCard = useMemo(
  //   () => (
  //     <>
  //       {!supervisionLoading ? (
  //         <IndicatorCard
  //           className="mb-2"
  //           title="督导巡店完成率"
  //           indicator={formatPer(supervision?.shopFinishRatio)}
  //           tooltip={ToolTipMap[AuditTypeEnum.督导巡检完成率]}
  //           percent={{
  //             same: formatPer(supervision?.shopFinishOverRatio),
  //             circle: formatPer(supervision?.shopFinishRatioRatio),
  //           }}
  //           type="full"
  //           // isWarning={!!alarmData?.redLineShopRatioAlarm}
  //           count={{
  //             label: '督导已巡检门店数',
  //             value: supervision?.shopCountFinish,
  //           }}
  //           handleClick={() => {
  //             handleTabChange(AuditTypeEnum.督导巡检完成率);
  //           }}
  //         />
  //       ) : (
  //         <VerticalShimmer />
  //       )}
  //     </>
  //   ),
  //   [supervisionLoading],
  // );

  return (
    <div>
      <Loading spinning={false}>
        <div hidden={hiddenDetail} className="bg-white">
          <div className="flex items-center justify-between pr-4 pl-1 border-solid border-b border-line">
            <Tabs
              className={styles.iAdmTabs1}
              activeKey={detailType[0]}
              onChange={(key) => {
                handleTabChange(key as AuditTypeEnum);
              }}
              stretch={false}
              style={{
                '--title-font-size': '0.875rem',
              }}
            >
              {typeOptions.map(({ value, label }) => (
                <Tabs.Tab title={label} key={value} />
              ))}
            </Tabs>
            {/* <DataTypeSelect
              options={typeOptions}
              defaultValue={detailType[0]}
              onChange={(key) => {
                setSorter([]);
                setDetailType([key as AuditTypeEnum]);
              }}
            /> */}
          </div>
        </div>
        <Card
          className="rounded-none px-4 border-b border-solid border-line border-px"
          bodyClassName="!py-2"
        >
          <div className="flex justify-between items-start">
            <CustomerFilter
              className="flex-1"
              value={{ groupId: params.groupId, shopCodes: params.shopCodes }}
              noBg={true}
              onChange={(e: any) => {
                setParams((p) => ({
                  ...p,
                  shopCodes: e.shopCodes,
                  groupId: e.groupId,
                }));
              }}
            />
            {hiddenHome && AuditTypeEnum.督导巡检完成率 === detailType[0] && (
              <DateFilter
                key="supervision"
                type="month"
                value={[dayjs(month).toDate(), dayjs(month).toDate()]}
                onlyMonth
                noBg={true}
                onChange={(e) => {
                  setMonth(() => dayjs(e[0]).format('YYYY-MM'));
                }}
                isSameYear
              />
            )}
            {((hiddenHome && AuditTypeEnum.督导巡检完成率 !== detailType[0]) ||
              hiddenDetail) && (
              <DateFilter
                noBg={true}
                value={[
                  dayjs(params.beginDate).toDate(),
                  dayjs(params.endDate).toDate(),
                ]}
                onChange={(e) => {
                  setParams((p) => ({ ...p, beginDate: e[0], endDate: e[1] }));
                }}
                isSameYear
              />
            )}
          </div>
        </Card>
        <div hidden={hiddenHome}>
          {permissionsMap.has(AuthorityEnum.红线门店占比) &&
            redLineIndicatorCard}
          {permissionsMap.has(AuthorityEnum.门店通过率) && passIndicatorCard}
          {permissionsMap.has(AuthorityEnum.门店整改完成率) &&
            rectifyIndicatorCard}
          {permissionsMap.has(AuthorityEnum.自检合格率) &&
            selfPassIndicatorCard}
          {permissionsMap.has(AuthorityEnum.自检完成率) &&
            unCompleteIndicatorCard}
          {/* <div className="flex mb-2 bg-white">
            <div className="flex-1 border-r border-line border-solid">
              {permissionsMap.has(AuthorityEnum.门店通过率) && passIndicatorCard}
            </div>
            <div className="flex-1">
              {permissionsMap.has(AuthorityEnum.门店整改完成率) && rectifyIndicatorCard}
            </div>
          </div>
          <div className="flex mb-2 bg-white">
            <div className="flex-1 border-r border-line border-solid">
              {permissionsMap.has(AuthorityEnum.自检合格率) && selfPassIndicatorCard}
            </div>
            <div className="flex-1">
              {permissionsMap.has(AuthorityEnum.自检完成率) && unCompleteIndicatorCard}
            </div>
          </div> */}
          {/* {permissionsMap.has(AuthorityEnum.督导巡检完成率) && (
            <Card
              className="rounded-none px-4 border-b border-solid border-line border-px"
              bodyClassName="!py-2"
            >
              <div className="flex items-center justify-between">
                <div className="text-B8 text-[14px]">时间筛选</div>
                <DateFilter
                  key="supervision"
                  type="month"
                  noBg={true}
                  value={[dayjs(month).toDate(), dayjs(month).toDate()]}
                  onlyMonth
                  onChange={(e) => {
                    setMonth(() => dayjs(e[0]).format('YYYY-MM'));
                  }}
                  isSameYear
                />
              </div>
            </Card>
          )}
          {permissionsMap.has(AuthorityEnum.督导巡检完成率) && supervisionIndicatorCard} */}
        </div>
        <div hidden={hiddenHome}>
          <INoticeBar
            status={showAlarmData.status}
            content={`当前管辖范围内，${
              showAlarmData.status === 'success'
                ? '无异常指标'
                : showAlarmData.text
            }`}
          />
        </div>
        {/* 诊断报告(店长角色默认不展示诊断报告) */}
        <div
          hidden={
            hiddenDetail ||
            AuditTypeEnum.触碰红线 !== detailType[0] ||
            !roleTypeIsManage()
          }
          className="px-2 pt-2"
        >
          <ExceptionReport
            className="text-grey"
            hasReport={!!reportData?.reportId}
            title="触碰“S项”"
            shopNum={reportData?.businessFieldObject?.count}
            time={reportData?.reportTime || ''}
            content={
              reportData?.anomalyStatus
                ? `您管辖范围下的门店，${
                    reportData?.businessFieldObject?.count ?? '-'
                  }家门店触碰“S项”，${(
                    reportData?.businessFieldObject?.topRedLineList || []
                  ).join(
                    '、',
                  )}${reportData?.businessFieldObject?.topRedLineList?.length}项出现次数最多`
                : '太棒了，您管辖范围下的门店没有出现触碰“S项”门店'
            }
            type="page"
            footerClick={() => {
              navigator(
                `/boards/exception/report${qs.stringify(
                  {
                    type: 'audit',
                    startCreateTime: params.beginDate,
                    endCreateTime: params.endDate,
                  },
                  { addQueryPrefix: true },
                )}`,
              );
            }}
            onDetailClick={() => {
              navigator(
                `/boards/quick_creation${qs.stringify(
                  {
                    type: 'audit',
                    reportId: reportData?.reportId,
                  },
                  { addQueryPrefix: true },
                )}`,
              );
            }}
            hiddenDetialEntry={!!!reportData?.anomalyStatus}
          />
        </div>
        <div
          hidden={hiddenDetail}
          className={`my-[10px] mx-2 ${styles.IndicatorCardSubWrap}`}
        >
          <DetailIndicator
            params={params}
            hiddenDetail={hiddenDetail}
            type={detailType[0]}
            redLine={redLine}
            pass={pass}
            rectify={rectify}
            selfPass={selfPass}
            unComplete={unComplete}
            // supervision={supervision}
            showPercentDateType={showPercentDateType}
          />
        </div>
        <div
          hidden={hiddenDetail}
          className="pb-2 sticky top-0 z-50"
          style={{ background: '#F5F5F5' }}
        >
          <Card
            className="rounded-none"
            style={{ padding: 0 }}
            bodyClassName="!py-0"
          >
            <div className="flex items-center justify-between px-4 py-2">
              <div className="text-base font-semibold">明细数据</div>
              {detailType[0] !== AuditTypeEnum.督导巡检完成率 && (
                <CustomerType
                  disabledGroup={disabledGroup}
                  disabledShop={disabledShop}
                  value={dataType}
                  onChange={(e) => setDataType(e as 'shop' | 'group')}
                />
              )}
            </div>
            <div className="flex items-center px-4 pb-1">
              <SearchBar
                className={`${styles.iAdmSearchBar} flex-1 mr-2`}
                style={{ '--border-radius': '8px', '--height': '2.5rem' }}
                onClear={() => setSearchValue('')}
                onSearch={setSearchValue}
                placeholder={
                  detailType[0] !== AuditTypeEnum.督导巡检完成率
                    ? '请输入门店/组织名称'
                    : '请输入督导姓名'
                }
              />
              <Sorter
                config={typeObj[detailType[0]].sortConfig[dataType]}
                value={sorter}
                onChange={setSorter}
              />
            </div>
            <div className="flex items-center justify-between pr-4 pl-1 border-solid border-b border-line">
              <Tabs
                className={styles.iAdmTabs1}
                activeKey={detailSubType}
                onChange={(key) => {
                  setDetailSubType(
                    key as RoleClassificationEnum | 'all' | SelfWorkSheetEnum,
                  );
                }}
                stretch={false}
                style={{
                  '--title-font-size': '0.875rem',
                }}
              >
                {typeObj[detailType[0]].options
                  .filter((v) => !v.auth || permissionsMap.has(v.auth))
                  .map(({ value, label }) => (
                    <Tabs.Tab title={label} key={value} />
                  ))}
              </Tabs>
            </div>
            {/* {detailType[0] === AuditTypeEnum.未通过巡检 && dataType === 'shop' && (
              <div className="py-2 px-4 border-b border-solid border-line">
                <Selector
                  key="patrol"
                  style={{
                    '--border-radius': '4px',
                    '--color': 'rgba(249,250,251)',
                    '--text-color': '#58595B',
                    '--checked-color': '#e1faf5',
                    '--padding': '4px',
                  }}
                  columns={4}
                  options={patrolTypeOptions}
                  showCheckMark={false}
                  value={[patrolType]}
                  onChange={(v) => {
                    if (v && v.length) {
                      setPatrolType(v[0]);
                    }
                  }}
                />
              </div>
            )}
            {detailType[0] === AuditTypeEnum.未完成整改 && dataType === 'shop' && (
              <div className="py-2 px-4 border-b border-solid border-line">
                <Selector
                  key="patrol"
                  style={{
                    '--border-radius': '4px',
                    '--color': 'rgba(249,250,251)',
                    '--text-color': '#58595B',
                    '--checked-color': '#e1faf5',
                    '--padding': '4px',
                  }}
                  columns={4}
                  showCheckMark={false}
                  options={rectifyTypeOptions}
                  value={[rectifyType]}
                  onChange={(v) => {
                    if (v && v.length) {
                      setRectifyType(v[0]);
                    }
                  }}
                />
              </div>
            )} */}
            <div className="flex items-center text-xs px-4 py-3 text-light overflow-x-auto whitespace-nowrap">
              {showGroupNames}
            </div>
          </Card>
        </div>
        <div hidden={hiddenDetail}>
          <DetailList
            loading={loading}
            ListData={ListData}
            detailType={detailType}
            params={params}
            detailSubType={detailSubType as 'all'}
          />
        </div>
      </Loading>
    </div>
  );
});

export default AuditBoard;
