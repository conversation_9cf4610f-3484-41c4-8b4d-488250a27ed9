import {
  typeObj,
  AuditTypeEnum,
  AuditDetailTitleMap,
  RoleClassificationEnum,
  SelfWorkSheetEnum,
} from '../enum';
import { useRequest } from 'ahooks';
import { useLocation, useSearchParams } from 'react-router-dom';
import { Table } from 'antd';
import { SafeArea } from 'antd-mobile';
import { Contact } from '../../components';
import { useEffect } from 'react';
import Loading from '@src/components/Loading';

const AuditDetail = () => {
  const [search] = useSearchParams();
  const [beginDate, endDate, shopCode, detailType] = [
    search.get('beginDate') as string,
    search.get('endDate') as string,
    search.get('shopCode'),
    search.get('detailType') as AuditTypeEnum,
  ];
  const { percent } = useLocation().state || {};
  const responsiblePersonId = search.get('id');
  const responsiblePersonPhone = search.get('phone');
  const responsiblePersonName = search.get('responsiblePersonName') || '';
  const detailSubType = search.get('detailSubType') || '';
  const title = search.get('title');

  useEffect(() => {
    document.title = `${title}${AuditDetailTitleMap[detailType] || ''}明细数据`;
  }, []);

  const { data, loading } = useRequest(async () => {
    const subParams = [
      AuditTypeEnum.触碰红线,
      AuditTypeEnum.未通过巡检,
      AuditTypeEnum.未完成整改,
    ].includes(detailType)
      ? {
          roleClassificationEnum:
            detailSubType === 'all' ? undefined : (detailSubType as RoleClassificationEnum),
        }
      : [AuditTypeEnum.自检不合格, AuditTypeEnum.未完成自检].includes(detailType)
        ? {
            selfWorkSheet:
              detailSubType === 'all' ? undefined : (detailSubType as SelfWorkSheetEnum),
          }
        : {};
    const res = await typeObj[detailType].getDetail({
      beginDate,
      endDate,
      shopCode,
      ...subParams,
    });
    return res;
  });
  return (
    <>
      <div className="pb-20 px-3 pt-3">
        <Loading spinning={loading}>
          {/* <Card> */}
          <Table
            rowKey={(record) => `${record?.reportId ?? record?.taskId}`}
            showSorterTooltip={false}
            bordered
            columns={typeObj[detailType].detailColumns?.map((v) => ({ ...v, align: 'center' }))}
            dataSource={data}
            expandable={typeObj[detailType].expandable}
            pagination={false}
            sticky={{
              offsetHeader: 0,
            }}
            // scroll={{ y: 600 }}
          />
          {/* </Card> */}
        </Loading>
      </div>
      <div className="fixed bottom-0 w-full bg-white">
        <Contact
          type="page"
          title={title!}
          percent={percent}
          manager={{
            name: responsiblePersonName!,
            phone: responsiblePersonPhone!,
            id: responsiblePersonId!,
          }}
        />
        <SafeArea position="bottom" />
      </div>
    </>
  );
};
export default AuditDetail;
