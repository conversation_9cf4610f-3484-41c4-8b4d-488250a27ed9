/**
 * 明细的指标
 */
import { IndicatorCardSub, IndicatorCardV2 } from '../../components';
import {
  AuditTypeEnum,
  RoleClassificationEnum,
  SelfWorkSheetEnum,
  SupervisionEnum,
  ToolTipMap,
} from '../enum';
import { useRequest } from 'ahooks';
import { getPass, getRectify, getRedLine, getSelfPass, getUnComplete } from '../api';
import { OverviewParams } from '../api.type';
import AuthorityEnum from '@src/common/authority';
import { observer } from 'mobx-react';
import { ReactNode } from 'react';
import userStore from '@src/store/user';
import { formatPer } from '@src/utils/helper';

type IProps = {
  type: AuditTypeEnum;
  showPercentDateType?: {
    same: 'day' | 'month' | 'default'; //同/环比类型
    circle: 'day' | 'month' | 'default'; //同/环比类型
  };
  redLine: any;
  pass: any;
  rectify: any;
  selfPass: any;
  unComplete: any;
  supervision?: any;
  params: OverviewParams;
  hiddenDetail: boolean;
};

const AuthorityWrap: React.FC<{ permission: string; children: ReactNode }> = observer(
  ({ permission, children }) => {
    const { permissionsMap } = userStore;
    if (!permissionsMap.has(permission)) {
      return <></>;
    }
    return children;
  },
);

const DetailIndicator: React.FC<IProps> = observer(
  ({
    type,
    showPercentDateType,
    redLine,
    pass,
    rectify,
    selfPass,
    unComplete,
    supervision,
    params,
    hiddenDetail,
  }) => {
    const { permissionsMap } = userStore;

    const { data: redLineHq } = useRequest(
      async () => {
        if (!permissionsMap.has(AuthorityEnum.redLineHq)) throw null;
        if (hiddenDetail || type !== AuditTypeEnum.触碰红线) throw null;
        const res = await getRedLine({
          ...params,
          roleClassificationEnum: RoleClassificationEnum.总部,
        });
        return res;
      },
      {
        refreshDeps: [params, hiddenDetail, type],
      },
    );
    const { data: redLineOnLine } = useRequest(
      async () => {
        if (!permissionsMap.has(AuthorityEnum.redLineOnLine)) throw null;
        if (hiddenDetail || type !== AuditTypeEnum.触碰红线) throw null;
        const res = await getRedLine({
          ...params,
          roleClassificationEnum: RoleClassificationEnum.线上,
        });
        return res;
      },
      {
        refreshDeps: [params, hiddenDetail, type],
      },
    );
    const { data: redLineOffLine } = useRequest(
      async () => {
        if (!permissionsMap.has(AuthorityEnum.redLineOffLine)) throw null;
        if (hiddenDetail || type !== AuditTypeEnum.触碰红线) throw null;
        const res = await getRedLine({
          ...params,
          roleClassificationEnum: RoleClassificationEnum.战区,
        });
        return res;
      },
      {
        refreshDeps: [params, hiddenDetail, type],
      },
    );
    const { data: redLineOther } = useRequest(
      async () => {
        if (!permissionsMap.has(AuthorityEnum.redLineOther)) throw null;
        if (hiddenDetail || type !== AuditTypeEnum.触碰红线) throw null;
        const res = await getRedLine({
          ...params,
          roleClassificationEnum: RoleClassificationEnum.其他,
        });
        return res;
      },
      {
        refreshDeps: [params, hiddenDetail, type],
      },
    );

    const { data: passHq } = useRequest(
      async () => {
        if (!permissionsMap.has(AuthorityEnum.unPassPatrolHq)) throw null;
        if (hiddenDetail || type !== AuditTypeEnum.未通过巡检) throw null;
        const res = await getPass({
          ...params,
          roleClassificationEnum: RoleClassificationEnum.总部,
        });
        return res;
      },
      {
        refreshDeps: [params, hiddenDetail, type],
      },
    );
    const { data: passOnLine } = useRequest(
      async () => {
        if (!permissionsMap.has(AuthorityEnum.unPassPatrolOnLine)) throw null;
        if (hiddenDetail || type !== AuditTypeEnum.未通过巡检) throw null;
        const res = await getPass({
          ...params,
          roleClassificationEnum: RoleClassificationEnum.线上,
        });
        return res;
      },
      {
        refreshDeps: [params, hiddenDetail, type],
      },
    );
    const { data: passOffLine } = useRequest(
      async () => {
        if (!permissionsMap.has(AuthorityEnum.unPassPatrolOffLine)) throw null;
        if (hiddenDetail || type !== AuditTypeEnum.未通过巡检) throw null;
        const res = await getPass({
          ...params,
          roleClassificationEnum: RoleClassificationEnum.战区,
        });
        return res;
      },
      {
        refreshDeps: [params, hiddenDetail, type],
      },
    );
    const { data: passOther } = useRequest(
      async () => {
        if (!permissionsMap.has(AuthorityEnum.unPassPatrolOther)) throw null;
        if (hiddenDetail || type !== AuditTypeEnum.未通过巡检) throw null;
        const res = await getPass({
          ...params,
          roleClassificationEnum: RoleClassificationEnum.其他,
        });
        return res;
      },
      {
        refreshDeps: [params, hiddenDetail, type],
      },
    );

    const { data: rectifyHq } = useRequest(
      async () => {
        if (!permissionsMap.has(AuthorityEnum.unCompleteRectifyHq)) throw null;
        if (hiddenDetail || type !== AuditTypeEnum.未完成整改) throw null;
        const res = await getRectify({
          ...params,
          roleClassificationEnum: RoleClassificationEnum.总部,
        });
        return res;
      },
      {
        refreshDeps: [params, hiddenDetail, type],
      },
    );
    const { data: rectifyOnLine } = useRequest(
      async () => {
        if (!permissionsMap.has(AuthorityEnum.unCompleteRectifyOnLine)) throw null;
        if (hiddenDetail || type !== AuditTypeEnum.未完成整改) throw null;
        const res = await getRectify({
          ...params,
          roleClassificationEnum: RoleClassificationEnum.线上,
        });
        return res;
      },
      {
        refreshDeps: [params, hiddenDetail, type],
      },
    );
    const { data: rectifyOffLine } = useRequest(
      async () => {
        if (!permissionsMap.has(AuthorityEnum.unCompleteRectifyOffLine)) throw null;
        if (hiddenDetail || type !== AuditTypeEnum.未完成整改) throw null;
        const res = await getRectify({
          ...params,
          roleClassificationEnum: RoleClassificationEnum.战区,
        });
        return res;
      },
      {
        refreshDeps: [params, hiddenDetail, type],
      },
    );
    const { data: rectifyOther } = useRequest(
      async () => {
        if (!permissionsMap.has(AuthorityEnum.unCompleteRectifyOther)) throw null;
        if (hiddenDetail || type !== AuditTypeEnum.未完成整改) throw null;
        const res = await getRectify({
          ...params,
          roleClassificationEnum: RoleClassificationEnum.其他,
        });
        return res;
      },
      {
        refreshDeps: [params, hiddenDetail, type],
      },
    );
    const { data: selfPassDaily } = useRequest(
      async () => {
        if (!permissionsMap.has(AuthorityEnum.unPassSelfDaily)) throw null;
        if (hiddenDetail || type !== AuditTypeEnum.自检不合格) throw null;
        const res = await getSelfPass({
          ...params,
          selfWorkSheet: SelfWorkSheetEnum.效期,
        });
        return res;
      },
      {
        refreshDeps: [params, hiddenDetail, type],
      },
    );
    const { data: selfPassTank } = useRequest(
      async () => {
        if (!permissionsMap.has(AuthorityEnum.unPassSelfTank)) throw null;
        if (hiddenDetail || type !== AuditTypeEnum.自检不合格) throw null;
        const res = await getSelfPass({
          ...params,
          selfWorkSheet: SelfWorkSheetEnum.隔油池,
        });
        return res;
      },
      {
        refreshDeps: [params, hiddenDetail, type],
      },
    );
    const { data: selfPassCoke } = useRequest(
      async () => {
        if (!permissionsMap.has(AuthorityEnum.unPassSelfCoke)) throw null;
        if (hiddenDetail || type !== AuditTypeEnum.自检不合格) throw null;
        const res = await getSelfPass({
          ...params,
          selfWorkSheet: SelfWorkSheetEnum.可乐机,
        });
        return res;
      },
      {
        refreshDeps: [params, hiddenDetail, type],
      },
    );
    const { data: selfPassIce } = useRequest(
      async () => {
        if (!permissionsMap.has(AuthorityEnum.unPassSelfIce)) throw null;
        if (hiddenDetail || type !== AuditTypeEnum.自检不合格) throw null;
        const res = await getSelfPass({
          ...params,
          selfWorkSheet: SelfWorkSheetEnum.制冰机,
        });
        return res;
      },
      {
        refreshDeps: [params, hiddenDetail, type],
      },
    );
    const { data: selfPassOil } = useRequest(
      async () => {
        if (!permissionsMap.has(AuthorityEnum.unPassSelfOil)) throw null;
        if (hiddenDetail || type !== AuditTypeEnum.自检不合格) throw null;
        const res = await getSelfPass({
          ...params,
          selfWorkSheet: SelfWorkSheetEnum.炸油品质,
        });
        return res;
      },
      {
        refreshDeps: [params, hiddenDetail, type],
      },
    );
    const { data: selfPassClean } = useRequest(
      async () => {
        if (!permissionsMap.has(AuthorityEnum.unPassSelfClean)) throw null;
        if (hiddenDetail || type !== AuditTypeEnum.自检不合格) throw null;
        const res = await getSelfPass({
          ...params,
          selfWorkSheet: SelfWorkSheetEnum.清洁,
        });
        return res;
      },
      {
        refreshDeps: [params, hiddenDetail, type],
      },
    );
    const { data: unCompleteDaily } = useRequest(
      async () => {
        if (!permissionsMap.has(AuthorityEnum.unCompleteSelfDaily)) throw null;
        if (hiddenDetail || type !== AuditTypeEnum.未完成自检) throw null;
        const res = await getUnComplete({
          ...params,
          selfWorkSheet: SelfWorkSheetEnum.效期,
        });
        return res;
      },
      {
        refreshDeps: [params, hiddenDetail, type],
      },
    );
    const { data: unCompleteTank } = useRequest(
      async () => {
        if (!permissionsMap.has(AuthorityEnum.unCompleteSelfTank)) throw null;
        if (hiddenDetail || type !== AuditTypeEnum.未完成自检) throw null;
        const res = await getUnComplete({
          ...params,
          selfWorkSheet: SelfWorkSheetEnum.隔油池,
        });
        return res;
      },
      {
        refreshDeps: [params, hiddenDetail, type],
      },
    );
    const { data: unCompleteCoke } = useRequest(
      async () => {
        if (!permissionsMap.has(AuthorityEnum.unCompleteSelfCoke)) throw null;
        if (hiddenDetail || type !== AuditTypeEnum.未完成自检) throw null;
        const res = await getUnComplete({
          ...params,
          selfWorkSheet: SelfWorkSheetEnum.可乐机,
        });
        return res;
      },
      {
        refreshDeps: [params, hiddenDetail, type],
      },
    );
    const { data: unCompleteIce } = useRequest(
      async () => {
        if (!permissionsMap.has(AuthorityEnum.unCompleteSelfIce)) throw null;
        if (hiddenDetail || type !== AuditTypeEnum.未完成自检) throw null;
        const res = await getUnComplete({
          ...params,
          selfWorkSheet: SelfWorkSheetEnum.制冰机,
        });
        return res;
      },
      {
        refreshDeps: [params, hiddenDetail, type],
      },
    );
    const { data: unCompleteOil } = useRequest(
      async () => {
        if (!permissionsMap.has(AuthorityEnum.unCompleteSelfOil)) throw null;
        if (hiddenDetail || type !== AuditTypeEnum.未完成自检) throw null;
        const res = await getUnComplete({
          ...params,
          selfWorkSheet: SelfWorkSheetEnum.炸油品质,
        });
        return res;
      },
      {
        refreshDeps: [params, hiddenDetail, type],
      },
    );
    const { data: unCompleteClean } = useRequest(
      async () => {
        if (!permissionsMap.has(AuthorityEnum.unCompleteSelfClean)) throw null;
        if (hiddenDetail || type !== AuditTypeEnum.未完成自检) throw null;
        const res = await getUnComplete({
          ...params,
          selfWorkSheet: SelfWorkSheetEnum.清洁,
        });
        return res;
      },
      {
        refreshDeps: [params, hiddenDetail, type],
      },
    );
    const IndicatorMap = {
      [AuditTypeEnum.触碰红线]: (
        <>
          <IndicatorCardV2
            key="redLine"
            title="稽核“S项”占比"
            indicator={formatPer(redLine?.touchRedLineRatio)}
            tooltip={ToolTipMap[AuditTypeEnum.触碰红线]}
            percent={{
              same: formatPer(redLine?.touchRedLineOverRatio),
              circle: formatPer(redLine?.touchRedLineRatioRatio),
            }}
            percentDateType={showPercentDateType}
            isWarning={!!redLine?.statusAlarm}
            columns={[
              {
                label: '总巡检次数',
                value: redLine?.patrolCount,
              },
              {
                label: '触碰“S项”条数',
                value: redLine?.touchRedLineItemCount,
              },
              {
                label: '触碰“S项”门店数量',
                value: redLine?.touchRedLineShopCount,
              },
            ]}
          />
          <AuthorityWrap permission={AuthorityEnum.redLineHq}>
            <IndicatorCardSub
              key="redLineHq"
              title="总部食安稽核“S项”占比"
              indicator={formatPer(redLineHq?.touchRedLineRatio)}
              tooltip={ToolTipMap[AuditTypeEnum.触碰红线 + RoleClassificationEnum.总部]}
              percent={{
                same: formatPer(redLineHq?.touchRedLineOverRatio),
                circle: formatPer(redLineHq?.touchRedLineRatioRatio),
              }}
              percentDateType={showPercentDateType}
              columns={[
                {
                  label: '总部食安稽核触碰“S项”条数',
                  value: redLineHq?.touchRedLineItemCount,
                },
                {
                  label: '总部食安稽核次数',
                  value: redLineHq?.patrolCount,
                },
                {
                  label: '总部食安稽核触碰“S项”门店数量',
                  value: redLineHq?.touchRedLineShopCount,
                },
              ]}
            />
          </AuthorityWrap>
          <AuthorityWrap permission={AuthorityEnum.redLineOnLine}>
            <IndicatorCardSub
              key="redLineOnLine"
              title="线上云巡检“S项”占比"
              indicator={formatPer(redLineOnLine?.touchRedLineRatio)}
              tooltip={ToolTipMap[AuditTypeEnum.触碰红线 + RoleClassificationEnum.线上]}
              percent={{
                same: formatPer(redLineOnLine?.touchRedLineOverRatio),
                circle: formatPer(redLineOnLine?.touchRedLineRatioRatio),
              }}
              percentDateType={showPercentDateType}
              columns={[
                {
                  label: '线上云巡检触碰“S项”条数',
                  value: redLineOnLine?.touchRedLineItemCount,
                },
                {
                  label: '线上云巡检次数',
                  value: redLineOnLine?.patrolCount,
                },
                {
                  label: '线上云巡检触碰“S项”门店数量',
                  value: redLineOnLine?.touchRedLineShopCount,
                },
              ]}
            />
          </AuthorityWrap>
          <AuthorityWrap permission={AuthorityEnum.redLineOffLine}>
            <IndicatorCardSub
              key="redLineOffLine"
              title="战区食安巡检“S项”占比"
              indicator={formatPer(redLineOffLine?.touchRedLineRatio)}
              tooltip={ToolTipMap[AuditTypeEnum.触碰红线 + RoleClassificationEnum.战区]}
              percent={{
                same: formatPer(redLineOffLine?.touchRedLineOverRatio),
                circle: formatPer(redLineOffLine?.touchRedLineRatioRatio),
              }}
              percentDateType={showPercentDateType}
              columns={[
                {
                  label: '战区食安巡检触碰“S项”条数',
                  value: redLineOffLine?.touchRedLineItemCount,
                },
                {
                  label: '战区食安总巡检次数',
                  value: redLineOffLine?.patrolCount,
                },
                {
                  label: '战区食安总巡检“S项”门店数量',
                  value: redLineOffLine?.touchRedLineShopCount,
                },
              ]}
            />
          </AuthorityWrap>
          <AuthorityWrap permission={AuthorityEnum.redLineOther}>
            <IndicatorCardSub
              key="redLineOther"
              title="其他巡检“S项”占比"
              indicator={formatPer(redLineOther?.touchRedLineRatio)}
              tooltip={ToolTipMap[AuditTypeEnum.触碰红线 + RoleClassificationEnum.其他]}
              percent={{
                same: formatPer(redLineOther?.touchRedLineOverRatio),
                circle: formatPer(redLineOther?.touchRedLineRatioRatio),
              }}
              percentDateType={showPercentDateType}
              columns={[
                {
                  label: '其他巡检触碰“S项”条数',
                  value: redLineOther?.touchRedLineItemCount,
                },
                {
                  label: '其他巡检次数',
                  value: redLineOther?.patrolCount,
                },
                {
                  label: '其他巡检“S项”门店数量',
                  value: redLineOther?.touchRedLineShopCount,
                },
              ]}
            />
          </AuthorityWrap>
        </>
      ),
      [AuditTypeEnum.未通过巡检]: (
        <>
          <IndicatorCardV2
            key="pass"
            title="门店稽核通过率"
            indicator={formatPer(pass?.shopPassRatio)}
            tooltip={ToolTipMap[AuditTypeEnum.未通过巡检]}
            isWarning={!!pass?.statusAlarm}
            percent={{
              same: formatPer(pass?.shopPassOverRatio),
              circle: formatPer(pass?.shopPassRatioRatio),
            }}
            columns={[
              {
                label: '总稽核报告数',
                value: pass?.patrolCount,
              },
              {
                label: '稽核通过报告数',
                value: pass?.patrolPassCount,
              },
            ]}
            percentDateType={showPercentDateType}
          />
          <AuthorityWrap permission={AuthorityEnum.unPassPatrolHq}>
            <IndicatorCardSub
              key="passHq"
              title="总部食安稽核通过率"
              indicator={formatPer(passHq?.shopPassRatio)}
              percent={{
                same: formatPer(passHq?.shopPassOverRatio),
                circle: formatPer(passHq?.shopPassRatioRatio),
              }}
              tooltip={ToolTipMap[AuditTypeEnum.未通过巡检 + RoleClassificationEnum.总部]}
              columns={[
                {
                  label: '食安中心稽核通过报告数',
                  value: passHq?.patrolPassCount,
                },
                {
                  label: '食安中心总稽核报告数',
                  value: passHq?.patrolCount,
                },
              ]}
              percentDateType={showPercentDateType}
            />
          </AuthorityWrap>
          <AuthorityWrap permission={AuthorityEnum.unPassPatrolOnLine}>
            <IndicatorCardSub
              key="passOnLine"
              title="线上云巡检通过率"
              indicator={formatPer(passOnLine?.shopPassRatio)}
              percent={{
                same: formatPer(passOnLine?.shopPassOverRatio),
                circle: formatPer(passOnLine?.shopPassRatioRatio),
              }}
              tooltip={ToolTipMap[AuditTypeEnum.未通过巡检 + RoleClassificationEnum.线上]}
              columns={[
                {
                  label: '线上云巡检通过报告数',
                  value: passOnLine?.patrolPassCount,
                },
                {
                  label: '线上云巡检报告数',
                  value: passOnLine?.patrolCount,
                },
              ]}
              percentDateType={showPercentDateType}
            />
          </AuthorityWrap>
          <AuthorityWrap permission={AuthorityEnum.unPassPatrolOffLine}>
            <IndicatorCardSub
              key="passOffLine"
              title="战区食安巡检通过率"
              indicator={formatPer(passOffLine?.shopPassRatio)}
              percent={{
                same: formatPer(passOffLine?.shopPassOverRatio),
                circle: formatPer(passOffLine?.shopPassRatioRatio),
              }}
              tooltip={ToolTipMap[AuditTypeEnum.未通过巡检 + RoleClassificationEnum.战区]}
              columns={[
                {
                  label: '战区食安部巡检通过报告数',
                  value: passOffLine?.patrolPassCount,
                },
                {
                  label: '战区食安部总巡检次数',
                  value: passOffLine?.patrolCount,
                },
              ]}
              percentDateType={showPercentDateType}
            />
          </AuthorityWrap>
          <AuthorityWrap permission={AuthorityEnum.unPassPatrolOther}>
            <IndicatorCardSub
              key="passOther"
              title="其他巡检通过率"
              indicator={formatPer(passOther?.shopPassRatio)}
              percent={{
                same: formatPer(passOther?.shopPassOverRatio),
                circle: formatPer(passOther?.shopPassRatioRatio),
              }}
              tooltip={ToolTipMap[AuditTypeEnum.未通过巡检 + RoleClassificationEnum.其他]}
              columns={[
                {
                  label: '其他巡检通过报告数',
                  value: passOther?.patrolPassCount,
                },
                {
                  label: '其他巡检次数',
                  value: passOther?.patrolCount,
                },
              ]}
              percentDateType={showPercentDateType}
            />
          </AuthorityWrap>
        </>
      ),
      [AuditTypeEnum.未完成整改]: (
        <>
          <IndicatorCardV2
            key="rectify"
            title="门店稽核整改完成率"
            indicator={formatPer(rectify?.rectifyRatio)}
            tooltip={ToolTipMap[AuditTypeEnum.未完成整改]}
            columns={[
              {
                label: '已完成整改的报告数',
                value: rectify?.rectifyCount,
              },
              {
                label: '需整改的报告数',
                value: rectify?.unRectifyCount,
              },
            ]}
            isWarning={!!rectify?.statusAlarm}
            percent={{
              same: formatPer(rectify?.rectifyOverRatio),
              circle: formatPer(rectify?.rectifyRatioRatio),
            }}
            percentDateType={showPercentDateType}
          />
          <AuthorityWrap permission={AuthorityEnum.unCompleteRectifyHq}>
            <IndicatorCardSub
              key="rectifyHq"
              title="总部食安稽核整改完成率"
              indicator={formatPer(rectifyHq?.rectifyRatio)}
              tooltip={ToolTipMap[AuditTypeEnum.未完成整改 + RoleClassificationEnum.总部]}
              columns={[
                {
                  label: '总部食安稽核已完成整改的报告数',
                  value: rectifyHq?.rectifyCount,
                },
                {
                  label: '总部食安稽核需整改的报告数',
                  value: rectifyHq?.unRectifyCount,
                },
              ]}
              percentDateType={showPercentDateType}
              percent={{
                same: formatPer(rectifyHq?.rectifyOverRatio),
                circle: formatPer(rectifyHq?.rectifyRatioRatio),
              }}
            />
          </AuthorityWrap>
          <AuthorityWrap permission={AuthorityEnum.unCompleteRectifyOnLine}>
            <IndicatorCardSub
              key="rectifyOnLine"
              title="线上云巡检整改完成率"
              indicator={formatPer(rectifyOnLine?.rectifyRatio)}
              tooltip={ToolTipMap[AuditTypeEnum.未完成整改 + RoleClassificationEnum.线上]}
              columns={[
                {
                  label: '线上云巡检已完成整改的报告数',
                  value: rectifyOnLine?.rectifyCount,
                },
                {
                  label: '线上云巡检需整改的报告数',
                  value: rectifyOnLine?.unRectifyCount,
                },
              ]}
              percentDateType={showPercentDateType}
              percent={{
                same: formatPer(rectifyOnLine?.rectifyOverRatio),
                circle: formatPer(rectifyOnLine?.rectifyRatioRatio),
              }}
            />
          </AuthorityWrap>
          <AuthorityWrap permission={AuthorityEnum.unCompleteRectifyOffLine}>
            <IndicatorCardSub
              key="rectifyOffLine"
              title="战区食安整改完成率"
              indicator={formatPer(rectifyOffLine?.rectifyRatio)}
              tooltip={ToolTipMap[AuditTypeEnum.未完成整改 + RoleClassificationEnum.战区]}
              columns={[
                {
                  label: '战区食安部已完成整改的报告数',
                  value: rectifyOffLine?.rectifyCount,
                },
                {
                  label: '战区食安部需整改的报告数',
                  value: rectifyOffLine?.unRectifyCount,
                },
              ]}
              percentDateType={showPercentDateType}
              percent={{
                same: formatPer(rectifyOffLine?.rectifyOverRatio),
                circle: formatPer(rectifyOffLine?.rectifyRatioRatio),
              }}
            />
          </AuthorityWrap>
          <AuthorityWrap permission={AuthorityEnum.unCompleteRectifyOther}>
            <IndicatorCardSub
              key="rectifyOther"
              title="其他巡检整改完成率"
              indicator={formatPer(rectifyOther?.rectifyRatio)}
              tooltip={ToolTipMap[AuditTypeEnum.未完成整改 + RoleClassificationEnum.其他]}
              columns={[
                {
                  label: '其他巡检已完成整改的报告数 ',
                  value: rectifyOther?.rectifyCount,
                },
                {
                  label: '其他巡检需整改的报告数',
                  value: rectifyOther?.unRectifyCount,
                },
              ]}
              percentDateType={showPercentDateType}
              percent={{
                same: formatPer(rectifyOther?.rectifyOverRatio),
                circle: formatPer(rectifyOther?.rectifyRatioRatio),
              }}
            />
          </AuthorityWrap>
        </>
      ),
      [AuditTypeEnum.自检不合格]: (
        <>
          <IndicatorCardV2
            key="selfPass"
            title="自检任务合格率"
            indicator={formatPer(selfPass?.selfPassRatio)}
            percent={{
              same: formatPer(selfPass?.selfPassOverRatio),
              circle: formatPer(selfPass?.selfPassRatioRatio),
            }}
            percentDateType={showPercentDateType}
            tooltip={ToolTipMap[AuditTypeEnum.自检不合格]}
            isWarning={!!selfPass?.statusAlarm}
            columns={[
              {
                label: '自检任务合格门店报告数',
                value: selfPass?.selfPassCount,
              },
              {
                label: '已点评自检报告数',
                value: selfPass?.selfCount,
              },
            ]}
          />
          <AuthorityWrap permission={AuthorityEnum.unPassSelfDaily}>
            <IndicatorCardSub
              key="selfPassDaily"
              title="《全国每日效期检查》合格率"
              indicator={formatPer(selfPassDaily?.selfPassRatio)}
              tooltip={ToolTipMap[AuditTypeEnum.自检不合格 + SelfWorkSheetEnum.效期]}
              columns={[
                {
                  label: '自检任务合格门店报告数 ',
                  value: selfPassDaily?.selfPassCount,
                },
                {
                  label: '已点评自检报告数',
                  value: selfPassDaily?.selfCount,
                },
              ]}
              percentDateType={showPercentDateType}
              percent={{
                same: formatPer(selfPassDaily?.selfPassOverRatio),
                circle: formatPer(selfPassDaily?.selfPassRatioRatio),
              }}
            />
          </AuthorityWrap>
          <AuthorityWrap permission={AuthorityEnum.unPassSelfTank}>
            <IndicatorCardSub
              key="selfPassTank"
              title="《全国隔油池清洁》合格率"
              indicator={formatPer(selfPassTank?.selfPassRatio)}
              tooltip={ToolTipMap[AuditTypeEnum.自检不合格 + SelfWorkSheetEnum.隔油池]}
              columns={[
                {
                  label: '自检任务合格门店报告数 ',
                  value: selfPassTank?.selfPassCount,
                },
                {
                  label: '已点评自检报告数',
                  value: selfPassTank?.selfCount,
                },
              ]}
              percentDateType={showPercentDateType}
              percent={{
                same: formatPer(selfPassTank?.selfPassOverRatio),
                circle: formatPer(selfPassTank?.selfPassRatioRatio),
              }}
            />
          </AuthorityWrap>
          <AuthorityWrap permission={AuthorityEnum.unPassSelfCoke}>
            <IndicatorCardSub
              key="selfPassCoke"
              title="《全国可乐机清洁》合格率"
              indicator={formatPer(selfPassCoke?.selfPassRatio)}
              tooltip={ToolTipMap[AuditTypeEnum.自检不合格 + SelfWorkSheetEnum.可乐机]}
              columns={[
                {
                  label: '自检任务合格门店报告数 ',
                  value: selfPassCoke?.selfPassCount,
                },
                {
                  label: '已点评自检报告数',
                  value: selfPassCoke?.selfCount,
                },
              ]}
              percentDateType={showPercentDateType}
              percent={{
                same: formatPer(selfPassCoke?.selfPassOverRatio),
                circle: formatPer(selfPassCoke?.selfPassRatioRatio),
              }}
            />
          </AuthorityWrap>
          <AuthorityWrap permission={AuthorityEnum.unPassSelfIce}>
            <IndicatorCardSub
              key="selfPassIce"
              title="《全国制冰机清洁》合格率"
              indicator={formatPer(selfPassIce?.selfPassRatio)}
              tooltip={ToolTipMap[AuditTypeEnum.自检不合格 + SelfWorkSheetEnum.制冰机]}
              columns={[
                {
                  label: '自检任务合格门店报告数 ',
                  value: selfPassIce?.selfPassCount,
                },
                {
                  label: '已点评自检报告数',
                  value: selfPassIce?.selfCount,
                },
              ]}
              percentDateType={showPercentDateType}
              percent={{
                same: formatPer(selfPassIce?.selfPassOverRatio),
                circle: formatPer(selfPassIce?.selfPassRatioRatio),
              }}
            />
          </AuthorityWrap>
          <AuthorityWrap permission={AuthorityEnum.unPassSelfOil}>
            <IndicatorCardSub
              key="selfPassOil"
              title="《全国炸油品质管理》合格率"
              indicator={formatPer(selfPassOil?.selfPassRatio)}
              tooltip={ToolTipMap[AuditTypeEnum.自检不合格 + SelfWorkSheetEnum.炸油品质]}
              columns={[
                {
                  label: '自检任务合格门店报告数 ',
                  value: selfPassOil?.selfPassCount,
                },
                {
                  label: '已点评自检报告数',
                  value: selfPassOil?.selfCount,
                },
              ]}
              percentDateType={showPercentDateType}
              percent={{
                same: formatPer(selfPassOil?.selfPassOverRatio),
                circle: formatPer(selfPassOil?.selfPassRatioRatio),
              }}
            />
          </AuthorityWrap>
          <AuthorityWrap permission={AuthorityEnum.unPassSelfClean}>
            <IndicatorCardSub
              key="selfPassClean"
              title="《全国月清洁任务》合格率"
              indicator={formatPer(selfPassClean?.selfPassRatio)}
              tooltip={ToolTipMap[AuditTypeEnum.自检不合格 + SelfWorkSheetEnum.清洁]}
              columns={[
                {
                  label: '自检任务合格门店报告数 ',
                  value: selfPassClean?.selfPassCount,
                },
                {
                  label: '已点评自检报告数',
                  value: selfPassClean?.selfCount,
                },
              ]}
              percentDateType={showPercentDateType}
              percent={{
                same: formatPer(selfPassClean?.selfPassOverRatio),
                circle: formatPer(selfPassClean?.selfPassRatioRatio),
              }}
            />
          </AuthorityWrap>
        </>
      ),
      [AuditTypeEnum.未完成自检]: (
        <>
          <IndicatorCardV2
            key="unComplete"
            title="自检任务完成率"
            indicator={formatPer(unComplete?.selfCompleteRatio)}
            percent={{
              same: formatPer(unComplete?.selfCompleteOverRatio),
              circle: formatPer(unComplete?.selfCompleteRatioRatio),
            }}
            percentDateType={showPercentDateType}
            tooltip={ToolTipMap[AuditTypeEnum.未完成自检]}
            isWarning={!!unComplete?.statusAlarm}
            columns={[
              {
                label: '已提交自检任务门店报告数',
                value: unComplete?.selfCompleteCount,
              },
              {
                label: '计划自检任务门店报告数',
                value: unComplete?.planSelfCount,
              },
            ]}
          />
          <AuthorityWrap permission={AuthorityEnum.unCompleteSelfDaily}>
            <IndicatorCardSub
              key="unCompleteDaily"
              title="《全国每日效期检查》完成率"
              indicator={formatPer(unCompleteDaily?.selfCompleteRatio)}
              tooltip={ToolTipMap[AuditTypeEnum.未完成自检 + SelfWorkSheetEnum.效期]}
              columns={[
                {
                  label: '已提交自检任务门店报告数',
                  value: unCompleteDaily?.selfCompleteCount,
                },
                {
                  label: '计划自检任务门店报告数',
                  value: unCompleteDaily?.planSelfCount,
                },
              ]}
              percentDateType={showPercentDateType}
              percent={{
                same: formatPer(unCompleteDaily?.selfCompleteOverRatio),
                circle: formatPer(unCompleteDaily?.selfCompleteRatioRatio),
              }}
            />
          </AuthorityWrap>
          <AuthorityWrap permission={AuthorityEnum.unCompleteSelfTank}>
            <IndicatorCardSub
              key="unCompleteTank"
              title="《全国隔油池清洁》完成率"
              indicator={formatPer(unCompleteTank?.selfCompleteRatio)}
              tooltip={ToolTipMap[AuditTypeEnum.未完成自检 + SelfWorkSheetEnum.隔油池]}
              columns={[
                {
                  label: '已提交自检任务门店报告数',
                  value: unCompleteTank?.selfCompleteCount,
                },
                {
                  label: '计划自检任务门店报告数',
                  value: unCompleteTank?.planSelfCount,
                },
              ]}
              percentDateType={showPercentDateType}
              percent={{
                same: formatPer(unCompleteTank?.selfCompleteOverRatio),
                circle: formatPer(unCompleteTank?.selfCompleteRatioRatio),
              }}
            />
          </AuthorityWrap>
          <AuthorityWrap permission={AuthorityEnum.unCompleteSelfCoke}>
            <IndicatorCardSub
              key="unCompleteCoke"
              title="《全国可乐机清洁》完成率"
              indicator={formatPer(unCompleteCoke?.selfCompleteRatio)}
              tooltip={ToolTipMap[AuditTypeEnum.未完成自检 + SelfWorkSheetEnum.可乐机]}
              columns={[
                {
                  label: '已提交自检任务门店报告数',
                  value: unCompleteCoke?.selfCompleteCount,
                },
                {
                  label: '计划自检任务门店报告数',
                  value: unCompleteCoke?.planSelfCount,
                },
              ]}
              percentDateType={showPercentDateType}
              percent={{
                same: formatPer(unCompleteCoke?.selfCompleteOverRatio),
                circle: formatPer(unCompleteCoke?.selfCompleteRatioRatio),
              }}
            />
          </AuthorityWrap>
          <AuthorityWrap permission={AuthorityEnum.unCompleteSelfIce}>
            <IndicatorCardSub
              key="unCompleteIce"
              title="《全国制冰机清洁》完成率"
              indicator={formatPer(unCompleteIce?.selfCompleteRatio)}
              tooltip={ToolTipMap[AuditTypeEnum.未完成自检 + SelfWorkSheetEnum.制冰机]}
              columns={[
                {
                  label: '已提交自检任务门店报告数',
                  value: unCompleteIce?.selfCompleteCount,
                },
                {
                  label: '计划自检任务门店报告数',
                  value: unCompleteIce?.planSelfCount,
                },
              ]}
              percentDateType={showPercentDateType}
              percent={{
                same: formatPer(unCompleteIce?.selfCompleteOverRatio),
                circle: formatPer(unCompleteIce?.selfCompleteRatioRatio),
              }}
            />
          </AuthorityWrap>
          <AuthorityWrap permission={AuthorityEnum.unCompleteSelfOil}>
            <IndicatorCardSub
              key="unCompleteOil"
              title="《全国炸油品质管理》完成率"
              indicator={formatPer(unCompleteOil?.selfCompleteRatio)}
              tooltip={ToolTipMap[AuditTypeEnum.未完成自检 + SelfWorkSheetEnum.炸油品质]}
              columns={[
                {
                  label: '已提交自检任务门店报告数',
                  value: unCompleteOil?.selfCompleteCount,
                },
                {
                  label: '计划自检任务门店报告数',
                  value: unCompleteOil?.planSelfCount,
                },
              ]}
              percentDateType={showPercentDateType}
              percent={{
                same: formatPer(unCompleteOil?.selfCompleteOverRatio),
                circle: formatPer(unCompleteOil?.selfCompleteRatioRatio),
              }}
            />
          </AuthorityWrap>
          <AuthorityWrap permission={AuthorityEnum.unCompleteSelfClean}>
            <IndicatorCardSub
              key="unCompleteClean"
              title="《全国月清洁任务》完成率"
              indicator={formatPer(unCompleteClean?.selfCompleteRatio)}
              tooltip={ToolTipMap[AuditTypeEnum.未完成自检 + SelfWorkSheetEnum.清洁]}
              columns={[
                {
                  label: '已提交自检任务门店报告数',
                  value: unCompleteClean?.selfCompleteCount,
                },
                {
                  label: '计划自检任务门店报告数',
                  value: unCompleteClean?.planSelfCount,
                },
              ]}
              percentDateType={showPercentDateType}
              percent={{
                same: formatPer(unCompleteClean?.selfCompleteOverRatio),
                circle: formatPer(unCompleteClean?.selfCompleteRatioRatio),
              }}
            />
          </AuthorityWrap>
        </>
      ),
      [AuditTypeEnum.督导巡检完成率]: (
        <>
          <IndicatorCardV2
            key="supervision"
            title="督导巡店完成率"
            indicator={formatPer(supervision?.shopFinishRatio)}
            percent={{
              same: formatPer(supervision?.shopFinishOverRatio),
              circle: formatPer(supervision?.shopFinishRatioRatio),
            }}
            tooltip={ToolTipMap[AuditTypeEnum.督导巡检完成率]}
            // isWarning={!!alarmData?.selfShopCompleteRatioAlarm}
            columns={[
              {
                label: '已巡检门店次数',
                value: supervision?.shopCountFinish,
              },
              {
                label: '计划巡检门店次数',
                value: supervision?.shopCount,
              },
            ]}
          />
          <AuthorityWrap permission={AuthorityEnum.foodSafe}>
            <IndicatorCardSub
              key="foodSafe"
              title="《QSC&食安》巡店完成率"
              indicator={formatPer(supervision?.shopFinishRatioByFoodSafety)}
              percent={{
                same: formatPer(supervision?.shopFinishOverRatioByFoodSafety),
                circle: formatPer(supervision?.shopFinishRatioRatioByFoodSafety),
              }}
              tooltip={ToolTipMap[AuditTypeEnum.督导巡检完成率 + SupervisionEnum.食安]}
              columns={[
                {
                  label: '已巡检门店次数',
                  value: supervision?.shopCountFinishByFoodSafety,
                },
                {
                  label: '计划巡检门店次数',
                  value: supervision?.shopCountByFoodSafety,
                },
              ]}
            />
          </AuthorityWrap>
          <AuthorityWrap permission={AuthorityEnum.bySupervision}>
            <IndicatorCardSub
              key="bySupervision"
              title="《督导巡店》巡店完成率"
              indicator={formatPer(supervision?.shopFinishRatioBySupervision)}
              percent={{
                same: formatPer(supervision?.shopFinishOverRatioBySupervision),
                circle: formatPer(supervision?.shopFinishRatioRatioBySupervision),
              }}
              tooltip={ToolTipMap[AuditTypeEnum.督导巡检完成率 + SupervisionEnum.督导]}
              columns={[
                {
                  label: '已巡检门店次数',
                  value: supervision?.shopCountFinishBySupervision,
                },
                {
                  label: '计划巡检门店次数',
                  value: supervision?.shopCountBySupervision,
                },
              ]}
            />
          </AuthorityWrap>
        </>
      ),
    };

    return IndicatorMap[type] || <></>;
  },
);

export default DetailIndicator;
