/**
 * 明细列表
 */
import { CustomerInfoV2 } from '../../components';
import { AuditTypeEnum, typeObj } from '../enum';
import { useNavigate } from 'react-router-dom';
import { OverviewParams } from '../api.type';
import { stringify } from 'qs';
import { Skeleton } from 'antd-mobile';
import PageList from '@src/components/PageList';

type IProps = {
  ListData: any;
  detailType: AuditTypeEnum[];
  params: OverviewParams;
  detailSubType: 'all';
  loading?: boolean;
};
const DetailList: React.FC<IProps> = ({ ListData, detailType, params, detailSubType, loading }) => {
  const navigator = useNavigate();
  if (loading) {
    return (
      <div>
        {new Array(3).fill('').map((_v, index) => (
          <div className="mb-2 px-4 bg-white py-3" key={index}>
            <Skeleton animated className="w-[200px] h-5" />
            <Skeleton animated className="w-full h-5 mt-2" />
            <Skeleton animated className="w-full h-5 mt-2" />
          </div>
        ))}
      </div>
    );
  }
  return (
    <PageList
      dataSource={ListData || []}
      itemRender={
        detailType[0] === AuditTypeEnum.督导巡检完成率
          ? typeObj[AuditTypeEnum.督导巡检完成率].itemRender
          : (item: any, index) => (
              <div className="mb-2 px-4 bg-white" key={index}>
                <CustomerInfoV2
                  type={item.type === 1 ? 'group' : 'shop'}
                  title={
                    item.type === 1 ? (
                      item.name
                    ) : (
                      <div className="flex items-center">
                        <span>
                          {item.shopCode}
                          {item.shopName || item.name}
                        </span>
                      </div>
                    )
                  }
                  titleText={item.name || item.shopName}
                  percent={typeObj[detailType[0]].percent[item.type === 1 ? 'group' : 'shop'][
                    detailSubType
                  ](item)}
                  columns={typeObj[detailType[0]].columns[item.type === 1 ? 'group' : 'shop'][
                    detailSubType
                  ](item)}
                  manager={{
                    id: item.responsiblePersonId,
                    name: item.responsiblePersonName,
                    phone: item.responsiblePersonPhone,
                  }}
                  navNext={() => {
                    navigator('/boards/audit/list?pageType=detail', {
                      state: {
                        params: { ...params, stopId: params.groupId, groupId: item.id },
                        detailType: detailType[0],
                        detailSubType: detailSubType,
                      },
                    });
                  }}
                  navDetail={() => {
                    console.log('item', item);
                    navigator(
                      `/boards/audit/detail?${stringify({
                        ...params,
                        shopCode: item.shopCode,
                        detailType: detailType[0],
                        phone: item.responsiblePersonPhone,
                        id: item.responsiblePersonId,
                        responsiblePersonName: item.responsiblePersonName,
                        title: item.name || item.shopName,
                        detailSubType: detailSubType,
                        pageType: 'detail', //防止返回刷新
                      })}`,
                      {
                        state: {
                          percent:
                            typeObj[detailType[0]].percent[item.type === 1 ? 'group' : 'shop'][
                              detailSubType
                            ](item),
                        },
                      },
                    );
                  }}
                />
              </div>
            )
      }
    />
  );
};

export default DetailList;
