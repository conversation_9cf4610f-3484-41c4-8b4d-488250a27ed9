import dayjs from 'dayjs';
import { getDetail, getList, getSupervisionList } from './api';
import { Badge } from 'antd';
import AuthorityEnum from '@src/common/authority';
import { OverviewParams } from './api.type';
import { CustomerInfoV2 } from '../components';
import { formatPer } from '@src/utils/helper';

export enum AuditTypeEnum {
  触碰红线 = 'redLine',
  未通过巡检 = 'unPassPatrol',
  未完成整改 = 'unCompleteRectify',
  自检不合格 = 'unPassSelf',
  未完成自检 = 'unCompleteSelf',
  督导巡检完成率 = 'supervision',
}

export enum RoleClassificationEnum {
  总部 = 'HEADQUARTERS_INSPECT',
  线上 = 'OPERATE_INSPECT',
  战区 = 'FOOD_SAFETY_CLASSIFICATION_INSPECT',
  其他 = 'OTHER_CLASSIFICATION_INSPECT',
}

export enum SelfWorkSheetEnum {
  效期 = 'DAILY_CHECK',
  隔油池 = 'TANK_CLEAN',
  可乐机 = 'COKE_MACHINE_CLEAN',
  制冰机 = 'ICE_MAKER_CLEAN',
  炸油品质 = 'FRIED_QUALITY_MANAGER',
  清洁 = 'MONTHLY_CLEAN',
}

export enum SupervisionEnum {
  食安 = 'BY_FOOD_SAFETY',
  督导 = 'BY_SUPERVISION',
}

export const getTypeOptions = () => [
  {
    label: (
      <div className="flex justify-center">
        <div>稽核“S项”占比</div>
        {/* {!isGroup && <div>({data?.redLineShopCount ?? '-'})</div>} */}
      </div>
    ),
    value: AuditTypeEnum.触碰红线,
    auth: AuthorityEnum.红线门店占比,
  },
  {
    label: (
      <div className="flex">
        <div>门店稽核通过率</div>
        {/* {!isGroup && <div>({data?.unPatrolPassShopCount ?? '-'})</div>} */}
      </div>
    ),
    value: AuditTypeEnum.未通过巡检,
    auth: AuthorityEnum.门店通过率,
  },
  {
    label: (
      <div className="flex">
        <div>门店稽核整改完成率</div>
        {/* {!isGroup && <div>({data?.unRectifyShopCount ?? '-'})</div>} */}
      </div>
    ),
    value: AuditTypeEnum.未完成整改,
    auth: AuthorityEnum.门店整改完成率,
  },
  {
    label: (
      <div className="flex">
        <div>自检任务合格率</div>
        {/* {!isGroup && <div>({data?.unSelfShopPassCount ?? '-'})</div>} */}
      </div>
    ),
    value: AuditTypeEnum.自检不合格,
    auth: AuthorityEnum.自检合格率,
  },
  {
    label: (
      <div className="flex">
        <div>自检任务完成率</div>
        {/* {!isGroup && <div>({data?.unSelfCompleteShopCount ?? '-'})</div>} */}
      </div>
    ),
    value: AuditTypeEnum.未完成自检,
    auth: AuthorityEnum.自检完成率,
  },
  // {
  //   label: (
  //     <div className="flex">
  //       <div>督导巡店完成率</div>
  //     </div>
  //   ),
  //   value: AuditTypeEnum.督导巡检完成率,
  //   auth: AuthorityEnum.督导巡检完成率,
  // },
];
export const typeObj = {
  [AuditTypeEnum.触碰红线]: {
    options: [
      {
        label: '全部稽核',
        value: 'all',
      },
      {
        label: '总部食安稽核',
        value: RoleClassificationEnum.总部,
        auth: AuthorityEnum.redLineHq,
      },
      {
        label: '线上云巡检',
        value: RoleClassificationEnum.线上,
        auth: AuthorityEnum.redLineOnLine,
      },
      {
        label: '战区食安巡检',
        value: RoleClassificationEnum.战区,
        auth: AuthorityEnum.redLineOffLine,
      },
      {
        label: '其他巡检',
        value: RoleClassificationEnum.其他,
        auth: AuthorityEnum.redLineOther,
      },
    ],
    percent: {
      shop: {
        all: (item: any) => ({
          label: '全部稽核“S项”占比',
          value: formatPer(item['touchRedLineRatio']),
        }),
        [RoleClassificationEnum.总部]: (item: any) => ({
          label: '总部食安稽核“S项”占比',
          value: formatPer(item['touchRedLineRatio']),
        }),
        [RoleClassificationEnum.线上]: (item: any) => ({
          label: '线上云巡检“S项”占比',
          value: formatPer(item['touchRedLineRatio']),
        }),
        [RoleClassificationEnum.战区]: (item: any) => ({
          label: '战区食安巡检“S项”占比',
          value: formatPer(item['touchRedLineRatio']),
        }),
        [RoleClassificationEnum.其他]: (item: any) => ({
          label: '其他巡检“S项”占比',
          value: formatPer(item['touchRedLineRatio']),
        }),
      },
      group: {
        all: (item: any) => ({
          label: '全部稽核“S项”占比',
          value: formatPer(item['touchRedLineRatio']),
        }),
        [RoleClassificationEnum.总部]: (item: any) => ({
          label: '总部食安稽核“S项”占比',
          value: formatPer(item['touchRedLineRatio']),
        }),
        [RoleClassificationEnum.线上]: (item: any) => ({
          label: '线上云巡检“S项”占比',
          value: formatPer(item['touchRedLineRatio']),
        }),
        [RoleClassificationEnum.战区]: (item: any) => ({
          label: '战区食安巡检“S项”占比',
          value: formatPer(item['touchRedLineRatio']),
        }),
        [RoleClassificationEnum.其他]: (item: any) => ({
          label: '其他巡检“S项”占比',
          value: formatPer(item['touchRedLineRatio']),
        }),
      },
    },
    columns: {
      shop: {
        all: (item: any) => [
          { label: '巡检次数', value: item['patrolCount'] },
          { label: '触碰“S项”条数', value: item['touchRedLineCount'] },
        ],
        [RoleClassificationEnum.总部]: (item: any) => [
          { label: '稽核次数', value: item['patrolCount'] },
          { label: '触碰“S项”条数', value: item['touchRedLineCount'] },
        ],
        [RoleClassificationEnum.线上]: (item: any) => [
          { label: '巡检次数', value: item['patrolCount'] },
          { label: '触碰“S项”条数', value: item['touchRedLineCount'] },
        ],
        [RoleClassificationEnum.战区]: (item: any) => [
          { label: '巡检次数', value: item['patrolCount'] },
          { label: '触碰“S项”条数', value: item['touchRedLineCount'] },
        ],
        [RoleClassificationEnum.其他]: (item: any) => [
          { label: '巡检次数', value: item['patrolCount'] },
          { label: '触碰“S项”条数', value: item['touchRedLineCount'] },
        ],
      },
      group: {
        all: (item: any) => [
          { label: '巡检次数', value: item['patrolCount'] },
          { label: '触碰“S项”条数', value: item['touchRedLineCount'] },
          { label: '触碰“S项”门店数', value: item['touchRedLineShopCount'] },
        ],
        [RoleClassificationEnum.总部]: (item: any) => [
          { label: '稽核次数', value: item['patrolCount'] },
          { label: '触碰“S项”条数', value: item['touchRedLineCount'] },
          { label: '触碰“S项”门店数', value: item['touchRedLineShopCount'] },
        ],
        [RoleClassificationEnum.线上]: (item: any) => [
          { label: '巡检次数', value: item['patrolCount'] },
          { label: '触碰“S项”条数', value: item['touchRedLineCount'] },
          { label: '触碰“S项”门店数', value: item['touchRedLineShopCount'] },
        ],
        [RoleClassificationEnum.战区]: (item: any) => [
          { label: '巡检次数', value: item['patrolCount'] },
          { label: '触碰“S项”条数', value: item['touchRedLineCount'] },
          { label: '触碰“S项”门店数', value: item['touchRedLineShopCount'] },
        ],
        [RoleClassificationEnum.其他]: (item: any) => [
          { label: '巡检次数', value: item['patrolCount'] },
          { label: '触碰“S项”条数', value: item['touchRedLineCount'] },
          { label: '触碰“S项”门店数', value: item['touchRedLineShopCount'] },
        ],
      },
    },
    sortConfig: {
      shop: [
        { label: '“S项”占比最高', value: 'touchRedLineRatio-desc' },
        { label: '“S项”占比最低', value: 'touchRedLineRatio-asc' },
        { label: '触碰“S项“条数最多', value: 'touchRedLineCount-desc' },
        { label: '触碰“S项“条数最少', value: 'touchRedLineCount-asc' },
      ],
      group: [
        { label: '“S项”占比最高', value: 'touchRedLineRatio-desc' },
        { label: '“S项”占比最低', value: 'touchRedLineRatio-asc' },
        { label: '触碰“S项“条数最多', value: 'touchRedLineCount-desc' },
        { label: '触碰“S项“条数最少', value: 'touchRedLineCount-asc' },
      ],
    },
    getList: {
      shop: (data: OverviewParams) => getList(data, 'redLine', 'Shop'),
      group: (data: OverviewParams) => getList(data, 'redLine', 'Group'),
    },
    getDetail: (data: OverviewParams) => getDetail(data, 'redLine'),
    detailColumns: [
      { title: '报告名称', dataIndex: 'reportName' },
      {
        title: 'S项',
        dataIndex: 'redLineItems',
        render: (text: { remark: string }[]) => !!text.length && text[0]?.remark,
      },
      {
        title: '提交时间',
        // width: 110,
        dataIndex: 'reportSubmitTime',
        // render: (val) => (val ? dayjs(val).format('MM/DD HH:mm') : '-'),
        sorter: (a: any, b: any) =>
          dayjs(a.reportSubmitTime).valueOf() - dayjs(b.reportSubmitTime).valueOf(),
      },
    ],
    expandable: {
      expandedRowRender: (record: any) => (
        <div className="p-2">
          <div className="flex justify-between items-center mb-3">
            <div>{record.reportName}</div>
            <div>{dayjs(record.reportSubmitTime).format('YYYY-MM-DD HH:mm:ss')}</div>
          </div>
          <div>S项</div>
          {record.redLineItems.map((item: { remark: string; checkItemRefTaskId: number }) => (
            <div className="my-1" key={item.checkItemRefTaskId}>
              <Badge status="error" />
              {item?.remark}
            </div>
          ))}
        </div>
      ),
      rowExpandable: (record: any) => record?.redLineItems?.length > 1,
    },
  },
  [AuditTypeEnum.未通过巡检]: {
    options: [
      {
        label: '全部稽核通过率',
        value: 'all',
      },
      {
        label: '总部食安稽核通过率',
        value: RoleClassificationEnum.总部,
        auth: AuthorityEnum.unPassPatrolHq,
      },
      {
        label: '线上云巡检通过率',
        value: RoleClassificationEnum.线上,
        auth: AuthorityEnum.unPassPatrolOnLine,
      },
      {
        label: '战区食安巡检通过率',
        value: RoleClassificationEnum.战区,
        auth: AuthorityEnum.unPassPatrolOffLine,
      },
      {
        label: '其他巡检通过率',
        value: RoleClassificationEnum.其他,
        auth: AuthorityEnum.unPassPatrolOther,
      },
    ],
    percent: {
      shop: {
        all: (item: any) => ({
          label: '门店稽核通过率',
          value: formatPer(item['patrolPassRatio']),
        }),
        [RoleClassificationEnum.总部]: (item: any) => ({
          label: '总部食安稽核通过率',
          value: formatPer(item['patrolPassRatio']),
        }),
        [RoleClassificationEnum.线上]: (item: any) => ({
          label: '线上云巡检通过率',
          value: formatPer(item['patrolPassRatio']),
        }),
        [RoleClassificationEnum.战区]: (item: any) => ({
          label: '战区食安巡检通过率',
          value: formatPer(item['patrolPassRatio']),
        }),
        [RoleClassificationEnum.其他]: (item: any) => ({
          label: '其他巡检通过率',
          value: formatPer(item['patrolPassRatio']),
        }),
      },
      group: {
        all: (item: any) => ({
          label: '门店稽核通过率',
          value: formatPer(item['patrolPassRatio']),
        }),
        [RoleClassificationEnum.总部]: (item: any) => ({
          label: '总部食安稽核通过率',
          value: formatPer(item['patrolPassRatio']),
        }),
        [RoleClassificationEnum.线上]: (item: any) => ({
          label: '线上云巡检通过率',
          value: formatPer(item['patrolPassRatio']),
        }),
        [RoleClassificationEnum.战区]: (item: any) => ({
          label: '战区食安巡检通过率',
          value: formatPer(item['patrolPassRatio']),
        }),
        [RoleClassificationEnum.其他]: (item: any) => ({
          label: '其他巡检通过率',
          value: formatPer(item['patrolPassRatio']),
        }),
      },
    },
    columns: {
      shop: {
        all: (item: any) => [
          { label: '稽核通过报告数', value: item['patrolPassCount'] },
          { label: '总稽核报告数', value: item['patrolCount'] },
        ],
        [RoleClassificationEnum.总部]: (item: any) => [
          { label: '食安中心稽核通过报告数', value: item['patrolPassCount'] },
          { label: '食安中心稽核报告数', value: item['patrolCount'] },
        ],
        [RoleClassificationEnum.线上]: (item: any) => [
          { label: '线上云巡检通过报告数', value: item['patrolPassCount'] },
          { label: '线上云总巡检报告数', value: item['patrolCount'] },
        ],
        [RoleClassificationEnum.战区]: (item: any) => [
          { label: '战区食安巡检通过报告数', value: item['patrolPassCount'] },
          { label: '战区食安总巡检报告数', value: item['patrolCount'] },
        ],
        [RoleClassificationEnum.其他]: (item: any) => [
          { label: '其他巡检通过报告数', value: item['patrolPassCount'] },
          { label: '其他巡检报告数', value: item['patrolCount'] },
        ],
      },
      group: {
        all: (item: any) => [
          { label: '稽核通过报告数', value: item['patrolPassCount'] },
          { label: '总稽核报告数', value: item['patrolCount'] },
        ],
        [RoleClassificationEnum.总部]: (item: any) => [
          { label: '食安中心稽核通过报告数', value: item['patrolPassCount'] },
          { label: '食安中心稽核报告数', value: item['patrolCount'] },
        ],
        [RoleClassificationEnum.线上]: (item: any) => [
          { label: '线上云巡检通过报告数', value: item['patrolPassCount'] },
          { label: '线上云总巡检报告数', value: item['patrolCount'] },
        ],
        [RoleClassificationEnum.战区]: (item: any) => [
          { label: '战区食安巡检通过报告数', value: item['patrolPassCount'] },
          { label: '战区食安总巡检报告数', value: item['patrolCount'] },
        ],
        [RoleClassificationEnum.其他]: (item: any) => [
          { label: '其他巡检通过报告数', value: item['patrolPassCount'] },
          { label: '其他巡检报告数', value: item['patrolCount'] },
        ],
      },
    },
    sortConfig: {
      shop: [
        { label: '稽核通过率最高', value: 'patrolPassRatio-desc' },
        { label: '稽核通过率最低', value: 'patrolPassRatio-asc' },
        { label: '稽核通过报告数最多', value: 'patrolPassCount-desc' },
        { label: '稽核通过报告数最少', value: 'patrolPassCount-asc' },
      ],
      group: [
        { label: '稽核通过率最高', value: 'patrolPassRatio-desc' },
        { label: '稽核通过率最低', value: 'patrolPassRatio-asc' },
        { label: '稽核通过报告数最多', value: 'patrolPassCount-desc' },
        { label: '稽核通过报告数最少', value: 'patrolPassCount-asc' },
      ],
    },
    getList: {
      shop: (data: OverviewParams) => getList(data, 'patrol/unPass', 'Shop'),
      group: (data: OverviewParams) => getList(data, 'patrol/unPass', 'Group'),
    },
    getDetail: (data: OverviewParams) => getDetail(data, 'patrol/unPass'),
    detailColumns: [
      { title: '报告名称', dataIndex: 'reportName' },
      {
        title: '得分',
        dataIndex: 'reportScore',
        sorter: (a: any, b: any) => a.reportScore - b.reportScore,
      },
      {
        title: '提交时间',
        dataIndex: 'reportSubmitTime',
        sorter: (a: any, b: any) =>
          dayjs(a.reportSubmitTime).valueOf() - dayjs(b.reportSubmitTime).valueOf(),
      },
    ],
    expandable: {},
  },
  [AuditTypeEnum.未完成整改]: {
    options: [
      {
        label: '全部稽核整改完成率',
        value: 'all',
      },
      {
        label: '总部食安稽核整改完成率',
        value: RoleClassificationEnum.总部,
        auth: AuthorityEnum.unCompleteRectifyHq,
      },
      {
        label: '线上云巡检整改完成率',
        value: RoleClassificationEnum.线上,
        auth: AuthorityEnum.unCompleteRectifyOnLine,
      },
      {
        label: '战区食安巡检整改完成率',
        value: RoleClassificationEnum.战区,
        auth: AuthorityEnum.unCompleteRectifyOffLine,
      },
      {
        label: '其他巡检整改完成率',
        value: RoleClassificationEnum.其他,
        auth: AuthorityEnum.unCompleteRectifyOther,
      },
    ],
    percent: {
      shop: {
        all: (item: any) => ({
          label: '门店整改完成率',
          value: formatPer(item['rectifyRatio']),
        }),
        [RoleClassificationEnum.总部]: (item: any) => ({
          label: '总部食安稽核整改完成率',
          value: formatPer(item['rectifyRatio']),
        }),
        [RoleClassificationEnum.线上]: (item: any) => ({
          label: '线上云巡检整改完成率',
          value: formatPer(item['rectifyRatio']),
        }),
        [RoleClassificationEnum.战区]: (item: any) => ({
          label: '战区食安整改完成率',
          value: formatPer(item['rectifyRatio']),
        }),
        [RoleClassificationEnum.其他]: (item: any) => ({
          label: '其他巡检整改完成率',
          value: formatPer(item['rectifyRatio']),
        }),
      },
      group: {
        all: (item: any) => ({
          label: '门店整改完成率',
          value: formatPer(item['rectifyRatio']),
        }),
        [RoleClassificationEnum.总部]: (item: any) => ({
          label: '总部食安稽核整改完成率',
          value: formatPer(item['rectifyRatio']),
        }),
        [RoleClassificationEnum.线上]: (item: any) => ({
          label: '线上云巡检整改完成率',
          value: formatPer(item['rectifyRatio']),
        }),
        [RoleClassificationEnum.战区]: (item: any) => ({
          label: '战区食安整改完成率',
          value: formatPer(item['rectifyRatio']),
        }),
        [RoleClassificationEnum.其他]: (item: any) => ({
          label: '其他巡检整改完成率',
          value: formatPer(item['rectifyRatio']),
        }),
      },
    },
    columns: {
      shop: {
        all: (item: any) => [
          { label: '已完成整改的报告数', value: item['rectifyCount'] },
          { label: '需整改的报告数', value: item['unRectifyCount'] },
        ],
        [RoleClassificationEnum.总部]: (item: any) => [
          { label: '总部食安稽核已完成整改的报告数', value: item['rectifyCount'] },
          { label: '总部食安稽核需整改的报告数', value: item['unRectifyCount'] },
        ],
        [RoleClassificationEnum.线上]: (item: any) => [
          { label: '线上云巡检已完成整改的报告数', value: item['rectifyCount'] },
          { label: '线上云巡检需整改的报告数', value: item['unRectifyCount'] },
        ],
        [RoleClassificationEnum.战区]: (item: any) => [
          { label: '战区食安已完成整改的报告数', value: item['rectifyCount'] },
          { label: '战区食安需整改的报告数', value: item['unRectifyCount'] },
        ],
        [RoleClassificationEnum.其他]: (item: any) => [
          { label: '其他巡检已完成整改的报告数', value: item['rectifyCount'] },
          { label: '其他巡检需整改的报告数', value: item['unRectifyCount'] },
        ],
      },
      group: {
        all: (item: any) => [
          { label: '已完成整改的报告数', value: item['rectifyCount'] },
          { label: '需整改的报告数', value: item['unRectifyCount'] },
        ],
        [RoleClassificationEnum.总部]: (item: any) => [
          { label: '总部食安稽核已完成整改的报告数', value: item['rectifyCount'] },
          { label: '总部食安稽核需整改的报告数', value: item['unRectifyCount'] },
        ],
        [RoleClassificationEnum.线上]: (item: any) => [
          { label: '线上云巡检已完成整改的报告数', value: item['rectifyCount'] },
          { label: '线上云巡检需整改的报告数', value: item['unRectifyCount'] },
        ],
        [RoleClassificationEnum.战区]: (item: any) => [
          { label: '战区食安已完成整改的报告数', value: item['rectifyCount'] },
          { label: '战区食安需整改的报告数', value: item['unRectifyCount'] },
        ],
        [RoleClassificationEnum.其他]: (item: any) => [
          { label: '其他巡检已完成整改的报告数', value: item['rectifyCount'] },
          { label: '其他巡检需整改的报告数', value: item['unRectifyCount'] },
        ],
      },
    },
    sortConfig: {
      shop: [
        { label: '整改完成率最高', value: 'rectifyRatio-desc' },
        { label: '整改完成率最低', value: 'rectifyRatio-asc' },
        { label: '需整改报告数最多', value: 'unRectifyCount-desc' },
        { label: '需整改报告数最少', value: 'unRectifyCount-asc' },
      ],
      group: [
        { label: '整改完成率最高', value: 'rectifyRatio-desc' },
        { label: '整改完成率最低', value: 'rectifyRatio-asc' },
        { label: '需整改报告数最多', value: 'unRectifyCount-desc' },
        { label: '需整改报告数最少', value: 'unRectifyCount-asc' },
      ],
    },
    getList: {
      shop: (data: OverviewParams) => getList(data, 'rectify/unComplete', 'Shop'),
      group: (data: OverviewParams) => getList(data, 'rectify/unComplete', 'Group'),
    },
    getDetail: (data: OverviewParams) => getDetail(data, 'rectify/unComplete'),
    detailColumns: [
      { title: '报告名称', dataIndex: 'reportName' },
      {
        title: '整改项',
        dataIndex: 'issueReportItems',
        render: (text: { remark: string }[]) => !!text.length && text[0]?.remark,
      },
      {
        title: '提交时间',
        dataIndex: 'reportSubmitTime',
        sorter: (a: any, b: any) =>
          dayjs(a.reportSubmitTime).valueOf() - dayjs(b.reportSubmitTime).valueOf(),
      },
    ],
    expandable: {
      expandedRowRender: (record: any) => (
        <div className="p-2">
          <div className="flex justify-between items-center mb-3">
            <div>{record.reportName}</div>
            <div>{dayjs(record.reportSubmitTime).format('YYYY-MM-DD HH:mm:ss')}</div>
          </div>
          <div>整改项</div>
          {record.issueReportItems.map((item: { remark: string; issueReportId: number }) => (
            <div className="my-1" key={item?.issueReportId}>
              <Badge status="error" />
              {item?.remark}
            </div>
          ))}
        </div>
      ),
      rowExpandable: (record: any) => record?.issueReportItems?.length > 1,
    },
  },
  [AuditTypeEnum.自检不合格]: {
    options: [
      {
        label: '全部自检合格率',
        value: 'all',
      },
      {
        label: '《全国每日效期检查》合格率',
        value: SelfWorkSheetEnum.效期,
        auth: AuthorityEnum.unPassSelfDaily,
      },
      {
        label: '《全国隔油池清洁》合格率',
        value: SelfWorkSheetEnum.隔油池,
        auth: AuthorityEnum.unPassSelfTank,
      },
      {
        label: '《全国可乐机清洁》合格率',
        value: SelfWorkSheetEnum.可乐机,
        auth: AuthorityEnum.unPassSelfCoke,
      },
      {
        label: '《全国制冰机清洁》合格率',
        value: SelfWorkSheetEnum.制冰机,
        auth: AuthorityEnum.unPassSelfIce,
      },
      {
        label: '《全国炸油品质管理》合格率',
        value: SelfWorkSheetEnum.炸油品质,
        auth: AuthorityEnum.unPassSelfOil,
      },
      {
        label: '《全国月清洁任务》合格率',
        value: SelfWorkSheetEnum.清洁,
        auth: AuthorityEnum.unPassSelfClean,
      },
    ],
    percent: {
      shop: {
        all: (item: any) => ({ label: '自检任务合格率', value: formatPer(item['selfPassRatio']) }),
        [SelfWorkSheetEnum.效期]: (item: any) => ({
          label: '自检任务合格率',
          value: formatPer(item['selfPassRatio']),
        }),
        [SelfWorkSheetEnum.隔油池]: (item: any) => ({
          label: '自检任务合格率',
          value: formatPer(item['selfPassRatio']),
        }),
        [SelfWorkSheetEnum.可乐机]: (item: any) => ({
          label: '自检任务合格率',
          value: formatPer(item['selfPassRatio']),
        }),
        [SelfWorkSheetEnum.制冰机]: (item: any) => ({
          label: '自检任务合格率',
          value: formatPer(item['selfPassRatio']),
        }),
        [SelfWorkSheetEnum.炸油品质]: (item: any) => ({
          label: '自检任务合格率',
          value: formatPer(item['selfPassRatio']),
        }),
        [SelfWorkSheetEnum.清洁]: (item: any) => ({
          label: '自检任务合格率',
          value: formatPer(item['selfPassRatio']),
        }),
      },
      group: {
        all: (item: any) => ({ label: '自检任务合格率', value: formatPer(item['selfPassRatio']) }),
        [SelfWorkSheetEnum.效期]: (item: any) => ({
          label: '自检任务合格率',
          value: formatPer(item['selfPassRatio']),
        }),
        [SelfWorkSheetEnum.隔油池]: (item: any) => ({
          label: '自检任务合格率',
          value: formatPer(item['selfPassRatio']),
        }),
        [SelfWorkSheetEnum.可乐机]: (item: any) => ({
          label: '自检任务合格率',
          value: formatPer(item['selfPassRatio']),
        }),
        [SelfWorkSheetEnum.制冰机]: (item: any) => ({
          label: '自检任务合格率',
          value: formatPer(item['selfPassRatio']),
        }),
        [SelfWorkSheetEnum.炸油品质]: (item: any) => ({
          label: '自检任务合格率',
          value: formatPer(item['selfPassRatio']),
        }),
        [SelfWorkSheetEnum.清洁]: (item: any) => ({
          label: '自检任务合格率',
          value: formatPer(item['selfPassRatio']),
        }),
      },
    },
    columns: {
      shop: {
        all: (item: any) => [
          { label: '任务点评合格报告数', value: item['selfPassCount'] },
          { label: '任务已点评报告数', value: item['selfCount'] },
        ],
        [SelfWorkSheetEnum.效期]: (item: any) => [
          { label: '任务点评合格报告数', value: item['selfPassCount'] },
          { label: '任务已点评报告数', value: item['selfCount'] },
        ],
        [SelfWorkSheetEnum.隔油池]: (item: any) => [
          { label: '任务点评合格报告数', value: item['selfPassCount'] },
          { label: '任务已点评报告数', value: item['selfCount'] },
        ],
        [SelfWorkSheetEnum.可乐机]: (item: any) => [
          { label: '任务点评合格报告数', value: item['selfPassCount'] },
          { label: '任务已点评报告数', value: item['selfCount'] },
        ],
        [SelfWorkSheetEnum.制冰机]: (item: any) => [
          { label: '任务点评合格报告数', value: item['selfPassCount'] },
          { label: '任务已点评报告数', value: item['selfCount'] },
        ],
        [SelfWorkSheetEnum.炸油品质]: (item: any) => [
          { label: '任务点评合格报告数', value: item['selfPassCount'] },
          { label: '任务已点评报告数', value: item['selfCount'] },
        ],
        [SelfWorkSheetEnum.清洁]: (item: any) => [
          { label: '任务点评合格报告数', value: item['selfPassCount'] },
          { label: '任务已点评报告数', value: item['selfCount'] },
        ],
      },
      group: {
        all: (item: any) => [
          { label: '任务点评合格报告数', value: item['selfPassCount'] },
          { label: '任务已点评报告数', value: item['selfCount'] },
        ],
        [SelfWorkSheetEnum.效期]: (item: any) => [
          { label: '任务点评合格报告数', value: item['selfPassCount'] },
          { label: '任务已点评报告数', value: item['selfCount'] },
        ],
        [SelfWorkSheetEnum.隔油池]: (item: any) => [
          { label: '任务点评合格报告数', value: item['selfPassCount'] },
          { label: '任务已点评报告数', value: item['selfCount'] },
        ],
        [SelfWorkSheetEnum.可乐机]: (item: any) => [
          { label: '任务点评合格报告数', value: item['selfPassCount'] },
          { label: '任务已点评报告数', value: item['selfCount'] },
        ],
        [SelfWorkSheetEnum.制冰机]: (item: any) => [
          { label: '任务点评合格报告数', value: item['selfPassCount'] },
          { label: '任务已点评报告数', value: item['selfCount'] },
        ],
        [SelfWorkSheetEnum.炸油品质]: (item: any) => [
          { label: '任务点评合格报告数', value: item['selfPassCount'] },
          { label: '任务已点评报告数', value: item['selfCount'] },
        ],
        [SelfWorkSheetEnum.清洁]: (item: any) => [
          { label: '任务点评合格报告数', value: item['selfPassCount'] },
          { label: '任务已点评报告数', value: item['selfCount'] },
        ],
      },
    },
    sortConfig: {
      shop: [
        { label: '自检任务合格率最高', value: 'selfPassRatio-desc' },
        { label: '自检任务合格率最低', value: 'selfPassRatio-asc' },
        { label: '任务点评合格报告数最多', value: 'selfPassCount-desc' },
        { label: '任务点评合格报告数最少', value: 'selfPassCount-asc' },
      ],
      group: [
        { label: '自检任务合格率最高', value: 'selfPassRatio-desc' },
        { label: '自检任务合格率最低', value: 'selfPassRatio-asc' },
        { label: '任务点评合格报告数最多', value: 'selfPassCount-desc' },
        { label: '任务点评合格报告数最少', value: 'selfPassCount-asc' },
      ],
    },
    getList: {
      shop: (data: OverviewParams) => getList(data, 'self/unPass', 'Shop'),
      group: (data: OverviewParams) => getList(data, 'self/unPass', 'Group'),
    },
    getDetail: (data: OverviewParams) => getDetail(data, 'self/unPass'),
    detailColumns: [
      { title: '报告名称', dataIndex: 'taskName' },
      {
        title: '得分',
        dataIndex: 'reportScore',
        sorter: (a: any, b: any) => a.reportScore - b.reportScore,
      },
      {
        title: '提交时间',
        dataIndex: 'reportSubmitTime',
        sorter: (a: any, b: any) =>
          dayjs(a.reportSubmitTime).valueOf() - dayjs(b.reportSubmitTime).valueOf(),
      },
    ],
    expandable: {},
  },
  [AuditTypeEnum.未完成自检]: {
    options: [
      {
        label: '全部自检完成率',
        value: 'all',
      },
      {
        label: '《全国每日效期检查》完成率',
        value: SelfWorkSheetEnum.效期,
        auth: AuthorityEnum.unCompleteSelfDaily,
      },
      {
        label: '《全国隔油池清洁》完成率',
        value: SelfWorkSheetEnum.隔油池,
        auth: AuthorityEnum.unCompleteSelfTank,
      },
      {
        label: '《全国可乐机清洁》完成率',
        value: SelfWorkSheetEnum.可乐机,
        auth: AuthorityEnum.unCompleteSelfCoke,
      },
      {
        label: '《全国制冰机清洁》完成率',
        value: SelfWorkSheetEnum.制冰机,
        auth: AuthorityEnum.unCompleteSelfIce,
      },
      {
        label: '《全国炸油品质管理》完成率',
        value: SelfWorkSheetEnum.炸油品质,
        auth: AuthorityEnum.unCompleteSelfOil,
      },
      {
        label: '《全国月清洁任务》完成率',
        value: SelfWorkSheetEnum.清洁,
        auth: AuthorityEnum.unCompleteSelfClean,
      },
    ],
    percent: {
      shop: {
        all: (item: any) => ({
          label: '自检任务完成率',
          value: formatPer(item['selfCompleteRatio']),
        }),
        [SelfWorkSheetEnum.效期]: (item: any) => ({
          label: '自检任务完成率',
          value: formatPer(item['selfCompleteRatio']),
        }),
        [SelfWorkSheetEnum.隔油池]: (item: any) => ({
          label: '自检任务完成率',
          value: formatPer(item['selfCompleteRatio']),
        }),
        [SelfWorkSheetEnum.可乐机]: (item: any) => ({
          label: '自检任务完成率',
          value: formatPer(item['selfCompleteRatio']),
        }),
        [SelfWorkSheetEnum.制冰机]: (item: any) => ({
          label: '自检任务完成率',
          value: formatPer(item['selfCompleteRatio']),
        }),
        [SelfWorkSheetEnum.炸油品质]: (item: any) => ({
          label: '自检任务完成率',
          value: formatPer(item['selfCompleteRatio']),
        }),
        [SelfWorkSheetEnum.清洁]: (item: any) => ({
          label: '自检任务完成率',
          value: formatPer(item['selfCompleteRatio']),
        }),
      },
      group: {
        all: (item: any) => ({
          label: '自检任务完成率',
          value: formatPer(item['selfCompleteRatio']),
        }),
        [SelfWorkSheetEnum.效期]: (item: any) => ({
          label: '自检任务完成率',
          value: formatPer(item['selfCompleteRatio']),
        }),
        [SelfWorkSheetEnum.隔油池]: (item: any) => ({
          label: '自检任务完成率',
          value: formatPer(item['selfCompleteRatio']),
        }),
        [SelfWorkSheetEnum.可乐机]: (item: any) => ({
          label: '自检任务完成率',
          value: formatPer(item['selfCompleteRatio']),
        }),
        [SelfWorkSheetEnum.制冰机]: (item: any) => ({
          label: '自检任务完成率',
          value: formatPer(item['selfCompleteRatio']),
        }),
        [SelfWorkSheetEnum.炸油品质]: (item: any) => ({
          label: '自检任务完成率',
          value: formatPer(item['selfCompleteRatio']),
        }),
        [SelfWorkSheetEnum.清洁]: (item: any) => ({
          label: '自检任务完成率',
          value: formatPer(item['selfCompleteRatio']),
        }),
      },
    },
    columns: {
      shop: {
        all: (item: any) => [
          { label: '已提交自检任务门店报告数', value: item['selfCompleteCount'] },
          { label: '计划自检任务门店报告数', value: item['planSelfCount'] },
        ],
        [SelfWorkSheetEnum.效期]: (item: any) => [
          { label: '已提交自检任务门店报告数', value: item['selfCompleteCount'] },
          { label: '计划自检任务门店报告数', value: item['planSelfCount'] },
        ],
        [SelfWorkSheetEnum.隔油池]: (item: any) => [
          { label: '已提交自检任务门店报告数', value: item['selfCompleteCount'] },
          { label: '计划自检任务门店报告数', value: item['planSelfCount'] },
        ],
        [SelfWorkSheetEnum.可乐机]: (item: any) => [
          { label: '已提交自检任务门店报告数', value: item['selfCompleteCount'] },
          { label: '计划自检任务门店报告数', value: item['planSelfCount'] },
        ],
        [SelfWorkSheetEnum.制冰机]: (item: any) => [
          { label: '已提交自检任务门店报告数', value: item['selfCompleteCount'] },
          { label: '计划自检任务门店报告数', value: item['planSelfCount'] },
        ],
        [SelfWorkSheetEnum.炸油品质]: (item: any) => [
          { label: '已提交自检任务门店报告数', value: item['selfCompleteCount'] },
          { label: '计划自检任务门店报告数', value: item['planSelfCount'] },
        ],
        [SelfWorkSheetEnum.清洁]: (item: any) => [
          { label: '已提交自检任务门店报告数', value: item['selfCompleteCount'] },
          { label: '计划自检任务门店报告数', value: item['planSelfCount'] },
        ],
      },
      group: {
        all: (item: any) => [
          { label: '已提交自检任务门店报告数', value: item['selfCompleteCount'] },
          { label: '计划自检任务门店报告数', value: item['planSelfCount'] },
        ],
        [SelfWorkSheetEnum.效期]: (item: any) => [
          { label: '已提交自检任务门店报告数', value: item['selfCompleteCount'] },
          { label: '计划自检任务门店报告数', value: item['planSelfCount'] },
        ],
        [SelfWorkSheetEnum.隔油池]: (item: any) => [
          { label: '已提交自检任务门店报告数', value: item['selfCompleteCount'] },
          { label: '计划自检任务门店报告数', value: item['planSelfCount'] },
        ],
        [SelfWorkSheetEnum.可乐机]: (item: any) => [
          { label: '已提交自检任务门店报告数', value: item['selfCompleteCount'] },
          { label: '计划自检任务门店报告数', value: item['planSelfCount'] },
        ],
        [SelfWorkSheetEnum.制冰机]: (item: any) => [
          { label: '已提交自检任务门店报告数', value: item['selfCompleteCount'] },
          { label: '计划自检任务门店报告数', value: item['planSelfCount'] },
        ],
        [SelfWorkSheetEnum.炸油品质]: (item: any) => [
          { label: '已提交自检任务门店报告数', value: item['selfCompleteCount'] },
          { label: '计划自检任务门店报告数', value: item['planSelfCount'] },
        ],
        [SelfWorkSheetEnum.清洁]: (item: any) => [
          { label: '已提交自检任务门店报告数', value: item['selfCompleteCount'] },
          { label: '计划自检任务门店报告数', value: item['planSelfCount'] },
        ],
      },
    },
    sortConfig: {
      shop: [
        { label: '自检任务完成率最高', value: 'selfCompleteRatio-desc' },
        { label: '自检任务完成率最低', value: 'selfCompleteRatio-asc' },
        { label: '已提交自检任务报告数最多', value: 'selfCompleteCount-desc' },
        { label: '已提交自检任务报告数最少', value: 'selfCompleteCount-asc' },
      ],
      group: [
        { label: '自检任务完成率最高', value: 'selfCompleteRatio-desc' },
        { label: '自检任务完成率最低', value: 'selfCompleteRatio-asc' },
        { label: '已提交自检任务报告数最多', value: 'selfCompleteCount-desc' },
        { label: '已提交自检任务报告数最少', value: 'selfCompleteCount-asc' },
      ],
    },
    getList: {
      shop: (data: OverviewParams) => getList(data, 'self/unComplete', 'Shop'),
      group: (data: OverviewParams) => getList(data, 'self/unComplete', 'Group'),
    },
    getDetail: (data: OverviewParams) => getDetail(data, 'self/unComplete'),
    detailColumns: [
      { title: '报告名称', dataIndex: 'taskName' },
      {
        title: '自检时间',
        dataIndex: 'selfDate',
        sorter: (a: any, b: any) => dayjs(a.selfDate).valueOf() - dayjs(b.selfDate).valueOf(),
      },
    ],
    expandable: {},
  },
  [AuditTypeEnum.督导巡检完成率]: {
    options: [
      {
        label: '全部巡店完成率',
        value: 'all',
      },
      {
        label: '《QSC&食安》巡店完成率',
        value: SupervisionEnum.食安,
        auth: AuthorityEnum.foodSafe,
      },
      {
        label: '《督导巡店》巡店完成率',
        value: SupervisionEnum.督导,
        auth: AuthorityEnum.bySupervision,
      },
    ],
    itemRender: (item: any, index: number) => (
      <div className="mb-2 px-4 pb-[1px] bg-white" key={index}>
        <CustomerInfoV2
          type={'shop'}
          title={item.userName}
          percent={{ label: '督导巡店完成率', value: formatPer(item['shopFinishRatio']) }}
          columns={[
            { label: '已巡检门店次数', value: item['shopFinishCount'] },
            { label: '计划巡检门店次数', value: item['shopCount'] },
          ]}
          hiddenFooter={true}
        />
      </div>
    ),
    percent: {
      shop: {
        all: () =>
          ({}) as {
            label: string;
            value: string;
          },
      },
      group: {
        all: () =>
          ({}) as {
            label: string;
            value: string;
          },
      },
    },
    columns: {
      shop: {
        all: () =>
          [] as {
            label: string;
            value: string;
          }[],
      },
      group: {
        all: () =>
          [] as {
            label: string;
            value: string;
          }[],
      },
    },
    sortConfig: {
      shop: [
        { label: '巡店完成率最高', value: 'shopFinishRatio-desc' },
        { label: '巡店完成率最低', value: 'shopFinishRatio-asc' },
      ],
      group: [
        { label: '巡店完成率最高', value: 'shopFinishRatio-desc' },
        { label: '巡店完成率最低', value: 'shopFinishRatio-asc' },
      ],
    },
    getList: {
      shop: (data: OverviewParams, extra?: { month: string }) =>
        getSupervisionList({
          ...data,
          beginDate: dayjs(extra?.month).startOf('M').format('YYYY-MM-DD'),
          endDate:
            dayjs(extra?.month).format('YYYY-MM') === dayjs().format('YYYY-MM')
              ? dayjs().format('YYYY-MM-DD')
              : dayjs(extra?.month).endOf('M').format('YYYY-MM-DD'),
        }),
      group: (data: OverviewParams, extra?: { month: string }) =>
        getSupervisionList({
          ...data,
          beginDate: dayjs(extra?.month).startOf('M').format('YYYY-MM-DD'),
          endDate:
            dayjs(extra?.month).format('YYYY-MM') === dayjs().format('YYYY-MM')
              ? dayjs().format('YYYY-MM-DD')
              : dayjs(extra?.month).endOf('M').format('YYYY-MM-DD'),
        }),
    },
    getDetail: (_data: OverviewParams) => {},
    detailColumns: [],
    expandable: {},
  },
};

export const AuditDetailTitleMap = {
  [AuditTypeEnum.触碰红线]: '触碰红线',
  [AuditTypeEnum.未通过巡检]: '未通过巡检',
  [AuditTypeEnum.未完成整改]: '未完成整改',
  [AuditTypeEnum.自检不合格]: '自检不合格',
  [AuditTypeEnum.未完成自检]: '未完成自检',
  [AuditTypeEnum.督导巡检完成率]: '督导巡店完成率',
};

export enum PatrolTypeEnum {
  全部巡检 = 'all',
  线上巡检 = '1',
  线下巡检 = '0',
}

export const patrolTypeOptions = [
  {
    label: '全部巡检',
    value: PatrolTypeEnum.全部巡检,
  },
  {
    label: '线上巡检',
    value: PatrolTypeEnum.线上巡检,
  },
  {
    label: '线下巡检',
    value: PatrolTypeEnum.线下巡检,
  },
];

export enum RectifyTypeEnum {
  全部整改 = 'all',
  线上整改 = '1',
  线下整改 = '0',
}

export const rectifyTypeOptions = [
  {
    label: '全部整改',
    value: RectifyTypeEnum.全部整改,
  },
  {
    label: '线上整改',
    value: RectifyTypeEnum.线上整改,
  },
  {
    label: '线下整改',
    value: RectifyTypeEnum.线下整改,
  },
];

export const ToolTipMap = {
  [AuditTypeEnum.触碰红线]: [
    '1.稽核“S项”占比=巡检报告中触碰"S项"条数/总巡检次数*100%',
    '2.稽核“S项”占比数据包含：总部食安稽核、线上云巡检、战区食安巡检、其他巡检的数据',
    '3.其他巡检为运营督导、运营经理、省运营经理、训练部人员巡检的报告',
    '4.检查表：《食安＆QSC检查表》、《线上巡检检查表》',
  ],
  [AuditTypeEnum.触碰红线 + RoleClassificationEnum.总部]: [
    '1.计算公式：食安中心稽核部稽核触碰“S项”条数/食安中心稽核部总稽核次数*100%',
    '2.检查表：仅统计《食安&QSC检查表》的数据',
  ],
  [AuditTypeEnum.触碰红线 + RoleClassificationEnum.线上]: [
    '1.计算公式：线上运营中心巡检触碰“S项”条数/线上运营中心总巡检次数*100%',
    '2.检查表：仅统计《线上巡检检查表》的数据',
  ],
  [AuditTypeEnum.触碰红线 + RoleClassificationEnum.战区]: [
    '1.计算公式：战区食安部巡检触碰“S项”条数/战区食安部总巡检次数*100%',
    '2.检查表：仅统计《食安&QSC检查表》的数据',
  ],
  [AuditTypeEnum.触碰红线 + RoleClassificationEnum.其他]: [
    '1.计算公式：运营督导、运营经理、省运营经理、训练部巡检触碰“S项”条数/运营督导、运营经理、省运营经理、训练部总巡检次数*100%',
    '2.检查表：仅统计《食安&QSC检查表》的数据',
  ],
  [AuditTypeEnum.未通过巡检]: [
    '1.门店稽核通过率=稽核通过报告数/总稽核报告数*100%',
    '2.稽核通过率数据包含：总部食安稽核、线上云巡检、战区食安巡检、其他巡检的数据',
    '3.其他巡检为运营督导、运营经理、省运营经理、训练部人员巡检的报告',
    '4.检查表：《食安＆QSC检查表》、《线上巡检检查表》',
  ],
  [AuditTypeEnum.未通过巡检 + RoleClassificationEnum.总部]: [
    '1.计算公式：食安中心稽核部稽核通过报告数/食安中心稽核部总稽核报告数*100%',
    '2.通过率为稽核部稽核的分数≥80分的报告',
    '3.检查表：仅统计《食安&QSC检查表》的数据',
  ],
  [AuditTypeEnum.未通过巡检 + RoleClassificationEnum.线上]: [
    '1.计算公式：线上运营中心巡检通过报告数/线上运营中心总巡检报告数*100% ',
    '2.通过率为线上运营中心巡检的分数≥80分的报告',
    '3.检查表：仅统计《线上巡检检查表》的数据',
  ],
  [AuditTypeEnum.未通过巡检 + RoleClassificationEnum.战区]: [
    '1.计算公式：战区食安部巡检通过报告数/战区食安部总巡检报告数*100%',
    '2.通过率为战区食安部巡检的分数≥80分的报告',
    '3.检查表：仅统计《食安&QSC检查表》的数据',
  ],
  [AuditTypeEnum.未通过巡检 + RoleClassificationEnum.其他]: [
    '1.计算公式：运营督导、运营经理、省运营经理、训练部巡检通过报告数/运营督导、运营经理、省运营经理、训练部总巡检报告数*100%',
    '2.通过率为运营督导、运营经理、省运营经理、训练部巡检的分数≥80分的报告',
    '3.检查表：仅统计《食安&QSC检查表》的数据',
  ],
  [AuditTypeEnum.未完成整改]: [
    '1.门店稽核整改完成率=已完成整改的报告数/需整改的报告数*100%',
    '2.稽核整改完成率数据包含：总部食安稽核、线上云巡检、战区食安巡检、其他巡检的数据',
    '3.其他巡检为运营督导、运营经理、省运营经理、训练部人员巡检的报告',
    '4.检查表：《食安＆QSC检查表》、《线上巡检检查表》',
  ],
  [AuditTypeEnum.未完成整改 + RoleClassificationEnum.总部]: [
    '1.计算公式：食安中心稽核部稽核已完成整改的报告数/食安中心稽核部稽核需整改的报告数*100%',
    '2.整改完成率为食安中心稽核部稽核的报告出现扣分项且需要整改的报告',
    '3.检查表：仅统计《食安&QSC检查表》的数据',
  ],
  [AuditTypeEnum.未完成整改 + RoleClassificationEnum.线上]: [
    '1.计算公式：线上运营中心巡检已完成整改的报告数/线上运营中心巡检需整改的报告数*100%',
    '2.整改完成率为线上运营中心巡检的报告出现扣分项且需要整改的报告',
    '3.检查表：仅统计《线上巡检检查表》的数据',
  ],
  [AuditTypeEnum.未完成整改 + RoleClassificationEnum.战区]: [
    '1.计算公式：战区食安部巡检已完成整改的报告数/战区食安部巡检需整改的报告数*100%',
    '2.整改完成率为战区食安部巡检的报告出现扣分项且需要整改的报告',
    '3.检查表：仅统计仅统计《食安&QSC检查表》的数据',
  ],
  [AuditTypeEnum.未完成整改 + RoleClassificationEnum.其他]: [
    '1.计算公式：运营督导、运营经理、省运营经理、训练部巡检已完成整改的报告数/运营督导、运营经理、省运营经理、训练部巡检需整改的报告数*100%',
    '2.整改完成率为运营督导、运营经理、省运营经理、训练部巡检的报告出现扣分项且需要整改的报告',
    '3.检查表：仅统计《食安&QSC检查表》的数据',
  ],
  [AuditTypeEnum.未完成自检]: [
    '1.自检完成率=已提交自检任务门店报告数/计划自检任务门店报告数*100%',
    '2.完成率取数：仅统计《全国每日效期检查》、《全国隔油池清洁(每日1次)》、《全国可乐机清洁(每日1次)》、《全国炸油品质管理》、《全国制冰机清洁(每月2次)》、《【全国】月清洁任务》的数据',
  ],
  [AuditTypeEnum.未完成自检 + SelfWorkSheetEnum.效期]: [
    '计算公式：已提交《全国每日效期检查》任务的报告数/计划自检《全国每日效期检查》任务报告数*100%',
  ],
  [AuditTypeEnum.未完成自检 + SelfWorkSheetEnum.隔油池]: [
    '计算公式：已提交《全国隔油池清洁》任务的报告数/计划自检《全国隔油池清洁》任务报告数*100%',
  ],
  [AuditTypeEnum.未完成自检 + SelfWorkSheetEnum.可乐机]: [
    '计算公式：已提交《全国可乐机清洁》任务的报告数/计划自检《全国可乐机清洁》任务报告数*100%',
  ],
  [AuditTypeEnum.未完成自检 + SelfWorkSheetEnum.制冰机]: [
    '计算公式：已提交《全国制冰机清洁》任务的报告数/计划自检《全国制冰机清洁》任务报告数*100%',
  ],
  [AuditTypeEnum.未完成自检 + SelfWorkSheetEnum.炸油品质]: [
    '计算公式：已提交《全国炸油品质管理》任务的报告数/计划自检《全国炸油品质管理》任务报告数*100%',
  ],
  [AuditTypeEnum.未完成自检 + SelfWorkSheetEnum.清洁]: [
    '计算公式：已提交《全国月清洁任务》》任务的报告数/计划自检《全国月清洁任务》任务报告数*100%',
  ],
  [AuditTypeEnum.自检不合格]: [
    '1.自检合格率=自检任务合格门店报告数/已点评自检报告数*100%',
    '2.合格率取数：仅统计《全国每日效期检查》、《全国隔油池清洁(每日1次)》、《全国可乐机清洁(每日1次)》、《全国炸油品质管理》、《全国制冰机清洁(每月2次)》、《【全国】月清洁任务》的数据',
  ],
  [AuditTypeEnum.自检不合格 + SelfWorkSheetEnum.效期]: [
    '计算公式：《全国每日效期检查》任务点评合格报告数/《全国每日效期检查》任务已点评报告数*100%',
  ],
  [AuditTypeEnum.自检不合格 + SelfWorkSheetEnum.隔油池]: [
    '计算公式：《全国隔油池清洁》任务点评合格报告数/《全国隔油池清洁》任务已点评报告数*100%',
  ],
  [AuditTypeEnum.自检不合格 + SelfWorkSheetEnum.可乐机]: [
    '计算公式：《全国可乐机清洁》任务点评合格报告数/《全国可乐机清洁》任务已点评报告数*100%',
  ],
  [AuditTypeEnum.自检不合格 + SelfWorkSheetEnum.制冰机]: [
    '计算公式：《全国制冰机清洁》任务点评合格报告数/《全国制冰机清洁》任务已点评报告数*100%',
  ],
  [AuditTypeEnum.自检不合格 + SelfWorkSheetEnum.炸油品质]: [
    '计算公式：《全国炸油品质管理》任务点评合格报告数/《全国炸油品质管理》任务已点评报告数*100%',
  ],
  [AuditTypeEnum.自检不合格 + SelfWorkSheetEnum.清洁]: [
    '计算公式：《全国月清洁任务》任务点评合格报告数/《全国月清洁任务》任务已点评报告数*100%',
  ],
  unPassPatrolOnline: [
    '线上运营中心巡检通过门店数/线上运营中心总巡检门店数*100%',
    '注: 1.通过门店数为线上运营中心巡检的分数≥80分的门店',
    '2.取数：仅统计《线上巡检检查表》的数据',
  ],
  unPassPatrolOffLine: [
    '稽核通过门店数/总稽核门店数*100%',
    '注: 1.稽核通过数据包含：总部食安稽核、战区食安巡检、其他巡检的数据',
    '2.其他巡检为运营督导、运营经理、省运营经理、训练部人员巡检的报告',
    '3.检查表包含：《食安＆QSC检查表》',
  ],
  unCompleteRectifyOnLine: [
    '门店稽核整改完成率=已整改门店数/需整改门店数*100%',
    '注: 1.稽核整改完成率数据包含：总部食安稽核、战区食安巡检、其他巡检的数据',
    '2.其他巡检为运营督导、运营经理、省运营经理、训练部人员巡检的报告',
    '3.检查表包含：《食安＆QSC检查表》',
  ],
  unCompleteRectifyOffLine: [
    '计算公式：线上运营中心巡检已整改门店数/线上运营中心巡检需整改门店数*100%',
    '注: 1.整改完成率为线上运营中心巡检的报告出现扣分项且需要整改的门店',
    '2.取数：仅统计《线上巡检检查表》的数据',
  ],
  [AuditTypeEnum.督导巡检完成率]: [
    '1.运营督导巡店完成率=运营督导已提交报告数/（督导管辖门店数*2）*100%',
    '2.督导巡店完成率数据取数：每月督导需到管辖的每家门店各完成一次《QSC&食安》巡检、《督导巡店检查表》巡检',
    '3.检查表：《食安＆QSC检查表》、《督导巡店检查表》',
  ],
  [AuditTypeEnum.督导巡检完成率 + SupervisionEnum.食安]: [
    '1.计算公式：运营督导已巡检门店数/运营督导管辖门店数*100%',
    '2.督导巡店完成率数据取数：每月督导需到管辖的每家门店完成一次《QSC&食安》巡检',
    '3.检查表：《食安＆QSC检查表》',
  ],
  [AuditTypeEnum.督导巡检完成率 + SupervisionEnum.督导]: [
    '1.计算公式：运营督导已巡检门店数/运营督导管辖门店数*100%',
    '2.督导巡店完成率数据取数：每月督导需到管辖的每家门店完成一次《督导巡店检查表》巡检',
    '3.检查表：《督导巡店检查表》',
  ],
};
