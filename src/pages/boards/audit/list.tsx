import { IconFont, Loading, PageList } from '@src/components';
import { Card, SearchBar } from 'antd-mobile';
import { CustomerInfoV2, Sorter } from '../components';
import { useLocation, useNavigate } from 'react-router-dom';
import { useRequest } from 'ahooks';
import { AuditTypeEnum, RoleClassificationEnum, SelfWorkSheetEnum, typeObj } from './enum';
import { stringify } from 'qs';
import { useMemo, useState } from 'react';
import { cloneDeep } from 'lodash';
import { usePageContext } from '@src/common/page-context';
import styles from '../index.module.scss';
import { commonSorter } from '@src/utils/helper';

const AuditList = () => {
  const {
    params,
    detailType,
    detailSubType,
  }: {
    params: any;
    detailType: AuditTypeEnum;
    detailSubType: 'all';
  } = useLocation().state || {};

  const { getGroupTree, tagIdObj } = usePageContext();
  const [searchValue, setSearchValue] = useState<string>('');
  const [sorter, setSorter] = useState<string[]>([]);

  const navigator = useNavigate();

  const showGroupNames = useMemo(() => {
    const { list } = getGroupTree(+params.groupId, +params.stopId);

    if (!list || !list.length) return <></>;
    // const firstNode = list[0];
    const curNode = list.pop();
    const breadcrumbList = list;
    return (
      <>
        {tagIdObj && params.stopId && (
          <>
            <span>{tagIdObj[params.stopId].name}</span>
            <IconFont type="icon-chevron-right" className="mx-1" />
          </>
        )}
        {breadcrumbList.map((v: { name: string; id: number }, index: number) => (
          <>
            <span
              key={v.id}
              className="text-primary"
              onClick={() => {
                const idx = breadcrumbList.length - index;
                navigator(-idx);
              }}
            >
              {v.name}
            </span>
            <IconFont type="icon-chevron-right" className="mx-1" />
          </>
        ))}
        <span>{curNode.name}</span>
      </>
    );
  }, [params.groupId]);

  const { data: list, loading } = useRequest(
    async () => {
      const subParams = [
        AuditTypeEnum.触碰红线,
        AuditTypeEnum.未通过巡检,
        AuditTypeEnum.未完成整改,
      ].includes(detailType)
        ? {
            roleClassificationEnum:
              detailSubType === 'all' ? undefined : (detailSubType as RoleClassificationEnum),
          }
        : [AuditTypeEnum.自检不合格, AuditTypeEnum.未完成自检].includes(detailType)
          ? {
              selfWorkSheet:
                detailSubType === 'all' ? undefined : (detailSubType as SelfWorkSheetEnum),
            }
          : {};
      const res = await typeObj[detailType].getList['group']({ ...params, ...subParams });
      return res;
    },
    { refreshDeps: [params, detailType] },
  );

  const ListData = useMemo(() => {
    let arr = list || [];
    // console.log(arr);

    if (searchValue) {
      arr = list?.filter(
        (v: any) => v?.name?.includes(searchValue) || v?.shopName?.includes(searchValue),
      );
    }
    if (sorter && sorter[0]) {
      const [key, order] = sorter[0].split('-');
      arr = cloneDeep(arr).sort((a: any, b: any) => commonSorter(a[key], b[key], order));
    }
    return arr;
  }, [list, searchValue, sorter]);

  return (
    <div>
      <Loading spinning={loading}>
        <div className="sticky top-0 z-10 pb-3" style={{ background: '#F5F5F5' }}>
          <Card className="rounded-none" bodyClassName="!pb-0">
            <div className="flex items-center mb-3">
              <SearchBar
                className={`${styles.iAdmSearchBar} flex-1 mr-2`}
                style={{ '--border-radius': '8px', '--height': '2.5rem' }}
                onClear={() => setSearchValue('')}
                onSearch={setSearchValue}
                placeholder="请输入门店/组织名称"
              />
              <Sorter
                config={typeObj[detailType].sortConfig['group']}
                value={sorter}
                onChange={setSorter}
              />
            </div>
            <div className="flex items-center text-xs mt-2 pb-3 text-gray-500 overflow-x-auto whitespace-nowrap">
              {showGroupNames}
            </div>
          </Card>
        </div>
        <PageList
          dataSource={ListData}
          itemRender={(item: any, index) => (
            <div className="mb-2 px-4 bg-white" key={index}>
              <CustomerInfoV2
                key={index}
                // type="group"
                type={item.type === 1 ? 'group' : 'shop'}
                title={
                  item.type === 1 ? item.name : `${item.shopCode}${item.shopName || item.name}`
                }
                percent={typeObj[detailType].percent[item.type === 1 ? 'group' : 'shop'][
                  detailSubType
                ](item)}
                columns={typeObj[detailType].columns[item.type === 1 ? 'group' : 'shop'][
                  detailSubType
                ](item)}
                manager={{
                  id: item.responsiblePersonId,
                  name: item.responsiblePersonName,
                  phone: item.responsiblePersonPhone,
                }}
                navNext={() => {
                  navigator('/boards/audit/list?pageType=detail', {
                    state: {
                      params: { ...params, groupId: item.id },
                      detailType: detailType,
                      detailSubType: detailSubType,
                    },
                    replace: false,
                  });
                }}
                navDetail={() => {
                  navigator(
                    `/boards/audit/detail?${stringify({
                      ...params,
                      shopCode: item.shopCode,
                      detailType: detailType,
                      phone: item.responsiblePersonPhone,
                      id: item.responsiblePersonId,
                      responsiblePersonName: item.responsiblePersonName,
                      title: item.name || item.shopName,
                      detailSubType: detailSubType,
                      pageType: 'detail', //防止返回刷新
                    })}`,
                    {
                      state: {
                        percent:
                          typeObj[detailType].percent[item.type === 1 ? 'group' : 'shop'][
                            detailSubType
                          ](item),
                      },
                    },
                  );
                }}
              />
            </div>
          )}
        />
      </Loading>
    </div>
  );
};

export default AuditList;
