import { HrTypeEnum } from './enum';

export type HrCommonParams = {
  beginDate?: string;
  dashboardType: HrTypeEnum | 'ALL';
  deptId?: string;
  endDate?: string;
  shopIds?: number[];
  statisticsMonth?: string;
};

export type HrCommonListItem = {
  percent: {
    label: string;
    value: string;
  };
  columns: {
    label: string;
    value: string;
  }[];
  type: number;
  directorId: number;
  directorName: string;
  directorPhone: string;
  groupId: number | string;
  shopId: number | string;
  groupName: string;
};

export type OrganizationItem = {
  achievementRate: number;
  employees: number;
  headcount: number;
  postId: number;
  postName: string;
};
