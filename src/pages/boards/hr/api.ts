import { get, post } from '@src/api';
import { HrCommonParams, OrganizationItem } from './api.type';

//统计数据汇总
export const getStatisticsData = (data: HrCommonParams) =>
  post('/om-api/common/headResource/dashboard/total/statistics', { data });

//告警状态
export const getWarningData = (data: HrCommonParams) =>
  post('/om-api/common/headResource/dashboard/alarm-status', { data });

export const getSchedulingList = (data: HrCommonParams & { filterGroup: string }) =>
  post('/om-api/common/headResource/dashboard/scheduling/list', { data });
export const getOrganizationList = (data: HrCommonParams & { filterGroup: string }) =>
  post('/om-api/common/headResource/dashboard/organization/list', { data });
export const getRecruitList = (data: HrCommonParams & { filterGroup: string }) =>
  post('/om-api/common/headResource/dashboard/recruit/list', { data });
export const getDepartList = (data: HrCommonParams & { filterGroup: string }) =>
  post('/om-api/common/headResource/dashboard/depart/list', { data });

export const getOrganizationDetail = (data: {
  groupId?: string;
  statisticsMonth: string;
  shopIds?: string;
}) =>
  get<{
    details: OrganizationItem[];
  }>('/om-api/common/headResource/dashboard/organization/detail', { params: data });
