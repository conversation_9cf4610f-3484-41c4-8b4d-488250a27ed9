import { formatPer } from '@src/utils/helper';
import { getDepartList, getOrganizationList, getRecruitList, getSchedulingList } from './api';
import { HrTypeEnum } from './enum';

export const methodMap = {
  [HrTypeEnum.排班数据]: {
    fn: getSchedulingList,
    percent: {
      label: '门店工时效率',
      key: 'schedulingRate',
      unit: '%',
    },
    columns: [
      {
        label: '差值',
        key: 'differenceTime',
        unit: 'h',
      },
      {
        label: '门店排定工时',
        key: 'scheduledTime',
        unit: 'h',
      },
      {
        label: '门店实际工时',
        key: 'actualSchedulingTime',
        unit: 'h',
      },
    ],
    sortConfig: [
      { label: '工时效率最高', value: 'schedulingRate-desc' },
      { label: '工时效率最低', value: 'schedulingRate-asc' },
      { label: '差值最高', value: 'differenceTime-desc' },
      { label: '差值最低', value: 'differenceTime-asc' },
    ],
  },
  [HrTypeEnum.编制数据]: {
    fn: getOrganizationList,
    percent: {
      label: '门店编制达成率',
      key: 'achievementRate',
      unit: '%',
    },
    columns: [
      {
        label: '门店编制人数',
        key: 'headcount',
        unit: '人',
      },
      {
        label: '门店在岗人数',
        key: 'employees',
        unit: '人',
      },
    ],
    sortConfig: [
      { label: '编制达成率最高', value: 'achievementRate-desc' },
      { label: '编制达成率最低', value: 'achievementRate-asc' },
    ],
  },
  [HrTypeEnum.招聘数据]: {
    fn: getRecruitList,
    percent: {
      label: '门店招聘达成率',
      key: 'achievementRate',
      unit: '%',
    },
    columns: [
      {
        label: '已入职人数',
        key: 'entryCount',
        unit: '人',
      },
      {
        label: '需求人数',
        key: 'headcount',
        unit: '人',
      },
    ],
    sortConfig: [
      { label: '招聘达成率最高', value: 'achievementRate-desc' },
      { label: '招聘达成率最低', value: 'achievementRate-asc' },
    ],
  },
  [HrTypeEnum.离职数据]: {
    fn: getDepartList,
    percent: {
      label: '离职率',
      key: 'departRate',
      unit: '%',
    },
    columns: [
      {
        label: '平均在职人数',
        key: 'entryCount',
        unit: '人',
      },
      {
        label: '离职人数',
        key: 'departCount',
        unit: '人',
      },
    ],
    sortConfig: [
      { label: '离职率最高', value: 'departRate-desc' },
      { label: '离职率最低', value: 'departRate-asc' },
    ],
  },
};

export const formatData = (res: any[], listType: HrTypeEnum) => {
  return res.map((item: any) => ({
    ...item,
    percent: {
      label: methodMap[listType].percent.label,
      value: formatPer(item[methodMap[listType].percent.key], methodMap[listType].percent.unit),
    },
    columns: methodMap[listType].columns.map((v) => ({
      label: v.label,
      value: formatPer(item[v.key], v.unit),
    })),
  }));
};
