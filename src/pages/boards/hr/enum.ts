import AuthorityEnum from '@src/common/authority';

export enum HrTypeEnum {
  排班数据 = 'SCHEDULING',
  编制数据 = 'ORGANIZATION',
  招聘数据 = 'RECRUIT',
  离职数据 = 'DEPART',
}

export const typeOptions = [
  {
    label: '门店工时效率',
    value: HrTypeEnum.排班数据,
    auth: AuthorityEnum.排班准确率,
  },
  {
    label: '门店编制达成率',
    value: HrTypeEnum.编制数据,
    auth: AuthorityEnum.编制达成率,
  },
  {
    label: '门店招聘达成率',
    value: HrTypeEnum.招聘数据,
    auth: AuthorityEnum.招聘达成率,
  },
  {
    label: '离职率',
    value: HrTypeEnum.离职数据,
    auth: AuthorityEnum.离职率,
  },
];

export const ToolTipMap = {
  [HrTypeEnum.排班数据]: ['门店实际工时/门店排定工时*100%'],
  [HrTypeEnum.编制数据]: ['门店在岗人数/门店编制人数*100%'],
  [HrTypeEnum.招聘数据]: [
    '已入职人数/需求人数*100% ',
    '注：需求人数（门店管理组：全年目标分摊至每月，门店店员：按业务需求提供）',
  ],
  [HrTypeEnum.离职数据]: [
    '离职人数/平均在职人数*100%',
    '注：平均在职人数=（月期初人数+月期末人数）/2）',
  ],
};
