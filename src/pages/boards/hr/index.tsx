import { Card, SearchBar, Tabs } from 'antd-mobile';
import {
  CustomerInfoV2,
  CustomerType,
  ExceptionReport,
  IndicatorCard,
  IndicatorCardV2,
  Sorter,
} from '../components';
import { HrTypeEnum, ToolTipMap, typeOptions } from './enum';
import { useMemo, useState } from 'react';
import { CustomerFilter, DateFilter, IconFont, Loading, PageList } from '@src/components';
import dayjs from 'dayjs';
import { useRequest } from 'ahooks';
import { getStatisticsData } from './api';
import { HrCommonListItem } from './api.type';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { formatData, methodMap } from './utils';
import { stringify } from 'qs';
import { cloneDeep } from 'lodash';
import AuthorityEnum from '@src/common/authority';
import { observer } from 'mobx-react';
import { userStore } from '@src/store';
import styles from '../index.module.scss';
import { usePageContext } from '@src/common/page-context';
import qs from 'qs';
import { checkReport, getReport, updateReadFlag } from '../api';
import { commonSorter, formatPer, getDatePercentType } from '@src/utils/helper';
import { roleTypeIsManage } from '@src/utils/tokenUtils';

const defaultParams = {
  startDate: dayjs().subtract(3, 'day').format('YYYY-MM-DD'),
  endDate: dayjs().subtract(3, 'day').format('YYYY-MM-DD'),
  dateFilterType: 'date',
  statisticsMonth: dayjs().subtract(1, 'month').format('YYYY-MM'),
  groupId: '',
  shopIds: [] as number[],
};

const HrBoard = observer(() => {
  const { pid, getGroupTree } = usePageContext();
  const { permissionsMap } = userStore;
  const disabledShop = !permissionsMap.has(AuthorityEnum.HrShopBtn);
  const disabledGroup = !permissionsMap.has(AuthorityEnum.HrGroupBtn);

  const showTypeOptions = typeOptions.filter((v) => permissionsMap.has(v.auth));
  const [listType, setListType] = useState<HrTypeEnum>(
    showTypeOptions.length ? showTypeOptions[0].value : HrTypeEnum.排班数据,
  );

  const [params, setParams] = useState({ ...defaultParams, groupId: pid });
  const [dataType, setDataType] = useState(disabledShop ? 'DEPT' : 'SHOP');
  const [keyWords, setKeyWords] = useState('');
  const [sorter, setSorter] = useState<string[]>([]);

  const [search] = useSearchParams();
  const hiddenDetail = search.get('pageType') !== 'detail'; //隐藏详情
  const hiddenHome = search.get('pageType') === 'detail'; //隐藏统计页

  const navigator = useNavigate();

  const { data: showReport, run: showReportRun } = useRequest(
    async () => {
      const res = await checkReport({ reportType: 2 });
      return res;
    },
    {
      ready: roleTypeIsManage(),
    },
  );
  const { data: reportData, run } = useRequest(
    async () => {
      const res = await getReport({ reportType: 2 });

      return res || {};
    },
    {
      ready: roleTypeIsManage(),
    },
  );
  const { run: updateReadRun } = useRequest(
    async (reportId: number) => {
      if (!reportId) throw null;
      const res = await updateReadFlag(reportId);
      return res;
    },
    {
      manual: false,
      onSuccess: () => {
        showReportRun();
      },
    },
  );

  const showGroupNames = useMemo(() => {
    if (!params.groupId) return dataType === 'DEPT' ? '全部组织' : '全部门店';
    const { list } = getGroupTree(+params.groupId);
    if (!list || !list.length) return <></>;
    const curNode = list[list.length - 1];
    const breadcrumbList = list.slice(0, list.length - 1);
    return (
      <>
        {breadcrumbList.map((v: { name: string; id: number }) => (
          <>
            <span
              key={v.id}
              className="text-primary"
              onClick={() => {
                setParams((p) => ({ ...p, groupId: String(v.id) }));
              }}
            >
              {v.name}
            </span>
            <IconFont type="icon-chevron-right" className="mx-1" />
          </>
        ))}
        <span>{curNode.name}</span>
      </>
    );
  }, [params.groupId, dataType]);

  const { data: statisticsData, loading: statisticsLoading } = useRequest(
    () =>
      getStatisticsData({
        dashboardType: 'ALL',
        statisticsMonth: params.statisticsMonth,
        beginDate: params.startDate,
        endDate: params.endDate,
        shopIds: params.shopIds,
        deptId: params.groupId,
      }),
    { refreshDeps: [params] },
  );

  // const { data: warningData } = useRequest(
  //   () =>
  //     getWarningData({
  //       dashboardType: 'ALL',
  //       statisticsMonth: params.statisticsMonth,
  //       beginDate: params.startDate,
  //       endDate: params.endDate,
  //       shopIds: params.shopIds,
  //       deptId: params.groupId,
  //     }),
  //   { refreshDeps: [params] },
  // );
  const warningData = {
    hasSchedulingAlarm: null,
    hasOrganizationAlarm: null,
    hasRecruitAlarm: null,
    hasDepartAlarm: null,
  };

  const getListParams = () => {
    return {
      beginDate: params.startDate,
      endDate: params.endDate,
      statisticsMonth: params.statisticsMonth,
      shopIds: params.shopIds,
      deptId: params.groupId,
      dashboardType: listType,
      filterGroup: dataType,
    };
  };

  const { data, loading } = useRequest(
    async () => {
      if (!showTypeOptions.length) throw null;
      const res = await methodMap[listType].fn(getListParams());
      //数据格式化处理
      const list: HrCommonListItem[] = formatData(res, listType);
      return list;
    },
    {
      refreshDeps: [params, listType, dataType],
    },
  );

  const showData = useMemo(() => {
    let arr: HrCommonListItem[] = data || [];
    if (keyWords) {
      arr = data?.filter((v) => v.groupName.includes(keyWords)) || [];
    }
    if (sorter && sorter[0]) {
      const [key, order] = sorter[0].split('-');
      arr = cloneDeep(arr).sort((a: any, b: any) => commonSorter(a[key], b[key], order));
    }
    return arr;
  }, [data, keyWords, sorter]);

  const { schedulingData, organizationData, recruitData, departData } = statisticsData || {
    schedulingData: {},
    organizationData: {},
    recruitData: {},
    departData: {},
  };

  // const showWarningData = useMemo(() => {
  //   const warningConfig = [
  //     { text: '工时效率存在异常', key: 'hasSchedulingAlarm' },
  //     { text: '编制达成率存在异常', key: 'hasOrganizationAlarm' },
  //     { text: '招聘达成率存在异常', key: 'hasRecruitAlarm' },
  //     { text: '离职率存在异常', key: 'hasDepartAlarm' },
  //   ];

  //   const warningList = warningConfig.filter((v) => warningData?.[v.key]);
  //   return {
  //     status: warningList.length ? 'danger' : 'success',
  //     text: warningList.map((v) => v.text).join('，'),
  //   };
  // }, [warningData]);

  const showPercentDateType = useMemo(() => {
    return {
      same: getDatePercentType(params.startDate, params.endDate) === 'day' ? 'day' : 'default',
      circle: 'default',
    } as {
      same: 'day' | 'month' | 'default';
      circle: 'day' | 'month' | 'default';
    };
  }, [params.startDate, params.endDate]);

  const handleTabChange = (key: HrTypeEnum) => {
    setSorter([]);
    setListType(key);
  };

  const IndicatorMap = {
    [HrTypeEnum.排班数据]: (
      <IndicatorCardV2
        className="rounded-lg"
        lineClassName="rounded-bl-base"
        title="门店工时准确率"
        tooltip={ToolTipMap[HrTypeEnum.排班数据]}
        indicator={formatPer(schedulingData?.rate)}
        percent={{
          same: formatPer(schedulingData?.dailyYoY),
          circle: formatPer(schedulingData?.dailyQoQ),
        }}
        percentDateType={showPercentDateType}
        isWarning={!!warningData?.hasSchedulingAlarm}
      />
    ),
    [HrTypeEnum.编制数据]: (
      <IndicatorCardV2
        title="门店编制达成率"
        className="rounded-lg"
        lineClassName="rounded-bl-base"
        indicator={formatPer(organizationData?.rate)}
        tooltip={ToolTipMap[HrTypeEnum.编制数据]}
        percent={{
          same: formatPer(organizationData?.monthQoQ),
          circle: formatPer(organizationData?.monthQoQ),
        }}
        isWarning={!!warningData?.hasOrganizationAlarm}
      />
    ),
    [HrTypeEnum.招聘数据]: (
      <IndicatorCardV2
        title="门店招聘达成率"
        className="rounded-lg"
        lineClassName="rounded-bl-base"
        indicator={formatPer(recruitData?.rate)}
        tooltip={ToolTipMap[HrTypeEnum.招聘数据]}
        percent={{
          same: formatPer(recruitData?.monthQoQ),
          circle: formatPer(recruitData?.monthQoQ),
        }}
        isWarning={!!warningData?.hasRecruitAlarm}
      />
    ),
    [HrTypeEnum.离职数据]: (
      <IndicatorCardV2
        title="离职率"
        className="rounded-lg"
        lineClassName="rounded-bl-base"
        indicator={formatPer(departData?.rate)}
        tooltip={ToolTipMap[HrTypeEnum.离职数据]}
        percent={{
          same: formatPer(departData?.monthQoQ),
          circle: formatPer(departData?.monthQoQ),
        }}
        isWarning={!!warningData?.hasDepartAlarm}
      />
    ),
  };

  return (
    <div>
      <Loading spinning={statisticsLoading || loading}>
        <div hidden={hiddenDetail} className="bg-white">
          <div className="pr-4 pl-1 justify-between flex items-center border-solid border-b border-line">
            <Tabs
              className={styles.iAdmTabs1}
              activeKey={listType}
              onChange={(key) => {
                handleTabChange(key as HrTypeEnum);
              }}
              stretch={false}
              style={{
                '--title-font-size': '0.875rem',
              }}
            >
              {showTypeOptions.map(({ value, label }) => (
                <Tabs.Tab title={label} key={value}></Tabs.Tab>
              ))}
            </Tabs>
            {/* <DataTypeSelect
                options={showTypeOptions}
                defaultValue={listType}
                onChange={(key) => {
                  setSorter([]);
                  setListType(key as HrTypeEnum);
                }}
              /> */}
          </div>
        </div>
        <Card
          className="rounded-none px-4 border-b border-solid border-line border-px"
          bodyClassName="!py-2"
        >
          <div className="flex items-center justify-between">
            <CustomerFilter
              noBg={true}
              value={params}
              onChange={(e: any) => {
                setParams((p) => ({ ...p, ...e }));
              }}
              shopKey="shopIds"
            />
            {hiddenHome && listType === HrTypeEnum.排班数据 && (
              <DateFilter
                key="gsxl"
                noBg={true}
                type={params.dateFilterType as 'month' | 'date'}
                value={[dayjs(params.startDate).toDate(), dayjs(params.endDate).toDate()]}
                onChange={(e, dateFilterType) => {
                  setParams((p) => ({
                    ...p,
                    startDate: e[0],
                    endDate: e[1],
                    dateFilterType: dateFilterType || 'date',
                  }));
                }}
              />
            )}
            {hiddenHome && listType !== HrTypeEnum.排班数据 && (
              <DateFilter
                key="other"
                noBg={true}
                onlyMonth
                type="month"
                value={[
                  dayjs(params.statisticsMonth).toDate(),
                  dayjs(params.statisticsMonth).toDate(),
                ]}
                onChange={(e) => {
                  setParams((p) => ({ ...p, statisticsMonth: dayjs(e[0]).format('YYYY-MM') }));
                }}
              />
            )}
          </div>
        </Card>
        <div hidden={hiddenHome}>
          {permissionsMap.has(AuthorityEnum.排班准确率) && (
            <>
              <Card
                className="rounded-none px-4 border-b border-solid border-line border-px"
                bodyClassName="!py-2"
              >
                <div className="flex items-center justify-between">
                  <div className="text-B8 text-[14px]">时间筛选</div>
                  <DateFilter
                    key="gsxl"
                    type={params.dateFilterType as 'month' | 'date'}
                    value={[dayjs(params.startDate).toDate(), dayjs(params.endDate).toDate()]}
                    onChange={(e, dateFilterType) => {
                      setParams((p) => ({
                        ...p,
                        startDate: e[0],
                        endDate: e[1],
                        dateFilterType: dateFilterType || 'date',
                      }));
                    }}
                  />
                </div>
              </Card>
              <IndicatorCard
                className="mb-2"
                title="门店工时准确率"
                tooltip={ToolTipMap[HrTypeEnum.排班数据]}
                indicator={formatPer(schedulingData?.rate)}
                percent={{
                  same: formatPer(schedulingData?.dailyYoY),
                  circle: formatPer(schedulingData?.dailyQoQ),
                }}
                type="full"
                percentDateType={showPercentDateType}
                isWarning={!!warningData?.hasSchedulingAlarm}
                handleClick={() => handleTabChange(HrTypeEnum.排班数据)}
              />
            </>
          )}
          <Card
            className="rounded-none px-4 border-b border-solid border-line border-px"
            bodyClassName="!py-2"
          >
            <div className="flex items-center justify-between">
              <div className="text-B8 text-[14px]">时间筛选</div>
              <DateFilter
                key="other"
                onlyMonth
                type="month"
                value={[
                  dayjs(params.statisticsMonth).toDate(),
                  dayjs(params.statisticsMonth).toDate(),
                ]}
                onChange={(e) => {
                  setParams((p) => ({ ...p, statisticsMonth: dayjs(e[0]).format('YYYY-MM') }));
                }}
              />
            </div>
          </Card>
          {permissionsMap.has(AuthorityEnum.编制达成率) && (
            <IndicatorCard
              title="门店编制达成率"
              indicator={formatPer(organizationData?.rate)}
              tooltip={ToolTipMap[HrTypeEnum.编制数据]}
              percent={{
                same: formatPer(organizationData?.monthQoQ),
                circle: formatPer(organizationData?.monthQoQ),
              }}
              type="full"
              isWarning={!!warningData?.hasOrganizationAlarm}
              handleClick={() => handleTabChange(HrTypeEnum.编制数据)}
            />
          )}
          {permissionsMap.has(AuthorityEnum.招聘达成率) && (
            <IndicatorCard
              className="border-t border-solid border-line border-px"
              title="门店招聘达成率"
              indicator={formatPer(recruitData?.rate)}
              tooltip={ToolTipMap[HrTypeEnum.招聘数据]}
              percent={{
                same: formatPer(recruitData?.monthQoQ),
                circle: formatPer(recruitData?.monthQoQ),
              }}
              type="full"
              isWarning={!!warningData?.hasRecruitAlarm}
              handleClick={() => {
                handleTabChange(HrTypeEnum.招聘数据);
                !!showReport && updateReadRun(showReport);
              }}
              hasReport={!!showReport}
            />
          )}
          {permissionsMap.has(AuthorityEnum.离职率) && (
            <IndicatorCard
              className="border-t border-solid border-line border-px"
              title="离职率"
              indicator={formatPer(departData?.rate)}
              tooltip={ToolTipMap[HrTypeEnum.离职数据]}
              percent={{
                same: formatPer(departData?.monthQoQ),
                circle: formatPer(departData?.monthQoQ),
              }}
              type="full"
              isWarning={!!warningData?.hasDepartAlarm}
              handleClick={() => handleTabChange(HrTypeEnum.离职数据)}
            />
          )}
        </div>
        {/* <div hidden={hiddenHome}>
          <INoticeBar
            status={showWarningData.status}
            className="mt-2"
            content={`当前管辖范围内，${
              showWarningData.status === 'success' ? '无异常指标' : showWarningData.text
            }`}
          />
        </div> */}

        <div
          hidden={hiddenDetail || HrTypeEnum.招聘数据 !== listType || !roleTypeIsManage()}
          className="px-2 pt-2"
        >
          <ExceptionReport
            className="text-grey"
            hasReport={!!reportData?.reportId}
            title="招聘进度低于时间进度"
            shopNum={reportData?.businessFieldObject?.count}
            time={reportData?.reportTime || ''}
            content={
              reportData?.anomalyStatus
                ? `截止${dayjs(reportData?.reportTime).format('YYYY年MM月DD号')}，${
                    reportData?.businessFieldObject?.count ?? 0
                  }家门店门店招聘进度低于时间进度`
                : `太棒了，截止到${dayjs(reportData?.reportTime).format(
                    'YYYY年MM月DD号',
                  )}您管辖范围下的门店招聘进度均正常`
            }
            type="page"
            footerClick={() => {
              navigator(
                `/boards/exception/report${qs.stringify(
                  {
                    type: 'hr',
                    startCreateTime: params.startDate,
                    endCreateTime: params.endDate,
                  },
                  { addQueryPrefix: true },
                )}`,
              );
            }}
            onDetailClick={() => {
              navigator(
                `/boards/quick_creation${qs.stringify(
                  {
                    type: 'hr',
                    reportId: reportData?.reportId,
                  },
                  { addQueryPrefix: true },
                )}`,
              );
            }}
            hiddenDetialEntry={!!!reportData?.anomalyStatus}
          />
        </div>
        <div hidden={hiddenDetail} className="my-[10px] mx-2">
          {IndicatorMap[listType] || <></>}
        </div>
        <div
          hidden={hiddenDetail}
          className="pb-2 sticky top-0 z-50"
          style={{ background: '#F5F5F5' }}
        >
          <Card className="rounded-none" style={{ padding: 0 }} bodyClassName="!py-0">
            <div className="flex items-center justify-between px-4 py-2">
              <div className="text-base font-semibold">明细数据</div>
              <CustomerType
                disabledGroup={disabledGroup}
                disabledShop={disabledShop}
                value={dataType === 'DEPT' ? 'group' : dataType === 'SHOP' ? 'shop' : undefined}
                onChange={(e) => setDataType(() => (e === 'shop' ? 'SHOP' : 'DEPT'))}
              />
            </div>
            <div className="flex items-center px-4 pb-1">
              <SearchBar
                className={`${styles.iAdmSearchBar} flex-1 mr-2`}
                placeholder="请输入门店/组织名称"
                onSearch={setKeyWords}
                onClear={() => setKeyWords('')}
                style={{ '--border-radius': '8px', '--height': '2.5rem' }}
              />
              <Sorter config={methodMap[listType].sortConfig} value={sorter} onChange={setSorter} />
            </div>

            <div className="flex items-center text-xs px-4 py-2 text-light overflow-x-auto whitespace-nowrap">
              {showGroupNames}
            </div>
          </Card>
        </div>
        <div hidden={hiddenDetail}>
          <PageList
            key={listType}
            dataSource={showData || []}
            itemRender={(item, index) => (
              <div className="mb-2 px-4 bg-white" key={index}>
                <CustomerInfoV2
                  type={item.type === 1 ? 'group' : 'shop'}
                  title={item?.groupName}
                  percent={item.percent}
                  columns={item.columns}
                  manager={{
                    name: item.directorName,
                    phone: item.directorPhone,
                    id: item.directorId,
                  }}
                  navNext={() => {
                    navigator('/boards/hr/list', {
                      state: {
                        params: {
                          ...getListParams(),
                          deptId: item.groupId || '',
                          stopId: params.groupId,
                        },
                        listType: listType,
                      },
                    });
                  }}
                  navDetail={
                    listType !== HrTypeEnum.编制数据
                      ? undefined
                      : () => {
                          navigator(
                            `/boards/hr/detail?${stringify({
                              groupId: item.groupId,
                              shopIds: item.shopId,
                              statisticsMonth: dayjs(params.statisticsMonth).format('YYYY-MM'),
                            })}`,
                            {
                              state: {
                                item,
                              },
                            },
                          );
                        }
                  }
                />
              </div>
            )}
          />
        </div>
      </Loading>
    </div>
  );
});

export default HrBoard;
