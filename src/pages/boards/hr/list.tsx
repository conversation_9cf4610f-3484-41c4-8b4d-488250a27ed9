import { IconFont, Loading, PageList } from '@src/components';
import { Card, SearchBar } from 'antd-mobile';
import { CustomerInfoV2, Sorter } from '../components';
import { useLocation, useNavigate } from 'react-router-dom';
import { useRequest } from 'ahooks';
import { formatData, methodMap } from './utils';
import { HrCommonListItem } from './api.type';
import { HrTypeEnum } from './enum';
import { stringify } from 'qs';
import { cloneDeep } from 'lodash';
import dayjs from 'dayjs';
import { useMemo, useState } from 'react';
import { usePageContext } from '@src/common/page-context';
import styles from '../index.module.scss';
import { commonSorter } from '@src/utils/helper';

const HrList = () => {
  const { params, listType }: { params: any; listType: HrTypeEnum } = useLocation().state || {};

  const { getGroupTree, tagIdObj } = usePageContext();
  const [keyWords, setKeyWords] = useState('');
  const [sorter, setSorter] = useState<string[]>([]);

  const navigator = useNavigate();

  const { data, loading } = useRequest(
    async () => {
      const res = await methodMap[listType].fn(params);
      //数据格式化处理
      const list: HrCommonListItem[] = formatData(res, listType);
      return list;
    },
    { refreshDeps: [params] },
  );

  const showGroupNames = useMemo(() => {
    const { list } = getGroupTree(+params.deptId, +params.stopId);
    if (!list || !list.length) return <></>;
    const curNode = list.pop();
    const breadcrumbList = list;
    return (
      <>
        {tagIdObj && params.stopId && (
          <>
            <span>{tagIdObj[params.stopId].name}</span>
            <IconFont type="icon-chevron-right" className="mx-1" />
          </>
        )}
        {breadcrumbList.map((v: { name: string; id: number }, index: number) => (
          <>
            <span
              key={v.id}
              className="text-primary"
              onClick={() => {
                const idx = breadcrumbList.length - index;
                navigator(-idx);
              }}
            >
              {v.name}
            </span>
            <IconFont type="icon-chevron-right" className="mx-1" />
          </>
        ))}
        <span>{curNode.name}</span>
      </>
    );
  }, [params.deptId]);

  const showData = useMemo(() => {
    let arr: HrCommonListItem[] = data || [];
    if (keyWords) {
      arr = data?.filter((v) => v.groupName.includes(keyWords)) || [];
    }
    if (sorter && sorter[0]) {
      const [key, order] = sorter[0].split('-');
      arr = cloneDeep(arr).sort((a: any, b: any) => commonSorter(a[key], b[key], order));
    }
    return arr;
  }, [data, keyWords, sorter]);

  return (
    <div>
      <Loading spinning={loading}>
        <div className="sticky top-0 z-10 pb-3" style={{ background: '#F5F5F5' }}>
          <Card className="rounded-none" bodyClassName="!pb-0">
            <div className="flex items-center mb-3">
              <SearchBar
                className={`${styles.iAdmSearchBar} flex-1 mr-2`}
                style={{ '--border-radius': '8px', '--height': '2.5rem' }}
                onSearch={setKeyWords}
                onClear={() => setKeyWords('')}
                placeholder="请输入门店/组织名称"
              />
              <Sorter config={methodMap[listType].sortConfig} value={sorter} onChange={setSorter} />
            </div>
            <div className="flex items-center text-xs mt-2 pb-3 text-gray-500 overflow-x-auto whitespace-nowrap">
              {showGroupNames}
            </div>
          </Card>
        </div>
        <PageList
          dataSource={showData}
          itemRender={(item, index) => (
            <div className="mb-2 px-4 bg-white" key={index}>
              <CustomerInfoV2
                type={item.type === 1 ? 'group' : 'shop'}
                title={item?.groupName}
                percent={item.percent}
                columns={item.columns}
                manager={{
                  name: item.directorName,
                  phone: item.directorPhone,
                  id: item.directorId,
                }}
                navNext={() => {
                  navigator('/boards/hr/list', {
                    state: {
                      params: { ...params, deptId: item.groupId },
                      listType: listType,
                    },
                    replace: false,
                  });
                }}
                navDetail={
                  listType !== HrTypeEnum.编制数据
                    ? undefined
                    : () => {
                        if (listType !== HrTypeEnum.编制数据) return;
                        navigator(
                          `/boards/hr/detail?${stringify({
                            shopIds: item.shopId,
                            statisticsMonth: dayjs(params.statisticsMonth).format('YYYY-MM'),
                          })}`,
                          {
                            state: {
                              item,
                            },
                          },
                        );
                      }
                }
              />
            </div>
          )}
        />
      </Loading>
    </div>
  );
};

export default HrList;
