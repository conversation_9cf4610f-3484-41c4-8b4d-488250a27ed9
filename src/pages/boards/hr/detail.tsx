import { useLocation, useSearchParams } from 'react-router-dom';
import { Contact } from '../components';
import { useRequest } from 'ahooks';
import { getOrganizationDetail } from './api';
import { Table } from 'antd';
import { Empty, SafeArea } from 'antd-mobile';
import { Loading } from '@src/components';
import { useEffect } from 'react';
import { commonSorter, formatPer } from '@src/utils/helper';

const HrDetail = () => {
  const [search] = useSearchParams();
  const { item } = useLocation().state || {};

  const shopIds = search.get('shopIds') || '';
  const statisticsMonth = search.get('statisticsMonth') || '';

  useEffect(() => {
    document.title = `${item?.groupName}编制明细数据`;
  }, []);

  const { data, loading } = useRequest(() => {
    return getOrganizationDetail({ shopIds: [shopIds] + '', statisticsMonth });
  });

  const { details } = data || { details: [] };

  if (!item) return <Empty description="暂无数据" />;

  return (
    <div>
      <div className="flex">
        <div
          className="w-[6px] h-[75px] rounded-l-[3px]"
          style={{
            background:
              'linear-gradient(356deg, #FFBC6A -1%, #FF8400 99%), linear-gradient(356deg, #FFBC6A -1%, #FF8400 99%)',
          }}
        ></div>
        <div className="bg-white flex items-center justify-between flex-1 pl-5 pr-6 text-center">
          <div className="leading-none">
            <div className="text-grey text-sm leading-none mb-2">{item.percent?.label}</div>
            <div className="text-[15px]">{item.percent.value}</div>
          </div>
          {item.columns.map((v: any, idx: number) => (
            <div key={idx} className="text-grey text-sm leading-none">
              <div className="mb-2">{v.label}</div>
              <div className="text-[15px]">{v.value}</div>
            </div>
          ))}
        </div>
      </div>
      <Loading spinning={loading}>
        <div className="mx-3 mt-3">
          <Table
            rowKey="postId"
            showSorterTooltip={false}
            bordered
            columns={[
              { title: '编制名称', dataIndex: 'postName', align: 'center' },
              {
                title: '门店编制人数',
                dataIndex: 'headcount',
                align: 'center',
                render: (val: number) => formatPer(val, '人'),
              },
              {
                title: '门店在岗人数',
                dataIndex: 'employees',
                align: 'center',
                render: (val: number) => formatPer(val, '人'),
              },
              {
                title: '达成率',
                dataIndex: 'achievementRate',
                align: 'center',
                render: (val: number) => formatPer(val, '%'),
                sorter: (a, b) => commonSorter(a.achievementRate, b.achievementRate),
              },
            ]}
            dataSource={details}
            pagination={false}
            scroll={{ y: 600 }}
          />
        </div>
      </Loading>
      <div className="fixed bottom-0 left-0 w-full bg-white">
        <Contact
          type="page"
          title={item?.groupName}
          percent={item.percent}
          manager={{
            name: item.directorName,
            phone: item.directorPhone,
            id: item.directorId,
          }}
        />
        <SafeArea position="bottom" />
      </div>
    </div>
  );
};

export default HrDetail;
