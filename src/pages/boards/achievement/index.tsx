import { useMemo, useState } from 'react';
import AuthorityEnum from '@src/common/authority';
import { usePageContext } from '@src/common/page-context';
import CustomerFilter from '@src/components/CustomerFilter';
import DateFilter from '@src/components/DateFilter';
import { IconFont } from '@src/components/IconFont';
import Loading from '@src/components/Loading';
import PageList from '@src/components/PageList';
import userStore from '@src/store/user';
import {
  commonSorter,
  formatAmount,
  formatPer,
  getDatePercentType,
} from '@src/utils/helper';
import { roleTypeIsManage } from '@src/utils/tokenUtils';
import { useRequest, useUpdateEffect } from 'ahooks';
import { Card, Checkbox, SearchBar, Skeleton, Tabs } from 'antd-mobile';
import { RightOutline } from 'antd-mobile-icons';
import clsx from 'clsx';
import dayjs from 'dayjs';
import { cloneDeep, orderBy, pick } from 'lodash';
import { observer } from 'mobx-react';
import qs from 'qs';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { AchievementFilter } from './AchievementFilter';
import {
  getListV2,
  getMaterialPurchasingTotalRate,
  getMaterialPurchasingTotalRateDetail,
  getOrderPaymentMode,
  getOrderScene,
  getOrderSource,
  getsecondaryComposite,
  getTotalInfoFromDataWarehouse,
  getTurnoverListV2,
} from './api';
import { overviewParams } from './api.type';
import {
  AchievementTypeEnum,
  SecondaryCompositeAvgToolTipMap,
  SecondaryCompositeToolTipMap,
  ToolTipMap,
  typeObj,
  typeOptions,
} from './enum';
import { ProgressBarCard, ProgressBarCardShimmer } from './ProgressBarCard';
import { checkReport, getReport, updateReadFlag } from '../api';
import {
  CustomerInfoV2,
  CustomerType,
  ExceptionReport,
  IndicatorCard,
  IndicatorCardV2,
  Sorter,
} from '../components';
import { BarCard } from '../components/BarCard';
import { HorizontalBarCard } from '../components/HorizontalBarCard';
import PopupTip from '../components/IndicatorCard/PopupTip';
import PieCard from '../components/PieCard';
import styles from '../index.module.scss';

const defaultParams = {
  beginDate: dayjs().format('YYYY-MM-DD'),
  endDate: dayjs().format('YYYY-MM-DD'),
  deptId: '',
  shopCodes: [],
  differenceDate: dayjs().subtract(1, 'months').format('YYYY-MM'), // 物料采购差异率使用的时间筛选
};

const percentDateTypeMap = {
  day: '周',
  month: '月',
  default: '',
};

/* eslint-disable complexity */
const Achievement = observer(() => {
  const { pid, getGroupTree } = usePageContext();
  const { permissionsMap } = userStore;
  const disabledShop = !permissionsMap.has(AuthorityEnum.AchievementShopBtn);
  const disabledGroup = !permissionsMap.has(AuthorityEnum.AchievementGroupBtn);

  const [params, setParams] = useState<overviewParams>({
    ...defaultParams,
    deptId: pid,
  });

  const showTypeOptions = typeOptions.filter((v) => permissionsMap.has(v.auth));
  const [detailType, setDetailType] = useState<AchievementTypeEnum[]>(
    showTypeOptions.length
      ? [showTypeOptions[0].value]
      : [AchievementTypeEnum.预计收入],
  );
  const [dataType, setDataType] = useState<'shop' | 'group'>(
    disabledShop ? 'group' : 'shop',
  );
  const [searchValue, setSearchValue] = useState<string>('');
  const [warnFilte, setWarnFilter] = useState(false);
  const [sorter, setSorter] = useState<string[]>([]);
  const navigator = useNavigate();

  const [achievementFilterParams, setAchievementFilterParams] = useState<
    Record<string, any>
  >({ businessDay: true });

  const { data: showReport, run: showReportRun } = useRequest(
    async () => {
      const res = await checkReport({ reportType: 3 });
      return res;
    },
    {
      ready: roleTypeIsManage(),
    },
  );
  const { data: reportData } = useRequest(
    async () => {
      const res = await getReport({ reportType: 3 });

      return res || {};
    },
    {
      ready: roleTypeIsManage(),
    },
  );
  const { run: updateReadRun } = useRequest(
    async (reportId: number) => {
      /* eslint-disable no-throw-literal */
      if (!reportId) throw null;
      const res = await updateReadFlag(reportId);
      return res;
    },
    {
      manual: false,
      onSuccess: () => {
        showReportRun();
      },
    },
  );

  const [search] = useSearchParams();
  const hiddenDetail = search.get('pageType') !== 'detail'; // 隐藏详情
  const hiddenHome = search.get('pageType') === 'detail'; // 隐藏统计页

  const showGroupNames = useMemo(() => {
    if (!params.deptId) return dataType === 'group' ? '全部组织' : '全部门店';
    const { list } = getGroupTree(+params.deptId);
    if (!list || !list.length) return <></>;
    const curNode = list[list.length - 1];
    const breadcrumbList = list.slice(0, list.length - 1);
    return (
      <>
        {breadcrumbList.map((v: { name: string; id: number }) => (
          <>
            <span
              key={v.id}
              className="text-primary"
              onClick={() => {
                setParams((p) => ({ ...p, deptId: String(v.id) }));
              }}
            >
              {v.name}
            </span>
            <IconFont type="icon-chevron-right" className="mx-1" />
          </>
        ))}
        <span>{curNode.name}</span>
      </>
    );
  }, [params.deptId, dataType]);

  const showPercentDateType = useMemo(() => {
    return {
      same:
        getDatePercentType(params.beginDate, params.endDate) === 'day'
          ? 'day'
          : 'default',
      circle: 'default',
    } as {
      same: 'day' | 'month' | 'default';
      circle: 'day' | 'month' | 'default';
    };
  }, [params.beginDate, params.endDate]);

  // const { data = [{}], loading } = useRequest(
  //   async () => {
  //     const allParams = { ...params };
  //     return Promise.all([
  //       // ...typeOptions.map(({ value }) => getTotalInfo(allParams, value)),
  //       getAlarmStatus(allParams, 'ac'),
  //       getAlarmStatus(allParams, 'turnover'),
  //     ]);
  //   },
  //   { refreshDeps: [params] },
  // );

  // const [acAlarmStatus, turnoverAlarmStatus] = data;

  const {
    data: totalInfoFromDataWarehouse,
    loading: totalInfoFromDataWarehouseLoading,
  } = useRequest(
    () => {
      const allParams = { ...params, ...achievementFilterParams };
      return getTotalInfoFromDataWarehouse(allParams);
    },
    { refreshDeps: [params, achievementFilterParams] },
  );

  const {
    data: orderSceneData,
    loading: orderSceneDataLoading,
    run: orderSceneRun,
  } = useRequest(
    () => {
      const payload = {
        ...params,
        businessDay: achievementFilterParams.businessDay,
        dataType:
          detailType[0] === 'estimatedRevenue'
            ? 0
            : detailType[0] === 'tc'
              ? 1
              : 2,
      };
      return getOrderScene(payload);
    },
    {
      refreshDeps: [detailType[0], totalInfoFromDataWarehouse?.dateType],
      ready: hiddenHome,
      refreshDepsAction: () => {
        if (
          totalInfoFromDataWarehouse?.dateType &&
          totalInfoFromDataWarehouse?.dateType !== 'Intelligence' &&
          [
            AchievementTypeEnum.预计收入,
            AchievementTypeEnum.有效订单数,
            AchievementTypeEnum.客单价,
          ].includes(detailType[0])
        ) {
          orderSceneRun();
        }
      },
    },
  );
  const {
    data: orderSourceData,
    loading: orderSourceLoading,
    run: orderSourceRun,
  } = useRequest(
    () => {
      const payload = {
        ...params,
        businessDay: achievementFilterParams.businessDay,
        dataType:
          detailType[0] === 'estimatedRevenue'
            ? 0
            : detailType[0] === 'tc'
              ? 1
              : 2,
      };
      return getOrderSource(payload);
    },
    {
      refreshDeps: [detailType[0], totalInfoFromDataWarehouse?.dateType],
      ready: hiddenHome,
      refreshDepsAction: () => {
        if (
          totalInfoFromDataWarehouse?.dateType &&
          totalInfoFromDataWarehouse?.dateType !== 'Intelligence' &&
          [
            AchievementTypeEnum.预计收入,
            AchievementTypeEnum.有效订单数,
            AchievementTypeEnum.客单价,
          ].includes(detailType[0])
        ) {
          orderSourceRun();
        }
      },
    },
  );
  const {
    data: secondaryCompositeData,
    loading: secondaryCompositeLoading,
    run: secondaryCompositeRun,
  } = useRequest(
    () => {
      const payload = {
        ...params,
        businessDay: achievementFilterParams.businessDay,
        dataType:
          detailType[0] === 'estimatedRevenue'
            ? 0
            : detailType[0] === 'tc'
              ? 1
              : 2,
      };
      return getsecondaryComposite(payload);
    },
    {
      refreshDeps: [detailType[0], totalInfoFromDataWarehouse?.dateType],
      ready: hiddenHome,
      refreshDepsAction: () => {
        if (
          totalInfoFromDataWarehouse?.dateType &&
          totalInfoFromDataWarehouse?.dateType !== 'Intelligence' &&
          [
            AchievementTypeEnum.预计收入,
            AchievementTypeEnum.有效订单数,
            AchievementTypeEnum.客单价,
          ].includes(detailType[0])
        ) {
          secondaryCompositeRun();
        }
      },
    },
  );
  const {
    data: orderPaymentModeData,
    loading: orderPaymentModeLoading,
    run: orderPaymentModeRun,
  } = useRequest(
    () => {
      const payload = {
        ...params,
        businessDay: achievementFilterParams.businessDay,
        dataType:
          detailType[0] === 'estimatedRevenue'
            ? 0
            : detailType[0] === 'tc'
              ? 1
              : 2,
      };
      return getOrderPaymentMode(payload);
    },
    {
      refreshDeps: [detailType[0], totalInfoFromDataWarehouse?.dateType],
      ready: hiddenHome,
      refreshDepsAction: () => {
        if (
          totalInfoFromDataWarehouse?.dateType &&
          totalInfoFromDataWarehouse?.dateType !== 'Intelligence' &&
          [
            AchievementTypeEnum.预计收入,
            AchievementTypeEnum.有效订单数,
            AchievementTypeEnum.客单价,
          ].includes(detailType[0])
        ) {
          orderPaymentModeRun();
        }
      },
    },
  );

  useUpdateEffect(() => {
    orderSceneRun();
    orderSourceRun();
    secondaryCompositeRun();
    orderPaymentModeRun();
  }, [params, achievementFilterParams.businessDay]);

  const { data: ListData = [], loading: ListDataLoading } = useRequest(
    async () => {
      /* eslint-disable no-throw-literal */
      if (!showTypeOptions.length) throw null;
      let res: any = [];
      if (detailType[0] === AchievementTypeEnum.物料采购差异率) {
        res = await getMaterialPurchasingTotalRateDetail({
          ...pick(params, ['deptId', 'shopCodes']),
          date: params?.differenceDate,
          dataType: dataType === 'shop' ? 2 : 1,
        });
      } else if (detailType[0] === AchievementTypeEnum.营业额预估准确率) {
        res = await getTurnoverListV2({
          ...params,
          filterGroup: dataType === 'shop' ? 'SHOP' : 'DEPT',
        });
      } else {
        res = await getListV2({
          ...params,
          filterGroup: dataType === 'shop' ? 'SHOP' : 'DEPT',
          businessDay: achievementFilterParams.businessDay,
          dataType:
            detailType[0] === 'estimatedRevenue'
              ? 0
              : detailType[0] === 'tc'
                ? 1
                : 2,
        });
      }

      return res;
    },
    {
      refreshDeps: [
        params,
        dataType,
        detailType[0],
        achievementFilterParams.businessDay,
      ],
      ready: hiddenHome,
    },
  );

  const { data: MaterialPurchasingTotalRate } = useRequest(
    () => {
      return getMaterialPurchasingTotalRate({
        ...pick(params, ['deptId', 'shopCodes']),
        date: params.differenceDate!,
      });
    },
    { refreshDeps: [params] },
  );

  // const alarmData = useMemo(
  //   () => ({
  //     status: acAlarmStatus || turnoverAlarmStatus,
  //     text: `${acAlarmStatus ? 'AC（客单价）' : ''}${
  //       acAlarmStatus && turnoverAlarmStatus ? '、' : ''
  //     }${turnoverAlarmStatus ? '营业额预估准确率' : ''}存在异常，请及时处理。`,
  //   }),
  //   [acAlarmStatus, turnoverAlarmStatus],
  // );
  const ListDataAfterFilterAndSorter = useMemo(() => {
    let _ListData = [];
    if (
      [
        AchievementTypeEnum.预计收入,
        AchievementTypeEnum.有效订单数,
        AchievementTypeEnum.客单价,
      ].includes(detailType[0])
    ) {
      const listMappingTable = {
        [AchievementTypeEnum.预计收入]: 'revenueItemResponse',
        [AchievementTypeEnum.有效订单数]: 'tcItemResponse',
        [AchievementTypeEnum.客单价]: 'acItemResponse',
      };
      _ListData = ListData.map((v: any) => {
        return {
          managerInfo: v.managerInfo,
          ...v[
            listMappingTable[detailType[0] as keyof typeof listMappingTable]
          ],
          groupId: v.groupId,
          type: v.type,
          shopId: v.shopId,
          shopName: v.shopName,
          groupName: v.groupName,
        };
      });
    } else {
      _ListData = ListData;
    }
    let arr = _ListData;
    if (searchValue) {
      arr = _ListData?.filter(
        (v: any) =>
          v?.groupName?.includes(searchValue) ||
          v?.shopName?.includes(searchValue),
      );
    }

    if (warnFilte) {
      arr = arr?.filter((o: any) => o?.anomaly);
    }
    if (sorter && sorter[0]) {
      const [key, order] = sorter[0].split('-');
      arr = cloneDeep(arr).sort((a: any, b: any) =>
        commonSorter(a[key] || 0, b[key] || 0, order),
      );
    }
    return arr;
  }, [ListData, searchValue, sorter, warnFilte]);

  const handleTabChange = (key: AchievementTypeEnum) => {
    setSorter([]);
    setDetailType([key]);
    setWarnFilter(false);
  };

  const isUp = (val: number) => {
    if (val === 0) return null;
    if (val > 0)
      return (
        <IconFont
          className="ml-1"
          type="icon-shangjiantou"
          style={{ color: '#DF5850' }}
        />
      );
    if (val < 0)
      return (
        <IconFont
          className="ml-1"
          type="icon-xiajiantou"
          style={{ color: '#00BBB4' }}
        />
      );
  };
  const renderSubIndicatorCard = (type: 'estimate' | 'tc' | 'ac') => {
    const typePremisstions = {
      estimate: [
        {
          code: AuthorityEnum.优惠金额,
          info: {
            title: '优惠金额',
            amount: 'discountAmount',
            qoq: 'discountAmountQoq',
            yoy: 'discountAmountYoy',
            tooltip: ['有效订单的优惠总金额'],
          },
        },
        {
          code: AuthorityEnum.退款金额,
          info: {
            title: '退款金额',
            amount: 'refundAmount',
            qoq: 'refundAmountQoq',
            yoy: 'refundAmountYoy',
            tooltip: ['退款订单的退款总金额'],
          },
        },
        {
          code: AuthorityEnum.日均收入,
          info: {
            title: '日均收入',
            amount: 'dailyAmount',
            qoq: 'dailyAmountQoq',
            yoy: 'dailyAmountYoy',
            tooltip: ['预计实付收入/统计的时间天数'],
          },
        },
        // {
        //   code: AuthorityEnum.其他支出,
        //   info: {
        //     title: '其他支出',
        //     amount: '',
        //     qoq: '',
        //     yoy: '',
        //   },
        // },
      ],
      tc: [
        {
          code: AuthorityEnum.优惠订单,
          info: {
            title: '优惠订单',
            amount: 'tcDiscountOrderCount',
            qoq: 'tcDiscountOrderCountQoq',
            yoy: 'tcDiscountOrderCountYoy',
            tooltip: ['参与优惠抵扣的订单数'],
          },
        },
        {
          code: AuthorityEnum.退款订单,
          info: {
            title: '退款订单',
            amount: 'tcRefundOrderCount',
            qoq: 'tcRefundOrderCountQoq',
            yoy: 'tcRefundOrderCountYoy',
            tooltip: ['退款订单的退款总额'],
          },
        },
        {
          code: AuthorityEnum.日均单数,
          info: {
            title: '日均单数',
            amount: 'tcDailyOrderCount',
            qoq: 'tcDailyOrderCountQoq',
            yoy: 'tcDailyOrderCountYoy',
            tooltip: ['TC/统计的时间天数'],
          },
        },
      ],
      ac: [
        // {
        //   code: AuthorityEnum.日均AC,
        //   info: {
        //     title: '日均AC',
        //     amount: 'acDailyRevenue',
        //     qoq: 'acDailyRevenueQoq',
        //     yoy: 'acDailyRevenueYoy',
        //   },
        // },
      ],
    };
    const authorizedDOMInfo: any[] = [];
    typePremisstions[type].forEach((element) => {
      permissionsMap.has(element.code) && authorizedDOMInfo.push(element.info);
    });
    return (
      <div className="grid grid-cols-2 rounded-b-lg bg-white ">
        {authorizedDOMInfo.map((o, idx) => {
          if (authorizedDOMInfo.length === 1) {
            return (
              <div className="p-4 col-span-2">
                {type !== 'ac' && (
                  <div className="text-dark-14 mb-3 ">
                    <span className="text-13 leading-[13px] mr-1 flex items-center">
                      {o.title}
                      {o.tooltip && (
                        <PopupTip
                          title={o.title}
                          list={o.tooltip}
                          fontSize="text-[15px]"
                        />
                      )}{' '}
                      :
                    </span>
                    <span className="font-semibold text-xs">
                      {type !== 'tc' && '￥'}
                      <span className="text-base leading-4 ">
                        {type === 'tc'
                          ? totalInfoFromDataWarehouse?.[o.amount]
                          : formatAmount(
                              totalInfoFromDataWarehouse?.[o.amount],
                            )}
                      </span>
                      {type === 'tc' && '单'}
                    </span>
                  </div>
                )}
                <div className="flex gap-4 ">
                  <div className="flex gap-x-[2px] items-center text-xs leading-3 ">
                    <span className="text-85">环比:</span>
                    <span className="text-5E">
                      {formatPer(Math.abs(totalInfoFromDataWarehouse?.[o.qoq]))}
                    </span>
                    {isUp(totalInfoFromDataWarehouse?.[o.qoq])}
                  </div>
                  <div className="flex gap-x-[2px] items-center text-xs leading-3">
                    <span className="text-85">
                      {percentDateTypeMap[showPercentDateType.same]}同比:
                    </span>
                    <span className="text-5E">
                      {formatPer(Math.abs(totalInfoFromDataWarehouse?.[o.yoy]))}
                    </span>
                    {isUp(totalInfoFromDataWarehouse?.[o.yoy])}
                  </div>
                </div>
              </div>
            );
          }
          return (
            <div
              key={o.amount}
              className={clsx('w-full p-4 border-line odd:border-r', {
                'border-b': authorizedDOMInfo.length > 2 && idx < 2,
              })}
            >
              <div className="text-13 leading-[13px] text-dark-14 mb-[10px] flex items-center">
                {o.title}
                {o.tooltip && (
                  <PopupTip
                    title={o.title}
                    list={o.tooltip}
                    fontSize="text-[15px]"
                  />
                )}
              </div>
              <div className="text-dark-14 mb-[10px] text-xs">
                {type !== 'tc' && '￥'}
                <span className="text-base leading-4 font-semibold ">
                  {type === 'tc'
                    ? totalInfoFromDataWarehouse?.[o.amount]
                    : formatAmount(totalInfoFromDataWarehouse?.[o.amount])}
                </span>
                {type === 'tc' && '单'}
              </div>
              <div className="flex gap-x-[2px] mb-2 items-center text-xs leading-3 ">
                <span className="text-85">环比:</span>
                <span className="text-5E">
                  {formatPer(Math.abs(totalInfoFromDataWarehouse?.[o.qoq]))}
                </span>
                {isUp(totalInfoFromDataWarehouse?.[o.qoq])}
              </div>
              <div className="flex gap-x-[2px] items-center text-xs leading-3">
                <span className="text-85">
                  {percentDateTypeMap[showPercentDateType.same]}同比:
                </span>
                <span className="text-5E">
                  {formatPer(Math.abs(totalInfoFromDataWarehouse?.[o.yoy]))}
                </span>
                {isUp(totalInfoFromDataWarehouse?.[o.yoy])}
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  const hasOwnAuthority = (type: any) => {
    switch (type) {
      case '订单场景':
        const typeAuthMap1: any = {
          [AchievementTypeEnum.预计收入]: AuthorityEnum.订单场景,
          [AchievementTypeEnum.有效订单数]: AuthorityEnum.TC各订单占比,
          [AchievementTypeEnum.客单价]: AuthorityEnum.各场景AC,
        };
        return permissionsMap.has(typeAuthMap1[detailType[0]]);

      case '各场景日均收入':
        const typeAuthMap2: any = {
          [AchievementTypeEnum.预计收入]: AuthorityEnum.各场景日均收入,
          [AchievementTypeEnum.有效订单数]: AuthorityEnum.各场景日均订单数,
        };
        return permissionsMap.has(typeAuthMap2[detailType[0]]);

      case '订单来源':
        const typeAuthMap3: any = {
          [AchievementTypeEnum.预计收入]: AuthorityEnum.订单来源,
          [AchievementTypeEnum.有效订单数]: AuthorityEnum.TC订单来源,
          [AchievementTypeEnum.客单价]: AuthorityEnum.各订单来源AC,
        };
        return permissionsMap.has(typeAuthMap3[detailType[0]]);

      case '各支付方式预计收入':
        const typeAuthMap4: any = {
          [AchievementTypeEnum.预计收入]: AuthorityEnum.各支付方式预计收入,
          [AchievementTypeEnum.有效订单数]: AuthorityEnum.各支付方式订单量,
          [AchievementTypeEnum.客单价]: AuthorityEnum.各支付方式AC,
        };
        return permissionsMap.has(typeAuthMap4[detailType[0]]);

      case '实付收入':
        const typeAuthMap5: any = {
          [AchievementTypeEnum.预计收入]: AuthorityEnum.实付收入,
          [AchievementTypeEnum.有效订单数]: AuthorityEnum.tc实付收入,
          [AchievementTypeEnum.客单价]: AuthorityEnum.ac实付收入,
        };
        return permissionsMap.has(typeAuthMap5[detailType[0]]);

      case '日均实付':
        const typeAuthMap6: any = {
          [AchievementTypeEnum.预计收入]: AuthorityEnum.日均实付,
          [AchievementTypeEnum.有效订单数]: AuthorityEnum.tc日均实付,
        };
        return permissionsMap.has(typeAuthMap6[detailType[0]]);

      default:
        return false;
    }
  };

  const IndicatorMap = {
    [AchievementTypeEnum.预计收入]: (
      <>
        <IndicatorCardV2
          title="预计实付收入"
          tooltip={ToolTipMap[AchievementTypeEnum.预计收入]}
          indicator={
            formatAmount(
              totalInfoFromDataWarehouse?.estimatedRevenue,
              '',
              undefined,
              true,
            ) +
            (Math.abs(totalInfoFromDataWarehouse?.estimatedRevenue) < 10000
              ? '元'
              : '万元')
          }
          percent={{
            same: formatPer(totalInfoFromDataWarehouse?.estimatedRevenueYoy),
            circle: formatPer(totalInfoFromDataWarehouse?.estimatedRevenueQoq),
          }}
          percentDateType={showPercentDateType}
        />
        {renderSubIndicatorCard('estimate')}
        {permissionsMap.has(AuthorityEnum.门店实时预计收入) && (
          <div className="bg-white mt-[10px] rounded-lg">
            <div
              className="bg-white h-10 flex items-center rounded-lg px-3"
              onClick={() => {
                navigator('/boards/achievement/detail/estimatedRevenue', {
                  state: { params: { ...params, dataType: 0 } },
                });
              }}
            >
              <IconFont
                type="icon-xingzhuangjiehe"
                className="mr-1 text-[16px] text-primary"
              />
              <div className="flex-1 text-grey text-[14px]">
                门店实时预计实付收入
              </div>
              <RightOutline className="text-B8 text-xs leading-none ml-1" />
            </div>
          </div>
        )}
      </>
    ),
    [AchievementTypeEnum.有效订单数]: (
      <>
        <IndicatorCardV2
          title="TC"
          tooltip={ToolTipMap[AchievementTypeEnum.有效订单数]}
          indicator={formatPer(totalInfoFromDataWarehouse?.tcOrderCount, '单')}
          percent={{
            same: formatPer(totalInfoFromDataWarehouse?.tcOrderCountYoy),
            circle: formatPer(totalInfoFromDataWarehouse?.tcOrderCountQoq),
          }}
          percentDateType={showPercentDateType}
          content={renderSubIndicatorCard('tc')}
        />
        {permissionsMap.has(AuthorityEnum.门店实时订单量) && (
          <div className="bg-white mt-[10px] rounded-lg">
            <div
              className="bg-white h-10 flex items-center rounded-lg px-3"
              onClick={() => {
                navigator('/boards/achievement/detail/tc', {
                  state: { params: { ...params, dataType: 1 } },
                });
              }}
            >
              <IconFont
                type="icon-xingzhuangjiehe"
                className="mr-1 text-[16px] text-primary"
              />
              <div className="flex-1 text-grey text-[14px]">门店实时订单数</div>
              <RightOutline className="text-B8 text-xs leading-none ml-1" />
            </div>
          </div>
        )}
      </>
    ),
    [AchievementTypeEnum.客单价]: (
      <div className="rounded-lg overflow-hidden">
        <IndicatorCardV2
          title="AC"
          tooltip={ToolTipMap[AchievementTypeEnum.客单价]}
          indicator={formatPer(totalInfoFromDataWarehouse?.acRevenue, '元')}
          percent={{
            same: formatPer(totalInfoFromDataWarehouse?.acRevenueYoy),
            circle: formatPer(totalInfoFromDataWarehouse?.acRevenueQoq),
          }}
          percentDateType={showPercentDateType}
          // isWarning={!!acAlarmStatus}
        />
        {renderSubIndicatorCard('ac')}
      </div>
    ),
    [AchievementTypeEnum.营业额预估准确率]: (
      <IndicatorCardV2
        title="营业额预估准确率"
        className="rounded-lg"
        lineClassName="rounded-bl-base"
        tooltip={ToolTipMap[AchievementTypeEnum.营业额预估准确率]}
        indicator={formatPer(
          totalInfoFromDataWarehouse?.estimatedTurnoverPrecision,
        )}
        count={{
          label: '预估营业额',
          value: formatAmount(totalInfoFromDataWarehouse?.estimatedTurnover),
        }}
        percentDateType={showPercentDateType}
        // isWarning={!!turnoverAlarmStatus}
      />
    ),
    [AchievementTypeEnum.物料采购差异率]: (
      <IndicatorCardV2
        title="物料采购差异率"
        className="rounded-lg"
        lineClassName="rounded-bl-base"
        indicator={formatPer(MaterialPurchasingTotalRate?.rate)}
        tooltip={ToolTipMap[AchievementTypeEnum.物料采购差异率]}
        percent={{
          same: formatPer(MaterialPurchasingTotalRate?.yoyRate),
          circle: formatPer(MaterialPurchasingTotalRate?.momRate),
        }}
        // percentDateType={showPercentDateType}
        isWarning={MaterialPurchasingTotalRate?.anomaly}
        isWarningText="存在门店异常"
        columns={[
          {
            label: '差异金额',
            value: `￥${formatAmount(
              MaterialPurchasingTotalRate?.gapTotalPrice || 0,
            )}`,
          },
          {
            label: '实际耗用金额',
            value: `￥${formatAmount(
              MaterialPurchasingTotalRate?.realConsumptionTotalPrice || 0,
            )}`,
          },
          {
            label: '理论耗用金额',
            value: `￥${formatAmount(MaterialPurchasingTotalRate?.expectConsumptionTotalPrice || 0)}`,
          },
        ]}
      />
    ),
  };

  const MaterialPurchasingIndicatorCard = useMemo(() => {
    return (
      <div className="mt-2">
        <div className="py-2 pl-4 pr-3 flex justify-between items-center bg-white">
          <span className="text-h14 text-[#B8B8B8]">时间筛选</span>
          <DateFilter
            value={[
              dayjs(params.differenceDate).toDate(),
              dayjs(params.differenceDate).toDate(),
            ]}
            onChange={(e) => {
              setParams((p) => ({
                ...p,
                differenceDate: dayjs(e?.[0]).format('YYYY-MM'),
              }));
            }}
            onlyMonth
            max={dayjs().subtract(1, 'month').toDate()}
            type="month"
          />
        </div>
        <IndicatorCard
          title="物料采购差异率"
          indicator={formatPer(MaterialPurchasingTotalRate?.rate)}
          percent={{
            same: formatPer(MaterialPurchasingTotalRate?.yoyRate),
            circle: formatPer(MaterialPurchasingTotalRate?.momRate),
          }}
          type="full"
          // percentDateType={showPercentDateType}
          isWarning={MaterialPurchasingTotalRate?.anomaly}
          isWarningText="存在门店异常"
          handleClick={() => {
            handleTabChange(AchievementTypeEnum.物料采购差异率);
          }}
        />
      </div>
    );
  }, [MaterialPurchasingTotalRate]);

  return (
    <div>
      <Loading spinning={totalInfoFromDataWarehouseLoading}>
        <div hidden={hiddenDetail} className="bg-white">
          <div className="flex items-center justify-between pr-4 pl-1 border-solid border-b border-line">
            <Tabs
              className={styles.iAdmTabs1}
              activeKey={detailType[0]}
              onChange={(key) => {
                handleTabChange(key as AchievementTypeEnum);
              }}
              stretch={false}
              style={{
                '--title-font-size': '0.875rem',
              }}
            >
              {showTypeOptions.map(({ value, label }) => (
                <Tabs.Tab title={label} key={value} />
              ))}
            </Tabs>
            {/* <DataTypeSelect
              options={showTypeOptions}
              defaultValue={detailType[0]}
              onChange={(key) => {
                setSorter([]);
                setDetailType([key as AchievementTypeEnum]);
              }}
            /> */}
          </div>
        </div>
        <Card
          className="rounded-none px-4 border-solid border-b border-line py-0"
          bodyClassName="!py-0"
        >
          <div className="flex justify-between items-start ">
            <CustomerFilter
              className="py-2"
              noBg={true}
              groupMaxWidth="3.625rem"
              value={{ groupId: params.deptId, shopCodes: params.shopCodes }}
              onChange={(e: any) => {
                setParams((p) => ({
                  ...p,
                  shopCodes: e.shopCodes,
                  deptId: e.groupId,
                }));
              }}
            />
            {detailType[0] === AchievementTypeEnum.物料采购差异率 ? (
              <DateFilter
                noBg={true}
                value={[
                  dayjs(params.differenceDate).toDate(),
                  dayjs(params.differenceDate).toDate(),
                ]}
                onChange={(e) => {
                  setParams((p) => ({
                    ...p,
                    differenceDate: dayjs(e?.[0]).format('YYYY-MM'),
                  }));
                }}
                max={dayjs().subtract(1, 'month').toDate()}
                key="month"
                onlyMonth
                type="month"
                className="my-2"
              />
            ) : (
              <DateFilter
                noBg={true}
                value={[
                  dayjs(params.beginDate).toDate(),
                  dayjs(params.endDate).toDate(),
                ]}
                onChange={(e: any) => {
                  setParams((p) => ({ ...p, beginDate: e[0], endDate: e[1] }));
                }}
                key="range"
                className="my-2"
                onlyDate={
                  totalInfoFromDataWarehouse?.dateType === 'Intelligence' &&
                  true
                }
                selectionMode={
                  totalInfoFromDataWarehouse?.dateType === 'Intelligence'
                    ? 'single'
                    : undefined
                }
              />
            )}
            {detailType[0] !== AchievementTypeEnum.物料采购差异率 &&
              detailType[0] !== AchievementTypeEnum.营业额预估准确率 && (
                <AchievementFilter
                  noBg={true}
                  value={achievementFilterParams}
                  onChange={(value: Record<string, any>) => {
                    setAchievementFilterParams(value);
                  }}
                  isIntelligence={
                    totalInfoFromDataWarehouse?.dateType === 'Intelligence'
                  }
                />
              )}
          </div>
        </Card>
        <div hidden={hiddenHome}>
          {permissionsMap.has(AuthorityEnum.预计收入) && (
            <div>
              <IndicatorCard
                title="预计实付收入"
                tooltip={ToolTipMap[AchievementTypeEnum.预计收入]}
                indicator={
                  <>
                    {/* {formatPer(totalInfoFromDataWarehouse?.estimatedRevenue).replace('%', '')} */}
                    {formatAmount(
                      totalInfoFromDataWarehouse?.estimatedRevenue,
                      '',
                      undefined,
                      true,
                    )}
                    <span className="text-[14px]">
                      （
                      {Math.abs(totalInfoFromDataWarehouse?.estimatedRevenue) <
                      10000
                        ? '元'
                        : '万元'}
                      ）
                    </span>
                  </>
                }
                percent={{
                  same: formatPer(
                    totalInfoFromDataWarehouse?.estimatedRevenueYoy,
                  ),
                  circle: formatPer(
                    totalInfoFromDataWarehouse?.estimatedRevenueQoq,
                  ),
                }}
                type="full"
                percentDateType={showPercentDateType}
                isChart={false}
                handleClick={() => {
                  handleTabChange(AchievementTypeEnum.预计收入);
                  !!showReport && updateReadRun(showReport);
                }}
                hasReport={!!showReport}
              />
              {permissionsMap.has(AuthorityEnum.门店实时预计收入) && (
                <div className="bg-white px-3 pb-4 mt-[-4px]">
                  <div
                    className="bg-FA h-10 flex items-center rounded-lg px-3"
                    onClick={() => {
                      navigator('/boards/achievement/detail/estimatedRevenue', {
                        state: {
                          params: {
                            ...params,
                            dataType: 0,
                          },
                        },
                      });
                    }}
                  >
                    <IconFont
                      type="icon-xingzhuangjiehe"
                      className="mr-1 text-[16px] text-primary"
                    />
                    <div className="flex-1 text-grey text-[14px]">
                      门店实时预计实付收入
                    </div>
                    <RightOutline className="text-B8 text-xs leading-none ml-1" />
                  </div>
                </div>
              )}
            </div>
          )}
          {permissionsMap.has(AuthorityEnum.TC) && (
            <div className="mt-2">
              <IndicatorCard
                title="TC"
                tooltip={ToolTipMap[AchievementTypeEnum.有效订单数]}
                indicator={formatPer(
                  totalInfoFromDataWarehouse?.tcOrderCount,
                  '单',
                )}
                percent={{
                  same: formatPer(totalInfoFromDataWarehouse?.tcOrderCountYoy),
                  circle: formatPer(
                    totalInfoFromDataWarehouse?.tcOrderCountQoq,
                  ),
                }}
                type="full"
                percentDateType={showPercentDateType}
                isChart={false}
                handleClick={() =>
                  handleTabChange(AchievementTypeEnum.有效订单数)
                }
              />
              {permissionsMap.has(AuthorityEnum.门店实时订单量) && (
                <div className="bg-white px-3 pb-4 mt-[-4px]">
                  <div
                    className="bg-FA h-10 flex items-center rounded-lg px-3"
                    onClick={() => {
                      navigator('/boards/achievement/detail/tc', {
                        state: { params: { ...params, dataType: 1 } },
                      });
                    }}
                  >
                    <IconFont
                      type="icon-xingzhuangjiehe"
                      className="mr-1 text-[16px] text-primary"
                    />
                    <div className="flex-1 text-grey text-[14px]">
                      门店实时订单数
                    </div>
                    <RightOutline className="text-B8 text-xs leading-none ml-1" />
                  </div>
                </div>
              )}
            </div>
          )}

          {permissionsMap.has(AuthorityEnum.AC) && (
            <IndicatorCard
              className="mt-2"
              title="AC"
              tooltip={ToolTipMap[AchievementTypeEnum.客单价]}
              indicator={formatPer(totalInfoFromDataWarehouse?.acRevenue, '元')}
              percent={{
                same: formatPer(totalInfoFromDataWarehouse?.acRevenueYoy),
                circle: formatPer(totalInfoFromDataWarehouse?.acRevenueQoq),
              }}
              type="full"
              percentDateType={showPercentDateType}
              isChart={false}
              // isWarning={!!acAlarmStatus}
              handleClick={() => handleTabChange(AchievementTypeEnum.客单价)}
            />
          )}
          {permissionsMap.has(AuthorityEnum.营业额预估准确率) && (
            <IndicatorCard
              className="mt-2"
              title="营业额预估准确率"
              tooltip={ToolTipMap[AchievementTypeEnum.营业额预估准确率]}
              indicator={formatPer(
                totalInfoFromDataWarehouse?.estimatedTurnoverPrecision,
              )}
              indicatorElement={
                <div className="flex flex-col justify-end items-end leading-none">
                  <div className="mt-2 text-5E text-[13px]">预估营业额</div>
                  <div className=" text-dark mt-1">
                    {/* {formatPer(totalInfoFromDataWarehouse?.estimatedTurnover).replace('%', '')} */}
                    <span className="text-[12px] leading-[24px] align-bottom mr-[2px]">
                      ￥
                    </span>
                    <span className="text-[16px] leading-[24px] align-bottom">
                      {formatAmount(
                        totalInfoFromDataWarehouse?.estimatedTurnover,
                        '',
                        undefined,
                        true,
                      )}
                    </span>
                    <span className="text-[12px] text-5E leading-[24px] align-bottom">
                      {`（${
                        Math.abs(
                          totalInfoFromDataWarehouse?.estimatedTurnover,
                        ) < 10000
                          ? '元'
                          : '万元'
                      }）`}
                    </span>
                  </div>
                </div>
              }
              type="full"
              percentDateType={showPercentDateType}
              // isWarning={!!turnoverAlarmStatus}
              handleClick={() =>
                handleTabChange(AchievementTypeEnum.营业额预估准确率)
              }
            />
          )}
          {permissionsMap.has(AuthorityEnum.物料采购差异率) &&
            MaterialPurchasingIndicatorCard}
        </div>
        {/* <div hidden={hiddenHome}>
          <INoticeBar
            className="mt-2"
            status={alarmData.status ? 'danger' : 'success'}
            content={`当前管辖范围内，${!alarmData.status ? '无异常指标。' : alarmData.text}`}
          />
        </div> */}

        {/* 诊断报告 */}
        <div
          hidden={
            hiddenDetail ||
            AchievementTypeEnum.预计收入 !== detailType[0] ||
            !roleTypeIsManage()
          }
          className="px-2 pt-2"
        >
          <ExceptionReport
            className="text-grey"
            hasReport={!!reportData?.reportId}
            title="外卖营业额环比下降超10%"
            shopNum={reportData?.businessFieldObject?.shopCount}
            time={reportData?.reportTime || ''}
            content={
              reportData?.anomalyStatus
                ? `截止${dayjs(reportData?.reportTime).format('YYYY年MM月DD号')}，${
                    reportData?.businessFieldObject?.shopCount ?? 0
                  }家门店外卖日均营业额环比下降超10%`
                : `太棒了，截止到${dayjs(reportData?.reportTime).format(
                    'YYYY年MM月DD号',
                  )}您管辖范围下的门店外卖营业数据都达到了标准`
            }
            type="page"
            footerClick={() => {
              navigator(
                `/boards/exception/report${qs.stringify(
                  {
                    type: 'achievement',
                    startCreateTime: params.beginDate,
                    endCreateTime: params.endDate,
                  },
                  { addQueryPrefix: true },
                )}`,
              );
            }}
            onDetailClick={() => {
              navigator(
                `/boards/quick_creation${qs.stringify(
                  {
                    type: 'achievement',
                    reportId: reportData?.reportId,
                  },
                  { addQueryPrefix: true },
                )}`,
              );
            }}
            hiddenDetialEntry={!!!reportData?.anomalyStatus}
          />
        </div>
        <div hidden={hiddenDetail} className="mt-2 mx-2 rounded-t-2xl">
          {IndicatorMap[detailType[0]] || <></>}
        </div>
        <div
          hidden={
            hiddenDetail ||
            AchievementTypeEnum.营业额预估准确率 === detailType[0] ||
            AchievementTypeEnum.物料采购差异率 === detailType[0]
          }
        >
          {hasOwnAuthority('实付收入') && (
            <div className="mt-[10px] mx-2 bg-white rounded-lg overflow-hidden">
              {!secondaryCompositeLoading && (
                <HorizontalBarCard
                  title={
                    detailType[0] === AchievementTypeEnum.预计收入
                      ? '实付收入分析'
                      : detailType[0] === AchievementTypeEnum.有效订单数
                        ? 'TC分析'
                        : 'AC分析'
                  }
                  id="HorizontalBarCard1"
                  titleClassName="text-[#333333] font-medium"
                  data={
                    detailType[0] === AchievementTypeEnum.预计收入
                      ? [
                          {
                            name: '实付收入(到店)',
                            value:
                              secondaryCompositeData?.revenueResponse
                                ?.amountToStore,
                            label: `￥${formatAmount(
                              secondaryCompositeData?.revenueResponse
                                ?.amountToStore,
                            )}`,
                            yoy: secondaryCompositeData?.revenueResponse
                              ?.yoyRateToStore,
                            qoq: secondaryCompositeData?.revenueResponse
                              ?.qoqRateToStore,
                          },
                          {
                            name: '实付收入(到家)',
                            value:
                              secondaryCompositeData?.revenueResponse
                                ?.amountToHome,
                            label: `￥${formatAmount(
                              secondaryCompositeData?.revenueResponse
                                ?.amountToHome,
                            )}`,
                            yoy: secondaryCompositeData?.revenueResponse
                              ?.yoyRateToHome,
                            qoq: secondaryCompositeData?.revenueResponse
                              ?.qoqRateToHome,
                          },
                        ]
                      : detailType[0] === AchievementTypeEnum.有效订单数
                        ? [
                            {
                              name: 'TC(到店)',
                              value:
                                secondaryCompositeData?.tcResponse?.tcToStore,
                              label: `${formatAmount(
                                secondaryCompositeData?.tcResponse?.tcToStore,
                              )}单`,
                              yoy: secondaryCompositeData?.tcResponse
                                ?.yoyRateToStore,
                              qoq: secondaryCompositeData?.tcResponse
                                ?.qoqRateToStore,
                            },
                            {
                              name: 'TC(到家)',
                              value:
                                secondaryCompositeData?.tcResponse?.tcToHome,
                              label: `${formatAmount(
                                secondaryCompositeData?.tcResponse?.tcToHome,
                              )}单`,
                              yoy: secondaryCompositeData?.tcResponse
                                ?.yoyRateToHome,
                              qoq: secondaryCompositeData?.tcResponse
                                ?.qoqRateToHome,
                            },
                          ]
                        : [
                            {
                              name: 'AC(到店)',
                              value:
                                secondaryCompositeData?.acResponse
                                  ?.amountToStore,
                              label: `￥${formatAmount(
                                secondaryCompositeData?.acResponse
                                  ?.amountToStore,
                              )}`,
                              yoy: secondaryCompositeData?.acResponse
                                ?.yoyRateToStore,
                              qoq: secondaryCompositeData?.acResponse
                                ?.qoqRateToStore,
                            },
                            {
                              name: 'AC(到家)',
                              value:
                                secondaryCompositeData?.acResponse
                                  ?.amountToHome,
                              label: `￥${formatAmount(
                                secondaryCompositeData?.acResponse
                                  ?.amountToHome,
                              )}`,
                              yoy: secondaryCompositeData?.acResponse
                                ?.yoyRateToHome,
                              qoq: secondaryCompositeData?.acResponse
                                ?.qoqRateToHome,
                            },
                          ]
                  }
                  tooltip={
                    SecondaryCompositeToolTipMap[
                      detailType[0] as keyof typeof SecondaryCompositeToolTipMap
                    ]
                  }
                />
              )}
            </div>
          )}
        </div>
        <div
          hidden={
            hiddenDetail ||
            AchievementTypeEnum.营业额预估准确率 === detailType[0] ||
            AchievementTypeEnum.物料采购差异率 === detailType[0] ||
            AchievementTypeEnum.客单价 === detailType[0]
          }
        >
          {hasOwnAuthority('日均实付') && (
            <div className="mt-[10px] mx-2 bg-white rounded-lg overflow-hidden">
              {!secondaryCompositeLoading && (
                <HorizontalBarCard
                  title={
                    detailType[0] === AchievementTypeEnum.预计收入
                      ? '日均实付收入分析'
                      : '日均TC分析'
                  }
                  id="HorizontalBarCard2"
                  titleClassName="text-[#333333] font-medium"
                  data={
                    detailType[0] === AchievementTypeEnum.有效订单数
                      ? [
                          {
                            name: '日均TC(到店)',
                            value:
                              secondaryCompositeData?.tcResponse?.avgTcToStore,
                            label: `${formatAmount(
                              secondaryCompositeData?.tcResponse?.avgTcToStore,
                            )}单`,
                            yoy: secondaryCompositeData?.tcResponse
                              ?.avgYoyRateToStore,
                            qoq: secondaryCompositeData?.tcResponse
                              ?.avgQoqRateToStore,
                          },
                          {
                            name: '日均TC(到家)',
                            value:
                              secondaryCompositeData?.tcResponse?.avgTcToHome,
                            label: `${formatAmount(
                              secondaryCompositeData?.tcResponse?.avgTcToHome,
                            )}单`,
                            yoy: secondaryCompositeData?.tcResponse
                              ?.avgYoyRateToHome,
                            qoq: secondaryCompositeData?.tcResponse
                              ?.avgQoqRateToHome,
                          },
                        ]
                      : [
                          {
                            name: '日均实付收入(到店)',
                            value:
                              secondaryCompositeData?.revenueResponse
                                ?.avgAmountToStore,
                            label: `￥${formatAmount(
                              secondaryCompositeData?.revenueResponse
                                ?.avgAmountToStore,
                            )}`,
                            yoy: secondaryCompositeData?.revenueResponse
                              ?.avgYoyRateToStore,
                            qoq: secondaryCompositeData?.revenueResponse
                              ?.avgQoqRateToStore,
                          },
                          {
                            name: '日均实付收入(到家)',
                            value:
                              secondaryCompositeData?.revenueResponse
                                ?.avgAmountToHome,
                            label: `￥${formatAmount(
                              secondaryCompositeData?.revenueResponse
                                ?.avgAmountToHome,
                            )}`,
                            yoy: secondaryCompositeData?.revenueResponse
                              ?.avgYoyRateToHome,
                            qoq: secondaryCompositeData?.revenueResponse
                              ?.avgQoqRateToHome,
                          },
                        ]
                  }
                  horizontalColorIndex={2}
                  tooltip={
                    SecondaryCompositeAvgToolTipMap[
                      detailType[0] as keyof typeof SecondaryCompositeAvgToolTipMap
                    ]
                  }
                />
              )}
            </div>
          )}
        </div>
        <div
          hidden={
            hiddenDetail ||
            AchievementTypeEnum.营业额预估准确率 === detailType[0] ||
            AchievementTypeEnum.物料采购差异率 === detailType[0]
          }
        >
          {!orderSceneDataLoading && (
            <div className="mt-[10px] mx-2 grid grid-cols-2 divide-x divide-line bg-white rounded-lg overflow-hidden">
              {hasOwnAuthority('订单场景') &&
                (detailType[0] === AchievementTypeEnum.客单价 ? (
                  <BarCard
                    title="订单场景"
                    id="BarCard1"
                    titleClassName="text-[#333333] font-medium"
                    data={(orderSceneData || []).map((o: any) => {
                      return {
                        name: o?.label,
                        value: o?.acIncome,
                        label: `￥${formatAmount(o?.acIncome)}`,
                      };
                    })}
                    layout="full"
                  />
                ) : (
                  <PieCard
                    title="订单场景"
                    titleClassName="text-[#333333] font-medium"
                    data={(orderSceneData || []).map((o: any) => {
                      if (detailType[0] === AchievementTypeEnum.有效订单数) {
                        return {
                          name: o?.label,
                          value: o?.orderCount,
                          label: `${formatAmount(o?.orderCount)}单`,
                        };
                      }
                      return {
                        name: o?.label,
                        value: o?.income,
                        label: `￥${formatAmount(o?.income)}`,
                      };
                    })}
                    layout="vertical"
                    // 如果有订单场景和各场景日均收入权限，则展示平分布局，否则独占布局
                    colSpan={
                      hasOwnAuthority('各场景日均收入') ? 'half' : 'full'
                    }
                  />
                ))}
              {hasOwnAuthority('各场景日均收入') && (
                <BarCard
                  title="日均收入"
                  id="BarCard2"
                  titleClassName="text-[#333333] font-medium"
                  data={(orderSceneData || []).map((o: any) => {
                    if (detailType[0] === AchievementTypeEnum.有效订单数) {
                      return {
                        name: o?.label,
                        value: o?.dailyOrderCount,
                        label: `${formatAmount(o?.dailyOrderCount)}单`,
                      };
                    }
                    return {
                      name: o?.label,
                      value: o?.dailyIncome,
                      label: `￥${formatAmount(o?.dailyIncome)}`,
                    };
                  })}
                  // 如果有订单场景和各场景日均收入权限，则展示平分布局，否则独占布局
                  layout={hasOwnAuthority('订单场景') ? 'half' : 'full'}
                />
              )}
            </div>
          )}
        </div>
        {hasOwnAuthority('订单来源') && (
          <div
            hidden={
              hiddenDetail ||
              AchievementTypeEnum.营业额预估准确率 === detailType[0] ||
              AchievementTypeEnum.物料采购差异率 === detailType[0]
            }
            className="mt-2 mx-2"
          >
            {!orderSourceLoading ? (
              detailType[0] === AchievementTypeEnum.客单价 ? (
                // <AcBarCard
                //   title="订单来源"
                //   data={(orderSourceData || []).map((o: any) => {
                //     return {
                //       name: o?.label,
                //       value: formatAmount(o?.income) ?? '-',
                //     };
                //   })}
                // />
                <div className="bg-white">
                  <BarCard
                    title="订单来源"
                    id="BarCard3"
                    titleClassName="text-[#333333] font-medium"
                    data={(orderSourceData || []).map((o: any) => {
                      return {
                        name: o?.label,
                        value: o?.acIncome,
                        label: `￥${formatAmount(o?.acIncome)}`,
                      };
                    })}
                    layout="full"
                  />
                </div>
              ) : (
                <ProgressBarCard
                  flag={detailType[0]}
                  title="订单来源"
                  data={orderBy(
                    orderSourceData || [],
                    ({ income, orderCount }) => orderCount || income || 0,
                    'desc',
                  ).map((o: any) => {
                    if (detailType[0] === AchievementTypeEnum.有效订单数) {
                      return {
                        name: o?.label,
                        value: o?.orderCount ?? '-',
                        percent: o?.tcProportion ?? 0,
                        type: 'number',
                      };
                    }
                    return {
                      name: o?.label,
                      value: formatAmount(o?.income) ?? '-',
                      percent: o?.revenueProportion ?? 0,
                      type: 'money',
                    };
                  })}
                />
              )
            ) : (
              <ProgressBarCardShimmer />
            )}
          </div>
        )}
        {hasOwnAuthority('各支付方式预计收入') && (
          <div
            hidden={
              hiddenDetail ||
              AchievementTypeEnum.营业额预估准确率 === detailType[0] ||
              AchievementTypeEnum.物料采购差异率 === detailType[0]
            }
            className="mt-2 mx-2"
          >
            {!orderPaymentModeLoading ? (
              detailType[0] === AchievementTypeEnum.客单价 ? (
                // <AcBarCard
                //   title="支付方式"
                //   data={(orderPaymentModeData || []).map((o: any) => {
                //     return {
                //       name: o?.label,
                //       value: formatAmount(o?.income) ?? '-',
                //     };
                //   })}
                // />
                <div className="bg-white rounded-lg overflow-hidden">
                  <BarCard
                    title="支付方式"
                    id="BarCard4"
                    titleClassName="text-[#333333] font-medium"
                    data={(orderPaymentModeData || []).map((o: any) => {
                      return {
                        name: o?.label,
                        value: o?.acIncome,
                        label: `￥${formatAmount(o?.acIncome)}`,
                      };
                    })}
                    layout="full"
                  />
                </div>
              ) : (
                <ProgressBarCard
                  flag={detailType[0]}
                  title="支付方式"
                  data={orderBy(
                    orderPaymentModeData || [],
                    ({ income, orderCount }) => orderCount || income || '',
                    'desc',
                  ).map((o: any) => {
                    if (detailType[0] === AchievementTypeEnum.有效订单数) {
                      return {
                        name: o?.label,
                        value: o?.orderCount ?? '-',
                        percent: o?.tcProportion ?? 0,
                        type: 'number',
                      };
                    }
                    return {
                      name: o?.label,
                      value: formatAmount(o?.income) ?? '-',
                      percent: o?.revenueProportion ?? 0,
                      type: 'money',
                    };
                  })}
                  progressBarColor="bg-[#4693F7]"
                />
              )
            ) : (
              <ProgressBarCardShimmer />
            )}
          </div>
        )}
        <div
          hidden={hiddenDetail}
          className="pb-2 mt-2 sticky top-0 z-50"
          style={{ background: '#F5F5F5' }}
        >
          <Card
            className="rounded-none  "
            style={{ padding: 0 }}
            bodyClassName="!py-0"
          >
            <div className="flex items-center justify-between px-4 py-2">
              <div className="text-base font-semibold">明细数据</div>
              <CustomerType
                disabledGroup={disabledGroup}
                disabledShop={disabledShop}
                value={dataType}
                onChange={(e) => {
                  setDataType(e as 'shop' | 'group');
                }}
              />
            </div>
            <div className="flex items-center px-4 pb-1">
              <SearchBar
                className={`${styles.iAdmSearchBar} flex-1 mr-2`}
                style={{ '--border-radius': '8px', '--height': '2.5rem' }}
                onClear={() => setSearchValue('')}
                onSearch={setSearchValue}
                placeholder="请输入门店/组织名称"
              />
              <Sorter
                config={typeObj[detailType[0]].sortConfig}
                value={sorter}
                onChange={setSorter}
              />
            </div>

            <div className="flex items-center text-xs px-4 py-2 text-light overflow-x-auto whitespace-nowrap">
              {showGroupNames}
            </div>
            {detailType[0] === AchievementTypeEnum.物料采购差异率 && (
              <div className="flex justify-end mx-4 py-[10px] border-t border-line text-5E">
                <Checkbox
                  checked={warnFilte}
                  onChange={(val) => {
                    setWarnFilter(val);
                  }}
                  style={{
                    '--font-size': '14px',
                    '--icon-size': '16px',
                    '--gap': '6px',
                    lineHeight: 1,
                  }}
                >
                  只看异常门店
                </Checkbox>
              </div>
            )}
          </Card>
        </div>
        <div hidden={hiddenDetail}>
          {ListDataLoading ? (
            <div>
              {new Array(3).fill('').map((_v, index) => (
                <div className="mb-2 px-4 bg-white py-3" key={index}>
                  <Skeleton animated className="w-[200px] h-5" />
                  <Skeleton animated className="w-full h-5 mt-2" />
                  <Skeleton animated className="w-full h-5 mt-2" />
                </div>
              ))}
            </div>
          ) : (
            <PageList
              dataSource={ListDataAfterFilterAndSorter}
              itemRender={(item: any, index) => (
                <div className="mb-2 px-4 bg-white" key={index}>
                  <CustomerInfoV2
                    type={item.type === 1 ? 'group' : 'shop'}
                    title={item.groupName || item.shopName}
                    percent={typeObj[detailType[0]].percent(item)}
                    columns={typeObj[detailType[0]].columns(item)}
                    // 只要物料采购差异率需要展示异常信息
                    isWarning={
                      detailType[0] === AchievementTypeEnum.物料采购差异率 &&
                      item?.anomaly
                    }
                    manager={
                      detailType[0] !== AchievementTypeEnum.营业额预估准确率
                        ? {
                            id: item.managerInfo?.userId,
                            name: item.managerInfo?.name,
                            phone: item.managerInfo?.phone,
                          }
                        : {
                            id: item.directorId,
                            name: item.directorName,
                            phone: item.directorPhone,
                          }
                    }
                    // 组织列表点击事件
                    navNext={() => {
                      navigator('/boards/achievement/list', {
                        state: {
                          params: {
                            ...params,
                            stopId: params.deptId,
                            groupId: item.groupId,
                            dataType:
                              detailType[0] === 'estimatedRevenue'
                                ? 0
                                : detailType[0] === 'tc'
                                  ? 1
                                  : 2,
                            businessDay: achievementFilterParams.businessDay,
                          },
                          detailType: detailType[0],
                        },
                      });
                    }}
                    // 门店列表点击事件
                    navDetail={(() => {
                      if (
                        detailType[0] === AchievementTypeEnum.有效订单数 &&
                        permissionsMap.has(AuthorityEnum.门店实时订单量)
                      ) {
                        return () => {
                          navigator('/boards/achievement/detail/tc', {
                            state: {
                              params: {
                                ...params,
                                shopCodes: [item.shopId],
                                dataType: 1,
                              },
                            },
                          });
                        };
                      }
                      if (
                        detailType[0] === AchievementTypeEnum.预计收入 &&
                        permissionsMap.has(AuthorityEnum.门店实时预计收入)
                      ) {
                        return () => {
                          navigator(
                            '/boards/achievement/detail/estimatedRevenue',
                            {
                              state: {
                                params: {
                                  ...params,
                                  shopCodes: [item.shopId],
                                  dataType: 0,
                                },
                              },
                            },
                          );
                        };
                      }
                      return undefined;
                    })()}
                  />
                </div>
              )}
            />
          )}
        </div>
      </Loading>
    </div>
  );
});

export default Achievement;
