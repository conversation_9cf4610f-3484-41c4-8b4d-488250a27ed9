type FilterItem = {
  label: string;
  value: string | number;
  disableBy?: [string, number][];
  disableKey?: boolean;
  colSpan?: number;
};

type FilterItemsMap = Map<FilterItem, FilterItem[]>;

export const filter_items: FilterItemsMap = new Map([
  [
    { label: '订单场景', value: 'orderScene' },
    [
      {
        label: '堂食',
        value: 0,
        disableBy: [
          ['orderScene', 3],
          ['orderScene', 2],
        ],
      },
      {
        label: '自提',
        value: 1,
        disableBy: [
          ['orderScene', 3],
          ['orderScene', 2],
        ],
      },
      { label: '平台外卖', value: 2, disableBy: [['orderScene', 3]], disableKey: true },
      { label: '自营外卖', value: 3, disableKey: true, disableBy: [['orderScene', 2]] },
    ],
  ],
  [
    { label: '订单来源', value: 'orderSource' },
    [
      { label: '微信小程序', value: 2, colSpan: 2, disableBy: [['orderScene', 2]] },
      { label: '支付宝小程序', value: 3, colSpan: 2, disableBy: [['orderScene', 2]] },
      { label: '抖音小程序', value: 4, disableBy: [['orderScene', 2]] },
      { label: '饿了么', value: 1, disableBy: [['orderScene', 3]] },
      { label: '美团外卖', value: 12, disableBy: [['orderScene', 3]] },
      // {
      //   label: '抖音团购',
      //   value: 11,
      //   disableBy: [
      //     ['orderScene', 3],
      //     ['orderScene', 2],
      //   ],
      // },
      // {
      //   label: '快手团购',
      //   value: 10,
      //   disableBy: [
      //     ['orderScene', 3],
      //     ['orderScene', 2],
      //   ],
      // },
      {
        label: '收银POS',
        value: 7,
        disableBy: [
          ['orderScene', 3],
          ['orderScene', 2],
        ],
      },
      {
        label: '员工APP',
        value: 6,
        disableBy: [
          ['orderScene', 3],
          ['orderScene', 2],
        ],
      },
      {
        label: '运营中心',
        value: 8,
        disableBy: [
          ['orderScene', 3],
          ['orderScene', 2],
        ],
      },
    ],
  ],
]);
// [
//   '支付方式',
//   [
//     { label: '微信', value: 1 },
//     { label: '支付宝', value: 2 },
//     { label: '现金', value: 3 },
//     // 美团外卖
//     // 饿了么
//     // 美团团购
//     // 抖音团购
//     // 快手团购
//     // 小师妹百事通
//     // 优惠券记账金额
//   ],
// ],
