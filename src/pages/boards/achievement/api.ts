import { post } from '@src/api';
import {
  getOrderScenePayload,
  MaterialPurchasingPayload,
  MaterialPurchasingResponse,
  overviewParams,
} from './api.type';
import { AchievementTypeEnum } from './enum';

// 获取汇总信息(废弃)
export const getTotalInfo = (data: overviewParams, type: string) =>
  post(`/om-api/common/kpi/dashboard/${type}/total`, { data });

// 获取汇总信息(数仓)
export const getTotalInfoFromDataWarehouse = (data: overviewParams) =>
  post<any>(`/om-api/common/performance/dashboard/v2/firstly/total`, { data });

// 获取订单场景汇总信息(数仓)
export const getOrderScene = (data: getOrderScenePayload) =>
  post<any>(`/om-api/common/performance/dashboard/v2/secondly/orderScene`, {
    data,
  });
// 获取支付方式汇总信息(数仓)
export const getOrderPaymentMode = (data: getOrderScenePayload) =>
  post<any>(
    `/om-api/common/performance/dashboard/v2/secondly/orderPaymentMode`,
    {
      data,
    },
  );
// 获取订单来源汇总信息(数仓)
export const getOrderSource = (data: getOrderScenePayload) =>
  post<any>(`/om-api/common/performance/dashboard/v2/secondly/orderSource`, {
    data,
  });
// 业绩看板二级复合指标(数仓)
export const getsecondaryComposite = (data: getOrderScenePayload) =>
  post<any>(
    `/om-api/common/performance/dashboard/v2/secondly/total/composite`,
    {
      data,
    },
  );

// 获取信息列表
export const getList = (data: overviewParams, type: AchievementTypeEnum) =>
  post(`/om-api/common/performance/dashboard/${type}/list`, { data });
// 获取信息列表v2
export const getListV2 = (data: overviewParams) =>
  post(`/om-api/common/performance/dashboard/v2/shopOrDept/list`, { data });
// 获取营业预估额信息列表
export const getTurnoverList = (
  data: overviewParams,
  type: AchievementTypeEnum,
) => post(`/om-api/common/performance/dashboard/${type}/list`, { data });
// 获取营业预估额信息列表V2
export const getTurnoverListV2 = (data: overviewParams) =>
  post(`/om-api/common/performance/dashboard/v2/turnover/list`, { data });

// 获取诊断预警状态
export const getAlarmStatus = (data: overviewParams, type: string) =>
  post(`/om-api/common/kpi/dashboard/${type}/alarmStatus`, { data });

// 获取业绩看板-物料差异率整体指标
export const getMaterialPurchasingTotalRate = (
  data: MaterialPurchasingPayload,
) =>
  post<MaterialPurchasingResponse>(
    `/om-api/common/performance/dashboard/v2/materialPurchasing/totalRate`,
    {
      data,
    },
  );
// 获取业绩看板-门店-组织物料差异率详情
export const getMaterialPurchasingTotalRateDetail = (data: any) =>
  post(
    `/om-api/common/performance/dashboard/v2/materialPurchasing/totalRateDetail`,
    {
      data,
    },
  );
