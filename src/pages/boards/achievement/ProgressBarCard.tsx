import { IconFont } from '@src/components/IconFont';
import { formatPer } from '@src/utils/helper';
import { DownOutline } from 'antd-mobile-icons';
import clsx from 'clsx';
import { useEffect, useState } from 'react';

interface ProgressBarCardProps {
  // tab切换时，需要收起列表
  flag: any;
  title: string;
  data: { name: string; value: any; percent: number; type: 'money' | 'number' }[];
  progressBarColor?: string;
}

export const ProgressBarCard = ({
  flag,
  title,
  data,
  progressBarColor = 'bg-primary',
}: ProgressBarCardProps) => {
  const [close, setClose] = useState(true);

  useEffect(() => {
    setClose(true);
  }, [flag]);

  return (
    <div className="p-4 rounded-lg bg-white relative pb-12">
      <h3 className="text-sm leading-[22px] font-medium text-[#333333]">{title}</h3>
      {data.length !== 0 && !data.every((o) => o?.value === '-') ? (
        <>
          <div className="mt-4 flex flex-col gap-5">
            {(close ? data.slice(0, 3) : data).map((o) => {
              return (
                <div className="flex flex-col gap-2" key={o.name}>
                  <div className="flex justify-between text-sm leading-[22px]">
                    <span className="text-85">
                      {o.name}（{formatPer(o.percent)}）
                    </span>
                    <span className="text-5E">
                      {o.type === 'money' && '¥'}
                      {o.value}
                      {o.type === 'number' && '单'}
                    </span>
                  </div>
                  <div className="bg-[#F5F5F5] rounded-[100px] w-full h-[6px] ">
                    <div
                      style={{
                        width: formatPer(o.percent),
                      }}
                      className={`h-full rounded-[100px] ${progressBarColor}`}
                    ></div>
                  </div>
                </div>
              );
            })}
          </div>
          <div
            style={{
              background: close
                ? 'linear-gradient(182deg, rgba(255, 255, 255, 0) 6%, #FFFFFF 72%)'
                : 'transparent',
            }}
            className="absolute bottom-3 left-0 w-full flex justify-center h-[90px] items-end text-[#B8B8B8]"
            onClick={() => {
              setClose((pre) => !pre);
            }}
          >
            <div className="flex items-center gap-x-2">
              {close ? '点击查看更多' : '点击收起'}
              <DownOutline
                className={clsx('text-xs', {
                  'rotate-180': !close,
                })}
              />
            </div>
          </div>
        </>
      ) : (
        <div className="flex flex-col items-center h-full gap-4">
          <IconFont type="icon-zanwushuju" className="text-[90px]  text-[#DCDCDC]" />
          <span className="text-[#858585] text-xs">暂无数据</span>
        </div>
      )}
    </div>
  );
};

export const ProgressBarCardShimmer = () => {
  return (
    <div className="p-4 rounded-lg bg-white relative">
      <h3 className="shimmer w-32 h-[22px] rounded-lg"></h3>
      <div className="mt-4 flex flex-col gap-5">
        <div className="flex flex-col gap-2">
          <div className="flex justify-between text-sm leading-[22px]">
            <span className="shimmer h-[22px] w-40 rounded-lg"></span>
            <span className="shimmer h-[22px] w-16 rounded-lg"></span>
          </div>
          <div className="shimmer rounded-[100px] w-full h-[6px] "></div>
        </div>
        <div className="flex flex-col gap-2">
          <div className="flex justify-between text-sm leading-[22px]">
            <span className="shimmer h-[22px] w-40 rounded-lg"></span>
            <span className="shimmer h-[22px] w-16 rounded-lg"></span>
          </div>
          <div className="shimmer rounded-[100px] w-full h-[6px] "></div>
        </div>
        <div className="flex flex-col gap-2">
          <div className="flex justify-between text-sm leading-[22px]">
            <span className="shimmer h-[22px] w-40 rounded-lg"></span>
            <span className="shimmer h-[22px] w-16 rounded-lg"></span>
          </div>
          <div className="shimmer rounded-[100px] w-full h-[6px] "></div>
        </div>
      </div>
    </div>
  );
};
