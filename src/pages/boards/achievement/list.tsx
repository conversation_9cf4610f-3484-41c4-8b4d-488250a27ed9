import { useMemo, useState } from 'react';
import { usePageContext } from '@src/common/page-context';
import { IconFont } from '@src/components/IconFont';
import Loading from '@src/components/Loading';
import PageList from '@src/components/PageList';
import { commonSorter } from '@src/utils/helper';
import { useRequest } from 'ahooks';
import { Card, SearchBar } from 'antd-mobile';
import { cloneDeep, pick } from 'lodash';
import { useLocation, useNavigate } from 'react-router-dom';
import {
  getListV2,
  getMaterialPurchasingTotalRateDetail,
  getTurnoverListV2,
} from './api';
import { AchievementTypeEnum, typeObj } from './enum';
import { CustomerInfoV2, Sorter } from '../components';
import styles from '../index.module.scss';

const AchievementList = () => {
  const {
    params,
    detailType,
  }: { params: any; detailType: AchievementTypeEnum } =
    useLocation().state || {};
  const { getGroupTree, tagIdObj } = usePageContext();
  const [searchValue, setSearchValue] = useState<string>('');
  const [sorter, setSorter] = useState<string[]>([]);

  const showGroupNames = useMemo(() => {
    const { list } = getGroupTree(+params.groupId, +params.stopId);

    if (!list || !list.length) return <></>;
    // const firstNode = list[0];
    const curNode = list.pop();
    const breadcrumbList = list;
    return (
      <>
        {tagIdObj && params.stopId && (
          <>
            <span>{tagIdObj[params.stopId].name}</span>
            <IconFont type="icon-chevron-right" className="mx-1" />
          </>
        )}
        {breadcrumbList.map(
          (v: { name: string; id: number }, index: number) => (
            <>
              <span
                key={v.id}
                className="text-primary"
                onClick={() => {
                  const idx = breadcrumbList.length - index;
                  navigator(-idx);
                }}
              >
                {v.name}
              </span>
              <IconFont type="icon-chevron-right" className="mx-1" />
            </>
          ),
        )}
        <span>{curNode.name}</span>
      </>
    );
  }, [params.groupId]);

  const { data: list = [], loading } = useRequest(
    async () => {
      if (detailType === AchievementTypeEnum.物料采购差异率) {
        return getMaterialPurchasingTotalRateDetail({
          ...pick(params, ['deptId', 'shopIds']),
          date: params?.differenceDate,
          dataType: 1,
          deptId: params.groupId,
        });
      } else if (detailType === AchievementTypeEnum.营业额预估准确率) {
        return getTurnoverListV2({
          ...params,
          filterGroup: 'DEPT',
          deptId: params.groupId,
        });
      } else {
        return getListV2({
          ...params,
          filterGroup: 'DEPT',
          deptId: params.groupId,
        });
      }
    },
    { refreshDeps: [params, detailType] },
  );

  const ListData = useMemo(() => {
    let _ListData = [];
    if (
      [
        AchievementTypeEnum.预计收入,
        AchievementTypeEnum.有效订单数,
        AchievementTypeEnum.客单价,
      ].includes(detailType)
    ) {
      const listMappingTable = {
        [AchievementTypeEnum.预计收入]: 'revenueItemResponse',
        [AchievementTypeEnum.有效订单数]: 'tcItemResponse',
        [AchievementTypeEnum.客单价]: 'acItemResponse',
      };
      _ListData = list.map((v: any) => {
        return {
          managerInfo: v.managerInfo,
          ...v[listMappingTable[detailType as keyof typeof listMappingTable]],
          groupId: v.groupId,
          type: v.type,
          shopId: v.shopId,
          shopName: v.shopName,
          groupName: v.groupName,
        };
      });
    } else {
      _ListData = list;
    }
    let arr = _ListData;

    if (searchValue) {
      arr = _ListData?.filter(
        (v: any) =>
          v?.groupName?.includes(searchValue) ||
          v?.shopName?.includes(searchValue),
      );
    }
    if (sorter && sorter[0]) {
      const [key, order] = sorter[0].split('-');
      arr = cloneDeep(arr).sort((a: any, b: any) =>
        commonSorter(a[key], b[key], order),
      );
    }
    return arr;
  }, [list, searchValue, sorter, detailType]);

  console.log('ListData', ListData);
  const navigator = useNavigate();

  return (
    <div>
      <Loading spinning={loading}>
        <div
          className="sticky top-0 z-10 pb-3"
          style={{ background: '#F5F5F5' }}
        >
          <Card className="rounded-none" bodyClassName="!pb-0">
            <div className="flex items-center mb-3">
              <SearchBar
                className={`${styles.iAdmSearchBar} flex-1 mr-2`}
                style={{ '--border-radius': '8px', '--height': '2.5rem' }}
                onClear={() => setSearchValue('')}
                onSearch={setSearchValue}
                placeholder="请输入门店/组织名称"
              />
              <Sorter
                config={typeObj[detailType].sortConfig}
                value={sorter}
                onChange={setSorter}
              />
            </div>
            <div className="flex items-center text-xs mt-2 pb-3 text-gray-500 overflow-x-auto whitespace-nowrap">
              {showGroupNames}
            </div>
          </Card>
        </div>
        <PageList
          dataSource={ListData}
          itemRender={(item: any, index) => (
            <div className="mb-2 px-4 bg-white" key={index}>
              <CustomerInfoV2
                type={item.type === 1 ? 'group' : 'shop'}
                title={item.groupName || item.shopName}
                percent={typeObj[detailType].percent(item)}
                columns={typeObj[detailType].columns(item)}
                manager={
                  detailType !== AchievementTypeEnum.营业额预估准确率
                    ? {
                        id: item.managerInfo?.userId,
                        name: item.managerInfo?.name,
                        phone: item.managerInfo?.phone,
                      }
                    : {
                        id: item.directorId,
                        name: item.directorName,
                        phone: item.directorPhone,
                      }
                }
                navNext={() => {
                  navigator('/boards/achievement/list', {
                    state: {
                      params: { ...params, groupId: item.groupId },
                      detailType,
                    },
                    replace: false,
                  });
                }}
                isWarning={
                  detailType === AchievementTypeEnum.物料采购差异率
                    ? item?.anomaly
                    : false
                }
              />
            </div>
          )}
        />
      </Loading>
    </div>
  );
};

export default AchievementList;
