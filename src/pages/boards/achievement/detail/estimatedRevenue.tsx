import { useMemo, useState } from 'react';
import { usePageContext } from '@src/common/page-context';
import CustomerFilter from '@src/components/CustomerFilter';
import DateFilter from '@src/components/DateFilter';
import Loading from '@src/components/Loading';
import { formatAmount, formatPer, thousandthFormat } from '@src/utils/helper';
import { useRequest } from 'ahooks';
import { Card, DatePicker } from 'antd-mobile';
import { DownOutline } from 'antd-mobile-icons';
import cn from 'classnames';
import dayjs from 'dayjs';
import { omit } from 'lodash';
import { observer } from 'mobx-react';
import { useLocation } from 'react-router-dom';
import { getEstimatedRevenueDetailData } from './api';
import DateTable from './components/DateTable';
import Line from './components/Line';
import TimeTable from './components/TimeTable';
import { DateTypeEnum, OrderTypeEnum } from './enum';
import { overviewParams } from '../api.type';

const defaultParams = {
  beginDate: dayjs().format('YYYY-MM-DD'),
  endDate: dayjs().format('YYYY-MM-DD'),
  date: dayjs().format('YYYY-MM-DD'),
  deptId: '',
  shopCodes: [],
  businessDay: true,
};
const estimatedRevenue = observer(() => {
  const { pid } = usePageContext();
  const { state } = useLocation();

  const [params, setParams] = useState<overviewParams & { date: string }>({
    ...defaultParams,
    deptId: pid,
    ...omit(state?.params, ['beginDate', 'endDate']),
  });

  const [type, setType] = useState<DateTypeEnum>(DateTypeEnum.小时订单);
  const [orderType, setOrderType] = useState<OrderTypeEnum>(OrderTypeEnum.当前);
  const [visible, setVisible] = useState<boolean>(false); // 控制单日选择

  const { data, loading } = useRequest(
    async () => {
      const res = await getEstimatedRevenueDetailData({
        ...params,
        queryType: type,
        tabType: 1,
      });

      return res;
    },
    { refreshDeps: [type, params] },
  );
  const {
    dashboardInternalDetail: curData,
    detailsOfSameTimeLastWeek: lastData,
  } = data || {
    dashboardInternalDetail: [],
    detailsOfSameTimeLastWeek: [],
  };
  const weekData = useMemo(() => {
    // 上周同期time要与当前数据的time保持一致
    const fotmatData: any[] = [];
    curData?.forEach((item: any) => {
      const findIndex: number = lastData?.findIndex((i: any) => {
        return item.time === i.time;
      });
      if (findIndex > -1) {
        fotmatData.push(lastData[findIndex]);
      } else {
        fotmatData.push({
          time: item.time,
          dayDate: item.dayDate,
          increaseRate: 0,

          totalAmount: 0,
          eatInAmount: 0,
          selfPickupAmount: 0,
          ptakeawayAmount: 0,
          stakeawayAmount: 0,
        });
      }
    });
    return fotmatData || [];
  }, [curData, lastData]);

  // 小时订单量tooltip
  const getTimeOrderTooltip = (
    curItem: { name: string; marker: string; seriesName: string }[],
  ) => {
    /* eslint-disable prefer-destructuring */
    const { name, marker, seriesName } = curItem[0];
    const data = curData?.find((v: any) => v.time === name);
    const weekSameData = weekData?.find((v) => v.time === name);
    if (curItem?.length > 1) {
      const { marker: weekSameMarker } = curItem[1];
      const str = `<div>
      <div>${marker}${dayjs(`${data?.dayDate} ${name}:00`)
        .subtract(30, 'm')
        .format('HH:mm')}~${name}</div>
        <div>预计收入${thousandthFormat(data?.totalAmount)}</div>
        <div>环比增长(昨日)：${formatPer(data?.increaseRate)}</div>
        <div>同比增长(上周)：${formatPer(data?.lastWeekIncreaseRate)}</div>
        <div style="padding-top:16px">${weekSameMarker}上周同期</div>
        <div>预计收入：${thousandthFormat(weekSameData?.totalAmount)}</div>
      <div>`;
      return str;
    } else {
      // 当前数据Or上周同期单个显示
      const showData = seriesName === '当前数据' ? data : weekSameData;
      let str = '';
      if (seriesName === '当前数据') {
        str = `<div>
        <div>${marker}${seriesName} ${dayjs(`${data?.dayDate} ${name}:00`)
          .subtract(30, 'm')
          .format('HH:mm')}~${name}</div>
        <div>预计收入：${thousandthFormat(showData?.totalAmount)}</div>
        <div>环比增长(昨日)：${formatPer(showData?.increaseRate)}</div>
        <div>同比增长(上周)：${formatPer(data?.lastWeekIncreaseRate)}</div>
      <div>`;
      } else {
        str = `<div>
        <div>${marker}${seriesName} ${dayjs(`${data?.dayDate} ${name}:00`)
          .subtract(30, 'm')
          .format('HH:mm')}~${name}</div>
        <div>预计收入：${thousandthFormat(showData?.totalAmount)}</div>
      <div>`;
      }
      return str;
    }
  };

  // 日订单tooltip
  const getDayOrderTooltip = (curItem: { name: string; marker: string }[]) => {
    /* eslint-disable prefer-destructuring */
    const { name, marker } = curItem[0];
    const data = curData.find((v: any) => v.dayDate === name);
    const str = `<div>
    <div>${marker}${name}</div>
      <div>预计收入：${thousandthFormat(data?.totalAmount)}</div>
      <div>环比增长(昨日)：${formatPer(data?.increaseRate)}</div>
    <div>`;
    return str;
  };

  const accumulateTotalAmount: number = useMemo(() => {
    const timeTotal: number = curData?.reduce(
      (acc: number, cur: { totalAmount: number }) =>
        acc + (cur?.totalAmount || 0),
      0,
    );
    return timeTotal || 0;
  }, [curData]);

  return (
    <Loading spinning={loading}>
      <Card className="rounded-none px-3" bodyClassName="!py-2">
        <div className="flex justify-between items-start">
          <CustomerFilter
            className="flex-1"
            value={{ groupId: params.deptId, shopCodes: params.shopCodes }}
            onChange={(e: any) => {
              setParams((p) => ({
                ...p,
                shopCodes: e.shopCodes,
                deptId: e.groupId,
              }));
            }}
          />
          {(data as any)?.dateType === 'Intelligence' && (
            <div className="flex gap-x-2">
              <button
                className={cn(
                  `focus:outline-none text-13 leading-[13px] bg-black/[0.03] rounded-[4px]  py-2 px-2`,
                  params?.businessDay
                    ? 'text-primary bg-primary/10'
                    : 'text-grey bg-black/[0.03]',
                )}
                onClick={() => {
                  setParams((pre) => ({
                    ...pre,
                    businessDay: true,
                  }));
                }}
              >
                营业日
              </button>
              <button
                className={cn(
                  'focus:outline-none text-13 leading-[13px] bg-black/[0.03] rounded-[4px]  py-2 px-2 ',
                  !params?.businessDay
                    ? 'text-primary bg-primary/10'
                    : 'text-grey bg-black/[0.03]',
                )}
                onClick={() => {
                  setParams((pre) => ({
                    ...pre,
                    businessDay: false,
                  }));
                }}
              >
                自然日
              </button>
            </div>
          )}
        </div>
      </Card>
      <div className="mt-px bg-white py-4">
        <div className="px-3 flex pb-3">
          <div className="flex items-center flex-1">
            <div className="text-85 text-[14px] leading-[22px] pr-[4px]">
              累计预计收入:
            </div>
            <div className="text-dark-14 text-[15px] leading-[22px] font-semibold">
              ￥{formatAmount(+accumulateTotalAmount.toFixed(2))}
            </div>
          </div>
          <div className="text-B8 text-13 leading-[21px]">
            预计实付收入数据折线图
          </div>
        </div>
        <div className="px-3 flex justify-between items-center pb-3">
          <div className="flex items-center text-xs leading-[22px]">
            {/* 补充条件(数据源接入的是智慧门店的不支持月份和日期范围查询) */}
            {/* eslint-disable no-constant-condition  */}
            {((data as any)?.dateType === 'Intelligence'
              ? [{ label: '小时预计实付收入', value: DateTypeEnum.小时订单 }]
              : [
                  { label: '小时预计实付收入', value: DateTypeEnum.小时订单 },
                  { label: '日预计实付收入', value: DateTypeEnum.日订单 },
                ]
            ).map((v) => (
              <div
                key={v.value}
                className={`rounded px-[6px] border border-solid border-px mr-2 ${
                  type === v.value
                    ? 'text-primary border-primary'
                    : 'text-85 border-DC'
                }`}
                onClick={() => {
                  v.value !== type && setType(v.value);
                  if (v.value === DateTypeEnum.小时订单) {
                    setParams((p) => ({
                      ...p,
                      date: params.beginDate,
                      beginDate: params.beginDate,
                      endDate: params.beginDate,
                    }));
                  }
                }}
              >
                {v.label}
              </div>
            ))}
          </div>
          <div>
            {type === DateTypeEnum.日订单 ? (
              <DateFilter
                value={[
                  dayjs(params.beginDate).toDate(),
                  dayjs(params.endDate).toDate(),
                ]}
                onChange={(e: any) => {
                  setParams((p) => ({ ...p, beginDate: e[0], endDate: e[1] }));
                }}
                noBg
              />
            ) : (
              <div
                className="text-primary flex items-center"
                onClick={() => setVisible(true)}
              >
                {dayjs(params.date).format('MM/DD')}
                <DownOutline className="text-xs ml-1" />
              </div>
            )}
          </div>
        </div>
        <div className="bg-white">
          {type === DateTypeEnum.小时订单 ? (
            <Line
              id="time_order"
              title=""
              category={curData?.map((v: any) => v.time) || []}
              seriesConfig={[
                {
                  name: '当前数据',
                  data: curData?.map((v: any) => v.totalAmount) || [],
                  active: true,
                },
                {
                  name: '上周同期',
                  data: weekData?.map((v) => v.totalAmount) || [],
                },
              ]}
              getTooltip={getTimeOrderTooltip}
            />
          ) : (
            <Line
              id="day_order"
              title=""
              category={curData?.map((v: any) => v.dayDate) || []}
              seriesConfig={[
                {
                  name: '当前数据',
                  data: curData?.map((v: any) => v.totalAmount) || [],
                  active: true,
                },
              ]}
              getTooltip={getDayOrderTooltip}
            />
          )}
        </div>
      </div>
      {type === DateTypeEnum.小时订单 ? (
        <div className="px-2 pt-[10px] pb-8">
          <div className="flex items-center pb-[10px] text-xs leading-[24px]">
            {[
              { label: '当前数据', value: OrderTypeEnum.当前 },
              { label: '上周同期订单', value: OrderTypeEnum.上周同期 },
            ].map((v) => (
              <div
                key={v.value}
                className={`rounded px-[6px] mr-2 ${
                  orderType === v.value
                    ? 'text-primary bg-primary-1'
                    : 'text-grey bg-line'
                }`}
                onClick={() => v.value !== orderType && setOrderType(v.value)}
              >
                {v.label}
              </div>
            ))}
          </div>
          <TimeTable
            data={orderType === OrderTypeEnum.当前 ? curData : weekData}
            columns={[
              {
                title: '合计',
                dataIndex: 'totalAmount',
                align: 'center',
                render: (v: any) => (v ? formatAmount(v) : '-'),
              },
            ]}
          />
        </div>
      ) : (
        <div className="px-2 pt-[10px] pb-8">
          <DateTable
            key="date"
            data={curData}
            columns={[
              {
                title: '门店预计收入',
                align: 'center',
                children: [
                  {
                    title: '堂食',
                    dataIndex: 'eatInAmount',
                    align: 'center',
                    render: (v: any) => (v ? formatAmount(v) : '-'),
                  },
                  {
                    title: '自提',
                    dataIndex: 'selfPickupAmount',
                    align: 'center',
                    render: (v: any) => (v ? formatAmount(v) : '-'),
                  },
                  {
                    title: '平台外卖',
                    dataIndex: 'ptakeawayAmount',
                    align: 'center',
                    render: (v: any) => (v ? formatAmount(v) : '-'),
                  },
                  {
                    title: '自营外卖',
                    dataIndex: 'stakeawayAmount',
                    align: 'center',
                    render: (v: any) => (v ? formatAmount(v) : '-'),
                  },
                ],
              },
              {
                title: '合计',
                dataIndex: 'totalAmount',
                align: 'center',
                render: (v: any) => (v ? formatAmount(v) : '-'),
              },
            ]}
          />
        </div>
      )}
      <DatePicker
        title="选择日期"
        visible={visible}
        onClose={() => {
          setVisible(false);
        }}
        max={new Date()}
        value={dayjs(params.date).toDate()}
        onConfirm={(e) => {
          setParams((p) => ({
            ...p,
            date: dayjs(e).format('YYYY-MM-DD'),
            beginDate: dayjs(e).format('YYYY-MM-DD'),
            endDate: dayjs(e).format('YYYY-MM-DD'),
          }));
        }}
      />
    </Loading>
  );
});

export default estimatedRevenue;
