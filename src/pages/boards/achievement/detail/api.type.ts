export type TcDetailParams = {
  beginDate: string;
  endDate: string;
  queryType: number;
  shopIds?: number[];
  deptId?: string;
  tabType: number; // 1-预计收入，2-TC
};

export type TcDetailItem = {
  dayDate: string; //日订单x轴用这个
  eatInOrder: number;
  groupId: number;
  groupName: string;
  increaseRate: number;
  innerOrderNum: number;
  pickUpOrderNum: number;
  takeOutOrderNum: number;
  selfPickupOrder: number;
  takeawayOrder: number;
  time: string; //小时订单x轴用这个
  totalOrderNum: number;
  lastWeekIncreaseRate: number;
};
export type TcDetailResponse = {
  dashboardOrderDetail: TcDetailItem[];
  ordersOfSameTimeLastWeek: TcDetailItem[];
};

export type DashboardInternalDetail = {
  dayDate: string; //日订单x轴用这个
  time: string; //小时订单x轴用这个
  increaseRate: number; // 昨天增长率
  lastWeekIncreaseRate: number; // 上周同比增长率
  // 预计收入的返回值
  eatInAmount: number; // 堂食预计收入（单位：元
  selfPickupAmount: number; // 自提预计收入（单位：元）
  ptakeawayAmount: number; // 平台外卖
  stakeawayAmount: number; // 自营外卖
  totalAmount: number; // 预计总收入（实付，单位：元）
  // TC
  eatInTcNum: number; // 堂食
  selfPickupTcNum: number; // 自提
  ptakeawayTcNum: number; // 平台外卖
  stakeawayTcNum: number; // 自营外卖
  totalTcNum: number;
};
export type EstimatedRevenueDetaiResponse = {
  dashboardInternalDetail: DashboardInternalDetail[];
  detailsOfSameTimeLastWeek: TcDetailItem[];
};
