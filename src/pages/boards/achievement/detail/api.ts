import { post } from '@src/api';
import {
  EstimatedRevenueDetaiResponse,
  TcDetailParams,
  TcDetailResponse,
} from './api.type';

// TC(有效订单)信息详情(废弃)
export const getTcDetailData = (data: TcDetailParams) =>
  post<TcDetailResponse>('/om-api/common/kpi/dashboard/tc/detail/list', {
    data,
  });

// 门店实时预计收入
export const getEstimatedRevenueDetailData = (data: TcDetailParams) =>
  post<EstimatedRevenueDetaiResponse>(
    '/om-api/common/performance/dashboard/v2/internal/detail/list',
    {
      data,
    },
  );
