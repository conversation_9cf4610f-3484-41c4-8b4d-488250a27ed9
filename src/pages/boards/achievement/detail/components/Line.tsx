import React, { useMemo } from 'react';
import ReactECharts, { EChartsOption } from 'echarts-for-react';

interface Props {
  id: string;
  title: string;
  seriesConfig: {
    name: string;
    data: any[];
    active?: boolean;
  }[];
  category: any[];
  getTooltip?: (arr: any[]) => string;
}

const Line: React.FC<Props> = ({
  title,
  seriesConfig,
  category,
  id,
  getTooltip,
}: Props) => {
  const options: EChartsOption = {
    color: [
      '#00BBB4',
      '#6384F0',
      '#5470c6',
      '#91cc75',
      '#fac858',
      '#ee6666',
      '#73c0de',
      '#3ba272',
    ],
    title: {
      text: title,
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      backgroundColor: '#333',
      borderColor: '#333',
      textStyle: {
        color: '#fff',
      },
      confine: true,
      formatter: (params: any[]) => {
        return getTooltip ? getTooltip(params) : '';
      },
    },
    legend: {
      left: '2%',
      top: 0,
      data: seriesConfig.map((v) => ({
        name: v.name,
        icon: 'roundRect',
        textStyle: {
          fontSize: 12,
          color: '#B8B8B8',
          height: 10,
          rich: {
            a: {
              verticalAlign: 'middle',
            },
          },
        },
      })),
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: category,
      axisLabel: {
        // rotate: 30, //让坐标值旋转一定的角度
        showMaxLabel: true,
      },
    },
    yAxis: {
      type: 'value',
    },
    series: seriesConfig.map((v) => ({
      name: v.name,
      type: 'line',
      data: v.data || [],
      smooth: true,
      lineStyle: {
        type: v.active ? 'solid' : 'dotted',
      },
    })),
  };

  const LineCharts = useMemo(() => {
    if (!category || !category.length) return <div />;
    return (
      <ReactECharts
        key={id}
        option={options}
        // style={{
        //   height: '100%',
        //   minHeight: `${data.length * 50 < 250 ? 250 : data.length * 50}px`,
        // }}
      />
    );
  }, [category]);

  return <div>{LineCharts}</div>;
};

export default Line;
