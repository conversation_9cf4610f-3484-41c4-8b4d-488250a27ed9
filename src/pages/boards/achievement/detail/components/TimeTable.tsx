import { Table } from 'antd';
import dayjs from 'dayjs';
import { nanoid } from 'nanoid';

const TimeTable: React.FC<{ data: any[]; columns?: any }> = ({
  data,
  columns,
}) => {
  return (
    <Table
      rowKey={nanoid()}
      showSorterTooltip={false}
      bordered
      columns={[
        {
          title: '时间',
          dataIndex: 'time',
          align: 'center',
          width: '8rem',
          render: (v, { dayDate }) =>
            v
              ? `${dayjs(`${dayDate} ${v}:00`)
                  .subtract(30, 'm')
                  .format(
                    'HH:mm',
                  )}~${dayjs(`${dayDate} ${v}:00`).format('HH:mm')}`
              : '-',
        },
        ...columns,
      ]}
      dataSource={data}
      pagination={false}
    />
  );
};

export default TimeTable;
