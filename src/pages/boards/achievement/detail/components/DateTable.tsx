import { Table } from 'antd';
import dayjs from 'dayjs';
import { nanoid } from 'nanoid';

const DateTable: React.FC<{ data: any[]; columns?: any }> = ({ data, columns }) => {
  const column = [
    {
      title: '时间',
      dataIndex: 'dayDate',
      align: 'center',
      render: (v: any) => (v ? dayjs(v).format('YYYY/MM/DD') : '-'),
    },
    ...columns,
  ];
  return (
    <Table
      rowKey={nanoid()}
      showSorterTooltip={false}
      bordered
      columns={column}
      dataSource={data}
      pagination={false}
      scroll={{ x: 450 }}
    />
  );
};

export default DateTable;
