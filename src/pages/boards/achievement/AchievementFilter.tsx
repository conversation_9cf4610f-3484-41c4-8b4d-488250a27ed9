import { useState } from 'react';
import { IDropdown } from '@src/components/IDropdown/IDropdown';
import cn from 'classnames';
import { filter_items } from './achievement_filter';

interface AchievementFilterProps {
  noBg?: boolean;
  value: Record<string, any>;
  onChange: (value: Record<string, any>) => void;
  isIntelligence?: boolean;
}

export const AchievementFilter = ({
  noBg,
  value,
  onChange,
  isIntelligence,
}: AchievementFilterProps) => {
  const [activity, setActivity] = useState<Record<string, any>>(value);
  const onOk = () => {
    onChange(activity);
  };
  const onClose = () => {
    // 重置一下activity
    setActivity(value);
  };

  const onClear = () => {
    setActivity({ businessDay: true });
  };

  const hasNonEmptyArrays = (obj: Record<string, any>) => {
    if (Object.keys(obj).length === 0) {
      return false; // 对象为空对象，返回 false
    }
    for (const key in obj) {
      if (Array.isArray(obj[key]) && obj[key].length === 0) {
        return false; // 如果值是空数组，则返回 false
      }
    }
    return true; // 所有值都不是空数组，返回 true
  };

  const isDisable = (disableBy: [string, number][]) => {
    for (const iterator of disableBy) {
      const res = (activity[iterator[0]] || []).some(
        (i: number) => i === iterator?.[1],
      );
      if (res) {
        return true;
      }
    }
  };

  return (
    <IDropdown
      onOk={onOk}
      onClose={onClose}
      onClear={onClear}
      isFilter={hasNonEmptyArrays(value)}
      noBg={!!noBg}
    >
      <div className="flex flex-col gap-y-5">
        {isIntelligence && (
          <div className="flex flex-col gap-y-2">
            <h3 className="text-dark-14 text-sm leading-[22px] font-medium ">
              统计周期
            </h3>
            <div className="grid grid-cols-4 gap-2">
              <button
                className={cn(
                  `focus:outline-none text-13 leading-[13px] bg-black/[0.03] rounded-[4px]  py-2 col-span-1 ${activity?.businessDay ? 'text-primary bg-primary/10' : 'text-grey bg-black/[0.03]'}`,
                )}
                onClick={() => {
                  setActivity((pre) => ({
                    ...pre,
                    businessDay: true,
                  }));
                }}
              >
                营业日
              </button>
              <button
                className={cn(
                  `focus:outline-none text-13 leading-[13px] bg-black/[0.03] rounded-[4px]  py-2 col-span-1 ${!activity?.businessDay ? 'text-primary bg-primary/10' : 'text-grey bg-black/[0.03]'}`,
                )}
                onClick={() => {
                  setActivity((pre) => ({
                    ...pre,
                    businessDay: false,
                  }));
                }}
              >
                自然日
              </button>
            </div>
          </div>
        )}
        {(() => {
          const RNode = [];
          for (const [key, value] of filter_items) {
            RNode.push(
              <div key={key.label} className="flex flex-col gap-y-2">
                <h3 className="text-dark-14 text-sm leading-[22px] font-medium ">
                  {key.label}
                </h3>
                <div className="grid grid-cols-4 gap-2">
                  {value.map((o) => {
                    return (
                      <button
                        key={o.label + o.value}
                        className={cn(
                          `focus:outline-none text-13 leading-[13px] bg-black/[0.03] rounded-[4px]  py-2 ${
                            o?.colSpan ? 'col-span-2' : 'col-span-1'
                          } ${
                            (activity?.[key.value] || []).some(
                              (item: any) => item === o.value,
                            )
                              ? 'text-primary bg-primary/10'
                              : 'text-grey bg-black/[0.03]'
                          }`,
                          {
                            'opacity-40': o?.disableBy
                              ? isDisable(o?.disableBy)
                              : false,
                          },
                        )}
                        onClick={() => {
                          setActivity((pre) => ({
                            ...pre,
                            [key.value]: (activity?.[key.value] || []).some(
                              (item: any) => item === o.value,
                            )
                              ? (activity?.[key.value] || []).filter(
                                  (item: any) => item !== o.value,
                                )
                              : [...(pre?.[key.value] || []), o.value],
                          }));

                          // 处理选中需要disable其他选项的筛选项
                          if (
                            !(activity?.[key.value] || []).some(
                              (item: any) => item === o.value,
                            ) &&
                            o.disableKey
                          ) {
                            // 选中了需要disable其他选项的筛选项
                            const disableBy = JSON.stringify([
                              key.value,
                              o.value,
                            ]);
                            // 过滤出，当前筛选项可选的筛选项
                            const allowSelect: any = {};
                            for (const [key, value] of filter_items) {
                              value.forEach((item) => {
                                if (
                                  !(item?.disableBy || [])
                                    .map((i) => JSON.stringify(i))
                                    .includes(disableBy)
                                ) {
                                  allowSelect[key.value] = [
                                    ...(allowSelect[key.value] || []),
                                    item.value,
                                  ];
                                }
                              });
                            }

                            setActivity((pre) => {
                              for (const [key, value] of Object.entries(pre)) {
                                if (Array.isArray(value)) {
                                  pre[key] = (value || [])?.filter((f: any) =>
                                    (allowSelect[key] || []).includes(f),
                                  );
                                }
                              }
                              return { ...pre };
                            });
                          }
                        }}
                        disabled={
                          o?.disableBy ? isDisable(o?.disableBy) : false
                        }
                      >
                        {o.label}
                      </button>
                    );
                  })}
                </div>
              </div>,
            );
          }
          return RNode;
        })()}
      </div>
    </IDropdown>
  );
};
