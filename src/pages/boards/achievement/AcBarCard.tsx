import clsx from 'clsx';

interface AcBarCardProps {
  title: string;
  data: { name: string; value: number }[];
}

export const AcBarCard = ({ title, data }: AcBarCardProps) => {
  return (
    <div className="rounded-lg bg-white relative pb-2">
      <h3 className="text-sm leading-[22px] font-semibold text-[#333333] border-b border-black/[0.03] py-3 px-4">
        {title}
      </h3>
      <div className="mt-[10px] grid grid-cols-3 mx-2 bg-black/[0.03] gap-[1px]">
        {data.map((o, idx) => {
          return (
            <div className="pt-[5px] pb-[10px] pl-[5px] flex flex-col gap-2 bg-white">
              <div
                className={clsx(
                  `flex flex-col text-h14 gap-y-1 ${(idx + 1) % 3 === 1 ? 'pl-2' : 'pl-4'}`,
                )}
              >
                <span className="text-85">{o.name}</span>
                <span className="text-5E">¥{o.value}</span>
              </div>
            </div>
          );
        })}
        {Array(data.length % 3 === 0 ? 0 : 3 - (data.length % 3)).fill(
          <div className="bg-white"></div>,
        )}
      </div>
    </div>
  );
};
