import AuthorityEnum from '@src/common/authority';
import { formatAmount, formatPer } from '@src/utils/helper';

export enum AchievementTypeEnum {
  预计收入 = 'estimatedRevenue',
  有效订单数 = 'tc',
  客单价 = 'ac',
  营业额预估准确率 = 'turnover',
  物料采购差异率 = 'materialPurchase',
}

export const typeOptions = [
  {
    label: '预计实付收入',
    value: AchievementTypeEnum.预计收入,
    auth: AuthorityEnum.预计收入,
  },
  {
    label: 'TC',
    value: AchievementTypeEnum.有效订单数,
    auth: AuthorityEnum.TC,
  },
  {
    label: 'AC',
    value: AchievementTypeEnum.客单价,
    auth: AuthorityEnum.AC,
  },
  {
    label: '营业额预估准确率',
    value: AchievementTypeEnum.营业额预估准确率,
    auth: AuthorityEnum.营业额预估准确率,
  },
  {
    label: '物料采购差异率',
    value: AchievementTypeEnum.物料采购差异率,
    auth: AuthorityEnum.物料采购差异率,
  },
];

export const typeObj = {
  [AchievementTypeEnum.预计收入]: {
    percent: (item: any) => ({
      label: '预计实付收入',
      value: `￥${formatAmount(item.totalAmount, '')}`,
    }),
    columns: (item: any) => [
      { label: '堂食', value: `￥${formatAmount(item.eatInAmount, '')}` },
      { label: '自提', value: `￥${formatAmount(item.selfPickupAmount, '')}` },
      {
        label: '平台外卖',
        value: `￥${formatAmount(item.ptakeawayAmount, '')}`,
      },
      {
        label: '自营外卖',
        value: `￥${formatAmount(item.stakeawayAmount, '')}`,
      },
    ],
    sortConfig: [
      { label: '预计实付收入最高', value: 'totalAmount-desc' },
      { label: '预计实付收入最低', value: 'totalAmount-asc' },
    ],
  },
  [AchievementTypeEnum.有效订单数]: {
    percent: (item: any) => ({
      label: 'TC',
      value: formatPer(item.totalOrder, ''),
    }),
    columns: (item: any) => [
      { label: '堂食', value: formatPer(item.eatInOrder, '') },
      { label: '自提', value: formatPer(item.selfPickupOrder, '') },
      { label: '平台外卖', value: formatPer(item.ptakeawayOrder, '') },
      { label: '自营外卖', value: formatPer(item.stakeawayOrder, '') },
    ],
    sortConfig: [
      { label: '有效订单数最高', value: 'totalOrder-desc' },
      { label: '有效订单数最低', value: 'totalOrder-asc' },
    ],
  },
  [AchievementTypeEnum.客单价]: {
    percent: (item: any) => ({
      label: 'AC',
      value: `￥${formatAmount(item.totalAmount, '')}`,
    }),
    columns: (item: any) => [
      { label: '堂食AC', value: `￥${formatAmount(item.eatInAmount, '')}` },
      { label: '自提AC', value: `￥${formatPer(item.selfPickupAmount, '')}` },
      {
        label: '平台外卖AC',
        value: `￥${formatPer(item.ptakeawayAmount, '')}`,
      },
      {
        label: '自营外卖AC',
        value: `￥${formatPer(item.stakeawayAmount, '')}`,
      },
    ],
    sortConfig: [
      { label: '客单价最高', value: 'totalAmount-desc' },
      { label: '客单价最低', value: 'totalAmount-asc' },
    ],
  },
  [AchievementTypeEnum.营业额预估准确率]: {
    percent: (item: any) => ({
      label: '营业额预估准确率',
      value: formatPer(item.rate),
    }),
    columns: (item: any) => [
      { label: '预估营业额', value: `￥${formatAmount(item.totalAmount, '')}` },
      {
        label: '实际营业额',
        value: `￥${formatAmount(item.actualTotalAmount, '')}`,
      },
    ],
    sortConfig: [
      { label: '准确率最高', value: 'rate-desc' },
      { label: '准确率最低', value: 'rate-asc' },
    ],
  },
  [AchievementTypeEnum.物料采购差异率]: {
    percent: (item: any) => ({
      label: '物料采购差异率',
      value: formatPer(item.rate),
    }),
    columns: (item: any) => [
      { label: '差异金额', value: `￥${formatAmount(item.gapTotalPrice, '')}` },
      {
        label: '实际耗用金额',
        value: `￥${formatAmount(item.realConsumptionTotalPrice, '')}`,
      },
      {
        label: '理论耗用金额',
        value: `￥${formatAmount(item.expectConsumptionTotalPrice, '')}`,
      },
    ],
    sortConfig: [
      { label: '采购差异率最大', value: 'rate-desc' },
      { label: '采购差异率最小', value: 'rate-asc' },
      { label: '采购差异金额最多', value: 'gapTotalPrice-desc' },
      { label: '采购差异金额最少', value: 'gapTotalPrice-asc' },
    ],
  },
};

export const ToolTipMap = {
  [AchievementTypeEnum.预计收入]: [
    '总实付收入=平台外卖实付收入+店内实付收入  注：平台外卖=美团外卖实付收入+饿了么外卖实付收入    店内实付收入=纯堂食实付收入+团购实付收入+自营外卖实付收入',
  ],
  [AchievementTypeEnum.有效订单数]: [
    '统计时间，未取消的已支付订单数（剔除整单退单后的总订单数）',
  ],
  [AchievementTypeEnum.客单价]: ['AC=预计实付收入/TC'],
  [AchievementTypeEnum.营业额预估准确率]: [
    `营业额预估准确率 = （总实付收入 - |总实付收入 - 预估营业额|）/ 总实付收入 × 100%`,
    '预估营业额为店长在云海手动填写',
  ],
  estimatedRevenuePie: [
    '门店总实付收入中，在堂食、自提和外卖3个场景的收入占比',
  ],
  TCPie: ['门店有效订单中，在堂食、自提和外卖3个场景的占比'],
  [AchievementTypeEnum.物料采购差异率]: [
    '1.物料采购差异率=差异金额/理论耗用金额',
    '2.差异金额=理论耗用金额/实际耗用金额',
  ],
};

export const SecondaryCompositeToolTipMap = {
  [AchievementTypeEnum.预计收入]: [
    <br />,
    <div className="font-bold text-black">实付收入（到店）</div>,
    '到店实付收入=纯堂食实付收入+团购实付收入',
    '注：',
    '纯堂食实付收入=收银POS实付收入+微信小程序实付收入＋支付宝小程序实付收入+抖音小程序实付收入+员工APP实付收入',
    '团购实付收入=美团团购实付收入+抖音团购实付收入+快手团购实付收入＋口碑团购实付收入+其他团购实付收入',
    <br />,
    <div className="font-bold text-black">实付收入（到家）</div>,
    '到家实付收入=自营外卖实付收入+饿了么外卖实付收+美团外卖实付收入',
  ],
  [AchievementTypeEnum.有效订单数]: [
    <br />,
    <div className="font-bold text-black">TC（到店）</div>,
    '到店TC=纯堂食TC+团购TC',
    '注：',
    '纯堂食TC=收银POS TC+微信小程序TC＋支付宝小程序TC+抖音小程序TC+员工APP TC',
    '团购TC=美团团购TC+抖音团购TC+快手团购TC＋口碑团购TC+其他团购TC',
    <br />,
    <div className="font-bold text-black">TC（到家）</div>,
    '到家TC=自营外卖TC+饿了么外卖TC+美团外卖TC',
  ],
  [AchievementTypeEnum.客单价]: [
    <br />,
    <div className="font-bold text-black">AC（到店）</div>,
    '到店AC=到店实付收入/到店TC ',
    <br />,
    <div className="font-bold text-black">AC（到家）</div>,
    '到家AC=外卖实付收入/外卖TC',
  ],
};
export const SecondaryCompositeAvgToolTipMap = {
  [AchievementTypeEnum.预计收入]: [
    <br />,
    <div className="font-bold text-black">日均实付收入（到店）</div>,
    '到店日均实付收入=统计时间内的到店实付收入/统计的天数',
    <br />,
    <div className="font-bold text-black">日均实付收入（到家）</div>,
    '到家日均实付收入=统计时间内的到家实付收入/统计的天数',
  ],
  [AchievementTypeEnum.有效订单数]: [
    <br />,
    <div className="font-bold text-black">日均TC（到店）</div>,
    '到店日均TC=统计时间内的到店TC/统计的天数',
    <br />,
    <div className="font-bold text-black">日均TC（到家）</div>,
    '到家日均TC=统计时间内的到家TC/统计的天数',
  ],
};
