export type overviewParams = {
  beginDate: string;
  endDate: string;
  shopCodes?: string[];
  deptId?: string; // 组织/区域ID
  shopId?: number;
  filterGroup?: string | 'DEPT' | 'SHOP';

  orderScene?: number[]; // 订单类型
  orderSource?: number[]; // 订单来源
  payChannel?: number[]; // 支付方式
  elsePayChannel?: number[]; // 其他支付方式;
  differenceDate?: string;
  businessDay?: boolean;
  dataType?: number;
};

export type overviewParamsResp = {
  acDailyRevenue?: string; // AC日均客单价
  acDailyRevenueQoq?: string; // AC日均客单价环比
  acDailyRevenueYoy?: string; // AC日均客单价同比
  acRevenue?: string; // AC客单价
  acRevenueQoq?: string; // AC客单价环比
  acRevenueYoy?: string; // AC客单价同比
  dailyAmount?: string; // 日均收入
  dailyAmountQoq?: string; // 日均收入环比
  dailyAmountYoy?: string; // 日均收入同比
  discountAmount?: string; // 优惠金额
  discountAmountQoq?: string; // 优惠金额环比
  discountAmountYoy?: string; // 优惠金额同比
  estimatedRevenue?: string; // 预计总收入
  estimatedRevenueQoq?: string; // 实付收入环比
  estimatedRevenueYoy?: string; // 实付收入同比
  estimatedTurnover?: string; // 预估营业额
  estimatedTurnoverPrecision?: string; // 预估准确率
  refundAmount?: string; // 退款金额
  refundAmountQoq?: string; // 退款金额环比
  refundAmountYoy?: string; // 退款金额同比
  tcDailyOrderCount?: string; // TC日均订单
  tcDailyOrderCountQoq?: string; // TC日均订单环比
  tcDailyOrderCountYoy?: string; // TC日均订单同比
  tcDiscountOrderCount?: string; // TC优惠订单
  tcDiscountOrderCountQoq?: string; // TC优惠订单环比
  tcDiscountOrderCountYoy?: string; // TC优惠订单同比
  tcOrderCount?: string; // TC有效订单数量
  tcOrderCountQoq?: string; // TC有效订单数量环比
  tcOrderCountYoy?: string; // TC有效订单数量同比
  tcRefundOrderCount?: string; // TC退款数量
  tcRefundOrderCountQoq?: string; // TC退款数量环比
  tcRefundOrderCountYoy?: string; // TC退款数量同比
};

export type getOrderScenePayload = {
  beginDate?: string;
  dataType?: number;
  deptId?: string;
  endDate?: string;
  shopIds?: number[];
  tabType?: number;
};

export type MaterialPurchasingPayload = {
  date: string;
  deptId?: string;
  shopIds?: number[];
};
export type MaterialPurchasingResponse = {
  anomaly: boolean; // 是否存在异常门店,true为存在false为不存在
  anomalyShopCodes: string[]; // 异常门店code
  expectConsumptionTotalPrice: number; // 理论耗用金额
  gapTotalPrice: number; // 差异金额,实际耗用金额- 理论耗用金额
  groupId: number; // 当前组织ID
  groupName: string; // 组织名称
  momRate: number; // 物料采购差异率环比
  rate: number; // 物料采购差异率
  realConsumptionTotalPrice: number; // 实际耗用金额
  statMonth: string; // 统计月份
  yoyRate: number; // 物料采购差异率同比
};
