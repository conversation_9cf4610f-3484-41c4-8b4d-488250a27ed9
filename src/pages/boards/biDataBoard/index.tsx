import { Loading } from '@src/components';
import { useRequest } from 'ahooks';
import { Empty } from 'antd-mobile';
import { twMerge } from 'tailwind-merge';
import { BoardTypeEnum, getBiDashboardData } from './request';

export default function BiDataBoard({ type, className }: { type: BoardTypeEnum; className?: string }) {
  const { data, loading } = useRequest(async () => {
    const res = await getBiDashboardData({ type });
    return res || {};
  });

  if (loading) {
    return <Loading />;
  }

  if (!data?.dashboardUrl) {
    return (
      <div className="flex flex-1 justify-center items-center">
        <Empty description="暂无数据" />
      </div>
    );
  }

  // eslint-disable-next-line jsx-a11y/iframe-has-title
  return <iframe className={twMerge('w-full h-screen min-w-full border-none', className)} src={data.dashboardUrl} />;
}
