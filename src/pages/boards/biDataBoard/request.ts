import { post } from '@src/api';

/**
 * bi看板数据类型枚举
 */
export enum BoardTypeEnum {
  营收数据看板 = 'REVENUE',
  用户满意度情况 = 'USER_SATISFACTION',
  智慧运营看板 = 'APP_SELF_PATROL',
  到家加盟业主看板 = 'HOME_OWNER',
  门店数据看板 = 'STRATEGY_HOME',
}

/**
 * 获取bi看板数据-策略系统看板前缀为tm
 * @param {object} data
 * @param {BoardTypeEnum} data.type
 * @returns
 */
export function getBiDashboardData(data: { type: BoardTypeEnum }) {
  const request_context = data.type === BoardTypeEnum.门店数据看板 ? 'tm' : 'om';
  return post<{ dashboardUrl: string }>(`/${request_context}-api/common/quickBi/dashboard`, { data });
}
