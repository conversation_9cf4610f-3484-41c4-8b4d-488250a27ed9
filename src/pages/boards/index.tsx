import { useEffect, useMemo, useState } from 'react';
import { PageContext } from '@src/common/page-context';
import { report } from '@src/common/report';
import BackTop from '@src/components/BackTop';
import useGroupTree from '@src/hooks/useGroupTree';
import AuditBoard from '@src/pages/boards/audit';
import HrBoard from '@src/pages/boards/hr';
import userStore from '@src/store/user';
import { getDataViewBoardEnum } from '@src/utils/utils';
import { ErrorBlock, SafeArea, Tabs } from 'antd-mobile';
import cn from 'classnames';
import { observer } from 'mobx-react';
import { parse, stringify } from 'qs';
import { useAliveController } from 'react-activation';
import { Outlet, useLocation, useNavigate, useSearchParams } from 'react-router-dom';
// import Achievement from './achievement';
import ALiAchievement from './achievement/aliDataBoard';
import styles from './index.module.scss';
import Market from './market';
// import Satisfaction from './satisfaction';
import ALiSatisfaction from './satisfaction/aliDataBoard';
import ErrorPage from '../error-page';

const Boards = observer(() => {
  const { drop } = useAliveController();
  const { pid, loading, getGroupTree, tagIdObj } = useGroupTree();
  const tabList = [
    {
      key: 'achievement',
      title: '业绩看板',
      // Component: Achievement,
      Component: ALiAchievement,
      // auth: AuthorityEnum.AchievementPage,
      auth: getDataViewBoardEnum('作战小组数据看板_业绩看板'),
      onClick: () => {
        report({
          type: 'ability',
          page: '数据看板',
          abilityButton: '作战小队数据-业绩看板',
        });
      },
    },
    {
      key: 'satisfaction',
      title: '满意度数据看板',
      // auth: AuthorityEnum.SatisfactionPage,
      auth: getDataViewBoardEnum('作战小组数据看板_满意度数据看板'),
      // Component: Satisfaction,
      Component: ALiSatisfaction,
      onClick: () => {
        report({
          type: 'ability',
          page: '数据看板',
          abilityButton: '作战小队数据-满意度数据看板',
        });
      },
    },
    {
      key: 'audit',
      title: '食安数据看板',
      Component: AuditBoard,
      // auth: AuthorityEnum.AuditPage,
      auth: getDataViewBoardEnum('作战小组数据看板_食安数据看板'),
      onClick: () => {
        report({
          type: 'ability',
          page: '数据看板',
          abilityButton: '作战小队数据-食安数据看板',
        });
      },
    },
    {
      key: 'hr',
      title: '人力数据看板',
      Component: HrBoard,
      // auth: AuthorityEnum.HrPage,
      auth: getDataViewBoardEnum('作战小组数据看板_人力数据看板'),
      onClick: () => {
        report({
          type: 'ability',
          page: '数据看板',
          abilityButton: '作战小队数据-人力数据看板',
        });
      },
    },
    {
      key: 'market',
      title: '市场分析看板',
      // auth: AuthorityEnum.marketPage,
      auth: getDataViewBoardEnum('作战小组数据看板_市场分析看板'),
      Component: Market,
      onClick: () => {
        report({
          type: 'ability',
          page: '数据看板',
          abilityButton: '作战小队数据-市场分析看板',
        });
      },
    },
  ];
  const showTabList = useMemo(() => {
    if (!userStore.permissionsMap.size) {
      return [];
    }
    return tabList.filter((v) => !v.auth || userStore.permissionsMap.has(v.auth));
  }, [userStore.permissionsMap]);

  const [search] = useSearchParams();
  search.get('pageType') === 'detail' && drop('/boards/exception/report');
  const location = useLocation();

  /**
   * ios 二级路由返回时，title有问题，这里强制改一下
   */
  useEffect(() => {
    if (location.pathname === '/boards') {
      document.title = '数据看板';
    }
  }, [location]);

  const navigate = useNavigate();
  const activeTab = showTabList.some((v) => v.key === search.get('activeTab'))
    ? search.get('activeTab')
    : showTabList.length && showTabList[0].key;
  const [activeKey, setActiveKey] = useState<string>(activeTab as string);

  useEffect(() => {
    const updateActiveTab = showTabList.some((v) => v.key === search.get('activeTab'))
      ? search.get('activeTab')
      : showTabList.length && showTabList[0].key;
    setActiveKey(updateActiveTab as string);
  }, [showTabList]);

  const Component = showTabList.find((v) => v.key === activeKey)?.Component || (() => <ErrorPage />);

  if (!showTabList.length) {
    return <ErrorBlock status="empty" title="您没有该页面权限" description={<span>请与客服人员联系！</span>} />;
  }

  return (
    <PageContext.Provider value={{ pid, getGroupTree, tagIdObj }}>
      <>
        <div hidden={location.pathname !== '/boards'} className="h-full overflow-y-auto scroll-smooth" id="boardsPage">
          <div className="bg-white pl-1" hidden={search.get('pageType') === 'detail'}>
            <Tabs
              className={styles.iAdmTabs}
              style={{ '--title-font-size': '1rem' }}
              activeKey={activeKey}
              onChange={(key) => {
                const { activeTab: _activeTab, ...rest } = parse(location.search.slice(1));
                const reportIndex = showTabList.findIndex((i) => i.key === key);
                showTabList?.[reportIndex]?.onClick();
                setActiveKey(key);
                navigate(`${location.pathname}?${stringify({ activeTab: key, ...rest })}`, {
                  replace: true,
                });
              }}
              stretch={false}
            >
              {showTabList.map(({ key, title }) => (
                <Tabs.Tab title={title} key={key} />
              ))}
            </Tabs>
          </div>
          {loading ? <></> : <Component />}
          <BackTop domId="boardsPage" />
        </div>
        <div className={cn('grow h-0', styles.boardsChildren)}>
          <Outlet />
        </div>
        <SafeArea position="bottom" />
      </>
    </PageContext.Provider>
  );
});

export default Boards;
