import { BrandsFilter, CustomerFilter, DateFilter, Loading, PageList } from '@src/components';
import { Sorter } from '../components';
import dayjs from 'dayjs';
import { useMemo, useState } from 'react';
import { MarketItem, MarketParams } from './api.type';
import { SearchBar, Space } from 'antd-mobile';
import { usePageContext } from '@src/common/page-context';
import styles from '../index.module.scss';
import { useRequest } from 'ahooks';
import { getList } from './api';
import { omit, cloneDeep } from 'lodash';
import { useNavigate } from 'react-router-dom';
import { commonSorter, formatPer } from '@src/utils/helper';

const defaultParams = {
  beginDate: dayjs().subtract(1, 'M').startOf('M').format('YYYY-MM-DD'),
  endDate: dayjs().subtract(1, 'M').endOf('M').format('YYYY-MM-DD'),
  statisticsMonth: dayjs().subtract(1, 'M').format('YYYY-MM'),
  deptId: '',
  brandNames: [],
};

const Market = () => {
  const { pid } = usePageContext();
  const [params, setParams] = useState<MarketParams>({ ...defaultParams, deptId: pid });
  const [keyWords, setKeyWords] = useState('');
  const [sorter, setSorter] = useState<string[]>([]);

  const navigate = useNavigate();

  const { data, loading } = useRequest(
    async () => {
      const res = await getList({
        ...omit(params, ['beginDate', 'endDate']),
        statisticsMonth: dayjs(params.beginDate).format('YYYY-MM'),
      });
      return res;
    },
    { refreshDeps: [params] },
  );

  const showData = useMemo(() => {
    let arr: MarketItem[] = data || [];
    if (keyWords) {
      arr = data?.filter((v: any) => v.brandName.includes(keyWords)) || [];
    }
    if (sorter && sorter[0]) {
      const [key, order] = sorter[0].split('-');
      arr = cloneDeep(arr).sort((a: any, b: any) => commonSorter(a[key], b[key], order));
    }
    return arr;
  }, [data, keyWords, sorter]);

  return (
    <div>
      <Loading spinning={loading}>
        <div className="flex justify-between items-start bg-white px-4 py-2">
          <Space>
            <CustomerFilter
              type="group"
              className="flex-1"
              noBg={true}
              value={{ groupId: params.deptId }}
              onChange={(e: any) => {
                setParams((p) => ({ ...p, deptId: e.groupId }));
              }}
            />
            <BrandsFilter
              noBg={true}
              value={params.brandNames}
              onChange={(brandNames) => setParams((p) => ({ ...p, brandNames }))}
            />
          </Space>
          <DateFilter
            noBg={true}
            type="month"
            onlyMonth
            value={[dayjs(params.beginDate).toDate(), dayjs(params.endDate).toDate()]}
            onChange={(e) => {
              setParams((p) => ({ ...p, beginDate: e[0], endDate: e[1] }));
            }}
          />
        </div>
        <div className="flex items-center px-4 py-2 border-t border-solid border-line bg-white">
          <SearchBar
            className={`${styles.iAdmSearchBar} flex-1 mr-2`}
            style={{ '--border-radius': '8px', '--height': '2.5rem' }}
            onClear={() => setKeyWords('')}
            onSearch={setKeyWords}
            placeholder="请输入门店/组织名称"
          />
          <Sorter
            config={[
              { label: '门店数量最多', value: 'shopNum-desc' },
              { label: '门店数量最少', value: 'shopNum-asc' },
              { label: '店均单量最多', value: 'orderNum-desc' },
              { label: '店均单量最少', value: 'orderNum-asc' },
            ]}
            value={sorter}
            onChange={setSorter}
          />
        </div>
        <div className="my-2">
          <PageList
            dataSource={showData}
            itemRender={(item) => (
              <div
                key={item.brandName}
                className="flex items-center mb-2 py-4 px-5 bg-white leading-none"
                onClick={() => {
                  navigate('/boards/market/detail', {
                    state: {
                      deptId: params.deptId,
                      brandName: item.brandName,
                      statisticsMonth: dayjs(params.beginDate).format('YYYY-MM'),
                    },
                  });
                }}
              >
                <div className="font-semibold flex-1" style={{ width: '7.5rem' }}>
                  {item.brandName}
                </div>
                <div className="flex-1 border-r border-solid border-px mr-4 border-line">
                  <div className="text-xs text-grey mb-3 flex items-center">
                    门店数:
                    <span className="text-base font-semibold ml-1">{item.shopNum ?? '-'}</span>
                  </div>
                  <div className="text-xs text-grey">
                    增长率:
                    <span className="font-semibold ml-1">
                      {formatPer(item.shopNumIncreaseRate)}
                    </span>
                  </div>
                </div>
                <div className="flex-1">
                  <div className="text-xs text-grey mb-3 flex items-center">
                    店均单量:
                    <span className="text-base font-semibold ml-1">{item.orderNum ?? '-'}</span>
                  </div>
                  <div className="text-xs text-grey">
                    涨幅:
                    <span className="font-semibold ml-1">
                      {formatPer(item.orderNumIncreaseRate)}
                    </span>
                  </div>
                </div>
              </div>
            )}
          />
        </div>
      </Loading>
    </div>
  );
};

export default Market;
