import React, { useMemo } from 'react';
import ReactECharts, { EChartsOption } from 'echarts-for-react';

interface Props {
  id: string;
  title: string;
  seriesConfig: {
    type: 'bar' | 'line';
    data: any[];
    smooth?: boolean;
  };
  category: any[];
  getTooltip?: (arr: any[]) => string;
}

const Bar: React.FC<Props> = ({ title, seriesConfig, category, id, getTooltip }: Props) => {
  const options: EChartsOption = {
    title: {
      text: title,
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      backgroundColor: '#333',
      borderColor: '#333',
      textStyle: {
        color: '#fff',
      },
      confine: true,
      formatter: (params: any[]) => {
        return getTooltip ? getTooltip(params) : '';
      },
    },
    legend: {
      show: false,
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: category,
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        type: seriesConfig.type || 'bar',
        data: seriesConfig.data || [],
        smooth: !!seriesConfig?.smooth,
        emphasis: {
          itemStyle: {
            color: '#378BFF',
          },
        },
        itemStyle: {
          color: seriesConfig.type === 'line' ? '#378BFF' : '#98eddf',
        },
      },
    ],
  };

  const BarCharts = useMemo(() => {
    if (!category || !category.length) return <div></div>;
    return (
      <ReactECharts
        key={id}
        option={options}
        // style={{
        //   height: '100%',
        //   minHeight: `${data.length * 50 < 250 ? 250 : data.length * 50}px`,
        // }}
      />
    );
  }, [category]);

  return <div>{BarCharts}</div>;
};

export default Bar;
