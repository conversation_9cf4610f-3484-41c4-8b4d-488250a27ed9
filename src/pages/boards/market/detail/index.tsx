import { useRequest } from 'ahooks';
import { getOrderData, getShopData } from './api';
import { Switch } from 'antd-mobile';
import { CustomerFilter, Loading } from '@src/components';
import { useLocation } from 'react-router-dom';
import { MarketChatParams } from './api.type';
import { useState } from 'react';
import Bar from './components/Bar';
import { formatPer, thousandthFormat } from '@src/utils/helper';

export default function MarketDetail() {
  const location = useLocation();

  document.title = location.state.brandName && `${location.state.brandName}数据`;

  const [params, setParams] = useState<MarketChatParams>({ ...location.state, inOperation: false });

  const { data: shopData, loading: shopLoading } = useRequest(
    async () => {
      const res = await getShopData({ ...params });
      return res;
    },
    { refreshDeps: [params] },
  );
  const { data: orderData, loading: orderLoading } = useRequest(
    async () => {
      const res = await getOrderData({ ...params });
      return res;
    },
    { refreshDeps: [params] },
  );

  const getOrderTooltip = (curItem: { name: string; marker: string }[]) => {
    const { name, marker } = curItem[0];
    const data = orderData?.datas[name];
    const str = `<div>
    <div>${marker}${name}</div>
      <div>营运门店：${thousandthFormat(data?.shopNum)}家</div>
      <div>店均订单数：${thousandthFormat(data?.orderNum)}</div>
      <div>增长率：${formatPer(data?.orderNumIncreaseRate)}</div>
      <div>>3000订单门店数：${thousandthFormat(data?.complianceShopNum)}家</div>
      <div>>3000订单门店数占比：${formatPer(data?.complianceRate)}</div>
      <div>环比：${formatPer(data?.qoq)}</div>
    <div>`;
    return str;
  };
  const getShopTooltip = (curItem: { name: string; marker: string }[]) => {
    const { name, marker } = curItem[0];
    const data = shopData?.datas[name];
    const str = `<div>
    <div>${marker}${name}</div>
      <div>门店数量：${thousandthFormat(data?.shopNum)}</div>
      <div>增长率：${formatPer(data?.shopNumIncreaseRate)}</div>
    <div>`;
    return str;
  };

  return (
    <div>
      <Loading spinning={shopLoading || orderLoading}>
        <div className="flex justify-between items-start bg-white px-4 py-2">
          <CustomerFilter
            type="group"
            className="flex-1"
            value={{ groupId: params.deptId }}
            onChange={(e) => {
              setParams((p) => ({ ...p, deptId: e.groupId }));
            }}
          />
          <div className="flex items-center">
            <div className="mr-2 text-grey">只查看营业门店</div>
            <Switch
              onChange={(v) => setParams((p) => ({ ...p, inOperation: v }))}
              style={{ '--width': '2.5rem', '--height': '1.5rem' }}
            />
          </div>
        </div>
        <div className="px-2 py-2">
          <div className="mb-2">
            <div className="text-base font-semibold leading-10">门店数量</div>
            <div className="bg-white">
              <Bar
                id="shop_num"
                title=""
                category={shopData?.categorys || []}
                seriesConfig={{ type: 'bar', data: shopData?.values || [] }}
                getTooltip={getShopTooltip}
              />
            </div>
          </div>
          <div className="mb-2">
            <div className="text-base font-semibold leading-10">门店订单数量</div>
            <div className="bg-white">
              <Bar
                id="order_num"
                title=""
                category={orderData?.categorys || []}
                seriesConfig={{ type: 'line', data: orderData?.values || [], smooth: true }}
                getTooltip={getOrderTooltip}
              />
            </div>
          </div>
        </div>
      </Loading>
    </div>
  );
}
