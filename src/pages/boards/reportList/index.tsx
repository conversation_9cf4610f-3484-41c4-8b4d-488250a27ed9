import { DateFilter, IconFont, Loading } from '@src/components';
import { Card, SafeArea } from 'antd-mobile';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import { ExceptionReport } from '../components';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useRequest } from 'ahooks';
import { getReportHistoryList } from './api';
import empty from '@src/assets/images/empty.png';
import { IPopup } from '@src/components/IPopup';
import qs from 'qs';
import WithKeepAlive from '@src/components/WithKeepAlive';

const TypeNameMap = {
  audit: '食安数据',
  hr: '人力数据',
  achievement: '业绩数据',
  satisfaction: '满意度数据',
};

export enum TypeMapEnum {
  audit = 1,
  hr,
  achievement,
  satisfaction,
}

const tip = {
  audit: {
    title: '门店触碰“S项”诊断报告生成规则',
    desc: '每天早上9:00生成前一天触碰“S项”的门店记录；检查表包括《食安＆QSC检查表》、《线上巡检检查表》',
  },
  hr: {
    title: '门店招聘进度报告生成规则',
    desc: '每周一早上9:00生成当前自然月门店累积数据报告；报告中包含招聘达成率低于时间进度的门店；',
  },
  achievement: {
    title: '门店外卖营业额报告生成规则',
    desc: '每周一早上9:00生成当前自然月门店累积数据报告；报告中包含门店外卖日均营业额低于上月门店外卖日均营业额10%的门店；',
  },
  satisfaction: {
    title: '门店差评回复率数据报告生成规则',
    desc: '每周四早上9:00生成当前自然月门店累积数据报告；报告中包含差评回复率低于95%的门店；',
  },
};

const ReportList = () => {
  const [search, setSearchParams] = useSearchParams();
  const [params, setParams] = useState({
    // 默认当月
    startCreateTime: dayjs().startOf('month').format('YYYY-MM-DD'),
    endCreateTime: dayjs().endOf('month').format('YYYY-MM-DD'),
  });
  const [illustrateVisible, setIllustrateVisible] = useState(false);
  const type = search.get('type') as keyof typeof TypeNameMap;

  const navigator = useNavigate();

  const { data, loading } = useRequest(
    () => {
      setSearchParams(
        (pre) => {
          return {
            ...qs.parse(pre.toString()),
            startCreateTime: dayjs(params.startCreateTime).format('YYYY-MM-DD'),
            endCreateTime: dayjs(params.endCreateTime).format('YYYY-MM-DD'),
          };
        },
        { replace: true },
      );
      return getReportHistoryList({
        startCreateTime: dayjs(params.startCreateTime).startOf('d').format('YYYY-MM-DD HH:mm:ss'),
        endCreateTime: dayjs(params.endCreateTime).endOf('d').format('YYYY-MM-DD HH:mm:ss'),
        reportType: TypeMapEnum[type],
      });
    },
    {
      refreshDeps: [params],
    },
  );

  const titleBuilder = (type: keyof typeof TypeNameMap) => {
    switch (type) {
      case 'audit':
        return '触碰“S项”';

      case 'hr':
        return '门店招聘进度报告';

      case 'achievement':
        return '门店外卖营业额数据报告';

      case 'satisfaction':
        return '门店差评回复率数据报告';

      default:
        return '';
    }
  };
  const contentBuilder = (type: keyof typeof TypeNameMap, data: any) => {
    switch (type) {
      case 'audit':
        return data?.anomalyStatus
          ? `${data?.businessFieldObject?.count ?? '-'}家门店触碰“S项”，${(
              data?.businessFieldObject?.topRedLineList || []
            ).join('、')}${data?.businessFieldObject?.topRedLineList?.length}项出现次数最多`
          : '太棒了，您管辖范围下的门店没有出现触碰“S项”门店';

      case 'hr':
        return data?.anomalyStatus
          ? [
              `截止${dayjs(data?.reportTime).format('YYYY年MM月DD号')}`,
              `本月${data?.businessFieldObject?.count}家门店招聘进度低于时间进度`,
            ]
          : `太棒了，截止到${dayjs(data?.reportTime).format(
              'YYYY年MM月DD号',
            )}您管辖范围下的门店招聘进度均正常`;

      case 'achievement':
        return data?.anomalyStatus
          ? [
              `截止${dayjs(data?.reportTime).format('YYYY年MM月DD号')}`,
              `本月${data?.businessFieldObject?.shopCount}家门店外卖日均营业额环比下降超10%`,
            ]
          : `太棒了，截止到${dayjs(data?.reportTime).format(
              'YYYY年MM月DD号',
            )}您管辖范围下的门店外卖营业数据都达到了标准`;

      case 'satisfaction':
        return data?.anomalyStatus
          ? [
              `截止${dayjs(data?.reportTime).format('YYYY年MM月DD号')}`,
              `本月${data?.businessFieldObject?.count}家门店差评回复率低于95%`,
            ]
          : `太棒了，您管辖范围下的门店没有出现差评回复率低于95%的门店`;

      default:
        return '';
    }
  };

  useEffect(() => {
    document.title = `${type ? TypeNameMap[type] : ''}历史报告`;
  }, [type]);

  return (
    <Loading spinning={loading}>
      <Card
        className="rounded-none px-4 border-b border-solid border-line border-px"
        bodyClassName="!py-2"
      >
        <div className="flex justify-between items-center">
          <div
            onClick={() => {
              setIllustrateVisible(true);
            }}
            className="flex items-center text-sm leading-[22px] text-[#5e5e5e] gap-x-1"
          >
            报告生成规则
            <IconFont type="icon-question-circle" />
          </div>
          <DateFilter
            value={[dayjs(params.startCreateTime).toDate(), dayjs(params.endCreateTime).toDate()]}
            onChange={(e) => {
              setParams((p) => ({ ...p, startCreateTime: e[0], endCreateTime: e[1] }));
            }}
            type="month"
          />
        </div>
      </Card>
      <div className="pt-[10px] px-3">
        {(data || [])?.length !== 0 ? (
          data.map((o: any) => {
            return (
              <ExceptionReport
                key={o?.reportId}
                hasReport={true}
                className="mb-[10px] text-85"
                title={titleBuilder(type)}
                shopNum={type === 'audit' ? o?.businessFieldObject?.count : null}
                time={o?.reportTime || ''}
                content={contentBuilder(type, o)}
                hiddenDetialEntry={!!!o?.anomalyStatus}
                onClick={() => {
                  !!o?.anomalyStatus &&
                    navigator(
                      `/boards/quick_creation${qs.stringify(
                        {
                          reportId: o?.reportId,
                          type,
                        },
                        { addQueryPrefix: true },
                      )}`,
                    );
                }}
              />
            );
          })
        ) : (
          <div className="w-full">
            <img
              src={empty}
              alt=""
              className="w-[178px] h-[148px] object-cover ml-[87px] mt-[127px]"
            />
            <div className="text-[#9E9E9E] text-13 leading-[14px] mt-[28px] mx-auto w-fit">
              暂无报告
            </div>
          </div>
        )}
      </div>
      <SafeArea position="bottom" />
      <IPopup
        title="说明"
        visible={illustrateVisible}
        onClose={() => {
          setIllustrateVisible(false);
        }}
        bodyStyle={{
          minHeight: '37.5vh',
        }}
      >
        <div className="px-6 pt-4 flex flex-col gap-y-1">
          <h3 className="text-sm leading-[22px] text-dark-14">{tip[type].title}</h3>
          <p className="text-[#5E5E5E] text-sm leading-[21px]">{tip[type].desc}</p>
        </div>
      </IPopup>
    </Loading>
  );
};

export default WithKeepAlive(ReportList);
