import { StarCustomerInfo } from '../components';
import AuthorityEnum from '@src/common/authority';
import './index.css';
import { formatPer } from '@src/utils/helper';

export enum SatisfactionType {
  体验星级得分 = 1,
  全渠道万次投诉率 = 2,
  差评回复率 = 4,
  出餐超时订单 = 8,
  平均出餐时长 = 9,
}

export const typeOptions = [
  {
    label: '顾客体验综合星级得分',
    value: SatisfactionType.体验星级得分,
    auth: AuthorityEnum.体验星级得分,
  },
  {
    label: '全渠道万次投诉率',
    value: SatisfactionType.全渠道万次投诉率,
    auth: AuthorityEnum.全渠道万次投诉率,
  },
  {
    label: '差评回复率',
    value: SatisfactionType.差评回复率,
    auth: AuthorityEnum.差评回复率,
  },
  {
    label: '平均出餐时长',
    value: SatisfactionType.平均出餐时长,
    auth: AuthorityEnum.平均出餐时长,
  },
  {
    label: '出餐超时订单',
    value: SatisfactionType.出餐超时订单,
    auth: AuthorityEnum.出餐时长超时率,
  },
];

export const typeObj = {
  [SatisfactionType.体验星级得分]: {
    cardRender: (
      item: any,
      index: number,
      params: any,
      detailType: number,
      navigator: any,
      stopId?: number | string,
    ) => (
      <StarCustomerInfo
        key={index}
        type={item.type === 1 ? 'group' : 'shop'}
        title={item.type === 1 ? item.groupName : `${item.shopId}${item.shopName}`}
        header="顾客体验综合星级得分"
        star={item.starScore || 0}
        indexArea={
          <div className="font-bold text-[20px] leading-none">
            {item.starScore?.toFixed(1)}
            <span className="text-base leading-none">分</span>
          </div>
        }
        innerNode={
          <div className="py-3 px-4 flex flex-col gap-4 border-t border-solid border-line">
            {[
              { label: '美团大众星级', value: item?.mtDzStar },
              { label: '出餐速度(分)', value: item?.avgMealTime },
              { label: '抖音团购投诉万次不满意率', value: formatPer(item?.tikTokRate) },
              { label: '外部舆情百万次不满意率', value: formatPer(item?.dineOuterRate) },
              { label: '内部服务态度+品质口味万次不满意率', value: formatPer(item?.dineInnerRate) },
              { label: '美团饿了么外卖星级得分', value: item?.mtEleStar },
            ].map((o) => (
              <div key={o.label} className="text-xs leading-3 flex ">
                <span className="text-85">{o.label}：</span>
                <span className="text-5E">{o.value}</span>
              </div>
            ))}
          </div>
        }
        manager={{ ...item?.managerInfo, id: item?.managerInfo?.userId }}
        navNext={() => {
          navigator('/boards/satisfaction/list', {
            state: {
              params: { ...params, stopId: stopId ? stopId : undefined, groupId: item.groupId },
              detailType: detailType,
            },
            replace: false,
          });
        }}
        percent={{ label: '顾客体验综合星级得分', value: formatPer(item.starScore, '分') }}
        noStar
      />
    ),
    sortConfig: [
      { label: '体验星级得分最高', value: 'starScore-desc' },
      { label: '体验星级得分最低', value: 'starScore-asc' },
    ],
  },
  [SatisfactionType.全渠道万次投诉率]: {
    cardRender: (
      item: any,
      index: number,
      params: any,
      detailType: number,
      navigator: any,
      stopId?: number | string,
    ) => (
      <StarCustomerInfo
        key={index}
        type={item.type === 1 ? 'group' : 'shop'}
        title={item.type === 1 ? item.groupName : `${item.shopId}${item.shopName}`}
        header="全渠道万次投诉率"
        indexArea={
          <div className="font-bold text-[20px] leading-none">{formatPer(item?.totalRate)}</div>
        }
        innerNode={
          <div className="py-3 px-4 grid grid-cols-2 gap-4 border-t border-solid border-line">
            {[
              { label: '外卖平台差评率', value: formatPer(item?.takeOutRate) },
              { label: '团购差评率', value: formatPer(item?.grouponRate) },
              { label: '小程序差评率', value: formatPer(item?.appletRate) },
              { label: '客服渠道万次投诉率', value: formatPer(item?.customRate) },
            ].map((o) => (
              <div key={o.label} className="text-xs leading-3 flex ">
                <span className="text-85">{o.label}：</span>
                <span className="text-5E">{o.value}</span>
              </div>
            ))}
          </div>
        }
        manager={{ ...item?.managerInfo, id: item?.managerInfo?.userId }}
        navNext={() => {
          navigator('/boards/satisfaction/list', {
            state: {
              params: { ...params, stopId: stopId ? stopId : undefined, groupId: item.groupId },
              detailType: detailType,
            },
            replace: false,
          });
        }}
        noStar
        percent={{ label: '全渠道万次投诉率', value: formatPer(item?.totalRate) }}
      />
    ),
    sortConfig: [
      { label: '全渠道万次投诉率最高', value: 'totalRate-desc' },
      { label: '全渠道万次投诉率最低', value: 'totalRate-asc' },
    ],
  },
  [SatisfactionType.差评回复率]: {
    cardRender: (
      item: any,
      index: number,
      params: any,
      detailType: number,
      navigator: any,
      stopId?: number | string,
    ) => (
      <StarCustomerInfo
        key={index}
        type={item.type === 1 ? 'group' : 'shop'}
        title={item.type === 1 ? item.groupName : `${item.shopId}${item.shopName}`}
        header="差评回复率"
        indexArea={
          <div className="font-bold text-[20px] leading-none">
            {formatPer(item?.totalReplyRate)}
          </div>
        }
        innerNode={
          <div className="border-t border-solid border-line">
            <table className="table-fixed w-full bg-white">
              <thead>
                <tr className="text-left divide-x border-b border-solid border-line divide-line text-xs leading-3 text-85">
                  <th className="py-2 px-5 font-normal relative">
                    <div className="out"></div>
                  </th>
                  <th className="py-2 px-5 font-normal">总差评数</th>
                  <th className="py-2 px-5 font-normal">差评回复数</th>
                </tr>
              </thead>
              <tbody>
                <tr className="text-left divide-x border-b border-solid border-line divide-line text-xs leading-3 text-85">
                  <td className="py-2 px-5">大众点评团购</td>
                  <td className="py-2 px-5 text-5E">{item?.dzRatings}</td>
                  <td className="py-2 px-5 text-5E">{item?.dzReplyRatings}</td>
                </tr>
                <tr className="text-left divide-x border-b border-solid border-line divide-line text-xs leading-3 text-85">
                  <td className="py-2 px-5">美团团购</td>
                  <td className="py-2 px-5 text-5E">{item?.mtGrouponRatings}</td>
                  <td className="py-2 px-5 text-5E">{item?.mtGrouponReplyRatings}</td>
                </tr>
                <tr className="text-left divide-x border-b border-solid border-line divide-line text-xs leading-3 text-85">
                  <td className="py-2 px-5">美团外卖</td>
                  <td className="py-2 px-5 text-5E">{item?.mtRatings}</td>
                  <td className="py-2 px-5 text-5E">{item?.mtReplyRatings}</td>
                </tr>
                <tr className="text-left divide-x border-b border-solid border-line divide-line text-xs leading-3 text-85">
                  <td className="py-2 px-5">饿了么外卖</td>
                  <td className="py-2 px-5 text-5E">{item?.eleRatings}</td>
                  <td className="py-2 px-5 text-5E">{item?.eleReplyRatings}</td>
                </tr>
              </tbody>
            </table>
          </div>
        }
        manager={{ ...item?.managerInfo, id: item?.managerInfo?.userId }}
        navNext={() => {
          navigator('/boards/satisfaction/list', {
            state: {
              params: { ...params, stopId: stopId ? stopId : undefined, groupId: item.groupId },
              detailType: detailType,
            },
            replace: false,
          });
        }}
        noStar
        percent={{ label: '差评回复率', value: formatPer(item?.totalReplyRate) }}
      />
    ),
    sortConfig: [
      { label: '差评回复率最高', value: 'totalReplyRate-desc' },
      { label: '差评回复率最低', value: 'totalReplyRate-asc' },
    ],
  },
  [SatisfactionType.平均出餐时长]: {
    cardRender: (
      item: any,
      index: number,
      params: any,
      detailType: number,
      navigator: any,
      stopId?: number | string,
    ) => (
      <StarCustomerInfo
        key={index}
        type={item.type === 1 ? 'group' : 'shop'}
        title={item.type === 1 ? item.name : `${item.shopId}${item.name}`}
        header="平均出餐时长(分)"
        indexArea={<div className="font-bold text-[20px] leading-none">{item?.foodTime}</div>}
        manager={{
          name: item?.managerInfo?.name,
          phone: item?.managerInfo?.phone,
          id: item?.managerInfo?.userId,
        }}
        navNext={() => {
          navigator('/boards/satisfaction/list', {
            state: {
              params: { ...params, stopId: stopId ? stopId : undefined, groupId: item.groupId },
              detailType: detailType,
            },
            replace: false,
          });
        }}
        noStar
        percent={{ label: '平均出餐时长(分)', value: formatPer(item?.foodTime) }}
        hasDetail={false}
      />
    ),
    sortConfig: [
      { label: '平均出餐时长最长', value: 'foodTime-desc' },
      { label: '平均出餐时长最短', value: 'foodTime-asc' },
    ],
  },
  [SatisfactionType.出餐超时订单]: {
    cardRender: (
      item: any,
      index: number,
      params: any,
      detailType: number,
      navigator: any,
      stopId?: number | string,
    ) => (
      <StarCustomerInfo
        key={index}
        type={item.type === 1 ? 'group' : 'shop'}
        title={item.type === 1 ? item.groupName : `${item.shopId}${item.shopName}`}
        header="出餐时长超时率"
        indexArea={
          <div className="font-bold text-[20px] leading-none">
            {formatPer(item?.mealOrderTimeoutRatio)}
          </div>
        }
        manager={{ ...item?.managerInfo, id: item?.managerInfo?.userId }}
        navNext={() => {
          navigator('/boards/satisfaction/list', {
            state: {
              params: { ...params, stopId: stopId ? stopId : undefined, groupId: item.groupId },
              detailType: detailType,
            },
            replace: false,
          });
        }}
        noStar
        percent={{ label: '出餐时长超时率', value: formatPer(item?.mealOrderTimeoutRatio) }}
        hasDetail={false}
      />
    ),
    sortConfig: [
      { label: '出餐订单超时率最高', value: 'mealOrderTimeoutRatio-desc' },
      { label: '出餐订单超时率最低', value: 'mealOrderTimeoutRatio-asc' },
    ],
  },
};

export const ToolTipMap = {
  [SatisfactionType.体验星级得分]: [
    '顾客体验星级得分=内部服务态度+品质口味万次不满意*0.25+外部舆情百万次不满意率*0.15+抖音团购万次不满意率*0.1+美团大众星级*0.2+美团饿了么星级*0.2+出餐速度得分*0.1',
  ],
  [SatisfactionType.全渠道万次投诉率]: [
    '(外卖平台差评数+团购差评数+客服渠道投诉数)/全店总TC）*10000',
  ],
  [SatisfactionType.差评回复率]: [
    '（大众点评团购差评回复数+美团团购差评回复数+美团外卖差评回复数+饿了么外卖差评回复数）/总差评评价数 *100',
    '注：其中差评定义大众点评团购、美团团购2星以下（含2星）评价为差评；美团外卖、饿了么外卖 1-2星评价为差评',
  ],
  [SatisfactionType.平均出餐时长]: [
    '平均出餐时间=总出餐时长/总订单数',
    '注：',
    '统计条件：',
    '①订单来源：堂食/自提订单、饿了么/美团订单、所有预订单均不参与计算、 自营外卖订单不参与计算',
    '②剔除出餐时长大于22分钟的异常订单',
  ],
  [SatisfactionType.出餐超时订单]: [
    '出餐订单超时率=出餐超时（默认为10分钟）的TC/总TC',
    '注：',
    '统计条件：',
    '①订单来源：堂食/自提订单、饿了么/美团订单、所有预订单均不参与计算、 自营外卖订单不参与计算',
    '②剔除出餐时长大于22分钟的异常订单',
  ],
};
