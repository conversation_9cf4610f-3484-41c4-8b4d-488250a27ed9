import { <PERSON>er<PERSON><PERSON>er, DateFilter, IconFont, Loading, PageList } from '@src/components';
import { SearchBar, Card, Tabs, Skeleton } from 'antd-mobile';
import dayjs from 'dayjs';
import { useState, useMemo } from 'react';
import { overviewParams } from './api.type';
import {
  CustomerType,
  ExceptionReport,
  IndicatorCard,
  IndicatorCardV2,
  Sorter,
} from '../components';
import { SatisfactionType, ToolTipMap, typeObj, typeOptions } from './enum';
import { useRequest } from 'ahooks';
import { cloneDeep } from 'lodash';
import {
  getList,
  getTotalBadReplyRate,
  getTotalFoodTime,
  getTotalInoutBadRate,
  getTotalMealOrderTimeoutRate,
  getTotalStarRate,
} from './api';
import { usePageContext } from '@src/common/page-context';
import { useNavigate, useSearchParams } from 'react-router-dom';
import styles from '../index.module.scss';
import AuthorityEnum from '@src/common/authority';
import { userStore } from '@src/store';
import { observer } from 'mobx-react';
import qs from 'qs';
import { checkReport, getReport, updateReadFlag } from '../api';
import { BarCard } from '../components/BarCard';
import { SShimmer } from '../audit/Shimmer/SShimmer';
import { BarBarCard } from './components/BarBarCard';
import { IndicatorCardShimmer } from '../components/IndicatorCard';
import { commonSorter, formatPer } from '@src/utils/helper';
import { roleTypeIsManage } from '@src/utils/tokenUtils';

const defaultParams = {
  beginDate: dayjs().subtract(1, 'M').startOf('M').format('YYYY-MM-DD'),
  endDate: dayjs().subtract(1, 'M').endOf('M').format('YYYY-MM-DD'),
  groupId: '',
  shopIds: [],
};
const SatisfactionTypeEN = {
  1: 'stars',
  2: 'inoutBads',
  4: 'badReplys',
  8: 'mealOrderTimeoutList',
  9: 'foodTimeList',
};

const Satisfaction = observer(() => {
  const { pid, getGroupTree } = usePageContext();
  const { permissionsMap } = userStore;
  const disabledShop = !permissionsMap.has(AuthorityEnum.SatisfactionShopBtn);
  const disabledGroup = !permissionsMap.has(AuthorityEnum.SatisfactionGroupBtn);
  const [params, setParams] = useState<overviewParams>({ ...defaultParams, groupId: pid });
  const showTypeOptions = typeOptions.filter((v) => permissionsMap.has(v.auth));
  const [detailType, setDetailType] = useState<SatisfactionType[]>(
    showTypeOptions.length ? [showTypeOptions[0].value] : [SatisfactionType.体验星级得分],
  );
  const [dataType, setDataType] = useState<'shop' | 'group'>(disabledShop ? 'group' : 'shop');
  const [searchValue, setSearchValue] = useState<string>('');
  const [sorter, setSorter] = useState<string[]>([]);
  const navigator = useNavigate();

  const { data: showReport, run: showReportRun } = useRequest(
    async () => {
      const res = await checkReport({ reportType: 4 });
      return res;
    },
    {
      ready: roleTypeIsManage(),
    },
  );
  const { data: reportData, run } = useRequest(
    async () => {
      const res = await getReport({ reportType: 4 });

      return res || {};
    },
    {
      ready: roleTypeIsManage(),
    },
  );
  const { run: updateReadRun } = useRequest(
    async (reportId: number) => {
      if (!reportId) throw null;
      const res = await updateReadFlag(reportId);
      return res;
    },
    {
      manual: false,
      onSuccess: () => {
        showReportRun();
      },
    },
  );

  const [search] = useSearchParams();
  const hiddenDetail = search.get('pageType') !== 'detail'; //隐藏详情
  const hiddenHome = search.get('pageType') === 'detail'; //隐藏统计页

  const { data: totalStarRate, loading: totalStarRateLoading } = useRequest(
    async () =>
      getTotalStarRate({
        date: dayjs(params.beginDate).format('YYYY-MM'),
        groupId: params.groupId,
        shopIds: params.shopIds,
      }),
    {
      refreshDeps: [params],
    },
  );
  const totalStarRateIndicatorCard = useMemo(
    () => (
      <>
        {!totalStarRateLoading ? (
          <IndicatorCard
            className="mt-2"
            title="顾客体验综合星级得分"
            tooltip={ToolTipMap[SatisfactionType.体验星级得分]}
            indicator={formatPer(totalStarRate?.starDetailResponse?.starScore, '分')}
            percent={{
              same: formatPer(totalStarRate?.yoyInoutBadRate),
              circle: formatPer(totalStarRate?.momInoutBadRate),
            }}
            type="full"
            isChart={false}
            // isWarning={!!Info?.reportResponse?.starStatus}
            handleClick={() => handleTabChange(SatisfactionType.体验星级得分)}
            isRate
            rateVal={totalStarRate?.starDetailResponse?.starScore}
          />
        ) : (
          <div className="mt-2">
            <IndicatorCardShimmer />
          </div>
        )}
      </>
    ),
    [totalStarRateLoading],
  );

  const { data: totalInoutBadRate, loading: totalInoutBadRateLoading } = useRequest(
    async () =>
      getTotalInoutBadRate({
        date: dayjs(params.beginDate).format('YYYY-MM'),
        groupId: params.groupId,
        shopIds: params.shopIds,
      }),
    {
      refreshDeps: [params],
    },
  );
  const inoutBadIndicatorCard = useMemo(
    () => (
      <>
        {!totalInoutBadRateLoading ? (
          <IndicatorCard
            className="mt-2"
            title="全渠道万次投诉率"
            tooltip={ToolTipMap[SatisfactionType.全渠道万次投诉率]}
            indicator={formatPer(totalInoutBadRate?.detailResponse?.totalRate)}
            percent={{
              same: formatPer(totalInoutBadRate?.yoyInoutBadRate),
              circle: formatPer(totalInoutBadRate?.momInoutBadRate),
            }}
            type="full"
            handleClick={() => handleTabChange(SatisfactionType.全渠道万次投诉率)}
          />
        ) : (
          <div className="mt-2">
            <SShimmer />
          </div>
        )}
      </>
    ),
    [totalInoutBadRateLoading],
  );

  const { data: totalBadReplyRate, loading: totalBadReplyRateLoading } = useRequest(
    async () =>
      getTotalBadReplyRate({
        date: dayjs(params.beginDate).format('YYYY-MM'),
        groupId: params.groupId,
        shopIds: params.shopIds,
      }),
    {
      refreshDeps: [params],
    },
  );
  const badReplyIndicatorCard = useMemo(
    () => (
      <>
        {!totalBadReplyRateLoading ? (
          <IndicatorCard
            className="mt-2"
            title="差评回复率"
            tooltip={ToolTipMap[SatisfactionType.差评回复率]}
            indicator={formatPer(totalBadReplyRate?.detailResponse?.totalReplyRate)}
            percent={{
              same: formatPer(totalBadReplyRate?.yoyBadReplyRate),
              circle: formatPer(totalBadReplyRate?.momBadReplyRate),
            }}
            type="full"
            // isWarning={!!Info?.reportResponse?.badReplyStatus}
            handleClick={() => {
              handleTabChange(SatisfactionType.差评回复率);
              !!showReport && updateReadRun(showReport);
            }}
            hasReport={!!showReport}
          />
        ) : (
          <div className="mt-2">
            <SShimmer />
          </div>
        )}
      </>
    ),
    [totalBadReplyRateLoading],
  );

  const { data: totalFoodTimeRate, loading: totalFoodTiemLoading } = useRequest(
    async () =>
      getTotalFoodTime({
        date: dayjs(params.beginDate).format('YYYY-MM'),
        groupId: params.groupId,
        shopIds: params.shopIds,
      }),
    {
      refreshDeps: [params],
    },
  );
  const totalFoodTimeRateIndicatorCard = useMemo(
    () => (
      <>
        {!totalFoodTiemLoading ? (
          <IndicatorCard
            className="mt-2"
            title="平均出餐时长(分)"
            tooltip={ToolTipMap[SatisfactionType.平均出餐时长]}
            indicator={formatPer(totalFoodTimeRate?.foodTime, '')}
            percent={{
              same: formatPer(totalFoodTimeRate?.yoyRate),
              circle: formatPer(totalFoodTimeRate?.momRate),
            }}
            type="full"
            isChart={false}
            handleClick={() => handleTabChange(SatisfactionType.平均出餐时长)}
          />
        ) : (
          <div className="mt-2">
            <IndicatorCardShimmer />
          </div>
        )}
      </>
    ),
    [totalFoodTiemLoading],
  );

  const { data: totalMealOrderTimeoutRate, loading: totalMealOrderTimeoutRateLoading } = useRequest(
    async () =>
      getTotalMealOrderTimeoutRate({
        date: dayjs(params.beginDate).format('YYYY-MM'),
        groupId: params.groupId,
        shopIds: params.shopIds,
      }),
    {
      refreshDeps: [params],
    },
  );
  const mealOrderTimeoutIndicatorCard = useMemo(
    () => (
      <>
        {!totalMealOrderTimeoutRateLoading ? (
          <IndicatorCard
            className="mt-2"
            title="出餐超时订单占比"
            tooltip={ToolTipMap[SatisfactionType.出餐超时订单]}
            indicator={formatPer(totalMealOrderTimeoutRate?.mealOrderTimeoutRate)}
            percent={{
              same: formatPer(totalMealOrderTimeoutRate?.mealOrderTimeoutOverRate),
              circle: formatPer(totalMealOrderTimeoutRate?.mealOrderTimeoutRateRate),
            }}
            type="full"
            handleClick={() => handleTabChange(SatisfactionType.出餐超时订单)}
            count={{
              label: '出餐超时TC',
              value: totalMealOrderTimeoutRate?.mealOrderTimeoutTc || '-',
            }}
          />
        ) : (
          <div className="mt-2">
            <SShimmer />
          </div>
        )}
      </>
    ),
    [totalMealOrderTimeoutRateLoading],
  );

  const { data: list = {}, loading: listLoading } = useRequest(
    async () => {
      if (!showTypeOptions.length) throw null;
      const { beginDate, groupId, shopIds } = params;
      return getList({
        groupId,
        shopIds,
        date: dayjs(beginDate).format('YYYY-MM'),
        selType: detailType[0],
        dataType: dataType === 'shop' ? 2 : 1,
      });
    },
    { refreshDeps: [params, detailType, dataType] },
  );

  const showGroupNames = useMemo(() => {
    if (!params.groupId) return dataType === 'group' ? '全部组织' : '全部门店';
    const { list } = getGroupTree(+params.groupId);
    if (!list || !list.length) return <></>;
    const curNode = list[list.length - 1];
    const breadcrumbList = list.slice(0, list.length - 1);
    return (
      <>
        {breadcrumbList.map((v: { name: string; id: number }) => (
          <>
            <span
              key={v.id}
              className="text-primary"
              onClick={() => {
                setParams((p) => ({ ...p, groupId: String(v.id) }));
              }}
            >
              {v.name}
            </span>
            <IconFont type="icon-chevron-right" className="mx-1" />
          </>
        ))}
        <span>{curNode.name}</span>
      </>
    );
  }, [params.groupId, dataType]);

  const ListData = useMemo(() => {
    let arr = list[SatisfactionTypeEN[detailType[0]]] || [];

    if (searchValue) {
      arr = arr?.filter(
        (v: any) =>
          v?.groupName?.includes(searchValue) ||
          v?.shopName?.includes(searchValue) ||
          v?.name?.includes(searchValue),
      );
    }
    if (sorter && sorter[0]) {
      const [key, order] = sorter[0].split('-');
      arr = cloneDeep(arr).sort((a: any, b: any) => commonSorter(a[key], b[key], order));
    }
    return arr;
  }, [list, searchValue, sorter]);

  const handleTabChange = (key: SatisfactionType) => {
    setSorter([]);
    setDetailType([+key]);
  };

  const IndicatorMap = {
    [SatisfactionType.体验星级得分]: (
      <>
        <IndicatorCardV2
          title="顾客体验综合星级得分"
          // lineClassName="rounded-bl-base"
          tooltip={ToolTipMap[SatisfactionType.体验星级得分]}
          indicator={formatPer(totalStarRate?.starDetailResponse?.starScore, '')}
          percent={{
            same: formatPer(totalStarRate?.yoyInoutBadRate),
            circle: formatPer(totalStarRate?.momInoutBadRate),
          }}
          // isWarning={!!Info?.reportResponse?.starStatus}
        />
        <div className="bg-white p-4 rounded-b-lg flex flex-col gap-y-4">
          {[
            { label: '美团大众星级:', value: totalStarRate?.starDetailResponse?.mtDzStar },
            { label: '出餐速度(分钟):', value: totalStarRate?.starDetailResponse?.avgMealTime },
            {
              label: '抖音团购万次不满意率:',
              value: formatPer(totalStarRate?.starDetailResponse?.tikTokRate),
            },
            {
              label: '外部舆情百万次不满意率:',
              value: formatPer(totalStarRate?.starDetailResponse?.dineOuterRate),
            },

            {
              label: '内部服务态度+品质口味万次不满意率:',
              value: formatPer(totalStarRate?.starDetailResponse?.dineInnerRate),
            },
            {
              label: '美团饿了么外卖星级:',
              value: totalStarRate?.starDetailResponse?.mtEleStar,
            },
          ].map((o) => {
            return (
              <div key={o.label} className="text-13 leading-[13px]">
                <span className="text-5E">{o.label}</span>
                <span className="text-dark-14 ml-1">{o.value}</span>
              </div>
            );
          })}
        </div>
      </>
    ),
    [SatisfactionType.全渠道万次投诉率]: (
      <>
        <IndicatorCardV2
          title="全渠道万次投诉率"
          tooltip={ToolTipMap[SatisfactionType.全渠道万次投诉率]}
          indicator={formatPer(totalInoutBadRate?.detailResponse?.totalRate)}
          percent={{
            same: formatPer(totalInoutBadRate?.yoyInoutBadRate),
            circle: formatPer(totalInoutBadRate?.momInoutBadRate),
          }}
        />
        <div className="bg-white rounded-b-lg ">
          <BarCard
            id="bar1"
            data={[
              {
                name: '外卖平台差评率',
                value: totalInoutBadRate?.detailResponse?.takeOutRate,
                label: formatPer(totalInoutBadRate?.detailResponse?.takeOutRate),
              },
              {
                name: '团购差评率',
                value: totalInoutBadRate?.detailResponse?.grouponRate,
                label: formatPer(totalInoutBadRate?.detailResponse?.grouponRate),
              },
              {
                name: '小程序差评率',
                value: totalInoutBadRate?.detailResponse?.appletRate,
                label: formatPer(totalInoutBadRate?.detailResponse?.appletRate),
              },
              {
                name: '客服渠道万次投诉率',
                value: totalInoutBadRate?.detailResponse?.customRate,
                label: formatPer(totalInoutBadRate?.detailResponse?.customRate),
              },
            ]}
            yType="%"
            layout="full"
            mt="mt-1"
          />
        </div>
      </>
    ),
    [SatisfactionType.差评回复率]: (
      <>
        <IndicatorCardV2
          title="差评回复率"
          tooltip={ToolTipMap[SatisfactionType.差评回复率]}
          indicator={formatPer(totalBadReplyRate?.detailResponse?.totalReplyRate)}
          percent={{
            same: formatPer(totalBadReplyRate?.yoyBadReplyRate),
            circle: formatPer(totalBadReplyRate?.momBadReplyRate),
          }}
          // isWarning={!!Info?.reportResponse?.badReplyStatus}
        />
        <div className="bg-white rounded-b-lg ">
          <BarBarCard
            dataset={{
              dimensions: ['product', '总差评数', '差评回复数'],
              source: totalBadReplyRate?.detailResponse
                ? [
                    {
                      product: '大众点评团购',
                      总差评数: totalBadReplyRate?.detailResponse?.dzRatings,
                      差评回复数: totalBadReplyRate?.detailResponse?.dzReplyRatings,
                    },
                    {
                      product: '美团团购',
                      总差评数: totalBadReplyRate?.detailResponse?.mtGrouponRatings,
                      差评回复数: totalBadReplyRate?.detailResponse?.mtGrouponReplyRatings,
                    },
                    {
                      product: '美团外卖',
                      总差评数: totalBadReplyRate?.detailResponse?.mtRatings,
                      差评回复数: totalBadReplyRate?.detailResponse?.mtReplyRatings,
                    },
                    {
                      product: '饿了么外卖',
                      总差评数: totalBadReplyRate?.detailResponse?.eleRatings,
                      差评回复数: totalBadReplyRate?.detailResponse?.eleReplyRatings,
                    },
                  ]
                : [],
            }}
            id="BarOfDataSet1"
          />
        </div>
      </>
    ),
    [SatisfactionType.平均出餐时长]: (
      <IndicatorCardV2
        title="平均出餐时长(分)"
        className="rounded-lg"
        lineClassName="rounded-bl-lg"
        tooltip={ToolTipMap[SatisfactionType.平均出餐时长]}
        indicator={formatPer(totalFoodTimeRate?.foodTime, '')}
        percent={{
          same: formatPer(totalFoodTimeRate?.yoyRate),
          circle: formatPer(totalFoodTimeRate?.momRate),
        }}
        // isWarning={!!Info?.reportResponse?.badReplyStatus}
      />
    ),
    [SatisfactionType.出餐超时订单]: (
      <>
        <IndicatorCardV2
          title="出餐超时率"
          tooltip={ToolTipMap[SatisfactionType.出餐超时订单]}
          indicator={formatPer(totalMealOrderTimeoutRate?.mealOrderTimeoutRate)}
          percent={{
            same: formatPer(totalMealOrderTimeoutRate?.mealOrderTimeoutOverRate),
            circle: formatPer(totalMealOrderTimeoutRate?.mealOrderTimeoutRateRate),
          }}
          // isWarning={!!Info?.reportResponse?.badReplyStatus}
        />
        <div className="bg-white p-4 rounded-b-lg flex justify-between items-center text-13 leading-[13px]">
          <span>出餐超时TC</span>
          <span className="text-base leading-4 text-dark-14">
            {totalMealOrderTimeoutRate?.mealOrderTimeoutTc}
          </span>
        </div>
      </>
    ),
  };

  return (
    <div>
      <Loading spinning={false}>
        <div hidden={hiddenDetail} className="bg-white">
          <div className="flex items-center justify-between pr-4 pl-1 border-solid border-b border-line">
            <Tabs
              className={styles.iAdmTabs1}
              activeKey={String(detailType[0])}
              onChange={(key) => {
                handleTabChange(key as unknown as SatisfactionType);
              }}
              stretch={false}
              style={{
                '--title-font-size': '0.875rem',
              }}
            >
              {showTypeOptions.map(({ value, label }) => (
                <Tabs.Tab title={label} key={value}></Tabs.Tab>
              ))}
            </Tabs>
            {/* <DataTypeSelect
              options={showTypeOptions}
              defaultValue={detailType[0] as unknown as string}
              onChange={(key) => {
                setSorter([]);
                setDetailType([+key]);
              }}
            /> */}
          </div>
        </div>
        <Card className="rounded-none px-4" bodyClassName="!py-2">
          <div className="flex justify-between items-start">
            <CustomerFilter
              noBg={true}
              className="flex-1"
              value={params}
              onChange={(e: any) => {
                setParams((p) => ({ ...p, ...e }));
              }}
              shopKey="shopIds"
            />
            <DateFilter
              type="month"
              onlyMonth
              noBg={true}
              value={[dayjs(params.beginDate).toDate(), dayjs(params.endDate).toDate()]}
              onChange={(e) => {
                setParams((p) => ({ ...p, beginDate: e[0], endDate: e[1] }));
              }}
            />
          </div>
        </Card>
        <div hidden={hiddenHome}>
          {permissionsMap.has(AuthorityEnum.体验星级得分) && totalStarRateIndicatorCard}
          {permissionsMap.has(AuthorityEnum.全渠道万次投诉率) && inoutBadIndicatorCard}

          {permissionsMap.has(AuthorityEnum.差评回复率) && badReplyIndicatorCard}
          {permissionsMap.has(AuthorityEnum.平均出餐时长) && totalFoodTimeRateIndicatorCard}
          {permissionsMap.has(AuthorityEnum.出餐时长超时率) && mealOrderTimeoutIndicatorCard}
        </div>
        {/* <div hidden={hiddenHome}>
          <INoticeBar
            className="mt-2"
            status={alarmData.status ? 'danger' : 'success'}
            content={`当前管辖范围内，${!alarmData.status ? '无异常指标。' : alarmData.text}`}
          />
        </div> */}

        {/* 诊断报告 */}
        <div
          hidden={
            hiddenDetail || SatisfactionType.差评回复率 !== detailType[0] || !roleTypeIsManage()
          }
          className="px-2 pt-2"
        >
          <ExceptionReport
            className="text-grey"
            hasReport={!!reportData?.reportId}
            title="门店差评回复率低于95%"
            shopNum={reportData?.businessFieldObject?.count}
            time={reportData?.reportTime || ''}
            content={
              reportData?.anomalyStatus
                ? `截止${dayjs(reportData?.reportTime).format('YYYY年MM月DD号')}，${dayjs(
                    reportData?.reportTime,
                  ).format('MM月份')}${
                    reportData?.businessFieldObject?.count ?? 0
                  }家门店差评回复率低于95%`
                : `太棒了，截止到${dayjs(reportData?.reportTime).format(
                    'YYYY年MM月DD号',
                  )}您管辖范围下的门店没有出现差评回复率低于95%的门店`
            }
            type="page"
            footerClick={() => {
              navigator(
                `/boards/exception/report${qs.stringify(
                  {
                    type: 'satisfaction',
                    startCreateTime: params.beginDate,
                    endCreateTime: params.endDate,
                  },
                  { addQueryPrefix: true },
                )}`,
              );
            }}
            onDetailClick={() => {
              navigator(
                `/boards/quick_creation${qs.stringify(
                  {
                    type: 'satisfaction',
                    reportId: reportData?.reportId,
                  },
                  { addQueryPrefix: true },
                )}`,
              );
            }}
            hiddenDetialEntry={!!!reportData?.anomalyStatus}
          />
        </div>
        <div hidden={hiddenDetail} className="my-[10px] mx-2">
          {(!hiddenDetail && IndicatorMap[detailType[0]]) || <></>}
        </div>
        <div
          hidden={hiddenDetail}
          className="pb-2 sticky top-0 z-50"
          style={{ background: '#F5F5F5' }}
        >
          <Card className="rounded-none" style={{ padding: 0 }} bodyClassName="!py-0">
            <div className="flex items-center justify-between px-4 py-2">
              <div className="text-base font-semibold">明细数据</div>
              <CustomerType
                value={dataType}
                onChange={(e) => setDataType(e as 'shop' | 'group')}
                disabledGroup={disabledGroup}
                disabledShop={disabledShop}
              />
            </div>
            <div className="flex items-center px-4 py-1" key={detailType[0]}>
              <SearchBar
                className={`${styles.iAdmSearchBar} flex-1 mr-2`}
                style={{ '--border-radius': '8px', '--height': '2.5rem' }}
                onClear={() => setSearchValue('')}
                onSearch={setSearchValue}
                placeholder="请输入门店/组织名称"
              />
              <Sorter
                config={typeObj[detailType[0]].sortConfig}
                value={sorter}
                onChange={setSorter}
              />
            </div>

            <div className="flex items-center text-xs px-4 py-2 text-light overflow-x-auto whitespace-nowrap">
              {showGroupNames}
            </div>
          </Card>
        </div>
        <div hidden={hiddenDetail}>
          {listLoading ? (
            <div>
              {new Array(3).fill('').map((_v, index) => (
                <div className="mb-2 px-4 bg-white py-3" key={index}>
                  <Skeleton animated className="w-[200px] h-5" />
                  <Skeleton animated className="w-full h-5 mt-2" />
                  <Skeleton animated className="w-full h-5 mt-2" />
                </div>
              ))}
            </div>
          ) : (
            <PageList
              dataSource={ListData}
              itemRender={(item: any, index) =>
                typeObj[detailType[0]].cardRender(
                  item,
                  index,
                  params,
                  detailType[0],
                  navigator,
                  params.groupId,
                )
              }
            />
          )}
        </div>
      </Loading>
    </div>
  );
});

export default Satisfaction;
