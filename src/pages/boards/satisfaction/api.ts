import { post } from '@src/api';
import { overviewParams } from './api.type';
// 废弃
export const getRateAndReportData = (data: overviewParams) =>
  post('api/rest/degree/statistics/degree/rateAndReport', { data });

// 综合星级满意度一级看板
export const getTotalStarRate = (data: overviewParams) =>
  post('/om-api/common/degree/statistics/getTotalStarRate', { data });
// 内外部全渠道投诉率一级看板
export const getTotalInoutBadRate = (data: overviewParams) =>
  post('/om-api/common/degree/statistics/getTotalInoutBadRate', { data });
// 差评回复率一级看板
export const getTotalBadReplyRate = (data: overviewParams) =>
  post('/om-api/common/degree/statistics/getTotalBadReplyRate', { data });
// 平均出餐时长一级看板
export const getTotalFoodTime = (data: overviewParams) =>
  post('/om-api/common/degree/statistics/app/summary', { data });
// 出餐超时订单一级看板
export const getTotalMealOrderTimeoutRate = (data: overviewParams) =>
  post('/om-api/common/degree/statistics/getTotalMealOrderTimeoutRate', { data });

export const getList = (data: overviewParams) =>
  post('/om-api/common/degree/statistics/diagnoStisc/detail', { data });
// app详细数据列表-平均出餐时长(废弃)
export const getFoodTimeList = (data: overviewParams) =>
  post('/om-api/common/degree/food-time/app/detail', { data });
