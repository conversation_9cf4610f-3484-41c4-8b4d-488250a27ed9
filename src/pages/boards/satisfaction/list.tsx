import { useLocation, useNavigate } from 'react-router-dom';
import { SatisfactionType, typeObj } from './enum';
import { useMemo, useState } from 'react';
import { useRequest } from 'ahooks';
import { getList } from './api';
import { cloneDeep } from 'lodash';
import { IconFont, Loading, PageList } from '@src/components';
import { Card, SearchBar } from 'antd-mobile';
import { Sorter } from '../components';
import dayjs from 'dayjs';
import styles from '../index.module.scss';
import { usePageContext } from '@src/common/page-context';
import { commonSorter } from '@src/utils/helper';

const SatisfactionTypeEN = {
  1: 'stars',
  2: 'inoutBads',
  4: 'badReplys',
  8: 'mealOrderTimeoutList',
  9: 'foodTimeList',
};

const SatisfactionList = () => {
  const { params, detailType }: { params: any; detailType: SatisfactionType } =
    useLocation().state || {};
  const { getGroupTree, tagIdObj } = usePageContext();
  const [searchValue, setSearchValue] = useState<string>('');
  const [sorter, setSorter] = useState<string[]>([]);
  const navigator = useNavigate();

  const showGroupNames = useMemo(() => {
    const { list } = getGroupTree(+params.groupId, +params.stopId);

    if (!list || !list.length) return <></>;
    // const firstNode = list[0];
    const curNode = list.pop();
    const breadcrumbList = list;
    return (
      <>
        {tagIdObj && params.stopId && (
          <>
            <span>{tagIdObj[params.stopId].name}</span>
            <IconFont type="icon-chevron-right" className="mx-1" />
          </>
        )}
        {breadcrumbList.map((v: { name: string; id: number }, index: number) => (
          <>
            <span
              key={v.id}
              className="text-primary"
              onClick={() => {
                const idx = breadcrumbList.length - index;
                navigator(-idx);
              }}
            >
              {v.name}
            </span>
            <IconFont type="icon-chevron-right" className="mx-1" />
          </>
        ))}
        <span>{curNode.name}</span>
      </>
    );
  }, [params.groupId]);

  const { data: list = {}, loading } = useRequest(
    async () => {
      const { beginDate, groupId, shopIds } = params;
      return getList({
        groupId,
        shopIds,
        date: dayjs(beginDate).format('YYYY-MM'),
        selType: detailType,
        dataType: 1,
      });
    },
    { refreshDeps: [params, detailType] },
  );
  const ListData = useMemo(() => {
    let arr = list[SatisfactionTypeEN[detailType]] || [];
    if (searchValue) {
      arr = arr?.filter(
        (v: any) =>
          v?.groupName?.includes(searchValue) ||
          v?.shopName?.includes(searchValue) ||
          v?.name?.includes(searchValue),
      );
    }
    if (sorter && sorter[0]) {
      const [key, order] = sorter[0].split('-');
      arr = cloneDeep(arr).sort((a: any, b: any) => commonSorter(a[key], b[key], order));
    }
    return arr;
  }, [list, searchValue, sorter]);

  return (
    <div>
      <Loading spinning={loading}>
        <div className="sticky top-0 z-10 pb-3" style={{ background: '#F5F5F5' }}>
          <Card className="rounded-none" bodyClassName="!pb-0">
            <div className="flex items-center mb-3">
              <SearchBar
                className={`${styles.iAdmSearchBar} flex-1 mr-2`}
                style={{ '--border-radius': '8px', '--height': '2.5rem' }}
                onClear={() => setSearchValue('')}
                onSearch={setSearchValue}
                placeholder="请输入门店/组织名称"
              />
              <Sorter config={typeObj[detailType].sortConfig} value={sorter} onChange={setSorter} />
            </div>
            <div className="flex items-center text-xs mt-2 pb-3 text-gray-500 overflow-x-auto whitespace-nowrap">
              {showGroupNames}
            </div>
          </Card>
        </div>
        <PageList
          dataSource={ListData}
          itemRender={(item: any, index) =>
            typeObj[detailType].cardRender(item, index, params, detailType, navigator)
          }
        />
      </Loading>
    </div>
  );
};

export default SatisfactionList;
