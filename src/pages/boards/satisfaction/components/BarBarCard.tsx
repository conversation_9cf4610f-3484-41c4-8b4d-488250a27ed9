import { IconFont } from '@src/components';
import { color } from '@src/components/IEcharts/Bar/BarOptionColor';
import { BarOfDataSet } from '@src/components/IEcharts/BarOfDataSet';
import { cloneDeep } from 'lodash';

interface BarBarCardProps {
  dataset?: {
    dimensions: string[];
    source: any[];
  };
  id: string;
}

export const BarBarCard = ({ id, dataset }: BarBarCardProps) => {
  return (
    <div className="p-4 pb-5 flex flex-col gap-4">
      {dataset?.source.length !== 0 ? (
        <>
          <div className="flex gap-4">
            {cloneDeep(dataset?.dimensions || [])
              .splice(1)
              .map((o, idx) => {
                return (
                  <div className="flex items-center gap-2">
                    <div
                      style={{
                        background: color[idx],
                      }}
                      className="w-[5px] h-[5px] rounded-full"
                    ></div>
                    <div className="text-xs leading-3 text-5E">{o}</div>
                  </div>
                );
              })}
          </div>
          <BarOfDataSet
            id={id}
            dataset={dataset}
            style={{
              height: '100px',
            }}
            yType="个"
          />
        </>
      ) : (
        <div className="flex flex-col items-center h-full gap-4">
          <IconFont type="icon-zanwushuju" className="text-[90px]  text-[#DCDCDC]" />
          <span className="text-[#858585] text-xs">暂无数据</span>
        </div>
      )}
    </div>
  );
};
