import { useMemo } from 'react';
import arriveHome from '@src/assets/images/arrive_home.png';
import dataRealTime from '@src/assets/images/data_real_time.png';
import doBattleGroup from '@src/assets/images/do_battle_group.png';
import selfCheckingRealTime from '@src/assets/images/self_checking_real_time.png';
import smartOperation from '@src/assets/images/smart_operation.png';
import storeDataBoard from '@src/assets/images/storeDataBoard.png';
import tstLogo from '@src/assets/images/tst_logo.png';
// import { roleTypeIsManage } from '@src/utils/tokenUtils';
import { userStore } from '@src/store';
import { getDataViewBoardEnum } from '@src/utils/utils';
import { Card, ErrorBlock } from 'antd-mobile';
import { observer } from 'mobx-react';
import { useNavigate } from 'react-router-dom';

const boardList = [
  {
    label: '自检实时情况',
    key: '/dataViewBoard',
    icon: selfCheckingRealTime,
    width: 47.47,
    height: 41.78,
    permission: getDataViewBoardEnum('自检实时情况'),
  },
  {
    label: '智慧运营看板',
    key: '/smartOperationBoard',
    icon: smartOperation,
    width: 46.55,
    height: 43.15,
    permission: getDataViewBoardEnum('智慧运营看板'),
  },
  {
    label: '作战小组数据',
    key: '/boards',
    icon: doBattleGroup,
    width: 51.01,
    height: 43.9,
    permission: getDataViewBoardEnum('作战小组数据看板'),
  },
  {
    label: '门店数据看板',
    key: '/storeDataViewBoard',
    icon: storeDataBoard,
    width: 47.47,
    height: 41.78,
    permission: getDataViewBoardEnum('门店数据看板'),
  },
  {
    label: '到家加盟业主看板',
    key: '/arrive',
    icon: arriveHome,
    width: 46.55,
    height: 43.15,
    permission: getDataViewBoardEnum('到家加盟业主看板'),
  },
  {
    label: '数据实时情况',
    key: '/dataViewBoardRealTime',
    icon: dataRealTime,
    width: 47.47,
    height: 41.78,
    permission: getDataViewBoardEnum('数据实时情况'),
  },
];

const SelectBoard = observer(() => {
  const navigate = useNavigate();

  const boards = useMemo(() => {
    return boardList.filter((f) =>
      !!f?.permission && typeof f?.permission === 'string'
        ? userStore.permissionsMap.has(f.permission)
        : !!f?.permission,
    );
  }, []);

  if (!boards?.length) {
    return <ErrorBlock status="empty" title="您没有该页面权限" description={<span>请与客服人员联系！</span>} />;
  }

  return (
    <div className="p-3">
      <div className="flex flex-wrap gap-y-4 justify-between">
        {boards.map((v) => (
          <Card
            className="rounded-lg w-[48%] h-[3.75rem] flex justify-center items-center relative overflow-hidden p-0"
            key={v.key}
            style={{
              boxShadow: '0px 4px 10px 0px rgba(0, 0, 0, 0.06)',
            }}
            onClick={() => {
              navigate(v.key);
            }}
          >
            <div className="flex items-center">
              <div className="w-[3.5rem] h-[3.5rem] flex justify-center items-center">
                <img src={v.icon} alt="icon_pic" width={v.width} height={v.height} />
              </div>
              <div className="text-[#000000] font-medium text-sm">{v.label}</div>
            </div>
            <div className="absolute top-0 right-0">
              <img src={tstLogo} alt="tst_logo" width={84} height={72} />
            </div>
          </Card>
        ))}
      </div>
    </div>
  );
});

export default SelectBoard;
