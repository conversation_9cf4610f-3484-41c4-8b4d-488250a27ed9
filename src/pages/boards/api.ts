import { get, post } from '@src/api';

export const sendMsg = (data: { userIds: number[] }) =>
  post('/om-api/common/headResource/dashboard/send-message', { data });

export const checkReport = (params: { reportType: 1 | 2 | 3 | 4 }) =>
  get('/om-api/common/report/diagnostic/checkReportExists', { params });
/**
 * 获取诊断报告详情
 * reportType 1稽查、2人力、3业绩、4满意度
 */
export const getReport = (params: { reportType: 1 | 2 | 3 | 4 }) =>
  get('/om-api/common/report/diagnostic/getReportInfo', { params });

export const updateReadFlag = (reportId: number) =>
  get('/om-api/common/report/diagnostic/markReadReport', { params: { reportId } });
