import { get, post } from '@src/api';

// 查询诊断报告历史列表
export const getReportItemList = (params: { reportId: string }) =>
  get('/om-api/common/report/diagnostic/getReportItemList', { params });
// 根据报告类型查看任务详情
export const getTaskDetailByReportType = (params: { reportType: 1 | 2 | 3 | 4 }) =>
  get('/om-api/common/routine/task/getTaskDetailByReportType', { params });
// 根据报告快速创建任务
type taskItemList = {
  anomalyId: number; // 异常门店ID
  sopIds: number[];
  taskInfo: string;
  taskTitle: string;
  taskTypeId: number;
};
type quickCreateTaskByReportPayload = {
  reportType: 1 | 2 | 3 | 4;
  taskDeadline: string;
  taskItemList: taskItemList[];
  finishAttachRequired?: 0 | 1; // 完成任务文件必填标识。0关闭1开启
  updateAttachRequired?: 0 | 1; // 更新任务进度文件必填标识
};
export const quickCreateTaskByReport = (data: quickCreateTaskByReportPayload) =>
  post('/om-api/common/routine/task/quickCreateTaskByReport', { data });
// 校验是否有快速创建任务的权限
export const checkStoreCreatePermission = (data: { shopIds: string[] }) =>
  post('/om-api/common/routine/task/checkStoreCreatePermission', { data });
