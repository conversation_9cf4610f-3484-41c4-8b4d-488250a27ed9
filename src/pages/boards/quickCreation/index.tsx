import { useEffect, useMemo, useState } from 'react';
import suc from '@src/assets/images/creation_suc.png';
import empty from '@src/assets/images/empty.png';
import { IconFont, Loading } from '@src/components';
import { IPopup } from '@src/components/IPopup';
import { useScrollFetch } from '@src/hooks';
import { formatPer } from '@src/utils/helper';
import { useRequest, useSelections, useToggle } from 'ahooks';
import { Button, Checkbox, InfiniteScroll, SafeArea, Toast } from 'antd-mobile';
import dayjs from 'dayjs';
import { flatten } from 'lodash';
import { useNavigate, useSearchParams } from 'react-router-dom';
import {
  checkStoreCreatePermission,
  getReportItemList,
  getTaskDetailByReportType,
  quickCreateTaskByReport,
} from './api';
import { TypeMapEnum } from '../reportList';

export default function QuickCreationIndex() {
  const [visible, setVisible] = useState(false);
  const [sendVisible, setSendVisible] = useState(false);
  const [taskVisible, setTaskVisible] = useState(false);
  const [search] = useSearchParams();
  const type = search.get('type') as keyof typeof TypeMapEnum;
  const [isCreationFlag] = useToggle(false);
  const navigate = useNavigate();

  const { data: taskDetail } = useRequest(() =>
    getTaskDetailByReportType({ reportType: TypeMapEnum[type] }),
  );

  const [taskList, setTaskList] = useState([]);
  const {
    list: listForRender,
    over,
    fetchNext,
  } = useScrollFetch(
    ({ pageNo, pageSize }) =>
      Promise.resolve(
        taskList.slice((pageNo - 1) * pageSize, pageNo * pageSize),
      ),
    [taskList],
    {
      pageSize: 20,
      computeOver: (_, { pageNo, pageSize }) => {
        return taskList.length <= pageNo * pageSize;
      },
    },
  );

  const {
    data: reportItemListData,
    loading,
    refresh,
  } = useRequest(
    () => getReportItemList({ reportId: search.get('reportId')! }),
    {
      onSuccess: (data) => {
        setTaskList(data?.items || []);
      },
    },
  );
  const {
    data: sucCount,
    run,
    loading: quickCreateTaskByReportLoading,
  } = useRequest(
    (data) =>
      quickCreateTaskByReport({
        ...data,
        finishAttachRequired: 0,
        updateAttachRequired: 0,
      }),
    {
      manual: true,
      onSuccess: () => {
        refresh();
        setVisible(false);
        setSendVisible(true);
      },
    },
  );

  const { selected, isSelected, toggle, setSelected, allSelected, toggleAll } =
    useSelections<any>(
      (reportItemListData?.items || []).map((o: any) => o.id),
      [],
    );

  const selectVal = useMemo(() => {
    return selected.map((s: any) => {
      return reportItemListData?.items.find((i: any) => {
        return i.id === s;
      });
    });
  }, [selected, reportItemListData]);
  const {
    list: selectValForRender,
    over: selectValOver,
    fetchNext: selectValFetchNext,
  } = useScrollFetch(
    ({ pageNo, pageSize }) =>
      Promise.resolve(
        selectVal.slice((pageNo - 1) * pageSize, pageNo * pageSize),
      ),
    [selectVal],
    {
      pageSize: 20,
      computeOver: (_, { pageNo, pageSize }) => {
        return selectVal.length <= pageNo * pageSize;
      },
    },
  );
  const infoMap = {
    audit: {
      title: '你管辖的门店存在“S项”',
      desc: '门店发生S项：需3天内追踪门店整改完成并复核。',
    },
    hr: {
      title: '你负责的门店招聘进度低于时间进度',
      desc: '实际招募达成率低于时间进度',
    },
    achievement: {
      title: '门店外卖日均营业额下降',
      desc: '通过四率分析（曝光率、进店转换率、下单转换率、复购率），确认营业额下降原因并完成改善',
    },
    satisfaction: {
      title: '您的门店差评回复率低于95%',
      desc: '针对差评回复率低于95%门店，需要改善至95%以上',
    },
  };

  const detailTitleBuilder = (
    type: keyof typeof TypeMapEnum,
    item: Record<string, any>,
    reportTime: string,
  ) => {
    switch (type) {
      case 'audit':
        // return `你管辖的${arr.map((o) => o?.shopId + o?.shopName).join('，')}存在“S项”`;
        return `你管辖的${item?.shopId + item?.shopName}在${dayjs(reportTime)
          .subtract(1, 'day')
          .format('YYYY年MM月DD号')}发生“S项”`;

      case 'hr':
        return `截止${dayjs(reportTime).format('YYYY年MM月DD号')}，本月${
          item?.shopId + item?.shopName
        }招募进度低于时间进度`;

      case 'achievement':
        return `截止${dayjs(reportTime).format('YYYY年MM月DD号')}，本月${
          item?.shopId + item?.shopName
        }门店外卖日均营业额下降${formatPer(Math.abs(item?.increaseRate))}`;

      case 'satisfaction':
        return `截止${dayjs(reportTime).format('YYYY年MM月DD号')}，本月${
          item?.shopId + item?.shopName
        }差评回复率为${formatPer(item?.totalReplyRate)}，低于95%`;

      default:
        return '';
    }
  };

  const detailDescBuilder = (
    type: keyof typeof TypeMapEnum,
    item: Record<string, any>,
  ) => {
    switch (type) {
      case 'audit':
        // return `${arr
        //   .map((o) => `${o?.shopId}${o?.shopName}发生"S"项：${o?.remark}`)
        //   .join('、')}。需3天内追踪门店整改完成并复核`;
        return `${item?.shopId}${item?.shopName}发生"S"项：${item?.remark}。需3天内追踪门店整改完成并复核`;

      case 'hr':
        return `${item?.shopId}${item?.shopName}招聘达成率：${formatPer(
          item?.achievedRate,
        )}，门店已入职人数：${item?.entryCount}，需求人数：${item?.headcount}，低于时间进度`;

      case 'achievement':
        return '通过四率分析（曝光率、进店转换率、下单转换率、复购率），确认营业额下降原因并完成改善';

      case 'satisfaction':
        return '针对差评回复率低于95%门店，需要改善至95%以上';

      default:
        return '';
    }
  };

  const {
    run: checkStoreCreatePermissionReq,
    loading: checkStoreCreatePermissionLoading,
  } = useRequest((data) => checkStoreCreatePermission(data), {
    manual: true,
    onSuccess: (data) => {
      if (data.length === 0) {
        run({
          reportType: TypeMapEnum[type],
          taskDeadline: taskDetail?.taskDeadline,
          taskItemList: selectVal.map((o) => ({
            anomalyId: o?.id,
            shopCode: o?.shopId,
            shopTableId: o?.shopTableId,
            sopIds: (taskDetail?.sopInfoList || []).map((s: any) => s?.id),
            taskTitle: detailTitleBuilder(
              type,
              { ...o },
              reportItemListData?.reportTime,
            ),
            taskInfo: detailDescBuilder(type, { ...o }),
            taskTypeId: taskDetail?.taskType.id,
          })),
        });
      } else {
        const noCheckShop = data.map((o: any) => {
          const item = selectVal.find((s) => s.shopId === o);
          toggle(item.id);
          return `${item?.shopId}${item?.shopName}`;
        });
        Toast.show({
          content: `您未获取对${noCheckShop.join(
            '，',
          )}派发任务的权限，请确认您拥有派发任务的门店权限`,
        });
        setVisible(false);
      }
    },
  });

  const cardBuilder = (type: keyof typeof TypeMapEnum, data: any) => {
    switch (type) {
      case 'audit':
        return (
          <div className=" flex flex-col gap-y-1 text-[#5E5E5E] text-sm leading-[22px] ">
            <div className="flex items-center gap-x-1 ">
              <span className="break-all">
                {data?.shopId} {data?.shopName}
              </span>
              {data.distributionStatus === 1 && (
                <span className="py-[2px] px-[3px] shrink-0 rounded-sm text-primary border border-primary text-xs leading-3 flex items-center justify-center">
                  已派发任务
                </span>
              )}
            </div>
            <div>报告名称：{data?.reportName}</div>
            <div className="text-[#141414]">
              <span className="font-semibold">门店触碰“S项”：</span>
              <span>{data?.remark}</span>
            </div>
          </div>
        );

      case 'hr':
        return (
          <div className=" flex flex-col gap-y-1 text-[#5E5E5E] text-sm leading-[22px] ">
            <div className="flex items-center gap-x-1 ">
              <span className="break-all">
                {data?.shopId} {data?.shopName}
              </span>
              {data.distributionStatus === 1 && (
                <span className="py-[2px] px-[3px] shrink-0 rounded-sm text-primary border border-primary text-xs leading-3 flex items-center justify-center">
                  已派发任务
                </span>
              )}
            </div>
            <div>
              门店已入职人数：{data?.entryCount}&nbsp;&nbsp;&nbsp;需求人数：
              {data?.headcount}
            </div>
            <div className="">
              <span>门店招聘达成率：</span>
              <span className="">{formatPer(data?.achievedRate)}</span>
            </div>
          </div>
        );

      case 'achievement':
        return (
          <div className=" flex flex-col gap-y-1 text-[#5E5E5E] text-sm leading-[22px] ">
            <div className="flex items-center gap-x-1 ">
              <span className="break-all">
                {data?.shopId} {data?.shopName}
              </span>
              {data.distributionStatus === 1 && (
                <span className="py-[2px] px-[3px] shrink-0 rounded-sm text-primary border border-primary text-xs leading-3 flex items-center justify-center">
                  已派发任务
                </span>
              )}
            </div>
            <div>
              {dayjs(data?.reportDate).format('MM月份')}，截止至诊断报告时间
            </div>
            <div className="">
              <span>外卖日均营业额：</span>
              <span>
                <span className="">{data?.thisMonthAverage}元</span>
                （环比下降
                {formatPer(Math.abs(data?.increaseRate))}）
              </span>
            </div>
          </div>
        );

      case 'satisfaction':
        return (
          <div className=" flex flex-col gap-y-2 text-[#5E5E5E] text-sm leading-[22px] ">
            <div className="flex items-center gap-x-1 ">
              <span className="break-all">
                {data?.shopId} {data?.shopName}
              </span>
              {data.distributionStatus === 1 && (
                <span className="py-[2px] px-[3px] shrink-0 rounded-sm text-primary border border-primary text-xs leading-3 flex items-center justify-center">
                  已派发任务
                </span>
              )}
            </div>
            <div className="text-h16 text-dark-14 font-bold">
              <span>差评回复率：</span>
              <span className="">{formatPer(data?.totalReplyRate)}</span>
            </div>
            <div className="flex flex-col gap-y-[2px]">
              <div className="">
                <span>大众点评差评回复率：</span>
                <span className="">{formatPer(data?.dzReplyRate)}</span>
              </div>
              <div className="">
                <span>美团团购差评回复率：</span>
                <span className="">{formatPer(data?.mtGrouponReplyRate)}</span>
              </div>
              <div className="">
                <span>美团外卖差评回复率：</span>
                <span className="">{formatPer(data?.mtReplyRate)}</span>
              </div>
              <div className="">
                <span>饿了么差评回复率：</span>
                <span className="">{formatPer(data?.eleReplyRate)}</span>
              </div>
            </div>
          </div>
        );

      default:
        break;
    }
  };

  useEffect(() => {
    if (!isCreationFlag) {
      setSelected([]);
    }
  }, [isCreationFlag]);

  return (
    <Loading
      spinning={
        loading ||
        quickCreateTaskByReportLoading ||
        checkStoreCreatePermissionLoading
      }
      maskClickable={false}
    >
      <div className="flex flex-col h-full">
        <div className="flex justify-between items-center bg-white p-4 border-b border-black/[0.03] shrink-0">
          <div className="flex items-center text-sm leading-[14px] text-[#5e5e5e] gap-x-1">
            <Checkbox
              style={{
                '--icon-size': '17px',
              }}
              onChange={(value) => {
                value
                  ? setTaskList(
                      (reportItemListData?.items || []).filter(
                        (o: any) => o?.distributionStatus === 0,
                      ),
                    )
                  : setTaskList(reportItemListData?.items || []);
              }}
            />
            仅查看未派发任务
          </div>
          {/* FIXME:20240528暂时关闭入口 */}
          {/* <span
            onClick={() => {
              isCreationFlagToggle();
            }}
            className="text-13 text-primary leading-[14px] "
          >
            {isCreationFlag ? '退出派发' : '派发任务'}
          </span> */}
        </div>
        <div className="grow overflow-y-scroll">
          {flatten(listForRender).length !== 0 ? (
            <div>
              {flatten(listForRender).map((o: any) => {
                return (
                  <div
                    onClick={() => {
                      isCreationFlag && toggle(o.id);
                    }}
                    key={o.id}
                    className="flex items-start px-4 py-3 gap-x-3 border-b bg-white border-black/[0.03]"
                  >
                    {isCreationFlag && (
                      <Checkbox
                        checked={isSelected(o.id)}
                        style={{
                          '--icon-size': '16px',
                          marginTop: 4,
                        }}
                      />
                    )}
                    {cardBuilder(type, o)}
                  </div>
                );
              })}
              <InfiniteScroll loadMore={fetchNext} hasMore={!over} />
            </div>
          ) : (
            <div className="w-full">
              <img
                src={empty}
                alt=""
                className="w-[178px] h-[148px] object-cover ml-[87px] mt-[127px]"
              />
              <div className="text-[#9E9E9E] text-13 leading-[14px] mt-[28px] mx-auto w-fit">
                暂无报告
              </div>
            </div>
          )}
        </div>
        {isCreationFlag && (
          <div className="flex h-[54px] w-full shrink-0">
            <div className="flex justify-between items-center grow bg-white px-4">
              <div className="flex gap-x-1 items-center justify-center">
                <Checkbox
                  checked={allSelected}
                  onClick={toggleAll}
                  style={{
                    '--icon-size': '17px',
                  }}
                />
                <span className="text-xs">全选</span>
              </div>
              <div className="flex flex-col gap-y-[7px] items-end">
                <span className="flex items-center text-sm leading-[14px] text-[#58595B]">
                  已选记录：
                  <span
                    className="text-lg leading-[18px] text-primary font-medium flex items-center gap-x-1"
                    onClick={() => {
                      setTaskVisible(true);
                    }}
                  >
                    {selected.length}
                    <IconFont
                      type="icon-chevron-up"
                      className="text-primary text-base"
                    />
                  </span>
                </span>
                <span className="text-xs leading-[13px] text-[#979797]">
                  点击查看已选派发任务门店
                </span>
              </div>
            </div>
            <div
              className="w-[132px] flex items-center justify-center bg-primary text-white text-sm leading-[14px] shrink-0"
              onClick={() => {
                if (selectVal.length === 0) {
                  Toast.show({ content: '请选择门店后在派发任务' });
                } else {
                  setVisible(true);
                }
              }}
            >
              确认派发任务
            </div>
          </div>
        )}
        <SafeArea position="bottom" />
        <IPopup
          visible={visible}
          title="确认派发任务"
          footer={
            <div className="bg-white flex gap-x-3 p-3">
              <Button
                color="primary"
                fill="outline"
                block
                className="h-[45px] text-base"
                onClick={() => {
                  setVisible(false);
                }}
              >
                取消
              </Button>
              <Button
                color="primary"
                fill="solid"
                block
                className="h-[45px] text-base"
                onClick={() => {
                  if ((taskDetail?.taskPrincipalRoles || []).length === 0) {
                    Toast.show({
                      content: '任务处理人为空',
                    });
                    return;
                  }
                  checkStoreCreatePermissionReq({
                    shopIds: selectVal.map((s: any) => s?.shopId),
                  });
                }}
              >
                确认发送
              </Button>
            </div>
          }
          onClose={() => {
            setVisible(false);
          }}
          bodyStyle={{ height: '75vh' }}
        >
          <div className="pb-4">
            <div className="pt-4 pr-4 text-15 leading-[23px] flex gap-x-3 ">
              <div className="rounded-lg w-1 shrink-0 bg-primary" />
              <span>
                确认将通过任务中心-常规任务向选中的门店
                {(taskDetail?.taskPrincipalRoles || [])
                  .map((o: any) => o.roleName)
                  .join('，')}
                派发任务
              </span>
            </div>
            <div className="rounded-lg bg-[#FAFAFA] px-4 pt-2 pb-3 mx-4 mt-3 flex flex-col">
              <div className="break-all">
                <span className=" h-[18px] w-14 border rounded-sm border-black/20  text-xs leading-3 inline-flex mr-3 items-center justify-center text-[#5e5e5e]">
                  任务预览
                </span>
                <span className="text-sm leading-[22px] text-dark-14">
                  {infoMap[type as keyof typeof TypeMapEnum].title}
                </span>
              </div>
              <span className="text-13 leading-[21px] mt-[1px] text-grey">
                {infoMap[type as keyof typeof TypeMapEnum].desc}
              </span>
              <div className="mt-[5px]">
                <IconFont
                  type="icon-shijian"
                  className="
                      text-[#C7C7C7] mr-1
                      "
                />
                <span className="text-13 leading-[21px] text-[#9E9E9E]">
                  任务截止时间：
                  <span className="text-grey">
                    {taskDetail?.taskDeadlineInterval}天
                  </span>
                </span>
              </div>
              <div className="mt-2">
                <IconFont
                  type="icon-yonghu"
                  className="
                      text-[#C7C7C7] mr-1
                      "
                />
                <span className="text-13 leading-[21px] text-[#9E9E9E]">
                  处理人：
                  <span className="text-grey">
                    {(taskDetail?.taskPrincipalRoles || [])
                      .map((o: any) => o.roleName)
                      .join('、')}
                  </span>
                </span>
              </div>
              <div className="mt-2">
                <IconFont type="icon-yonghu" className="text-[#C7C7C7] mr-1" />
                <span className="text-13 leading-[21px] text-[#9E9E9E]">
                  抄送人：
                  <span className="text-grey">
                    {(taskDetail?.taskRecipientRoles || [])
                      .map((o: any) => o.roleName)
                      .join('、')}
                  </span>
                </span>
              </div>
              <div className="mt-2">
                <IconFont
                  type="icon-wenti"
                  className="
                      text-[#C7C7C7] mr-1
                      "
                />
                <span className="text-13 leading-[21px] text-[#9E9E9E]">
                  任务类型：
                  <span className="text-grey">
                    {taskDetail?.taskType?.taskType}
                  </span>
                </span>
              </div>
              <div className="mt-2">
                <IconFont
                  type="icon-xinxi"
                  className="
                      text-[#C7C7C7] mr-1
                      "
                />
                <span className="text-13 leading-[21px] text-[#9E9E9E]">
                  SOP：
                  <span className="text-grey">
                    {(taskDetail?.sopInfoList || [])
                      .map((o: any) => o.name)
                      .join(',')}
                  </span>
                </span>
              </div>
              <div className="mt-2">
                <IconFont
                  type="icon-shezhi-shezhi-tianchong"
                  className="
                      text-[#C7C7C7] mr-1
                      "
                />
                <span className="text-13 leading-[21px] text-[#9E9E9E]">
                  任务验收设置：
                  <span className="text-grey">开启</span>
                </span>
              </div>
              <div className="mt-2 flex items-start">
                <IconFont
                  type="icon-fujianziliao"
                  className="
                      text-[#C7C7C7] mr-1 mt-[3px]
                      "
                />
                <div className="flex flex-col">
                  <span className="text-13 leading-[21px] text-[#9E9E9E]">
                    更新进度附件上传设置：
                    <span className="text-grey">选填</span>
                  </span>
                  <span className="text-13 leading-[21px] text-[#9E9E9E]">
                    完成任务附件上传设置：
                    <span className="text-grey">选填</span>
                  </span>
                </div>
              </div>
            </div>
          </div>
        </IPopup>
        <IPopup
          visible={sendVisible}
          title="确认派发任务"
          onClose={() => {
            setSendVisible(false);
          }}
        >
          <div className="flex flex-col gap-y-[21px] px-5 pb-8">
            <div className="flex flex-col items-center pt-[21px] gap-y-[21px]">
              <img
                src={suc}
                className="object-cover w-[126.5px] h-[105.7px]"
                alt=""
              />
              <span className="text-sm leading-[14px] text-dark-14">
                任务派发成功
              </span>
            </div>

            <div className="flex flex-col text-sm leading-[22px] text-[#5E5E5E]">
              <span>已成功派发{sucCount ?? 0}条任务；</span>
              <span> 您可前往任务中心-常规任务-我派发的任务查看</span>
              <span>
                任务处理进度。
                <span
                  onClick={() => {
                    navigate('/tasks/list/normalWork?queryType=created');
                  }}
                  className="text-primary underline underline-offset-4"
                >
                  去查看
                </span>
              </span>
            </div>
          </div>
        </IPopup>
        <IPopup
          visible={taskVisible}
          title={`已选派发任务门店（${selected.length}）`}
          titlePadding="px-4"
          onClose={() => {
            setTaskVisible(false);
          }}
          bodyStyle={{ height: '65vh' }}
        >
          <div className="flex flex-col divide-y-[1px] divide-black/[0.03]">
            {flatten(selectValForRender).map((item) => {
              return (
                <div
                  key={item.id}
                  className="flex justify-between items-center px-4 py-3 gap-x-10"
                >
                  {cardBuilder(type, item)}
                  <span
                    onClick={() => {
                      toggle(item.id);
                    }}
                    className="h-[19px] w-[34px] flex justify-center items-center rounded-[1px] text-13 leading-[13px] shrink-0 text-grey border border-[#979797] "
                  >
                    移除
                  </span>
                </div>
              );
            })}
            <InfiniteScroll
              loadMore={selectValFetchNext}
              hasMore={!selectValOver}
            />
          </div>
        </IPopup>
      </div>
    </Loading>
  );
}
