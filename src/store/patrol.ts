import { CheckItem, ChecklistData } from '@src/common/api.type';
import { DetailItem } from '@src/pages/patrol/api.type';
import { queryChecklistDetail } from '@src/pages/tasks/api';
import { UpdateChecklistItem } from '@src/pages/tasks/api.type';
import { makeAutoObservable, observable, runInAction, toJS } from 'mobx';

class PatrolStore {
  taskId: Nullable<number> = null;
  reportId: Nullable<number> = null;
  summaryText: Nullable<string> = '';
  shopSummary: Nullable<string> = '';
  cross: boolean = false;
  workSheetList: DetailItem[] = [];
  basicInfo: Omit<ChecklistData, 'data'> | {} = {};
  requiredList: (CheckItem & {
    patrolWorksheetId: number;
    patrolWorksheetName?: string;
  })[] = [];
  checkItem: CheckItem | null = null;
  isBack: boolean = false;
  curWatchTime: number = 0;
  videoLimitTime: number = 0;
  imageURLS: {
    url: string;
    type: 'IMG' | 'VIDEO';
  }[] = [];

  subType: Nullable<string> = null;
  signType: Nullable<string> = '';
  shopId: string = '';
  taskStatus: Nullable<string> = null; // 任务状态

  constructor() {
    makeAutoObservable(this, {
      taskId: observable,
      reportId: observable,
      summaryText: observable,
      shopSummary: observable,
      workSheetList: observable,
      basicInfo: observable,
      requiredList: observable,
      checkItem: observable,
      isBack: observable,
      curWatchTime: observable,
      videoLimitTime: observable,
      imageURLS: observable,
      subType: observable,
      signType: observable,
      shopId: observable,
      cross: observable,
      taskStatus: observable,
    });
  }
  setCheckItem = (data: CheckItem | null) => {
    this.checkItem = data;
  };
  getWorkSheets = async (id: number) => {
    const res = await queryChecklistDetail({ taskId: id });
    console.log(res, '=rs');
    runInAction(() => {
      const {
        appPatrolReport,
        taskId,
        summaryText,
        shopSummary,
        subType,
        signType,
        shopId,
        cross,
        taskStatus,
        ...rest
      } = res || {};
      this.taskId = taskId;
      this.cross = cross;
      this.workSheetList = appPatrolReport?.data || [];
      this.reportId = taskId;
      this.signType = signType;
      this.shopId = shopId;
      this.summaryText = summaryText;
      this.basicInfo = rest;
      this.shopSummary = shopSummary;
      this.subType = subType;
      this.taskStatus = taskStatus;
    });
    return res;
  };

  updateWorkSheet = (params: {
    payload: Omit<UpdateChecklistItem, 'patrolWorksheetId'>;
    patrolWorksheetId: number;
    reportId: number;
    relatedCheckTypeId: number;
    filledItemCount: number;
    allFilledItemCount: number;
    relatedFilledItemCount: number;
    imageURLS?: CheckItem['imageURLS'];
    copyUsers?: { id: number; name: string }[];
    // 当场整改
    checkListTypeId?: number;
    itemId?: number;
    localExplainTypePayload?: any;
    // 当场整改
  }) => {
    console.log(params, '=1111数据有变嘛');
    console.log(toJS(this.workSheetList), '=1111workSheetList');
    const newWorkSheetList = this.workSheetList;
    const {
      patrolWorksheetId,
      payload,
      relatedCheckTypeId,
      filledItemCount,
      allFilledItemCount,
      imageURLS,
      // 当场整改
      checkListTypeId,
      localExplainTypePayload,
      itemId,
      // 当场整改
    } = params || {};
    const workSheetIdx = newWorkSheetList.findIndex((v) => v.patrolWorksheetId === +patrolWorksheetId);
    const workSheet = newWorkSheetList[workSheetIdx];
    const { children: groups } = workSheet;
    const categoryIdx = groups?.findIndex(
      (v) => v.patrolWorksheetCategoryId === relatedCheckTypeId || v.patrolWorksheetCategoryId === checkListTypeId,
    );
    const itemIdx = groups?.[categoryIdx]?.children.findIndex(
      (v) => v?.itemId === payload?.patrolWorksheetItemId || v?.itemId === itemId,
    );
    let item = {
      ...groups?.[categoryIdx]?.children[itemIdx],
      ...payload,
      ...(localExplainTypePayload || {}),
      hasFillItem: true,
      imageURLS: imageURLS || [],
    };
    if (payload?.qualified) {
      item = {
        ...item,
        rectifyImageUrls: [],
        rectifyImages: [],
        rectifyReason: undefined,
      } as any;
    }
    workSheet.filledCount = allFilledItemCount;
    workSheet.children[categoryIdx].filledCount = filledItemCount;
    workSheet.children[categoryIdx].children[itemIdx] = item as any;
    newWorkSheetList[workSheetIdx] = { ...workSheet };
    console.log('🚀 ~ PatrolStore ~ newWorkSheetList:', toJS(newWorkSheetList));
    runInAction(() => {
      this.workSheetList = [...newWorkSheetList];
    });
  };
}
export default new PatrolStore();
