import {
  getPatrolUnqualified,
  getUnqualified,
  queryDiagnosticDataIfnfo,
  queryDiagnosticGroupRank,
  queryDiagnosticSummary,
  queryPatrolDataChart,
  queryPatrolDataIfnfo,
  queryPatrolDataView,
  queryPatrolGroupRank,
  querySelfReportCompleted,
  querySelfReportDetailList,
  querySelfReportGroupRank,
  querySelfReportView,
} from '@src/pages/dataViewBoard/api';
import { checkDataParams } from '@src/pages/dataViewBoard/api.type';
import {
  getTacticsPatrolUnqualified,
  getTacticsUnqualified,
  queryTacticsSelfReportCompleted,
  queryTacticsSelfReportDetailList,
  queryTacticsSelfReportGroupRank,
  queryTacticsSelfReportView,
} from '@src/pages/dataViewBoard/tactics/api';
import { formatDateToUTC } from '@src/utils/utils';
import dayjs from 'dayjs';
import { orderBy } from 'lodash';
import { makeObservable, observable } from 'mobx';

class DataBoardStore {
  searchObj: { [key: string]: any } = {};

  // 自检看板数据
  selfDataChartView = {
    summaryData: [], // 汇总数据
    viewData: [], // 图表数据
    loading: false,
    run: (searchParmas: checkDataParams) => this.getSelfDataChartView(searchParmas),
  };
  // 自检子组织情况数据
  SelfOrgsSituation = {
    viewData: [],
    loading: false,
    run: (searchParmas: checkDataParams) => this.getSelfOrgsSituationView(searchParmas),
  };
  // 自检不合格检查项
  selfUnqualifiedItem = {
    data: [] as any[],
    loading: false,
    run: (searchParmas: checkDataParams) => this.getSelfUnqualifiedItem(searchParmas),
  };
  // 自检不合格检查表
  selfUnqualifiedSheet = {
    data: [] as any[],
    loading: false,
    run: (searchParmas: checkDataParams) => this.getSelfUnqualifiedSheet(searchParmas),
  };
  patrolChartView = {
    // 巡检看板数据
    viewData: [], // 图表数据
    summaryData: [], // 统计数据
    loading: false,
    run: (searchParmas: checkDataParams) => {
      this.getPatrolChartView(searchParmas);
    },
  };
  diagnosisChartView = {
    summaryData: [], // 统计数据
    loading: false,
    run: (searchParmas: checkDataParams) => {
      this.getDiagnosisChartView(searchParmas);
    },
  };
  // 巡检子组织情况数据
  patrolOrgsSituation = {
    viewData: [],
    loading: false,
    run: (searchParmas: checkDataParams) => this.getPatrolOrgsSituation(searchParmas),
  };
  diagnosticOrgsSituation = {
    viewData: [],
    loading: false,
    run: (searchParmas: checkDataParams) => this.getDiagnosisSituation(searchParmas),
  };
  // 巡检不合格检查项
  patrolUnqualifiedItem = {
    data: [] as any[],
    loading: false,
    run: (searchParmas: checkDataParams) => this.getPatrolUnqualifiedItem(searchParmas),
  };
  // 策略-巡检不合格情况
  patrolUnqualifiedSituation = {
    viewData: [] as any[],
    loading: false,
    run: (searchParams: checkDataParams) => this.getTacticsPatrolUnqualifiedSituation(searchParams),
  };

  getSelfReportGroupRankLoading = false;
  constructor() {
    makeObservable(this, {
      searchObj: observable,
      selfDataChartView: observable,
      patrolChartView: observable,
      diagnosisChartView: observable,
      SelfOrgsSituation: observable,
      patrolOrgsSituation: observable,
      diagnosticOrgsSituation: observable,
      selfUnqualifiedItem: observable,
      patrolUnqualifiedItem: observable,
      patrolUnqualifiedSituation: observable,
      getSelfReportGroupRankLoading: observable,
    });
  }
  setSearchObj = (obj: any) => {
    this.searchObj = obj;
  };
  getSelfDataChartView = async (searchParmas: any) => {
    this.selfDataChartView.loading = true;
    const { beginDate: _b, endDate: _e, taskIds: _t, isTactics = false, ...rest } = searchParmas;

    const getSelfReportCompleted = isTactics ? queryTacticsSelfReportCompleted : querySelfReportCompleted;
    const getSelfReportView = isTactics ? queryTacticsSelfReportView : querySelfReportView;

    try {
      const data = await Promise.all([
        getSelfReportCompleted({
          ...rest,
          startDate: searchParmas.beginDate,
          endDate: searchParmas.endDate,
          planId: searchParmas.planId,
        }).then((res) => {
          this.selfDataChartView.summaryData = res;
        }),
        getSelfReportView({
          ...rest,
          startDate: searchParmas.beginDate,
          endDate: searchParmas.endDate,
          planId: searchParmas.planId,
        }).then((res) => {
          this.selfDataChartView.viewData = res;
        }),
      ]);
      this.selfDataChartView.loading = false;
      return data;
    } catch (e) {}
  };
  // 巡检数据看板预览
  getPatrolChartView = async (searchParmas: any) => {
    this.patrolChartView.loading = true;
    try {
      const data = await Promise.all([
        queryPatrolDataView({
          ...searchParmas,
          type: searchParmas?.shopIds ? 3 : 1, // 默认按组织1;门店3
          startDate: searchParmas.beginDate,
          endDate: searchParmas.endDate,
          patrolUserIds: searchParmas?.patrolUserIds ? [searchParmas?.patrolUserIds] : undefined,
        }).then((res) => {
          this.patrolChartView.summaryData = res;
        }),
        queryPatrolDataChart({
          ...searchParmas,
          type: searchParmas?.shopIds ? 3 : 1, // 默认按组织1;门店3
          startDate: searchParmas.beginDate,
          endDate: searchParmas.endDate,
          patrolUserIds: searchParmas?.patrolUserIds ? [searchParmas?.patrolUserIds] : undefined,
        }).then((res) => {
          this.patrolChartView.viewData = res;
        }),
      ]);
      this.patrolChartView.loading = false;
      return data;
    } catch (e) {
      this.patrolChartView.loading = false;
    }
  };

  // 诊断巡检看板
  getDiagnosisChartView = async (searchParmas: any) => {
    this.diagnosisChartView.loading = true;
    try {
      const res = await queryDiagnosticSummary({
        ...searchParmas,
        type: searchParmas?.shopIds ? 3 : 1, // 默认按组织1;门店3
        startDate: searchParmas.beginDate,
        endDate: searchParmas.endDate,
        patrolUserIds: searchParmas?.patrolUserIds ? [searchParmas?.patrolUserIds] : undefined,
      });
      this.diagnosisChartView.summaryData = res;
      this.diagnosisChartView.loading = false;
    } catch (error) {
      this.diagnosisChartView.loading = false;
    }
  };

  getSelfOrgsSituationView = async (searchParmas: any) => {
    this.SelfOrgsSituation.loading = true;
    const { beginDate: _b, endDate: _e, isTactics = false, ...rest } = searchParmas;

    const getSelfReportDetailList = isTactics ? queryTacticsSelfReportDetailList : querySelfReportDetailList;

    const data: any = await new Promise((resolve) => {
      getSelfReportDetailList({
        ...rest,
        type: searchParmas?.shopIds ? 3 : 1, // 默认按组织1;门店3
        systemType: 0,
        planTaskIds: searchParmas?.planId ? [searchParmas?.planId] : null,
        startDate: dayjs(searchParmas.beginDate).format('YYYY-MM-DD'),
        endDate: dayjs(searchParmas.endDate).format('YYYY-MM-DD'),
        ...(isTactics ? { planTaskIds: undefined } : {}),
      })
        .then((res) => {
          resolve(res);
        })
        .finally(() => {
          this.SelfOrgsSituation.loading = false;
        });
    });
    this.SelfOrgsSituation.viewData = data;

    return data;
  };
  getPatrolOrgsSituation = async (searchParmas: any) => {
    this.patrolOrgsSituation.loading = true;
    const data: any = await new Promise((resolve) => {
      queryPatrolDataIfnfo({
        ...searchParmas,
        type: searchParmas?.shopIds ? 3 : 1, // 默认按组织1;门店3
        planTaskIds: searchParmas?.planId ? [searchParmas?.planId] : null,
        startDate: searchParmas.beginDate,
        endDate: searchParmas.endDate,
        patrolUserIds: searchParmas?.patrolUserIds ? [searchParmas?.patrolUserIds] : undefined,
      })
        .then((res) => {
          resolve(res);
        })
        .finally(() => {
          this.patrolOrgsSituation.loading = false;
        });
    });
    this.patrolOrgsSituation.viewData = data;
    return data;
  };
  getDiagnosisSituation = async (searchParmas: any) => {
    this.diagnosticOrgsSituation.loading = true;
    const data: any = await new Promise((resolve) => {
      queryDiagnosticDataIfnfo({
        ...searchParmas,
        type: searchParmas?.shopIds ? 3 : 1, // 默认按组织1;门店3
        planTaskIds: searchParmas?.planId ? [searchParmas?.planId] : null,
        startDate: searchParmas.beginDate,
        endDate: searchParmas.endDate,
        patrolUserIds: searchParmas?.patrolUserIds ? [searchParmas?.patrolUserIds] : undefined,
      })
        .then((res) => {
          resolve(res);
        })
        .finally(() => {
          this.diagnosticOrgsSituation.loading = false;
        });
    });
    this.diagnosticOrgsSituation.viewData = data;
    return data;
  };
  // 自检不合格检查项
  getSelfUnqualifiedItem = async (searchParmas: any) => {
    this.selfUnqualifiedItem.loading = true;
    const { beginDate: _b, endDate: _e, ...rest } = searchParmas;

    const fetchUnqualified = searchParmas?.isTactics ? getTacticsUnqualified : getUnqualified;

    const data: any = await new Promise((resolve) => {
      fetchUnqualified({
        ...rest,
        startDate: dayjs(`${searchParmas?.beginDate} 00:00:00`),
        endDate: dayjs(`${searchParmas?.endDate} 23:59:59`),
      })
        .then((res) => {
          resolve(res);
        })
        .finally(() => {
          this.selfUnqualifiedItem.loading = false;
        });
    });
    const orderData = orderBy(data, 'noPassCount', 'desc');
    this.selfUnqualifiedItem.data = orderData;
    return orderData;
  };
  // 自检不合格表
  getSelfUnqualifiedSheet = async (searchParmas: any) => {
    this.selfUnqualifiedSheet.loading = true;

    const { beginDate: _b, endDate: _e, isTactics = false, ...rest } = searchParmas;

    const fetchUnqualified = isTactics ? getTacticsUnqualified : getUnqualified;

    const data: any = await new Promise((resolve) => {
      fetchUnqualified({
        ...rest,
        startDate: dayjs(`${searchParmas?.beginDate} 00:00:00`),
        endDate: dayjs(`${searchParmas?.endDate} 23:59:59`),
      })
        .then((res) => {
          resolve(res);
        })
        .finally(() => {
          this.selfUnqualifiedSheet.loading = false;
        });
    });
    const orderData = orderBy(data, 'noPassCount', 'desc');
    this.selfUnqualifiedSheet.data = orderData;
    return orderData;
  };
  // 巡检不合格检查项
  getPatrolUnqualifiedItem = async (searchParmas: any) => {
    this.patrolUnqualifiedItem.loading = true;

    const data: any = await new Promise((resolve) => {
      getPatrolUnqualified({
        ...searchParmas,
        startDate: formatDateToUTC(dayjs(searchParmas?.beginDate).startOf('day')),
        endDate: formatDateToUTC(dayjs(searchParmas?.endDate).endOf('day')),
        patrolUserIds: searchParmas?.patrolUserIds ? [searchParmas?.patrolUserIds] : undefined,
        workSheetId: undefined,
        workSheetIds: searchParmas?.workSheetId ? [searchParmas?.workSheetId] : undefined,
      })
        .then((res) => {
          resolve(res);
        })
        .finally(() => {
          this.patrolUnqualifiedItem.loading = false;
        });
    });
    const orderData = orderBy(data, 'noPassRatio', 'desc');
    this.patrolUnqualifiedItem.data = orderData;
    return orderData;
  };

  // 获取巡检不合格情况
  getTacticsPatrolUnqualifiedSituation = async (searchParmas: any) => {
    this.patrolUnqualifiedSituation.loading = true;

    const data = await new Promise<any>((resolve) => {
      const { beginDate, endDate, ...rest } = searchParmas;
      getTacticsPatrolUnqualified({
        startDate: formatDateToUTC(dayjs(beginDate).startOf('day')),
        endDate: formatDateToUTC(dayjs(endDate).endOf('day')),
        ...rest,
      })
        .then((res) => {
          resolve(res);
        })
        .finally(() => {
          this.patrolUnqualifiedSituation.loading = false;
        });
    });

    this.patrolUnqualifiedSituation.viewData = data;
    return data;
  };

  // 自检子组织折线趋势数据
  getSelfReportGroupRank = async (
    groupId?: any,
    params?: {
      /** 是否为策略 */
      isTactics?: boolean;
    },
  ) => {
    this.getSelfReportGroupRankLoading = true;

    const getSelfReportGroupRank = params?.isTactics ? queryTacticsSelfReportGroupRank : querySelfReportGroupRank;

    const data: any = await new Promise((resolve) => {
      const { beginDate: _b, endDate: _e, ...rest } = this.searchObj;
      getSelfReportGroupRank({
        ...rest,
        planTaskIds: this.searchObj?.planId ? [this.searchObj?.planId] : null,
        startDate: this.searchObj?.beginDate,
        endDate: this.searchObj?.endDate,
        groupId: groupId ? groupId : this.searchObj?.groupId,
      })
        .then((res) => {
          resolve(res);
        })
        .finally(() => {
          this.getSelfReportGroupRankLoading = false;
        });
    });
    return data;
  };
  // 巡检子组织折线趋势数据
  getPatrolGroupRank = async (groupId?: any) => {
    const data: any = await new Promise((resolve) => {
      const { beginDate: _b, endDate: _e, ...rest } = this.searchObj;

      queryPatrolGroupRank({
        ...rest,
        planTaskIds: this.searchObj?.planId ? [this.searchObj?.planId] : null,
        startDate: this.searchObj?.beginDate,
        endDate: this.searchObj?.endDate,
        lastStartDate: this.searchObj?.beginDate,
        lastEndDate: this.searchObj?.endDate,
        groupId: groupId ? groupId : this.searchObj?.groupId,
        type: 1,
        patrolUserIds: this.searchObj?.patrolUserIds ? [this.searchObj?.patrolUserIds] : undefined,
      })
        .then((res) => {
          resolve(res);
        })
        .finally(() => {});
    });
    return data;
  };
  getDiagnosisGroupRank = async (groupId?: any) => {
    const data: any = await new Promise((resolve) => {
      const { beginDate: _b, endDate: _e, ...rest } = this.searchObj;

      queryDiagnosticGroupRank({
        ...rest,
        planTaskIds: this.searchObj?.planId ? [this.searchObj?.planId] : null,
        startDate: this.searchObj?.beginDate,
        endDate: this.searchObj?.endDate,
        lastStartDate: this.searchObj?.beginDate,
        lastEndDate: this.searchObj?.endDate,
        groupId: groupId ? groupId : this.searchObj?.groupId,
        type: 1,
        patrolUserIds: this.searchObj?.patrolUserIds ? [this.searchObj?.patrolUserIds] : undefined,
      })
        .then((res) => {
          resolve(res);
        })
        .finally(() => {});
    });
    return data;
  };
}
export default new DataBoardStore();
