import { queryConfiglist } from '@src/pages/tasks/api';
import { makeObservable, observable } from 'mobx';

class GlobalStore {
  cachedConfig: any;
  constructor() {
    makeObservable(this, {
      cachedConfig: observable,
    });
  }
  getCorporationConfig = async () => {
    if (this.cachedConfig) {
      return this.cachedConfig;
    }
    let res = await queryConfiglist();

    return (this.cachedConfig = res?.reduce(
      (acc: { [x: string]: any }, cur: { key: string | number; value: any }) => {
        acc[cur.key] = cur.value;
        return acc;
      },
      {},
    ));
  };
}
export default new GlobalStore();
