import { makeObservable, observable } from 'mobx';

class ChecklistSelectStore {
  fromUrl: string = ''; //来源页面
  checklists: any[] = [];
  constructor() {
    makeObservable(this, {
      fromUrl: observable,
      checklists: observable,
    });
  }

  setFromUrl(url: string) {
    this.fromUrl = url;
  }
  setChecklists(checklists: any[]) {
    this.checklists = checklists;
  }
}

const checklistSelectStore = new ChecklistSelectStore();
export default checklistSelectStore;
