import { ShopPromotionIntentDetail } from '@src/pages/flow/types';
import { makeObservable, observable } from 'mobx';

interface IShopPromotionIntentListItem {
  id: number;
  brandId: number;
  date: string;
  shopId: string;
  shopName: string;
  platform: number;
  platformName: string;
  shopIdPlat: string;
  areaFlag?: boolean;
  amtTransPreM1: number;
  rateTraffic: number;
  amtOrderTransPreM1: number;
  realAmtOrderTransPreM1: number;
  transPreM1: number;
  joinFlag: number;
  status: number;
  sendFlag: number;
  tryTime: number;
  batchId: number;
  sourceId: number;
  balance: number;
  statusDesc: string;
  shopNamePlat: string;
  pushPersonName?: string;
  pushPersonPhone?: string;
  amtTransPreM1Min: number;
  amtTransPreM1Max: number;
  balanceLess: boolean;
}

class ShopStore {
  shopPromotionIntentList: IShopPromotionIntentListItem[] = []; // 门店列表
  selectedShopIds: string[] = []; // 选中的门店id列表

  constructor() {
    makeObservable(this, {
      shopPromotionIntentList: observable,
    });
  }

  setShopPromotionIntentList(data: any) {
    this.shopPromotionIntentList = data;
  }
  /** 根据intentId 映射 */
  get shopFlowMap(): Record<number, ShopPromotionIntentDetail> {
    const map: any = {};
    this.shopPromotionIntentList.forEach((item) => {
      map[item.id] = item;
    });
    return map;
  }

  /** 根据shopId 映射 */
  get shopPromotionMap(): Record<string, ShopPromotionIntentDetail> {
    const map: any = {};
    this.shopPromotionIntentList.forEach((item) => {
      map[item.shopId] = item;
    });
    return map;
  }

  getSelectShops = () => {
    const data = this.selectedShopIds.map((id) => {
      return this.shopPromotionMap[id];
    });
    return data;
  };

  setSelectedShopIds = (shopId: string[]) => {
    this.selectedShopIds = shopId;
  };
}

export default new ShopStore();
