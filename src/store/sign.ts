import { GeoLocation, patrolTaskListItem, QueryTaskListItem } from '@src/pages/tasks/api.type';
import { getDistance } from '@src/utils/utils';
import { makeObservable, observable } from 'mobx';

class SignStore {
  locating = false;
  signDistance = 0;
  shopInfo: patrolTaskListItem = {
    shopId: 0,
    shopName: '',
    shopAddress: '',
    shopPhone: '',
    shopManagerName: '',
    shopLat: 0,
    shopLog: 0,
    taskId: 0,
    reportId: 0,
    shopNo: '',
    taskDate: '',
    createByName: '',
    createByAvatar: '',
    shopManagerId: 0,
    status: 0,
    statusDesc: '',
    taskType: 0,
    createTime: '',
    signed: 0,
    shopType: 'JOIN',
    subType: 0,
    signType: 'SYSTEM',
    cross: false,
  };
  shopLocation: GeoLocation = {
    longitude: 0,
    latitude: 0,
    address: '',
    title: '',
  };
  userLocation: GeoLocation = {
    longitude: 0,
    latitude: 0,
    address: '',
    title: '',
  };
  edited: boolean = false;
  queryTaskShopInfo: QueryTaskListItem | undefined;

  constructor() {
    makeObservable(this, {
      locating: observable,
      signDistance: observable,
      shopInfo: observable,
      shopLocation: observable,
      userLocation: observable,
      edited: observable,
      queryTaskShopInfo: observable,
    });
  }
  get distance() {
    return getDistance(this.shopLocation, this.userLocation);
  }
  get out() {
    return this.distance > this.signDistance;
  }
  setShopLocation = async (location: GeoLocation) => {
    this.shopLocation = location;
  };
  setUserLocation = async (location: GeoLocation) => {
    this.userLocation = location;
  };
  initial = async ({ shopInfo, shopLocation, signDistance }: any = {}) => {
    this.shopInfo = shopInfo;
    this.shopLocation = shopLocation;
    this.signDistance = +signDistance || 0;
    this.edited = false;
  };
  setTaskShopInfo = async (item: QueryTaskListItem | any) => {
    this.queryTaskShopInfo = item;
  };
}

export default new SignStore();
