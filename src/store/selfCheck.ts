import { formatWheets, getSelfReportReview } from '@src/pages/selfCheckComment/api';
import { SelfReportDetail, sheetsItem } from '@src/pages/selfCheckComment/api.type';
import _ from 'lodash';
import { makeObservable, observable, runInAction, toJS } from 'mobx';

class SelfCheckStore {
  previewInfos: SelfReportDetail = {};
  selfWorkSheetList: sheetsItem[] = [];
  curCheckSheetInfo: sheetsItem = {};
  // 可申诉项
  hasAppeal: boolean = false;

  constructor() {
    makeObservable(this, {
      previewInfos: observable,
      selfWorkSheetList: observable,
      curCheckSheetInfo: observable,
      hasAppeal: observable,
    });
  }

  getPreviewInfos = async (data: {
    taskId: string;
    // notFilledItemHandleType?: 'SET_FULL_SCORE' | 'SET_NOT_APPLY';
  }) => {
    try {
      const res = await getSelfReportReview(data?.taskId);
      runInAction(() => {
        this.previewInfos = res;
        this.selfWorkSheetList = formatWheets(res?.worksheets);
      });
    } catch (error) {
    } finally {
    }
  };
  setPreviewInfos = (value: SelfReportDetail) => {
    this.previewInfos = value;
    this.selfWorkSheetList = value?.worksheets;
  };
  setCurCheckSheet = (SheetInfo: sheetsItem) => {
    this.curCheckSheetInfo = SheetInfo;
  };
  updateSelfWorkSheet = (params: any) => {
    console.log(params, '=返回的更新params');
    const { taskWorksheetId, taskCategoryId, payload, allFilledItemCount, filledItemCount, relatedFilledItemCount } =
      params || {};
    const updateSheet = this.selfWorkSheetList;
    console.log(toJS(updateSheet), '=updateSheet');

    const workSheetIdx = updateSheet?.findIndex((v) => v.taskWorksheetId === +taskWorksheetId);
    const workSheet = updateSheet[workSheetIdx];
    const { categories: groups } = workSheet;
    const categoryIdx = groups?.findIndex((v) => v.taskCategoryId === +taskCategoryId);
    const relatedIdx = groups?.findIndex((v) => v.taskCategoryId === 0);
    const itemIdx = groups?.[categoryIdx]?.items.findIndex((v) => v.taskWorksheetItemId === payload?.itemId);
    const item = { ...groups?.[categoryIdx]?.items[itemIdx], ...payload };
    workSheet.filledCount = allFilledItemCount;
    workSheet.categories[categoryIdx].filledCount = filledItemCount;
    workSheet.categories[categoryIdx].items[itemIdx] = item;
    if (relatedIdx !== -1 && _.isNumber(relatedFilledItemCount)) {
      const idx = workSheet.categories[relatedIdx].items.findIndex((v) => v.taskWorksheetItemId === payload.itemId);
      workSheet.categories[relatedIdx].filledCount = relatedFilledItemCount;
      workSheet.categories[relatedIdx].items[idx] = item;
    }
    updateSheet[workSheetIdx] = { ...workSheet };
    this.selfWorkSheetList = [...updateSheet];
  };

  secondUpdateSelfWorkSheet = (params: any) => {
    console.log(params, '=返回的更新第二点评人params');
    const { taskWorksheetId, taskCategoryId, payload } = params || {};
    const updateSheet = this.selfWorkSheetList;

    const workSheetIdx = updateSheet?.findIndex((v) => v.taskWorksheetId === +taskWorksheetId);
    const workSheet = updateSheet[workSheetIdx];
    const { categories: groups } = workSheet;
    const categoryIdx = groups?.findIndex((v) => v.taskCategoryId === +taskCategoryId);
    const itemIdx = groups?.[categoryIdx]?.items.findIndex((v) => v.taskWorksheetItemId === payload?.itemId);
    const item = { ...groups?.[categoryIdx]?.items[itemIdx], ...payload };
    workSheet.categories[categoryIdx].items[itemIdx] = item;
    updateSheet[workSheetIdx] = { ...workSheet };
    this.selfWorkSheetList = [...updateSheet];
  };

  setHasAppeal = (value: boolean) => {
    this.hasAppeal = value;
  };
}

export default new SelfCheckStore();
