import { DetailItem, ReportDetailInfo, type TReportSecondReviewInfo } from '@src/pages/patrol/api.type';
import { makeObservable, observable, runInAction } from 'mobx';

class PatrolCheckStore {
  reportDetailInfos: ReportDetailInfo = {};
  patrolSheetList: DetailItem[] = [];
  curPatrolSheetInfo: DetailItem = {};

  constructor() {
    makeObservable(this, {
      reportDetailInfos: observable,
      patrolSheetList: observable,
      curPatrolSheetInfo: observable,
    });
  }
  setReportDetailInfos = (data: ReportDetailInfo) => {
    this.reportDetailInfos = data;
  };
  setReportSheetList = (data: DetailItem[]) => {
    this.patrolSheetList = data;
  };
  setCurPatrolSheet = (SheetInfo: DetailItem) => {
    this.curPatrolSheetInfo = SheetInfo;
  };
  updatePatrolReviewSheet = (updata: any) => {
    const {
      patrolWorksheetId,
      patrolWorksheetCategoryId,
      itemId,
      content,
      qualified,
      // 是否为第二点评
      isSecondReview,
      secondContent,
      secondQualified,
    } = updata;

    const newWorkSheetList = this.patrolSheetList;
    const workSheetIdx = newWorkSheetList.findIndex((v) => v?.patrolWorksheetId === patrolWorksheetId);
    const workSheet = newWorkSheetList[workSheetIdx];
    const { children: groups } = workSheet;
    const categoryIdx = groups?.findIndex((v) => v.patrolWorksheetCategoryId === patrolWorksheetCategoryId);
    const itemIdx = groups?.[categoryIdx]?.children.findIndex((v) => v.itemId === itemId);
    const itemData = groups?.[categoryIdx]?.children[itemIdx];

    const item = {
      ...itemData,
      reportReviewInfo: {
        ...itemData?.reportReviewInfo,
        content: isSecondReview ? itemData?.reportReviewInfo?.content : content,
        qualified: isSecondReview ? itemData?.reportReviewInfo?.qualified : qualified,
      },
      reportSecondReviewInfo: isSecondReview
        ? ({
            ...itemData?.reportSecondReviewInfo,
            content: secondContent,
            qualified: secondQualified,
          } as TReportSecondReviewInfo)
        : undefined,
    };

    workSheet.children[categoryIdx].children[itemIdx] = item;
    newWorkSheetList[workSheetIdx] = { ...workSheet };
    runInAction(() => {
      this.patrolSheetList = [...newWorkSheetList];
    });
  };
}

export default new PatrolCheckStore();
