import { getPermissions, getRoleIdList, getUser } from '@src/common/api';
import { yytUserInfo } from '@src/common/api.type';
import { getIsSuper } from '@src/pages/selfCheckComment/api';
import { roleTypeIsManage } from '@src/utils/tokenUtils';
import { makeObservable, observable } from 'mobx';
class UserStore {
  permissions: string[] = [];
  permissionsMap: Map<string, string> = new Map();
  userInfo: { phone?: string; userId?: number; nickName?: string } = {};
  isSuperManger: boolean = false;
  yytUserInfo: yytUserInfo = {}; // 营运通用户信息
  // 标志完成用户权限的获取
  getUserAuthAlready: boolean = false;
  webEntry: string | null = null;
  roleIdList: number[] = [];
  constructor() {
    makeObservable(this, {
      permissions: observable,
      permissionsMap: observable,
      userInfo: observable,
      getUserAuthAlready: observable,
      webEntry: observable,
      roleIdList: observable,
      isSuperManger: observable,
    });
  }

  getPermissionsMap = () => {
    return this.permissionsMap;
  };

  setPermissionsMap = (authObj: Map<string, string>) => {
    this.permissionsMap = authObj;
  };

  getUserAuth = async () => {
    const permissions = await getPermissions();
    const _permissionMap = new Map();
    permissions.forEach((element) => {
      _permissionMap.set(element, element);
    });
    this.setPermissionsMap(_permissionMap);

    this.getUserAuthAlready = true;
  };
  getRoleIdList = async () => {
    const data = await getRoleIdList();
    const list = data?.roleInfoList?.map((v: any) => {
      return v?.roleId;
    });
    this.roleIdList = list;
  };
  getUser = async () => {
    const userInfo = await getUser();
    this.userInfo = { phone: userInfo?.mobile, userId: userInfo?.shUserId, nickName: userInfo?.nickName };
  };
  getUserInfo = () => {
    return this.userInfo;
  };
  getWebEntry = () => {
    return this.webEntry ?? localStorage.getItem('webEntry');
  };
  getIsSuperManger = async () => {
    const isDudao = roleTypeIsManage();
    const isSuper = isDudao ? await getIsSuper() : { data: false };
    return (this.isSuperManger = isSuper?.data);
  };
}

const userStore = new UserStore();
export default userStore;
