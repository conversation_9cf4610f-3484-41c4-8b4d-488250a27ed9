import { makeObservable, observable } from 'mobx';

class InspectStore {
  inspectors: any[] = []; //巡检人
  inspectorMap: any[] = []; //巡检人关系映射
  organizations: any[] = []; //巡检组织
  organizationMap: any = {}; //巡检组织id映射

  selectInspectorValue: any; //巡检人选择值
  inspectorCallback: any; //巡检人回调
  selectOrganizationIds: string[] = []; //巡检组织选择值-关联从属关系
  organizationCallback: any; //巡检组织回调
  selectChildIds: number[] = []; //巡检组织子级id集  用来显示已选
  selectParentIds: number[] = []; //巡检组织子级id集  用来显示已选
  selectOrgIds: string[] = []; //巡检组织选择值-真正选择值

  constructor() {
    makeObservable(this, {
      inspectors: observable,
      organizations: observable,
      organizationMap: observable,
      selectOrganizationIds: observable,

      selectInspectorValue: observable,
      inspectorCallback: observable,
      organizationCallback: observable,
      selectChildIds: observable,
      selectParentIds: observable,
    });
  }

  setInspector({ inspectors }: any) {
    this.inspectors = inspectors; //巡检人
    // this.inspectorMap = inspectorMap; //巡检人关系映射
  }
  setOrganizations({ organizations }: any) {
    this.organizations = organizations;
  }
  setInspectOrg({ organizationMap }: any) {
    this.organizationMap = organizationMap; //巡检组织id映射
  }

  setSelectInspectorValue(value: any) {
    this.selectInspectorValue = value;
  }
  setInspectorCallback(callback: any) {
    this.inspectorCallback = callback;
  }

  setSelectOrganizationIds(values: string[]) {
    this.selectOrganizationIds = values;
  }
  setOrganizationCallback(callback: any) {
    this.organizationCallback = callback;
  }
  setSelectChildIds(values: any) {
    this.selectChildIds = values;
  }
  setSelectParentIds(values: any) {
    this.selectParentIds = values;
  }

  setSelectOrgIds(values: string[]) {
    this.selectOrgIds = values;
  }
}
const inspectStore = new InspectStore();

export default inspectStore;
