import path from 'path';
import react from '@vitejs/plugin-react';
// import eslint from 'vite-plugin-eslint';
import dotenv from 'dotenv';
import { defineConfig } from 'vite';

dotenv.config();

export default defineConfig({
  mode: 'development',
  server: {
    open: true,
    port: 5174,
    host: true,
    fs: {
      strict: true,
    },
    proxy: {
      '/api': {
        target: 'https://speed-test.tastientech.com/',
        changeOrigin: true,
      },
      '/om-api': {
        target: 'https://gzt-test.tastientech.com/',
        changeOrigin: true,
      },
      '/tm-api': {
        target: 'https://gzt-test.tastientech.com/',
        changeOrigin: true,
      },
      '/rm-api': {
        target: 'https://gzt-test.tastientech.com/',
        changeOrigin: true,
      },
      '/cc-api': {
        target: 'https://tastien-test.tastientech.com/',
        // target: 'http://***********:8099',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/cc-api/, '/api'),
      },
      '/yy-api': {
        target: 'https://yy-test.tastientech.com/',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/yy-api/, '/api'),
      },
      '/weboffice-admin-api': {
        target: 'http://weboffice-admin.tastientech.com/',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/weboffice-admin-api/, '/api'),
      },
    },
  },
  plugins: [
    react(),
    // eslint({
    //   cache: false,
    //   include: ['src/**/*.ts', 'src/**/*.tsx', 'src/**/*.vue'],
    //   exclude: ['node_modules'],
    // }),
  ],
  resolve: {
    alias: {
      '@src': path.resolve(__dirname, '../src'),
      '@components': path.resolve(__dirname, '../src/components'),
      '@assets': path.resolve(__dirname, '../src/assets'),
      '@flow': path.resolve(__dirname, '../src/flow'),
    },
    extensions: ['.tsx', '.ts', '.js'],
  },
});
