import path from 'path';
import react from '@vitejs/plugin-react';
import dotenv from 'dotenv';
import { visualizer } from 'rollup-plugin-visualizer';
import { defineConfig } from 'vite';
import viteCompression from 'vite-plugin-compression';

dotenv.config();

export default defineConfig(({ mode }) => {
  console.log('mode', mode);

  return {
    mode: 'production',
    plugins: [
      react(),
      viteCompression(),
      // @ts-ignore
      visualizer({
        open: false,
        gzipSize: true,
        brotliSize: true,
        filename: 'stats.html',
      }),
    ],
    resolve: {
      alias: {
        '@src': path.resolve(__dirname, '../src'),
        '@components': path.resolve(__dirname, '../src/components'),
        '@assets': path.resolve(__dirname, '../src/assets'),
        '@flow': path.resolve(__dirname, '../src/flow'),
      },
      extensions: ['.tsx', '.ts', '.js'],
    },
    // 打包配置
    build: {
      rollupOptions: {
        treeshake: true,
        output: {
          chunkFileNames: 'js/[name]-[hash].js',
          entryFileNames: 'js/[name]-[hash].js',
          assetFileNames: '[ext]/[name]-[hash].[ext]',
          manualChunks(id) {
            if (id.includes('node_modules')) {
              if (id.includes('echarts')) {
                return 'echarts';
              }
              if (id.includes('ali-oss')) {
                return 'ali-oss';
              }
              if (id.includes('vconsole')) {
                return 'vconsole';
              }
              return 'vendor';
            }
            if (id.includes('src/components')) {
              return 'components'; // Assign both modules to a common 'components' chunk
            }
          },
        },
      },
      minify: 'terser',
      // terserOptions: {
      //   //生产环境时移除console
      //   compress: {
      //     drop_console: true,
      //     drop_debugger: true,
      //   },
      // },
    },
    experimental: {
      renderBuiltUrl: (filename, { hostType }) => {
        if (hostType === 'html' && mode === 'gray') {
          return `${filename}?tag=gray`;
        }
        return { relative: true };
      },
    },
  };
});
