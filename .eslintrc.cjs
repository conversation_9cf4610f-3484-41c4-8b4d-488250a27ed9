module.exports = {
  root: true,
  plugins: ['unused-imports'],
  parser: '@typescript-eslint/parser',
  extends: ['@tastien/eslint-config-tastien/next'],
  rules: {
    '@next/next/no-img-element': 'off', // 禁止使用 <img> 元素
    'max-lines': 'off', // 文件最大行数限制
    'no-inline-comments': 'off', // 禁止内联注释
    'max-classes-per-file': 'off', // 每个文件中的类数限制
    'max-lines-per-function': 'off', 
    'no-restricted-globals':'warn',
    "@typescript-eslint/no-shadow": "off",
    "padding-line-between-statements": "off",
    // 未使用变量自动移除
    '@typescript-eslint/no-unused-vars': 'off',
    'unused-imports/no-unused-imports': 'error',
    'unused-imports/no-unused-vars': [
      'warn',
      {
        vars: 'all',
        varsIgnorePattern: '^_',
        args: 'after-used',
        argsIgnorePattern: '^_',
      },
    ],
  }
};
