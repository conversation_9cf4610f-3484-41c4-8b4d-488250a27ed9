{"name": "tatt-h5", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode development --config ./config/vite.config.dev.ts", "build": "vite build --mode production --config ./config/vite.config.prod.ts", "build:gray": "vite build --mode gray --config ./config/vite.config.prod.ts", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "prettier": "prettier . --write", "sync-flag": "ts-node --project tsconfig.script.json  ./scripts/sync-flag.ts", "prepare": "husky install"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@ant-design/icons": "^5.3.1", "@sentry/react": "^8.20.0", "@sentry/tracing": "^7.114.0", "@tanstack/react-query": "^5.28.9", "@tastien/rn-bridge": "1.0.14", "ahooks": "^3.7.10", "ali-oss": "^6.20.0", "antd": "^5.15.1", "antd-mobile": "^5.34.0", "antd-mobile-icons": "^0.3.0", "axios": "^1.6.7", "callapp-lib": "^3.5.3", "classnames": "^2.5.1", "clsx": "^2.1.0", "crypto-js": "^4.2.0", "dayjs": "^1.11.10", "echarts": "^5.5.0", "echarts-for-react": "^3.0.2", "lodash": "^4.17.21", "memoize-one": "^6.0.0", "mobile-detect": "^1.4.5", "mobx": "^6.12.0", "mobx-react": "^9.1.0", "moment": "^2.30.1", "nanoid": "^5.0.6", "numeral": "^2.0.6", "pinyin-pro": "^3.19.6", "prop-types": "^15.8.1", "qs": "^6.12.0", "ramda": "^0.29.1", "react": "^18.2.0", "react-activation": "^0.12.4", "react-dom": "^18.2.0", "react-router-dom": "^6.22.2", "react-window": "^1.8.11", "tailwind-merge": "^2.5.3", "vconsole": "^3.15.1", "xlsx": "^0.18.5"}, "devDependencies": {"@commitlint/cli": "^19.3.0", "@commitlint/config-conventional": "^19.2.2", "@tastien/eslint-config-tastien": "^0.0.10", "@types/ali-oss": "^6.16.11", "@types/crypto-js": "^4.2.2", "@types/lodash": "^4.14.202", "@types/node": "^20.11.30", "@types/numeral": "^2.0.5", "@types/qs": "^6.9.12", "@types/ramda": "^0.29.11", "@types/react": "^18.2.56", "@types/react-dom": "^18.2.19", "@types/react-window": "^1.8.8", "@typescript-eslint/parser": "^7.0.2", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.18", "dotenv": "^16.4.5", "eslint-plugin-unused-imports": "^4.0.0", "hash.js": "^1.1.7", "husky": "^8.0.3", "lint-staged": "^15.2.2", "postcss": "^8.4.35", "postcss-pxtorem": "^6.1.0", "prettier": "^3.2.5", "rollup-plugin-visualizer": "^5.12.0", "sass": "^1.71.1", "tailwindcss": "^3.4.1", "terser": "5.30.3", "ts-node": "^10.9.2", "typescript": "^5.2.2", "vite": "5.1.4", "vite-plugin-compression": "^0.5.1", "vite-plugin-html": "^3.2.2"}, "packageManager": "yarn@1.22.19", "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.json": ["prettier --write"], "*.{scss,less,html}": ["prettier --write"], "*.md": ["prettier --write"]}, "husky": {"hooks": {"pre-commit": "lint-staged", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}}