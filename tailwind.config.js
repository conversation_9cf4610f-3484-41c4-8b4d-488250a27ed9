/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: {
        primary: '#378BFF', //主题色
        'primary-1': 'rgba(55, 139, 255, 0.1)', //主题浅色
        dark: '#151516', // 一级正文/标题
        'dark-14': '#141414', //新的黑色
        grey: '#58595B', // 副标题
        'grey-2': '#C7C7C7', // 副标题
        light: '#9C9C9C', // 内容
        line: 'rgba(0, 0, 0, 0.03)', //border
        85: '#858585', //border
        B8: '#B8B8B8', //border
        DC: '#DCDCDC',
        FA: '#FAFAFA',
        '5E': '#5E5E5E',
        '9C': '#9C9C9C',
        F0: '#F0F0F0',
      },
      fontSize: {
        13: '13px',
        15: '15px',
        17: '17px',
        19: '19px',
        // 带行高的字体
        h12: ['12px', '20px'],
        h13: ['13px', '21px'],
        h14: ['14px', '22px'],
        h15: ['15px', '23px'],
        h16: ['16px', '24px'],
        h17: ['17px', '25px'],
      },
      borderRadius: {
        base: '4px',
      },
    },
  },
  plugins: [],
};
