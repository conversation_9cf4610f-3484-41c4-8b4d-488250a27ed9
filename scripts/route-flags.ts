import { createFlag } from './flags';

const ROUTE_FLAGS = {
  dataBoard: createFlag('dataBoard', '数据看板-公用', {
    boards: createFlag('dataBoard:boards', '作战小组数据看板', {
      audit: createFlag('dataBoard:boards:audit', '稽查看板', {
        shop: createFlag('dataBoard:boards:audit:shopBtn', '查看门店列表'),
        group: createFlag('dataBoard:boards:audit:groupBtn', '查看组织列表'),
        redLine: createFlag('dataBoard:boards:audit:data:redLine', '稽核“S项”占比', {
          hq: createFlag('dataBoard:boards:audit:data:redLine:hq', '总部食安稽核“S项”占比'),
          onLine: createFlag('dataBoard:boards:audit:data:redLine:onLine', '线上云巡检“S项”占比'),
          offLine: createFlag(
            'dataBoard:boards:audit:data:redLine:offLine',
            '战区食安巡检“S项”占比',
          ),
          other: createFlag('dataBoard:boards:audit:data:redLine:other', '其他巡检“S项”占比'),
        }),
        unPassPatrol: createFlag('dataBoard:boards:audit:data:unPassPatrol', '门店稽核通过率', {
          hq: createFlag('dataBoard:boards:audit:data:unPassPatrol:hq', '总部食安稽核通过率'),
          onLine: createFlag('dataBoard:boards:audit:data:unPassPatrol:onLine', '线上云巡检通过率'),
          offLine: createFlag(
            'dataBoard:boards:audit:data:unPassPatrol:offLine',
            '战区食安巡检通过率',
          ),
          other: createFlag('dataBoard:boards:audit:data:unPassPatrol:other', '其他巡检通过率'),
        }),
        unCompleteRectify: createFlag(
          'dataBoard:boards:audit:data:unCompleteRectify',
          '门店稽核整改完成率',
          {
            hq: createFlag(
              'dataBoard:boards:audit:data:unCompleteRectify:hq',
              '总部食安稽核整改完成率',
            ),
            onLine: createFlag(
              'dataBoard:boards:audit:data:unCompleteRectify:onLine',
              '线上云巡检整改完成率',
            ),
            offLine: createFlag(
              'dataBoard:boards:audit:data:unCompleteRectify:offLine',
              '战区食安整改完成率',
            ),
            other: createFlag(
              'dataBoard:boards:audit:data:unCompleteRectify:other',
              '其他巡检整改完成率',
            ),
          },
        ),
        unPassSelf: createFlag('dataBoard:boards:audit:data:unPassSelf', '自检任务合格率', {
          daily: createFlag(
            'dataBoard:boards:audit:data:unPassSelf:daily',
            '《全国每日效期检查》合格率',
          ),
          tank: createFlag(
            'dataBoard:boards:audit:data:unPassSelf:tank',
            '《全国隔油池清洁》合格率',
          ),
          coke: createFlag(
            'dataBoard:boards:audit:data:unPassSelf:coke',
            '《全国可乐机清洁》合格率',
          ),
          ice: createFlag('dataBoard:boards:audit:data:unPassSelf:ice', '《全国制冰机清洁》合格率'),
          oil: createFlag(
            'dataBoard:boards:audit:data:unPassSelf:oil',
            '《全国炸油品质管理》合格率',
          ),
          clean: createFlag(
            'dataBoard:boards:audit:data:unPassSelf:clean',
            '《全国月清洁任务》合格率',
          ),
        }),
        unCompleteSelf: createFlag('dataBoard:boards:audit:data:unCompleteSelf', '自检任务完成率', {
          daily: createFlag(
            'dataBoard:boards:audit:data:unCompleteSelf:daily',
            '《全国每日效期检查》完成率',
          ),
          tank: createFlag(
            'dataBoard:boards:audit:data:unCompleteSelf:tank',
            '《全国隔油池清洁》完成率',
          ),
          coke: createFlag(
            'dataBoard:boards:audit:data:unCompleteSelf:coke',
            '《全国可乐机清洁》完成率',
          ),
          ice: createFlag(
            'dataBoard:boards:audit:data:unCompleteSelf:ice',
            '《全国制冰机清洁》完成率',
          ),
          oil: createFlag(
            'dataBoard:boards:audit:data:unCompleteSelf:oil',
            '《全国炸油品质管理》完成率',
          ),
          clean: createFlag(
            'dataBoard:boards:audit:data:unCompleteSelf:clean',
            '《全国月清洁任务》完成率',
          ),
        }),
        // FIXME:运管项目1.0版本暂时去掉督导巡检完成率这个卡片
        // supervision: createFlag('dataBoard:boards:audit:data:supervision', '督导巡检完成率', {
        //   foodSafe: createFlag(
        //     'dataBoard:boards:audit:data:supervision:foodSafe',
        //     '《QSC&食安》巡检完成率',
        //   ),
        //   bySupervision: createFlag(
        //     'dataBoard:boards:audit:data:supervision:bySupervision',
        //     '《督导巡店》巡检完成率',
        //   ),
        // }),
      }),
      hr: createFlag('dataBoard:boards:hr', '人力看板', {
        shop: createFlag('dataBoard:boards:hr:shopBtn', '查看门店列表'),
        group: createFlag('dataBoard:boards:hr:groupBtn', '查看组织列表'),
        scheduling: createFlag('dataBoard:boards:hr:data:scheduling', '排班准确率'),
        organization: createFlag('dataBoard:boards:hr:data:organization', '编制达成率'),
        recruit: createFlag('dataBoard:boards:hr:data:recruit', '招聘达成率'),
        depart: createFlag('dataBoard:boards:hr:data:depart', '离职率'),
      }),
      achievement: createFlag('dataBoard:boards:achievement', '业绩看板', {
        shop: createFlag('dataBoard:boards:achievement:shopBtn', '查看门店列表'),
        group: createFlag('dataBoard:boards:achievement:groupBtn', '查看组织列表'),
        scheduling: createFlag('dataBoard:boards:achievement:data:estimatedRevenue', '预计收入', {
          discountAmount: createFlag(
            'dataBoard:boards:achievement:data:estimatedRevenue:discountAmount',
            '优惠金额',
          ),
          refundAmount: createFlag(
            'dataBoard:boards:achievement:data:estimatedRevenue:refundAmount',
            '退款金额',
          ),
          averageDailyIncome: createFlag(
            'dataBoard:boards:achievement:data:estimatedRevenue:averageDailyIncome',
            '日均收入',
          ),
          // otherExpenses: createFlag(
          //   'dataBoard:boards:achievement:data:estimatedRevenue:otherExpenses',
          //   '其他支出',
          // ),
          orderScene: createFlag(
            'dataBoard:boards:achievement:data:estimatedRevenue:orderScene',
            '订单场景',
          ),
          orderSceneAverageDailyIncome: createFlag(
            'dataBoard:boards:achievement:data:estimatedRevenue:orderSceneAverageDailyIncome',
            '各场景日均收入',
          ),
          orderSource: createFlag(
            'dataBoard:boards:achievement:data:estimatedRevenue:orderSource',
            '订单来源',
          ),
          orderPaymentMode: createFlag(
            'dataBoard:boards:achievement:data:estimatedRevenue:orderPaymentMode',
            '各支付方式预计收入',
          ),
          realTime: createFlag(
            'dataBoard:boards:achievement:data:estimatedRevenue:realTime',
            '门店实时预计收入',
          ),
          actualPaymentIncome: createFlag(
            'dataBoard:boards:achievement:data:estimatedRevenue:actualPaymentIncome',
            '实付收入分析',
          ),
          dailyActualPayment: createFlag(
            'dataBoard:boards:achievement:data:estimatedRevenue:dailyActualPayment',
            '日均实付收入分析',
          ),
        }),
        tc: createFlag('dataBoard:boards:achievement:data:tc', 'TC', {
          discount: createFlag('dataBoard:boards:achievement:data:tc:discount', '优惠订单'),
          refund: createFlag('dataBoard:boards:achievement:data:tc:refund', '退款订单'),
          averageDaily: createFlag('dataBoard:boards:achievement:data:tc:averageDaily', '日均单数'),
          orderScene: createFlag('dataBoard:boards:achievement:data:tc:orderScene', 'TC各订单占比'),
          orderSceneAverageDailyIncome: createFlag(
            'dataBoard:boards:achievement:data:tc:orderSceneAverageDailyIncome',
            '各场景日均订单数',
          ),
          orderSource: createFlag('dataBoard:boards:achievement:data:tc:orderSource', '订单来源'),
          orderPaymentMode: createFlag(
            'dataBoard:boards:achievement:data:tc:orderPaymentMode',
            '各支付方式订单量',
          ),
          realTime: createFlag('dataBoard:boards:achievement:data:realTime', '门店实时TC'),
          actualPaymentIncome: createFlag(
            'dataBoard:boards:achievement:data:tc:actualPaymentIncome',
            'TC分析',
          ),
          dailyActualPayment: createFlag(
            'dataBoard:boards:achievement:data:tc:dailyActualPayment',
            '日均TC分析',
          ),
        }),
        ac: createFlag('dataBoard:boards:achievement:data:ac', 'AC', {
          // dailyAC: createFlag('dataBoard:boards:achievement:data:ac:dailyAc', '日均AC'),

          orderScene: createFlag('dataBoard:boards:achievement:data:ac:orderScene', '各场景AC'),
          orderSource: createFlag(
            'dataBoard:boards:achievement:data:ac:orderSource',
            '各订单来源AC',
          ),
          orderPaymentMode: createFlag(
            'dataBoard:boards:achievement:data:ac:orderPaymentMode',
            '各支付方式AC',
          ),
          actualPaymentIncome: createFlag(
            'dataBoard:boards:achievement:data:ac:actualPaymentIncome',
            'AC分析',
          ),
        }),
        turnover: createFlag('dataBoard:boards:achievement:data:turnover', '营业额预估准确率'),
        materialPurchase: createFlag(
          'dataBoard:boards:achievement:data:materialPurchase',
          '物料采购差异率',
        ),
      }),
      satisfaction: createFlag('dataBoard:boards:satisfaction', '满意度数据看板', {
        shop: createFlag('dataBoard:boards:satisfaction:shopBtn', '查看门店列表'),
        group: createFlag('dataBoard:boards:satisfaction:groupBtn', '查看组织列表'),
        star: createFlag('dataBoard:boards:satisfaction:data:star', '体验星级得分'),
        inout: createFlag('dataBoard:boards:satisfaction:data:inout', '全渠道万次投诉率'),
        reply: createFlag('dataBoard:boards:satisfaction:data:reply', '差评回复率'),
        avgMeal: createFlag('dataBoard:boards:satisfaction:data:avgMeal', '平均出餐时长'),
        timeoutMeal: createFlag('dataBoard:boards:satisfaction:data:timeoutMeal', '出餐时长超时率'),
      }),
      market: createFlag('dataBoard:boards:market', '市场分析看板'),
    }),
    yytBoards: createFlag('dataBoard:yytBoards', '营运通数据看板'),
  }),
};

export { ROUTE_FLAGS };
